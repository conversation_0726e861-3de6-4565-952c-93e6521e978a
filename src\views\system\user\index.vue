<template>
  <div class="app-container">
    <!--查询  -->
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="tableQuery" :rules="rules">
      <el-row>
        <el-col :span="24" :xs="24">
          <!-- 登录名/手机号 -->
          <el-form-item prop="loginName"><el-input style="width: 200px; margin-right: 15px" v-model="tableQuery.loginName" placeholder="手机号"></el-input></el-form-item>
          <el-form-item prop="realName"><el-input style="width: 200px; margin-right: 15px" v-model="tableQuery.realName" placeholder="用户姓名"></el-input></el-form-item>
          <el-form-item prop="roleTag">
            <el-select style="width: 200px" v-model="tableQuery.roleTag" filterable value-key="value" placeholder="请选择角色" clearable>
              <el-option v-for="(item, index) in roleNewOptions" :key="index" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <span style="margin-right: 15px"></span>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  this.$refs['dataQuery'].validate((valid) => {
                    if (!valid) return;
                    this.fetchData();
                  });
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div style="margin-bottom: 20px"></div>
    <!--    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleCreate" v-perm="'b:user:add'">-->
    <!--      {{ textMap.create }}-->
    <!--    </el-button>-->
    <el-row>
      <el-button type="primary" @click="handleCreate()">新增用户</el-button>
    </el-row>
    <div style="margin-bottom: 20px"></div>
    <!--列表-->
    <el-table
      style="width: 100%"
      :data="tableData"
      v-loading.body="tableLoading"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#666' }"
    >
      <el-table-column prop="id" label="用户id"></el-table-column>
      <el-table-column prop="realname" label="用户姓名" width="150"></el-table-column>
      <el-table-column prop="username" label="手机号" width="150"></el-table-column>
      <!-- <el-table-column prop="merchantName" label="商户名" width="310"></el-table-column> -->
      <el-table-column label="角色">
        <template slot-scope="scope">
          <el-tag style="margin: 2px" v-for="role in scope.row.roles" :key="role.val">{{ role.name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="创建时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.createTime)"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="time" label="更新时间">
        <template slot-scope="scope">
          <span v-text="parseTime(scope.row.updated)"></span>
        </template>
      </el-table-column> -->
      <el-table-column prop="merchantCode" label="操作" width="150px">
        <template v-slot="{ row, $index }">
          <el-button type="text" @click="handleUpdate($index, row)">编辑用户</el-button>
          <!-- <el-button class="red" type="text" @click="handleDelete($index, row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-bottom: 30px"></div>
    <!--分页-->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="tablePage.currentPage"
      :page-sizes="[10, 20, 30, 40, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tablePage.totalItems"
    ></el-pagination>
    <!--弹出窗口：新增/编辑用户-->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" @close="closeCreat()" width="50%" :close-on-click-modal="false">
      <el-form :rules="NewRules" ref="dataForm" :model="temp" label-position="left" label-width="120px" v-loading="userLoading">
        <el-form-item label="用户姓名" prop="realName">
          <el-input v-model="temp.realName" placeholder="请输入用户姓名" :disabled="!(this.dialogStatus == 'create')"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="temp.mobile"
            placeholder="请输入手机号"
            :disabled="!(this.dialogStatus == 'create')"
            type="text"
            oninput="value=value.replace(/[^\d]/g, '')"
            maxlength="11"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="关联商户号" prop="merchantCode">
          <el-input v-model="temp.merchantCode" placeholder="请输入关联商户号" :disabled="!(this.dialogStatus == 'create')"></el-input>
        </el-form-item> -->

        <el-form-item label="角色" label-width="120px">
          <el-select v-model="temp.roleValList" multiple placeholder="请选择" style="width: 100%">
            <el-option v-for="item in roleNewOptions" :key="item.id" :label="item.name" :value="dialogStatus == 'create' ? item.val : item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="密码" prop="pwd">
          <el-input type="password" v-model="temp.pwd" :placeholder="textMap2[dialogStatus]"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPwd">
          <el-input type="password" v-model="temp.confirmPwd" placeholder="请再次输入密码"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCreat()" :disabled="userLoading">取消</el-button>
        <el-button v-if="this.dialogStatus == 'create'" type="primary" @click="addUser" :disabled="userLoading">确定</el-button>
        <el-button v-else type="primary" @click="updateData" :disabled="userLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import optionApi from '@/api/option';
  import userApi from '@/api/user';
  import { parseTime, resetTemp } from '@/utils';
  import { root, confirm, pageParamNames } from '@/utils/constants';
  import debounce from 'lodash/debounce';
  import { isvalidPhone } from '@/utils/validate';
  import { log } from 'bpmn-js-token-simulation';

  export default {
    name: 'UserManage',

    data() {
      var validPhone = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入电话号码'));
        } else if (!isvalidPhone(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      };

      var validatePass = (rule, value, callback) => {
        if (!value) {
          callback();
        } else {
          if(value.length < 6){
            callback(new Error('密码长度不能小于6位'));
          }else{
            callback();
          }
        }
      };

      let validatePass2 = (rule, value, callback) => {
        // if (value === '') {
        //   callback(new Error('请再次输入密码'));
        // } else
        if (value != this.temp.pwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        userLoading: false,
        roleNewOptions: [], //新角色集合
        parseTime: parseTime,
        tableLoading: false,
        tableData: [],
        tableQuery: {
          loginName: null,
          roleTag: null,
          realName: null
        },
        // tablePage: {
        //   current: null,
        //   pages: null,
        //   size: null,
        //   total: null
        // },
        tablePage: {
          currentPage: 1,
          totalPage: null,
          size: 10,
          totalItems: null
        },
        dialogFormVisible: false,
        editRolesDialogVisible: false,
        dialogStatus: '',
        // temp: {
        //   idx: null, //tableData中的下标
        //   uid: null,
        //   uname: null,
        //   nick: null,
        //   pwd: null,
        //   pwd2: null,
        //   created: null,
        //   updated: null
        // },
        temp: {
          mobile: null,
          realName: null,
          pwd: null,
          confirmPwd: null,
          merchantCode: null,
          roleValList: []
        },
        //新增
        newDialogFormVisible: false,
        NewRules: {
          mobile: [
            {
              required: true,
              message: '请填写手机号',
              trigger: 'blur'
            },
            {
              validator: validPhone,
              trigger: 'blur'
            }
          ],
          realName: [
            {
              required: true,
              message: '请填写用户名称',
              trigger: 'blur'
            },
            {
              pattern: /.*\S.*/,
              message: '请输入正确的用户姓名',
              trigger: 'blur'
            }
            // {
            //   validator: validPhone,
            //   trigger: "blur",
            // },
          ],
          merchantName: [
            {
              required: true,
              message: '请填写商户名称',
              trigger: 'blur'
            }
            // {
            //   validator: validPhone,
            //   trigger: "blur",
            // },
          ],
          pwd: [{ required: false, pattern: /^\S+$/, message: '密码不可包含空字符', trigger: 'blur' },{ validator: validatePass, trigger: 'change' }],
          confirmPwd: [{ validator: validatePass2, trigger: 'change' }]
        },
        // 添加用户的角色的数据
        createUserRolesDataRids: [],
        password2: null, //新建用户确认密码
        textMap: {
          update: '编辑用户',
          create: '新增用户'
        },
        textMap2: {
          update: '如需修改，请输入密码',
          create: '请输入密码'
        },
        rules: {
          realName: [{ required: false, min: 0, max: 10, message: '推荐人姓名最多输入10个字符', trigger: 'blur' }],
          mobile: [
            { required: false, min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ]
        },
        checkAll: false,
        isIndeterminate: true,
        //所有角色(管理员除外)
        roleOptions: [],
        roleMap: new Map(),
        // 更新用户的角色的数据
        updateUserRolesData: {
          idx: null,
          id: null,
          rids: []
        }
      };
    },

    created() {
      this.initData();
      this.fetchData();
    },

    watch: {
      //延时查询
      'tableQuery.nick': debounce(function () {
        this.fetchData();
      }, 500)
    }, //watch

    methods: {
      //新增
      handleCreate() {
        resetTemp(this.temp);
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate();
        });
      },
      closeCreat() {
        this.dialogFormVisible = false;
        this.userLoading = false;
        this.temp = {
          mobile: null,
          realName: null,
          pwd: null,
          confirmPwd: null,
          merchantCode: null,
          roleValList: []
        };
      },
      // 新增
      addUser() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          this.temp.merchantCode = JSON.parse(localStorage.getItem('sysUserInfo')).merchantCode;
          // var creatData = Object.assign({}, this.addData)//copy obj=
          this.userLoading = true;
          userApi
            .addNewUser(this.temp)
            .then((res) => {
              // 表格中更新时间
              // return console.log(res);
              // tempData.updated = res.data.updated
              this.userLoading = false;
              this.closeCreat();
              this.$message.success('添加成功');
              this.fetchData();
            })
            .catch((res) => {
              this.userLoading = false;
            });
        });
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        // this.tableQuery.roleTag = '';
        this.fetchData();
      },
      initData() {
        //所有角色选项
        var that = this;
        //获取全部新角色
        userApi.queryRole().then((res) => {
          res.data.forEach((obj) => {
            if (obj.value != root.rval) {
              //排除管理员
              var rd = { id: obj.id, name: obj.rname, val: obj.rval };
              that.roleNewOptions.push(rd);
            }
          });
        });
        // //获取全部角色
        // optionApi.listRoleOptions().then((res) => {
        //   res.data.forEach((obj) => {
        //     //
        //     if (obj.value != root.rval) {
        //       //排除管理员
        //       var rd = { id: obj.value, val: obj.label };
        //       that.roleOptions.push(rd);
        //       that.roleMap.set(obj.value, obj.label);
        //     }
        //   });
        // });
      },

      hasAdminRole(row) {
        if (row && row.roles) {
          return row.roles.some((role) => role.rval == root.rval);
        }
        return false;
      },

      //全选
      handleCheckAllChange(val) {
        let allRids = this.roleOptions.map((role) => role.id);
        this.updateUserRolesData.rids = val ? allRids : [];
        this.isIndeterminate = false;
      },

      //分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      //查询
      fetchData() {
        this.tableLoading = true;
        userApi.queryUser(this.tableQuery, this.tablePage).then((res) => {
          this.tableData = res.data.data;
          this.tableLoading = false;
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
      },
      bindCheck() {
        console.log(this.updateUserRolesData);
      },
      //更新
      handleUpdate(idx, row) {
        console.log(row, 'row');
        // this.temp = Object.assign({}, row); // copy obj
        // this.temp.idx = idx
        this.temp.mobile = row.username;
        this.temp.realName = row.realname;
        this.temp.id = row.id;
        // let roleIds = row.roles.map((role) => role.val)
        // roleIds = this.roleNewOptions.filter(item => roleIds.includes(item.id))
        // this.temp.roleIds = roleIds.map((role) => role.val);
        this.temp.roleValList = row.roles.map((role) => role.val);
        console.log(this.temp.roleIds, 'this.temp.roleIds');

        // this.temp.pwd = 123456;
        // this.temp.confirmPwd = 123456;
        this.dialogStatus = 'update';
        this.dialogFormVisible = true;
        this.$nextTick(() => this.$refs['dataForm'].clearValidate());
      },
      updateData() {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) return;
          var tempData = Object.assign({}, this.temp); //copy obj=
          // tempData.roleIds = this.updateUserRolesData.rids;
          this.userLoading = true;
          userApi.editNewUser(tempData).then((res) => {
            console.log(res);
            this.userLoading = false;
            // 表格中更新时间
            // tempData.updated = res.data.updated
            this.dialogFormVisible = false;
            this.$message.success('更新成功');
          }).catch((err) => {
            this.userLoading = false;
          })
        });
      }

      // //删除
      // handleDelete(idx, row) {
      //   const h = this.$createElement;
      //   this.$confirm('提示', {
      //     title: '提示',
      //     message: h('div', [h('p', `删除后，该用户将无法使用该账号登录`), h('p', `请确认，是否删除“${row.realname}”用户?`)]),
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   })
      //     .then(() => {
      //       userApi.deleteUser({ id: row.id }).then((res) => {
      //         this.tableData.splice(idx, 1);
      //         this.tablePage.totalItems;
      //         this.dialogFormVisible = false;
      //         this.$message.success('删除成功');
      //       });
      //     })
      //     .catch(() => {
      //       this.$message.info('已取消删除');
      //     });
      // },
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .role-checkbox {
    margin-left: 0px;
    margin-right: 15px;
  }
  .el-form-item.w100 > :last-child {
    width: calc(100% - 120px) !important;
    // :last-child {
    //   width: 100% !important;
    // }
    .el-select {
      width: 100% !important;
    }
  }
</style>
