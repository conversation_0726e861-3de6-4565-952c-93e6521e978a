
import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const systemRouter = {
  path: '/system',
  component: Layout,
  meta: {
    perm: 'm:sys',
    title: '权限管理',
    icon: 'chart'
  },
  children: [
    {
      path: 'user_manage',
      name: 'user_manage',
      component: _import('system/user/index'),
      meta: {
        perm: 'm:sys:user',
        title: '用户管理',
        noCache: true,
        icon: 'usermanage'
      }
    },
    {
      path: 'role_manage',
      name: 'role_manage',
      component: _import('system/role/index'),
      meta: {
        perm: 'm:sys:role',
        title: '角色管理',
        noCache: true,
        icon: 'roleTagManage'
      }
    },
    {
      hidden: true,
      path: 'role_manage/:roleId/assign_perm',
      name: 'role_manage_assign_perm',
      component: _import('system/role/assign_perm'),
      meta: {
        hiddenTag: true,
        title: '角色授权'
      }
    },
    {
      path: 'perm_manage',
      name: 'perm_manage',
      component: _import('system/perm/index'),
      meta: {
        perm: 'm:sys:perm',
        title: '权限管理',
        noCache: true,
        icon: 'role_perm'
      }
    }
  ]
}

export default systemRouter
