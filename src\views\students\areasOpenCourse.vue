<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程分类:">
            <el-select v-model="dataQuery.categoryCode" filterable value-key="value" placeholder="请选择" @change="check(dataQuery.categoryCode)">
              <el-option v-for="(item, index) in categoryType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.bigClassCode" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in bigClassType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程学段:">
            <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程等级:">
            <el-select v-model="dataQuery.courseLevel" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseLevelType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.courseType" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseTypes" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="教材版本:">
            <el-select v-model="dataQuery.courseEdition" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseEditionType" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button size="small" type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="warning" icon="el-icon-plus" @click="openCourse()">开通</el-button>
        <el-button size="small" type="primary" @click="headerList()" style="margin: 0 0 20px 20px">列表显示属性</el-button>
      </div>

      <el-table
        class="course-table"
        ref="multipleTable"
        v-loading="tableLoading"
        :key="itemKey"
        tooltip-effect="dark"
        :row-key="getRowKey"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        :cell-style="{ 'text-align': 'center' }"
        :default-sort="{ prop: 'date', order: 'descending' }"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="item.value == 'courseCode' || item.value == 'courseName' || item.value == 'courseCover' ? 180 : ''"
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'courseCover'" class="demo-image__preview">
              <el-image v-if="row.courseCover" class="table_list_pic" :src="row.courseCover" @click="openImg(row)"></el-image>
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 表格 -->
      <!-- <el-table class="course-table" ref="multipleTable" v-loading="tableLoading" :key="itemKey" tooltip-effect="dark"
        :row-key="getRowKey" :data="tableData" @selection-change="handleSelectionChange" stripe border
        :default-sort="{ prop: 'date', order: 'descending' }">
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="courseCode" label="课程编号" width="140" sortable align="center"></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="180" sortable align="center"></el-table-column>
        <el-table-column prop="categoryName" label="所属分类" width="" sortable align="center"></el-table-column>
        <el-table-column prop="bigClassName" label="课程类型" width="" sortable align="center"></el-table-column>
        <el-table-column prop="coursStageName" label="课程学段" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseLevelName" label="课程等级" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseTypeName" label="课程类型" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseEditionName" label="教材版本" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseContentTypeName" label="题目类型" width="" sortable align="center"></el-table-column>
        <el-table-column prop="wordCount" label="单词数" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseCover" label="课程封面" width="150" sortable align="center">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.courseCover" class="table_list_pic" :src="scope.row.courseCover"
                @click="openImg(scope.row)"></el-image>
            </div>
          </template>
        </el-table-column> -->

      <!-- <el-table-column prop="courseUnitPrice" label="课程报名价" width="120" sortable></el-table-column> -->
      <!-- <el-table-column prop="courseMemberDiscount" label="会员折扣" width="110" sortable></el-table-column> -->
      <!-- </el-table> -->
    </div>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog title="授权码录入" :visible.sync="showAuthCode" width="30%" :close-on-click-modal="false">
      <el-form :inline="true" style="margin-bottom: 20px" model="form">
        <h3 style="text-align: center">超出异地登录开课限制，如需继续操作，请输入授权码</h3>
        <br />
        <el-form-item label="风控授权码:" prop="authCode" style="width: 100%; text-align: center">
          <el-input placeholder="请输入授权码" v-model="authCode" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="info" @click="closeAuthCode()">取消</el-button>
        <el-button size="mini" type="primary" :disabled="disabledA" @click="BindingCode()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="30%" :before-close="handleClose">
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import courseApi from '@/api/student/areasStudentCourseList';
  import courseApitwo from '@/api/student/courseChildren';
  import enTypes from '@/api/student/bstatus';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';

  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';

  import { pageParamNames } from '@/utils/constants';
  import { ossPrClient } from '@/api/alibaba';
  import riskAuthCodeApi from '@/api/student/riskAuthCode';
  export default {
    name: 'areasOpenCourse',
    components: {
      HeaderSettingsDialog
    },
    data() {
      return {
        disabledA: '',
        tableLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        submitOpenCourse: {
          studentCode: '',
          courseId: []
        },
        selectKey: [],
        tableLoading: false,
        dataQuery: {
          categoryCode: '',
          categoryName: ''
        },
        activeType: [], // 活动类型

        tableData: [], //表格数据
        //授权码录入
        showAuthCode: false,
        authCode: undefined,
        dialogVisible: false, // 修改弹窗是否展示
        addOrUpdate: true, // 是新增还是修改
        addCourseData: {}, // 新增课程
        updateCourseData: {}, // 修改数据
        rules: {
          // 表单提交规则
          categoryCode: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          bigClassCode: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          courseStage: [
            {
              required: true,
              message: '必填',
              trigger: 'blur'
            }
          ],
          courseLevel: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseEdition: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseName: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseUnitPrice: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseMemberDiscount: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseContentType: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseType: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          fileVersion: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          courseDescription: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ],
          isEnable: [
            {
              required: true,
              message: '必填',
              trigger: 'change'
            }
          ]
        },

        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表

        fileDetailListPending: [], // 待处理已上传图片信息
        fileDetailList: [], // 上传图片已有图片列表

        uploadLoading: false, // 上传图片加载按钮
        fullscreenLoading: false, // 保存啥的加载

        content: '',
        isUploadSuccess: true, // 是否上传成功
        isShowRelevance: true, // 新增或修改是否展示关联产品
        itemKey: '',
        radio: '0', //单选框状态 值必须是字符串
        value1: [],
        categoryType: [], //课程分类
        bigClassType: [], //课程类型
        courseStageType: [], //课程学段类型
        courseEditionType: [], //教材版本
        courseTypes: [], //课程类型
        courseLevelType: [], //课程等级
        multipleSelection: [], //多选数据
        fromCourse: false, // 来自页面

        coverImg: '',
        dialogOpenimg: false,

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '课程编号',
            value: 'courseCode'
          },
          {
            name: '课程名称',
            value: 'courseName'
          },
          {
            name: '所属分类',
            value: 'categoryName'
          },
          {
            name: '课程类型',
            value: 'bigClassName'
          },
          {
            name: '课程学段',
            value: 'coursStageName'
          },
          {
            name: '课程等级',
            value: 'courseLevelName'
          },
          {
            name: '课程类型',
            value: 'courseTypeName'
          },
          {
            name: '教材版本',
            value: 'courseEditionName'
          },
          {
            name: '题目类型',
            value: 'courseContentTypeName'
          },
          {
            name: '单词数',
            value: 'wordCount'
          },
          {
            name: '课程封面',
            value: 'courseCover'
          }
        ],
        tableHeaderList: [] // 获取表头数据
      };
    },
    created() {
      this.dataQuery.studentCode = window.localStorage.getItem('studentCode');
      this.dataQuery.merchantCode = window.localStorage.getItem('merchantCode');
      //this.dataQuery.studentCode=  ls.getItem('areaStudentCode');
      this.fetchData();
      ossPrClient();
      this.fromCourse = this.$route.query.fromCourse ?? '';
      this.dataQuery.courseStage = this.$route.query.level ?? '';
      this.getCategoryType();
      //获取学段下拉框
      this.getStady();
      //获取课程等级下拉框
      this.getCourseLevel();
      //获取课程类型下拉学段
      this.getCourseType();
      //获取教材版本下拉学段
      this.getCourseEditionType();
      this.getHeaderlist();
    },
    methods: {
      handleClose() {
        this.coverImg = '';
        this.dialogOpenimg = false;
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      getRowKey(row) {
        return row.id;
      },
      //重置
      rest() {
        this.dataQuery.courseCode = '';
        this.dataQuery.categoryCode = '';
        this.dataQuery.courseName = '';
        this.dataQuery.bigClassCode = '';
        this.dataQuery.courseStage = '';
        this.dataQuery.courseLevel = '';
        this.dataQuery.courseType = '';
        this.dataQuery.courseEdition = '';
        this.fetchData01();
      },
      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.itemKey = Math.random();
        this.fetchData();
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        courseApi.courseList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
        });
      },
      // 这里
      openCourse() {
        if (this.fromCourse) {
          if (this.multipleSelection.length <= 0) {
            this.$message('请选择课程');
            return false;
          }
          let multipleSelection = this.multipleSelection.map((item) => {
            return {
              courseId: item.id,
              courseName: item.courseName
            };
          });
          window.localStorage.setItem('multipleSelection', JSON.stringify(multipleSelection));
          this.$router.push({
            path: '/course/coursePackageList',
            name: 'coursePackageList',
            params: {
              choose: this.fromCourse
            }
          });
        } else {
          this.submitOpenCourse = {
            studentCode: '',
            courseId: []
          };
          if (this.multipleSelection.length <= 0) {
            this.$message('请选择课程');
            return false;
          }
          if (this.multipleSelection.length > 3) {
            this.$message('选择的单词题型不能超过3个');
            return false;
          }
          const studentCode = window.localStorage.getItem('studentCode');
          const merchantCode = window.localStorage.getItem('merchantCode');
          console.log(studentCode);
          this.submitOpenCourse.studentCode = studentCode;
          this.submitOpenCourse.merchantCode = merchantCode;
          for (let i = 0; i < this.multipleSelection.length; i++) {
            this.submitOpenCourse.courseId.push(this.multipleSelection[i].id);
          }
          this.$confirm('确定操作吗?', '开课', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            courseApi.submitOpenCourse(this.submitOpenCourse).then((res) => {
              if (!res.data.userCode) {
                this.dialogVisible = false;
                this.disabledA = false;
                this.showAuthCode = true;
                return;
              }

              this.showBackRecharge = false;
              this.submitOpenCourse = {
                studentCode: window.localStorage.getItem('studentCode'),
                merchantCode: window.localStorage.getItem('merchantCode'),
                courseId: []
              };
              this.$message.success('开课成功');
              this.fetchData01();
            });
          });
        }
      },

      BindingCode() {
        if (!this.authCode) {
          this.$message.warning('授权码不能为空');
          return;
        }
        riskAuthCodeApi
          .bindingCode(this.authCode)
          .then((res) => {
            if (res.success) {
              this.$message.success('授权码录入成功');
              this.showAuthCode = false;
              this.authCode = '';
              this.submitBackRecharge();
            }
          })
          .catch((err) => {});
      },
      closeAuthCode() {
        this.showAuthCode = false;
        this.authCode = '';
      },
      // 多选的值
      handleSelectionChange(val) {
        // console.log(val);
        this.multipleSelection = val;
      },

      // 状态改变事件
      change(radio) {
        if (radio == '1') {
          this.addCourseData.isEnable = 1;
        } else {
          this.addCourseData.isEnable = 0;
        }
      },

      // 获取分类返回类型
      getCategoryType() {
        courseApitwo.categoryType().then((res) => {
          this.categoryType = res.data;
        });
      },
      //根据课程分类获取课程类型的下拉列表
      check(categoryCode) {
        console.log(categoryCode);
        courseApi.checkClassification(categoryCode).then((res) => {
          this.bigClassType = res.data;
        });
      },
      //获取学段下拉框
      getStady() {
        var enType = 'CourseStage';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取课程等级下拉框
      getCourseLevel() {
        var enType = 'CourseLevel';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseLevelType = res.data;
        });
      },
      //获取课程类型下拉学段
      getCourseType() {
        var enType = 'CourseType';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseTypes = res.data;
        });
      },
      //获取教材版本下拉学段
      getCourseEditionType() {
        // var enType = "CourseEdition";
        enTypes.getEnumerationcheckList().then((res) => {
          this.courseEditionType = res.data;
        });
      },
      //获开通和暂停
      change1(radio) {
        this.addCourseData.isEnable = 1;
      },
      change2(radio) {
        this.updateCourseData.isEnable = 1;
      },
      change4(radio) {
        this.updateCourseData.isEnable = 0;
      },
      change3(radio) {
        this.addCourseData.isEnable = 0;
      },

      //进入子类
      enterChildrenList(courseCode, courseContentType, courseName) {
        console.log(courseContentType + 'wyy');
        const that = this;
        if (courseContentType === 'Word') {
          that.$router.push({
            path: '/course/courseMake',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Reading' || courseContentType === 'ClozeTest') {
          that.$router.push({
            path: '/course/courseReading',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Listening') {
          that.$router.push({
            path: '/course/courseListening',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'WenZong') {
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
      },

      openImg(row) {
        this.coverImg = row.courseCover;
        this.dialogOpenimg = true;
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'areasOpenCourse',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'areasOpenCourse'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 15px 0 0 15px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .mt22 {
    margin-top: 22px;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;

    /* .el-dialog__body{
    text-align:center !important;
  } */
  }
</style>
