<!-- 查询班级上课时间下拉框组件 -->
<template>
  <el-popover v-model="visible">
    <div class="popover-content">
      <!-- 星期选择列表 -->
      <div class="week-list">
        <div
          v-for="week in weeklist"
          :key="week.value"
          class="week-list-item"
          :class="{ 'week-list-item-active': weekSelect.includes(week.value) }"
          @click="handleClickWeek(week.value)"
        >
          {{ week.label }}
        </div>
      </div>

      <!-- 上课时间列表 -->
      <div class="study-time-list" v-loading="studyTimeLoading">
        <template v-if="studyTimeList.length > 0">
          <div v-for="(item, index) in studyTimeList" :key="index" class="study-time-list-item">
            <div class="item-title">{{ weeksFormat(weeklist, item.usableWeek) }}</div>
            <div class="time-list">
              <div
                v-for="(time, index) in item.timeList"
                :key="index"
                class="time-list-item"
                :class="{ 'time-list-item-active': time.id == studyTimeSelectId }"
                @click="handleClickStudyTime(item.usableWeek, time)"
              >
                {{ time.text }}
              </div>
            </div>
          </div>
        </template>
        <div v-else class="study-time-list-empty">暂无数据</div>
      </div>
    </div>
    <el-input
      slot="reference"
      v-model="studyTimeSelectText"
      class="study-time-input"
      readonly
      :disabled="disabled"
      :placeholder="placeholder"
      :style="studyTimeInputStyle"
      @focus="handleInputClick"
    >
      <span slot="suffix" v-if="clearable" style="padding: 0 6px" @click.stop="handleClearIconClick()">
        <i class="el-icon-arrow-down"></i>
        <i class="el-icon-close"></i>
      </span>
      <span slot="suffix" v-else style="padding: 0 6px">
        <i class="el-icon-arrow-down" style="height: auto"></i>
      </span>
    </el-input>
  </el-popover>
</template>

<script>
  import { getStudyDateData } from '@/api/oneToManyClass/pendingCompletionClassInfo';
  function debounce(func, wait = 1000, immediate = false) {
    let timer;
    console.log(1);
    return function () {
      console.log(123);
      let context = this,
        args = arguments;
      if (timer) clearTimeout(timer);
      if (immediate) {
        let callNow = !timer;
        timer = setTimeout(() => {
          timer = null;
        }, wait);
        if (callNow) func.apply(context, args);
      } else {
        timer = setTimeout(() => {
          func.apply(context, args);
        }, wait);
      }
    };
  }
  export default {
    name: 'BaseClassStudyTimeSelect',
    model: {
      prop: 'value',
      event: 'input'
    },
    props: {
      value: {
        type: String,
        default: ''
      },
      // 课程大类id
      curriculumId: {
        type: String,
        default: ''
      },
      // 年级(形如：'1,2')
      grade: {
        type: String,
        default: ''
      },
      // 是否体验课
      isExperience: {
        type: Boolean,
        default: false
      },
      // 显示的时间分隔符
      timeSeparator: {
        type: String,
        default: '~'
      },
      // 是否可清除
      clearable: {
        type: Boolean,
        default: false
      },
      // 提示语
      placeholder: {
        type: String,
        default: '请选择'
      },
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        timeOutId: null,
        needUpdate: false,
        visible: false,
        weekSelect: [],
        weeklist: [
          { value: '1', label: '周一' },
          { value: '2', label: '周二' },
          { value: '3', label: '周三' },
          { value: '4', label: '周四' },
          { value: '5', label: '周五' },
          { value: '6', label: '周六' },
          { value: '7', label: '周日' }
        ],
        oldQueryWeek: '',
        studyTimeList: [],
        studyTimeLoading: false,
        studyTimeSelectId: '',
        studyTimeSelectText: '',
        downEl: null,
        closeEl: null
      };
    },
    computed: {
      studyTimeInputStyle() {
        return { '--study-time-input': this.visible ? '180deg' : '0deg' };
      },
      disabledGet() {
        return this.disabled || !(this.curriculumId && this.grade);
      }
    },
    watch: {
      visible(val) {
        if (val) {
          if (this.disabledGet) {
            setTimeout(() => {
              this.visible = false;
            });
            return;
          }
          (this.studyTimeList.length == 0 || this.needUpdate) && this.getStudyTimeListData(this.weekSelect);
        }
      },
      curriculumId() {
        this.weekSelect = [];
        this.needUpdate = true;
      },
      grade() {
        this.handleClearIconClick({ gradeChange: true });
        this.needUpdate = true;
      },
      value(val) {
        if (!val) {
          this.studyTimeList = [];
          this.studyTimeSelectId = '';
          this.studyTimeSelectText = '';
          this.weekSelect = [];
        }
      }
    },
    mounted() {
      if (this.clearable) {
        this.$nextTick(() => {
          document.getElementsByClassName('study-time-input')[0].addEventListener('mouseenter', this.handleMouseEnter);
          document.getElementsByClassName('study-time-input')[0].addEventListener('mouseleave', this.handleMouseLeave);
        });
      }
    },
    beforeDestroy() {
      if (this.clearable) {
        document.getElementsByClassName('study-time-input')[0].removeEventListener('mouseenter', this.handleMouseEnter);
        document.getElementsByClassName('study-time-input')[0].removeEventListener('mouseleave', this.handleMouseLeave);
      }
    },
    methods: {
      handleInputClick() {
        if (this.curriculumId && this.grade) return;
        let tipText = '请先选择';
        if (!this.curriculumId) {
          tipText += '课程';
          this.$message.error(tipText);
        } else if (!this.grade) {
          tipText += '年级';
          this.$message.error(tipText);
        }
      },
      handleClickWeek(value) {
        if (this.weekSelect.includes(value)) {
          this.weekSelect = this.weekSelect.filter((item) => item !== value);
        } else {
          this.weekSelect.push(value);
        }
        this.delayFunction();
      },
      delayFunction: debounce(function () {
        this.getStudyTimeListData(this.weekSelect);
      }, 1000),
      handleClickStudyTime(week, time) {
        this.studyTimeSelectId = time.id;
        const weekText = this.weeksFormat(this.weeklist, week);
        let studyTimeSelect;
        if (this.isExperience) {
          studyTimeSelect = {
            id: time.id,
            week: week,
            weekText: weekText,
            startTime: time.startTime,
            endTime: time.endTime
          };
        } else {
          studyTimeSelect = [];
          let weekArray = week.split('');
          weekArray.forEach((val) => {
            studyTimeSelect.push({
              id: time.id,
              usableWeek: Number(val),
              startTime: time.startTime,
              endTime: time.endTime
            });
          });
        }
        this.studyTimeSelectText = `${weekText} ${time.text}`;
        this.$emit('input', time.id);
        this.$emit('change', studyTimeSelect);
        this.visible = false;
      },
      getStudyTimeListData(weekSelect) {
        this.studyTimeLoading = true;
        const week = weekSelect.sort().join('');
        this.oldQueryWeek = week;
        getStudyDateData({ type: this.isExperience ? 1 : 2, curriculumId: this.curriculumId, grade: this.grade, week: week })
          .then((res) => {
            if (this.oldQueryWeek == week) {
              this.oldQueryWeek = '';
              this.studyTimeList = this.handleStudyDateData(res.data);
              this.studyTimeLoading = false;
              this.needUpdate = false;
            }
          })
          .catch(() => {
            this.studyTimeList = [];
            this.studyTimeLoading = false;
            this.needUpdate = false;
          });
      },
      // 处理请求获取的上课时间数据
      handleStudyDateData(res) {
        if (!Array.isArray(res)) {
          return [];
        }
        let studyTimeList = [];
        res.forEach((weekItem) => {
          let item = { usableWeek: '', timeList: [] };
          item.usableWeek = String(weekItem.week);
          weekItem.result.forEach((timeItem) => {
            item.timeList.push({
              id: timeItem.id,
              startTime: timeItem.startTime,
              endTime: timeItem.endTime,
              text: `${timeItem.startTime}${this.timeSeparator}${timeItem.endTime}`
            });
          });
          studyTimeList.push(item);
        });
        return studyTimeList;
      },
      handleClearIconClick({ gradeChange } = {}) {
        if (this.studyTimeSelectText) {
          this.studyTimeSelectId = '';
          this.studyTimeSelectText = '';
          this.weekSelect = [];
          this.$emit('input', '');
          this.$emit('change', this.isExperience ? {} : []);
          if (gradeChange) {
            this.value && this.$message.error('年级已变更，请重新选择上课时间');
          } else {
            this.handleMouseLeave();
          }
        }
      },
      weeksFormat(array, usableWeek) {
        let values = String(usableWeek).split('');
        let formatText = '';
        if (Array.isArray(values)) {
          values.forEach((each) => {
            let week = array.find((item) => item.value == each);
            if (week) {
              formatText += `${week.label}、`;
            }
          });
        }
        formatText = formatText.substring(0, formatText.length - 1) + ' ';
        return formatText;
      },
      handleMouseEnter() {
        this.downEl = this.downEl || document.querySelector('.study-time-input .el-icon-arrow-down');
        this.closeEl = this.closeEl || document.querySelector('.study-time-input .el-icon-close');
        this.downEl.style.display = this.studyTimeSelectText ? 'none' : 'inline-block';
        this.closeEl.style.display = this.studyTimeSelectText ? 'inline-block' : 'none';
      },
      handleMouseLeave() {
        this.downEl = this.downEl || document.querySelector('.study-time-input .el-icon-arrow-down');
        this.closeEl = this.closeEl || document.querySelector('.study-time-input .el-icon-close');
        this.downEl.style.display = 'inline-block';
        this.closeEl.style.display = 'none';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .study-time-input {
    ::v-deep .el-input__suffix .el-icon-arrow-down {
      transform: rotate(var(--study-time-input));
      transition: transform 0.3s ease-in-out;
    }
    ::v-deep .el-input__suffix .el-icon-close {
      display: none;
    }
  }
  .popover-content {
    display: flex;
    .week-list {
      display: flex;
      flex-direction: column;
      border-right: 1px solid #e4e7ed;
      .week-list-item {
        user-select: none;
        min-width: 50px;
        padding: 10px;
        border-radius: 4px;
        margin-right: 12px;
        margin-bottom: 6px;
        font-weight: bold;
        &:hover {
          background-color: #f5f5f5;
        }
      }
      .week-list-item-active {
        color: #409eff;
      }
    }
    .study-time-list {
      width: 370px;
      height: 350px;
      padding-left: 12px;
      overflow-y: scroll;
      .study-time-list-item {
        .item-title {
          padding: 10px 0;
          margin-bottom: 6px;
          font-weight: bold;
        }
        .time-list {
          display: flex;
          flex-wrap: wrap;
          max-width: 350px;
          .time-list-item {
            padding: 10px;
            margin-right: 15px;
            margin-bottom: 6px;
            border-radius: 4px;
            background-color: #f5f5f5;
          }
          .time-list-item-active {
            color: #fff;
            background-color: #409eff;
          }
        }
      }
      .study-time-list-empty {
        text-align: center;
        line-height: 350px;
        font-weight: bold;
        font-size: 20px;
      }
    }
    ::v-deep .study-time-list .el-loading-spinner {
      position: absolute !important;
    }
  }
</style>
