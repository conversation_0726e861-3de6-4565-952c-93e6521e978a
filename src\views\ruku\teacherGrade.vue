<template>
  <div style="padding: 30px;">
    <div style="margin-bottom: 20px;display: flex;align-items: center;justify-content:space-between;font-weight: bold">
      <div>
        <span>教练等级</span>
        <span style="margin-left: 30px;color: red">必须先设置初级教练，并且正式课开始值与试课开始值为1，否则无法填写试课与上课反馈！影响教练工资发放！！！！！</span>
      </div>
      <el-button type="primary" @click="teacherGradeoperate('add', null)">新增教练等级</el-button>
    </div>
    <el-table :data="teacherlist" border style="width: 100%" :header-cell-style="getRowClass">
      <el-table-column prop="name" label="名称" width="" align="center"></el-table-column>
      <el-table-column prop="value" label="正式课开始值（学时）" width="" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.value) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="configState" label="试课开始值（学时）" width="" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.configState) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" label="等级" width="" align="center">
        <template slot-scope="scope">
          <span>{{ '等级' + convertToChinese(scope.row.level) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="满足条件" width="" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.remark == 1 ? '满足一个条件即可' : '满足两个条件即可' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="teacherGradeoperate('edit', scope.row)">编辑</el-button>
          <!-- <el-button type="danger" size="small" @click="openDelete(scope.row.id)" v-if="scope.row.id != teacherlist[0].id">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="设置教练等级" :visible.sync="dialogVisible" :width="screenWidth > 1300 ? '40%' : '95%'"
      :before-close="handleClose" :close-on-click-modal="false">
      <el-form ref="levelForm" :model="levelList" :label-width="screenWidth > 1300 ? '180px' : '100px'" :rules="rules">
        <el-form-item label="名称" prop="name">
          <el-select v-model="levelList.name" placeholder="请选择" @change="changeLevel">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-input v-model="levelList.name" @input="change" placeholder="请先输入" style="width: 180px;"></el-input> -->
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input v-model="levelList.level" placeholder="选择名称后自动带入" style="width: 200px;" disabled></el-input>
          <!-- <el-input v-model="levelList.level" @input="change" type="number" :min="1" placeholder="请输入数字"
                        style="width: 180px;" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')"></el-input> -->
        </el-form-item>
        <el-form-item label="正式课开始值（学时）" prop="value">
          <el-input :min="1" placeholder="请输入" v-model="levelList.value" style="width: 200px;" @input="change"
            type="number" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')"></el-input><span
            style="margin-left: 20px;">节</span>
        </el-form-item>
        <el-form-item label="试课开始值（学时）" prop="configState">
          <el-input :min="1" placeholder="请输入" v-model="levelList.configState" style="width: 200px;" @input="change"
            onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" type="number"></el-input><span
            style="margin-left: 20px;">节</span>
        </el-form-item>
        <el-form-item label="" prop="remark">
          <el-radio-group v-model="levelList.remark">
            <el-radio :label="1">满足一个条件即视为达标</el-radio>
            <el-radio :label="2">满足两个条件即视为达标</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="onSubmit('levelForm')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="删除教练等级" :visible.sync="deleteVisible" width="380px" :close-on-click-modal="false"
      class="delete_kit">
      <i class="el-icon-delete delete_icon"></i>
      <span>是否确定删除？</span>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="deleteVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="deleteLevel">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTeacherLevelList, addOrUpdateTeacherLevel, deleteTeacherLevel } from "@/api/rukuManage/zhuTeacher"
import { getDeliverCodes } from "@/api/FinanceApi/assistantWages";

export default {
  data() {
    return {
      dialogVisible: false,
      teacherlist: [], // 试课奖励列表
      screenWidth: window.screen.width,

      url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
      show: false, // 判断登录用户是否有权限
      levelList: {
        name: '',
        value: '',
        level: '',
        configState: '',
        remark: '',
        id: ''
      },

      rules: {
        name: [
          { required: true, message: '请选择名称', trigger: 'blur' }
        ],
        // level: [
        //     { required: true, message: '请输入等级', trigger: 'blur' }
        // ],
        value: [
          { required: true, message: '请输入正式课区间起始值', trigger: 'blur' }
        ],
        configState: [
          { required: true, message: '请输入试课区间起始值', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请选择满足条件', trigger: 'change' }
        ]
      },

      deleteVisible: false,
      deleteId: '',


      options: [{
        value: '1',
        label: '初级教练'
      }, {
        value: '2',
        label: '中级教练'
      }, {
        value: '3',
        label: '高级教练'
      }, {
        value: '4',
        label: '最高级教练'
      }, {
        value: '5',
        label: '特级教练'
      }],
      value: ''

    };
  },
  computed: {},
  created() { },
  mounted() {
    // this.getSignal();
    this.initData();
  },
  methods: {
    change(e) {
      this.$forceUpdate();
    },

    // 选择
    changeLevel(e) {
      this.levelList.name = this.options[e - 1].label;
      this.levelList.level = e;
    },

    async initData() {
      await getTeacherLevelList().then((res) => {
        console.log(res);
        this.teacherlist = res.data;
      });
    },

    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.levelList.level === "1") {
            if (Number(this.levelList.value) > 1 || Number(this.levelList.configState) > 1) {
              this.$message.error("课时初始值需为1节");
              return
            }
          }
          const loading = this.$loading({
            lock: true,
            text: '加载中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          addOrUpdateTeacherLevel(this.levelList).then(res => {
            loading.close()
            this.$message.success("操作成功");
            this.$refs[formName].resetFields();
            this.dialogVisible = false;
            this.initData();
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    openDelete(id) {
      this.deleteId = id;
      this.deleteVisible = true;
    },

    deleteLevel() {
      const loading = this.$loading({
        lock: true,
        text: '删除中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let data = {
        id: this.deleteId
      }
      deleteTeacherLevel(data).then(res => {
        loading.close();
        this.deleteVisible = false;
        this.$message.success("操作成功");
        this.initData();
      }).catch(err => {
        loading.close();
      })
    },

    // 判断教练相关功能显示
    async getSignal() {
      await getDeliverCodes().then(res => {
        this.show = res.data;
        if (this.show) {
          this.initData();
        } else {
          this.$message.error('您暂无该权限');
        }
      })
    },

    handleClose() {
      this.$refs['levelForm'].resetFields();
      this.dialogVisible = false;
    },

    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },

    teacherGradeoperate(val, item) {
      console.log(val)
      console.log(item)
      if (val == 'edit') {
        this.levelList.id = item.id;
        this.levelList.name = item.name;
        this.levelList.value = item.value;
        this.levelList.level = item.level;
        this.levelList.configState = item.configState;
        // this.levelList = item;
        this.levelList.remark = Number(item.remark);
      } else {
        this.levelList.id = '';
        this.levelList.name = '';
        this.levelList.level = '';
        this.levelList.remark = '';
        this.levelList.value = '';
        this.levelList.configState = '';
      }
      this.dialogVisible = true;
    },

    // 数字转化
    convertToChinese(num) {
      var arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      var arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿']
      if (!num || isNaN(num)) return '零'
      var english = num.toString().split('')
      var result = ''
      for (var i = 0; i < english.length; i++) {
        var des_i = english.length - 1 - i// 倒序排列设值
        result = arr2[i] + result
        var arr1_index = english[des_i]
        result = arr1[arr1_index] + result
      }
      result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十') // 将【零千、零百】换成【零】 【十零】换成【十】
      result = result.replace(/零+/g, '零') // 合并中间多个零为一个零
      result = result.replace(/零亿/g, '亿').replace(/零万/g, '万') // 将【零亿】换成【亿】【零万】换成【万】
      result = result.replace(/亿万/g, '亿') // 将【亿万】换成【亿】
      result = result.replace(/零+$/, '') // 移除末尾的零
      // 将【一十】换成【十】
      result = result.replace(/^一十/g, '十')
      return result

      // var chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]; // 0-9的中文表达
      // var unit = ['', '十', '百', '千']; // 单位依次为空、十、百、千等
      // if (isNaN(num)) return ''; // 如果输入不是有效的数字则返回空字符串
      // num += ""; // 将数字转换成字符串类型
      // var len = num.length; // 获取数字长度
      // var result = []; // 存放结果的数组
      // for (var i = 0; i < len; i++) {
      //     var n = parseInt(num[i]); // 提取当前位置上的数字

      //     if (n !== 0 || (!result.length && !i)) { // 若该位置上的数字非零或者是第一位并且之前没有其他数字时才添加到结果数组中
      //         result.push(chnNumChar[n] + unit[len - i - 1]); // 根据数字在数组中的索引值选择相应的中文表达，再与单位进行合并
      //     } else {
      //         result.pop(); // 若该位置上的数字为零，则去除最后一个元素（因为多了一个“零”）
      //     }
      // }
      // return result.join(''); // 将结果数组按顺序连接起来作为最终结果
    }
  }
};
</script>

<style>
body {
  background-color: #fff !important;
}
</style>
<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

.wages ::v-deep.el-dialog__body {
  padding: 0 !important;
}

.price {
  display: flex;
  justify-content: space-between;
  width: 280px;
  color: #c0c4cc;
  padding: 0 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  border: 1px solid #dfe4ed;
}

.amount {
  width: 200px;
  height: 36px;
  margin-left: 4px;
  line-height: 36px;
  padding: 0 15px;
  color: #c0c4cc;
  background-color: #f5f7fa;
  border-radius: 5px;
  box-sizing: border-box;
  border: 1px solid #dfe4ed;
}

.course {
  width: 300px;
  color: #c0c4cc;
  padding: 0 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  border: 1px solid #dfe4ed;
}

.no_data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

::v-deep .el-card__body {
  height: 100%;
}

::v-deep.delete_kit .el-dialog__title {
  margin-left: 1vw !important;
}

.delete_icon {
  position: absolute;
  left: 0.7vw;
  top: 22px;
  color: #bf0502;
  font-size: 18px;
}
</style>
