<!-- 新班级列表 -->
<template>
  <div class="app-container">
    <!-- 课程类型 -->
    <el-row style="margin: 20px 0 20px 20px">
      <el-col :span="2.5">
        <el-radio-group v-model="courseType" size="medium" @change="handleTabsClick">
          <el-radio-button label="1">试课</el-radio-button>
          <el-radio-button label="2">正式课</el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <!-- 状态 -->
    <el-row justify="space-between" type="flex" style="margin: 20px 0 20px 20px" v-if="isAdmin">
      <el-col>
        <el-tabs v-model="orderStatus" @tab-click="handleTabsClick">
          <el-tab-pane label="派单中" name="1"></el-tab-pane>
          <el-tab-pane label="等待成班中" name="2"></el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <!--  -->
    <DispatchInProgressTrial ref="DispatchInProgressTrial" v-if="switchTab === '1'" :gradeList="gradeList"></DispatchInProgressTrial>
    <DispatchInProgress ref="DispatchInProgress" v-if="switchTab === '2'" :gradeList="gradeList"></DispatchInProgress>
    <WaitingBuildingClassTrial v-if="switchTab === '3'" :gradeList="gradeList"></WaitingBuildingClassTrial>
    <WaitingBuildingClass v-if="switchTab === '4'" :gradeList="gradeList"></WaitingBuildingClass>
  </div>
</template>

<script>
  import { GradeType } from '@/api/studentClass/changeList';
  import DispatchInProgressTrial from './components/newClassList/DispatchInProgressTrial.vue'; // 派单中-试课
  import DispatchInProgress from './components/newClassList/DispatchInProgress.vue'; // 派单中-正式课
  import WaitingBuildingClassTrial from './components/newClassList/WaitingBuildingClassTrial.vue'; // 等待成班中-试课
  import WaitingBuildingClass from './components/newClassList/WaitingBuildingClass.vue'; // 等待成班中-正式课
  import checkPermission from '@/utils/permission';

  export default {
    name: 'newClassList',
    components: {
      DispatchInProgressTrial,
      DispatchInProgress,
      WaitingBuildingClassTrial,
      WaitingBuildingClass
      // HeaderSettingsDialog
    },
    data() {
      return {
        gradeList: [], // 年级列表
        courseType: '1', // 当前选中课程类型
        orderStatus: '1', // 订单状态
        isAdmin: false // 是否是管理员
      };
    },
    computed: {
      // 切换tab
      switchTab() {
        if (this.orderStatus === '1' && this.courseType === '1') return '1';
        if (this.orderStatus === '1' && this.courseType === '2') return '2';
        if (this.orderStatus === '2' && this.courseType === '1') return '3';
        if (this.orderStatus === '2' && this.courseType === '2') return '4';
      }
    },
    created() {
      this.getGradeList(); // 获取年级列表
      this.isAdmin = checkPermission(['admin', 'JiaofuManager']);
      // 注册切换tab事件
      window.addEventListener('keydown', this.handleTabsNext);
    },
    destroyed() {
      window.removeEventListener('keydown', this.handleTabsNext);
    },
    activated() {
      window.addEventListener('keydown', this.handleTabsNext);
    },
    deactivated() {
      console.log(1111);

      window.removeEventListener('keydown', this.handleTabsNext);
    },
    mounted() {},
    methods: {
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      handleTabsNext(e) {
        if (e.code === 'Space' || e.code === 'Tab') {
          console.log(this.orderStatus, this.courseType, this.$refs.DispatchInProgress);
          if (this.switchTab === '1') this.$refs.DispatchInProgressTrial?.handleTabsNext();
          if (this.switchTab === '2') this.$refs.DispatchInProgress?.handleTabsNext();
          e.preventDefault(); // 阻止默认事件
        }
      },
      // 切换课程大类tabs
      handleTabsClick() {
        // console.log('🚀 ~ handleTabsClick ~ handleTabsClick:', this.courseType, this.orderStatus);
        // TODO: 调用接口获取课程列表
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  ::v-deep.el-date-editor.el-input {
    width: 100%;
  }
  ::v-deep.el-input-number.is-controls-right .el-input__inner {
    text-align: left;
  }
  ::v-deep.el-select {
    width: 100%;
  }
  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  ::v-deep .el-select-dropdown__empty {
    padding: 110px 0;
  }
  //   >>> .el-select-dropdown__empty {
  //     padding: 110px 0;
  //   }
  //   :deep(.el-select-dropdown__empty) {
  //     padding: 110px 0;
  //   }
</style>
