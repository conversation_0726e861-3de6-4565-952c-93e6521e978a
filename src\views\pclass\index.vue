<!--交付中心-正式学员管理-学员列表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="120px" ref="searchNum" :model="searchNum" :inline="true">
        <!-- 1 -->
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model.trim="searchNum.name" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="inputclass" :xs="24" v-if="isTeamLeader">
            <el-form-item label="所属交付小组:" prop="team">
              <el-select v-model="searchNum.teamId" clearable size="small" placeholder="请选择" style="width: 10vw" @change="handlechangeDownCompany(priceId)">
                <el-option v-for="(item, index) in leaderTeamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="inputclass" :xs="24" v-if="!isTeamLeader">
            <el-form-item label="门店:" prop="merchantCodeOrMerchantPhone">
              <el-input v-model="searchNum.merchantCodeOrMerchantPhone" clearable placeholder="请输入门店账号或门店手机号" style="width: 220px" size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item v-if="isAdmin" label="交付中心编号:" prop="deliverMerchant">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" :xs="24">
            <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8" :xs="24">
            <el-form-item v-if="isAdmin" label="指派状态:" prop="isAssign">
              <el-select v-model="searchNum.isAssign" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in assignStatus" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="创建时间:" prop="timeAll">
              <el-date-picker
                v-model="timeAll"
                :style="{ width: screenWidth > 1300 ? '16vw' : '90%' }"
                size="small"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                align="right"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24" v-if="!isTeamLeader">
            <el-form-item label="所属学管师：" prop="learnTubeTeacherName">
              <el-select
                v-el-select-loadmore="loadmore_manageTeacher"
                :loading="manageTeacherLoad"
                :filter-method="manageTeacherFilterValue"
                clearable
                v-model="searchNum.learnTubeTeacherName"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @blur="clearSearchRecord"
                @change="changeManageTeacher"
              >
                <el-option v-for="item in manageTeacherArr" :key="item.userId" :label="item.realName" :value="item.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="inputclass" :xs="24">
            <el-form-item label="是否建群:" prop="hasChat">
              <el-select v-model="searchNum.hasChat" clearable placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="已建群" value="1"></el-option>
                <el-option label="未建群" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 4.2.2 -->
          <el-col :span="8" class="inputclass" :xs="24">
            <el-form-item label="上课教练:" prop="arrange">
              <el-select v-model="searchNum.arrange" clearable placeholder="请选择">
                <el-option v-for="(item, index) in optionsTecher" :label="item.label" :value="item.value" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="剩余未交付课时:" prop="remainderCourseHours">
              <el-select v-model="searchNum.remainderCourseHours" placeholder="请选择" clearable>
                <el-option v-for="item in haveCourseHoursList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16" class="btnclass" :xs="24">
            <el-form-item>
              <el-button type="primary" size="mini" @click="stufenFn()" v-if="!isTeamLeader">学管分配</el-button>
              <el-button type="primary" style="margin-right: 10px" size="mini" icon="el-icon-search" @click="initData01">查询</el-button>
              <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <el-button type="warning " @click="changeTeamFn()" style="margin: 20px 0 20px 20px" v-if="!isAdmin && !isTeamLeader">批量更换小组申请</el-button>

    <el-table
      v-loading="tableLoading"
      :data="paikeList"
      style="width: 100%"
      id="out-table"
      ref="configurationTable"
      @selection-change="studentXueguan"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column width="50" type="selection" align="center"></el-table-column>
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 700 : 160"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }" v-if="item.value != 'lockCourseHours'">
          <div v-if="item.value == 'operate'">
            <div class="btnBox" v-if="row.payStatus !== 1 || !row.planId">
              <el-button type="warning" size="mini" v-if="row.payStatus === 4 || row.payStatus === 3" @click="paikeBtn(row)">再次编辑</el-button>
              <el-button v-else type="success" size="mini" @click="paikeBtn(row)">排课</el-button>
            </div>
            <div class="btnBox" v-else>
              <el-button type="success" size="mini" v-if="row.sendType === 0 && row.payStatus === 1 && row.studentSource === '1'" @click="sendBtn(row, 1)">发送给门店</el-button>
              <el-button type="success" size="mini" v-if="row.sendType !== 0 && row.payStatus === 1 && row.studentSource === '1'" @click="sendBtn(row, 1)">
                再次发送给门店
              </el-button>
              <el-button type="success" size="mini" v-if="row.sendType === 0 && row.payStatus === 1 && row.studentSource === '2'" @click="sendBtn(row, 2)">
                发送给门店和家长
              </el-button>
              <el-button type="success" size="mini" v-if="row.sendType !== 0 && row.payStatus === 1 && row.studentSource === '2'" @click="sendBtn(row, 2)">
                再次发送给门店和家长
              </el-button>
              <el-button type="danger" size="mini" v-if="row.payStatus === 1" @click="cancelBtn(row)">取消</el-button>
              <el-button type="warning" size="mini" v-if="row.payStatus === 4" @click="paikeBtn(row)">再次编辑</el-button>
            </div>
            <el-button type="primary" size="mini" v-if="!isTeamLeader && (row.payStatus === 2 || (row.payStatus === 0 && row.planId))" @click="classCard(row)">
              查看课程表
            </el-button>
            <!-- <el-button type="primary" size="mini" @click="classCard(row)">课程表</el-button> -->
            <el-button type="primary" size="mini" @click="LessonTestReport(row)">试课报告</el-button>
            <el-button type="primary" size="mini" @click="getDetail(row.id)" v-if="!isTeamLeader">派单记录</el-button>
            <!--(value = "学生对接状态 0无  1未填写  2已填写")studentContactStatus;-->
            <el-button type="primary" size="mini" @click="openAbutment(row)" v-if="row.studentContactStatus === 2">上课信息对接表</el-button>
            <!-- <el-button type="primary" size="mini" @click="studentFn(row)">学员信息表</el-button>
            <el-button type="primary" size="mini" @click="changeFn(row)">咨询记录表</el-button>
            <el-button type="success" size="mini" @click="lookBtn(row)">数据查看</el-button>
            <el-button type="success" size="mini" @click="translateFn(row)">打印</el-button> -->
            <el-button type="warning" icon="el-icon-s-promotion" style="margin-top: 10px" v-if="isAdmin" size="mini" @click="transferStudent(row)">学员转移</el-button>
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="beforeCommand(row, 1)">开通课程</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 12)">开通超级阅读课程</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 2)">学员课程记录</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 3)">学员销课记录</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 4)">21天抗遗忘复习计划</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 5)">学员词汇测试</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 6)">学员测验打印</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 7)">数据查看</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 8)">打印</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 9)">编辑</el-dropdown-item>
                <el-dropdown-item v-if="isAdmin" :command="beforeCommand(row, 10)">代付学时</el-dropdown-item>
                <el-dropdown-item :command="beforeCommand(row, 11)">开通全能听力</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-else-if="item.value == 'firstTime'">
            <span>{{ getFormatToService(row.firstTime, row.firstWeek) }}</span>
          </div>
          <div v-else-if="item.value == 'teamName'">
            <span>{{ row.teamName }}</span>
            <i v-if="!isAdmin && !isTeamLeader" class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editTeam(row)"></i>
          </div>
          <div v-else-if="item.value == 'studyTeacherName'">
            <span>{{ row.studyTeacherName || '无' }}</span>
            <i v-if="!isAdmin" class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editTeacher(row)"></i>
            <!-- <i v-if="!isAdmin" class="el-icon-edit" style="color: #66b1ff;margin-left: 10px;"
              @click="openChoseTeacher(row, row.studyTeacherName, 1)"></i> -->
          </div>
          <div v-else-if="item.value == 'replaceTeacherName'">
            <span>{{ row.replaceTeacherName || '无' }}</span>
            <i v-if="!isAdmin" class="el-icon-edit" style="color: #409eff; margin-left: 10px" @click="editReplaceTeacher(row)"></i>
            <!-- <i v-if="!isAdmin" class="el-icon-edit" style="color: #66b1ff;margin-left: 10px;"
              @click="openChoseTeacher(row, row.studyTeacherName, 1)"></i> -->
          </div>
          <div v-else-if="item.value == 'reviewTeacherName'">
            <span>{{ row.reviewTeacherName || '无' }}</span>
            <!-- <i v-if="!isAdmin" class="el-icon-edit" style="color: #66b1ff;margin-left: 10px;"
              @click="openChoseTeacher(row, row.reviewTeacherName, 2)"></i> -->
          </div>
          <div v-else-if="item.value == 'hasChat'">
            <el-tag :type="row.hasChat == 1 ? 'success' : 'danger'">{{ row.hasChat == 1 ? '已建群' : '未建群' }}</el-tag>
            <!-- <i v-if=" !isAdmin" class="el-icon-edit" style="color: #66b1ff;margin-left: 10px;"
              @click="openChoseTeacher(row, row.reviewTeacherName, 2)"></i> -->
          </div>

          <div v-else-if="item.value == 'deliverName'">
            <el-button v-if="row.deliverMerchant == 'A0001'" type="primary" size="mini" @click="editFn(row.id)">指派</el-button>
            <span v-else>{{ row.deliverName }}</span>
          </div>

          <div v-else-if="item.value == 'referrerRemark'">
            <span>{{ row.referrerRemark || '无' }}</span>
            <i class="el-icon-edit" style="color: #66b1ff; margin-left: 10px" @click="openNotes(row)"></i>
          </div>

          <div v-else-if="item.value == 'totalDeliverHours'">
            <span>{{ row.totalDeliverHours }}</span>
            <span @click="showLockCourse(row)" class="detailTip">详情</span>
          </div>

          <div v-else-if="item.value == 'teachingType'">
            <span v-if="row.teachingType == 1 || row.teachingType == 2 || row.teachingType == 3">
              {{ row.teachingType != 1 ? (row.teachingType == 2 ? '线下' : '远程和线下') : '远程' }}
            </span>
            <span v-else>暂无</span>
          </div>
          <div v-else-if="item.value == 'reviewType'">
            <span v-if="row.reviewType">{{ row.reviewType == 1 ? '教练带复习' : '自行复习' }}</span>
            <span v-else>暂无</span>
          </div>
          <div v-else-if="item.value == 'totalReviewMinutes'">
            <span v-if="row.totalReviewMinutes">{{ row.totalReviewMinutes }}</span>
            <span v-else>--</span>
          </div>
          <div v-else-if="item.value == 'haveReviewMinutes'">
            <span v-if="row.haveReviewMinutes">{{ row.haveReviewMinutes }}</span>
            <span v-else>--</span>
          </div>
          <div v-else-if="item.value == 'reviewWeek'">
            <span v-if="row.reviewWeek">{{ getReviewWeek(row.reviewWeek) + row.reviewTime }}</span>
            <span v-else>--</span>
          </div>
          <div v-else-if="item.value == 'payStatus'">
            <el-tag v-if="!row.planId" type="info">未排课</el-tag>
            <el-tag v-if="row.payStatus === 1">待支付</el-tag>
            <el-tag v-if="row.payStatus === 2 || (row.payStatus === 0 && row.planId)" type="success">已支付</el-tag>
            <el-tag v-if="row.payStatus === 3" type="danger">未支付</el-tag>
            <el-tag v-if="row.payStatus === 4" type="danger">已取消</el-tag>
          </div>

          <div v-else-if="item.value == 'isEnable'">
            <el-switch v-model="row.isEnable" :active-value="1" :inactive-value="0" active-color="#13ce66" @change="enableChange(row)"></el-switch>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>

        <template slot="header" v-if="item.value == 'lockCourseHours'">
          <span>已锁定学时</span>
          <el-popover placement="top" width="400" align="center">
            <p>已锁定学时=已排未上交付学时+剩余可排交付学时</p>
            <i class="el-icon-question" slot="reference"></i>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <!-- 指派弹框 -->
    <el-dialog :visible.sync="tkedialog" :width="screenWidth > 1300 ? '30%' : '90%'" title="指派交付中心" :class="screenWidth > 1300 ? 'assgin' : 'assgin-phone'">
      <el-form ref="form" :model="assign" label-width="150px" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
        <el-form-item label="应属交付中心">
          <el-input v-model="assign.belongDeliverName" disabled />
        </el-form-item>

        <el-form-item label="重新指派交付中心">
          <el-cascader
            v-model="deliverMerchantCode"
            :options="assign.deliverList"
            filterable
            :props="{
              label: 'deliverMerchantName',
              value: 'deliverMerchantCode'
            }"
            clearable
          ></el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAss">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 挂载弹窗 -->
    <paikeDialog :childVisible="childVisible" :rowlist="rowlist" :paikeBtn="paikeBtn" @changeDrawer="changeDrawer" ref="addRole" @updateList="initData" :paikeList="paikeList" />
    <translateDialog :translateDle="translateDle" @transDrawer="transDrawer" ref="translate" />

    <sumlookDialog :islook.sync="islook" :idser="idser" :teacher="teacher" :contentType="contentType" ref="sumlookDialog" />
    <LeaveProcessingDialog />
    <studentTransfer :transfer-from="transferFrom" :transfer-visible="transferVisible" @transferClose="transferClose" ref="studentTransfer" />

    <testReport :reportVisible="reportVisible" @changeReport="changeReport" ref="testReport" :reportList="reportList"></testReport>

    <el-dialog title="学管分配" :visible.sync="dialogVisible" :width="screenWidth > 1300 ? '30%' : '90%'" :before-close="handleClose('dialogVisible')">
      <div style="margin-bottom: 1vw">学员数量 : {{ idList.length }}</div>
      <div style="margin-bottom: 1vw">学员名称 : {{ nameList }}</div>
      <div>
        选择学管：
        <el-select v-model="valueTeacher" size="mini" placeholder="请选择">
          <el-option v-for="item in teacherList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer" style="margin-left: 1vw">
        <el-button type="primary" size="mini" @click="sendFn">确 定</el-button>
        <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 已锁定课时详情   -->
    <el-dialog
      :title="lockCourseData.length > 0 ? lockCourseData[0].name + '的学时详情' : '学时详情'"
      :visible.sync="showLockCourseVisible"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      :before-close="handleClose('showLockCourseVisible')"
    >
      <el-table
        v-loading="tableLoading"
        :data="lockCourseData"
        style="width: 100%; margin-top: 20px"
        @selection-change="studentXueguan"
        :header-cell-style="getRowClass"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="totalDeliverHours" label="已购交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="planDeliverHours" min-width="160" header-align="center">
          <template slot="header">
            <span>已排交付学时</span>
            <el-popover placement="top" width="400" align="center">
              <p>已排交付学时=已排已上交付学时+已排未上交付学时</p>
              <i class="el-icon-question" slot="reference"></i>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="planAndStudyDeliverHours" label="已排已上交付学时" width="150" header-align="center"></el-table-column>
        <el-table-column prop="planAndNoStudyDeliverHours" label="已排未上交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="lessonsDeliverWithoutFeedback" label="已上未反馈学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="haveDeliverHours" label="剩余可排交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="refundHours" label="已退学时" min-width="120" header-align="center"></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="代付学时,非非常极度必要请勿使用！！！！！！！"
      :visible.sync="showAgentDialog"
      :width="screenWidth > 1300 ? '40%' : '90%'"
      :style="{ height: screenWidth > 1300 ? '800px' : '650px' }"
    >
      <el-form :model="agentHours">
        <el-form-item label="学员编号" :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.studentCode" disabled autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="门店编号" disabled :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.merchantCode" disabled autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="收款交付中心编号" :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.deliverMerchant" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="学生学时" disabled :label-width="formLabelWidthMax" :gutter="20">
          <el-tag>剩余交付学时{{ studentCourseInfo.remainDeliverHours }}</el-tag>
          &nbsp;
          <el-tag type="success">剩余交付英语学时{{ studentCourseInfo.enHours }}</el-tag>
          &nbsp;
          <el-tag type="warning">已上未反馈学时{{ studentCourseInfo.lessonsDeliverWithoutFeedback }}</el-tag>
        </el-form-item>
        <el-form-item label="剩余交付学时代付学时" :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.hour" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="已上未反馈代付学时" :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.learningHour" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="是否扣除英语或课程包学时" :label-width="formLabelWidthMax">
          <el-select v-model="agentHours.cutEnHour" placeholder="请选择">
            <el-option label="扣除" value="true"></el-option>
            <el-option label="不扣除" value="false"></el-option>
          </el-select>
          <span style="color: #909399; margin-left: 2em">该配置对已上未反馈学时不生效</span>
        </el-form-item>
        <el-form-item label="是否分佣" :label-width="formLabelWidthMax">
          <el-select v-model="agentHours.commission" placeholder="请选择">
            <el-option label="分佣" value="true"></el-option>
            <el-option label="不分佣" value="false"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" :label-width="formLabelWidthMax">
          <el-input v-model="agentHours.remark" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAgentDialog = false">取 消</el-button>
        <el-button type="primary" @click="sendAgent">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="编辑" :visible.sync="StudentUpdateFn" :width="screenWidth > 1300 ? '30%' : '90%'">
      <el-form :model="studentInfo">
        <el-form-item label="学生姓名" :label-width="studentInfoLabelWidth">
          <el-input v-model="studentInfo.realName" autocomplete="off" @change="selectFn3(studentInfo)"></el-input>
        </el-form-item>

        <el-form-item label="年级" :label-width="studentInfoLabelWidth">
          <el-select v-model="value1" placeholder="" @change="selectFn(value1)">
            <el-option v-for="item in options" :key="item.value1" :label="item.label" :value="item.value1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="学校" :label-width="studentInfoLabelWidth">
          <el-input v-model="studentInfo.school" autocomplete="off" @change="selectFn1(studentInfo)"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="StudentUpdateFn = false">取 消</el-button>
        <el-button type="primary" @click="updateStudentInfo1">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 备注编辑 -->
    <el-dialog title="修改备注" :visible.sync="dialogNotes" :width="screenWidth > 1300 ? '30%' : '90%'" center :class="screenWidth > 1300 ? 'notes' : 'notes-phone'">
      <div style="display: flex; align-items: center; margin-top: 30px">
        <span>推荐人备注：</span>
        <el-input v-model="notesValue" placeholder="请输入备注" style="width: 50%"></el-input>
      </div>
      <span slot="footer">
        <el-button @click="dialogNotes = false">取 消</el-button>
        <el-button type="primary" @click="editNotes">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 选择教练 -->
    <el-dialog :title="getDialogChoseTeacherTitle()" @close="closeChoseTeacher" :visible.sync="dialogChoseTeacher" :width="screenWidth > 1300 ? '30%' : '90%'" center>
      <div style="margin-bottom: 40px">
        <div v-if="curChoseTeacher" style="display: flex; align-items: center; margin-top: 30px">
          <span>{{ getCurChoseTeacherTitle() }}：</span>
          <el-input v-model="curChoseTeacher" style="width: 50%" disabled></el-input>
        </div>

        <div style="display: flex; align-items: center; margin-top: 30px">
          <span>{{ getDialogChoseTeacherTitle() }}：</span>
          <el-select
            style="width: 50%"
            v-el-select-loadmore="handleLoadmore"
            :loading="loadingShip"
            :filter-method="filterValue"
            v-model="newChoseTeacher"
            filterable
            remote
            reserve-keyword
            placeholder="请选择"
          >
            <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeChoseTeacher">取 消</el-button>
        <el-button type="primary" @click="updateStudentBinding">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 上课信息对接表 -->
    <el-dialog title="上课信息对接表" :visible.sync="dialogAbutment" :width="screenWidth > 1300 ? '60%' : '90%'" :close-on-click-modal="false" center>
      <div style="margin-bottom: 40px; margin-top: -30px; font-size: 15px">
        <span>创建时间：{{ abutmentList.submitTime ? abutmentList.submitTime : abutmentList.updateTime }}</span>
      </div>
      <el-form ref="abutmentList" :model="abutmentList" :label-width="screenWidth > 1300 ? '150px' : '70px'">
        <el-form-item label="学员姓名" prop="studentName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentCode }}</span>
          </div>
        </el-form-item>
        <el-form-item label="联系方式" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.mobile }}</span>
          </div>
        </el-form-item>
        <el-form-item label="课程类型" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.curriculumName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.referrerPhone ? abutmentList.referrerPhone : '无' }}</span>
          </div>
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="充值学时" prop="rechargeHour" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.rechargeHour }}</span>
          </div>
        </el-form-item>
        <el-form-item label="课程规划" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass thesaurus" style="margin: 10px 0 0" v-for="(item, index) in coursrList" :key="index">
            <span style="margin: 0 15px">{{ item.courseName }}</span>
            <div class="vocabulary" v-if="item.isFirstWordBase">首节课词库</div>
          </div>
        </el-form-item>
        <el-form-item label="上课时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-form-item label="" v-for="(val, index) in abutmentList.studyTimeList" :key="index">
            <el-row style="margin-bottom: 10px; margin-left: 0">
              <el-col :span="8" :xs="24">
                <div class="timeClass" style="margin: 0 0">
                  <span style="margin: 0 15px">{{ getWeekName(val.usableWeek) }}</span>
                </div>
              </el-col>
              <el-col :span="16" :xs="24">
                <div class="timeClass">
                  <span style="margin: 0 15px">{{ val.startTime }}</span>
                  <span>至</span>
                  <span style="margin: 0 15px">{{ val.endTime }}</span>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="abutmentList.curriculumName == '鼎英语'" label="复习时间" prop :style="{ width: screenWidth > 1300 ? '80%' : '100%' }" style="margin-top: 10px">
          <el-row>
            <el-col :span="3" :xs="12" v-for="(item, index) in reviewWeekList" :key="index">
              <span class="week">{{ getWeekName(item) }}</span>
            </el-col>
          </el-row>
          <div class="timeClass" style="margin: 10px 0 0" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <span style="margin: 0 15px">开始时间:</span>
            <span style="margin: 0 15px">{{ abutmentList.reviewTime }}</span>
          </div>
        </el-form-item>
        <div style="position: relative; display: flex; margin-bottom: 22px; align-items: center" v-if="abutmentList.firstTime">
          <div :style="{ width: screenWidth > 1300 ? '150px' : '85px' }" style="text-align: end; padding-right: 12px">
            首次上课时间
            <div style="color: #999999; font-size: 11px">限制24小时之后</div>
          </div>
          <div class="timeClass" style="margin-left: 0; line-height: 36px" :style="{ width: screenWidth > 1300 ? '36%' : '100%' }">
            <span style="margin: 0 15px">{{ getFormatToService(abutmentList.firstTime, abutmentList.firstWeek) }}</span>
          </div>
        </div>
        <el-form-item label="是否试课" prop="isExp" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isExp == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item
          label="词汇量检测"
          v-if="abutmentList.isExp === 1 && abutmentList.curriculumName == '鼎英语'"
          prop="wordBase"
          :style="{ width: screenWidth > 1300 ? '50%' : '100%' }"
        >
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.wordBase }}</span>
          </div>
        </el-form-item>
        <el-form-item label="是否新生" prop="isNewStudent" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isNewStudent == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 20px">{{ abutmentList.remark }}</span>
            <!-- <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" disabled /> -->
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogAbutment = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="更换小组" :visible.sync="teamDialog" width="40%" :before-close="closeTeam">
      <el-form ref="teamForm" :model="teamForm" label-width="120px">
        <el-form-item label="当前小组 " v-if="idList.length <= 1">
          <el-input v-model="teamForm.oldTeam" style="width: 70%" disabled></el-input>
        </el-form-item>
        <div v-if="idList.length > 1" style="margin-bottom: 1vw">学员名称 : {{ nameList }}</div>
        <el-form-item label="更换小组">
          <!-- <el-input v-model="teamForm.teamId"></el-input> -->
          <el-select v-el-select-loadmore="loadmoreTeam" :loading="loadingTeam" v-model="teamForm.teamId" placeholder="">
            <el-option v-for="(item, index) in teamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="首次上课时间：" prop="deliverMerchantCode">
          <div style="display: flex">
            <el-date-picker v-model="time1" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptions1" placeholder="选择日期"></el-date-picker>
            <el-time-picker v-model="time2" :picker-options="pickerOptions2" value-format="HH:mm" format="HH:mm" placeholder="任意时间点"></el-time-picker>
          </div>
        </el-form-item>
        <!-- 复习时间 -->
        <el-form-item label="复习时间" prop :style="{ width: screenWidth > 1300 ? '80%' : '100%' }" style="margin-top: 10px" v-if="needTime">
          <el-row>
            <el-checkbox-group v-model="reviewTimes" size="small">
              <el-checkbox-button v-for="(item, index) in normalWeekData" :label="index" :key="index">{{ item }}</el-checkbox-button>
            </el-checkbox-group>
          </el-row>
        </el-form-item>
        <el-form-item label=" " v-if="needTime">
          <el-time-picker v-model="time3" :picker-options="pickerOptions3" value-format="HH:mm" format="HH:mm" placeholder="任意时间点"></el-time-picker>
        </el-form-item>
        <!-- 复习时间 -->
      </el-form>
      <div slot="footer">
        <el-button @click="closeTeam">取 消</el-button>
        <el-button type="primary" @click="submitChange" :loading="isSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="更换上课教练" :visible.sync="teacherDialog" width="40%" :before-close="closeTeacher">
      <el-form ref="teacherForm" :model="teacherForm" label-width="120px">
        <el-form-item label="当前教练 ">
          <el-input v-model="teacherForm.oldTeacher" style="width: 70%" disabled></el-input>
        </el-form-item>
        <el-form-item label="更换教练">
          <el-select v-model="teacherForm.newTeacherId" placeholder="" filterable>
            <el-option v-for="(item, index) in changeTeacherList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeTeacher">取 消</el-button>
        <el-button type="primary" @click="submitChangeTeacher" :loading="isSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="更换代课教练" :visible.sync="rTeacherDialog" width="40%" :before-close="closeRTeacher">
      <el-form ref="rTeacherForm" :model="rTeacherForm" label-width="120px">
        <el-form-item label="当前教练 ">
          <el-input v-model="rTeacherForm.oldTeacher" style="width: 70%" disabled></el-input>
        </el-form-item>
        <el-form-item label="更换教练">
          <el-select v-model="rTeacherForm.newTeacherId" placeholder="" filterable>
            <el-option v-for="(item, index) in changeTeacherList1" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeRTeacher">取 消</el-button>
        <el-button type="primary" @click="submitChangeRTeacher" :loading="isSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 流转指派历史弹框 -->
    <el-dialog title="历史派单记录" :visible.sync="dialogHistory" :close-on-click-modal="false" width="30%" center>
      <div style="overflow: auto; margin: 30px 0; height: 400px" v-loading="loadingHistory">
        <!-- <ul class="infinite-list" v-infinite-scroll="load" style="overflow:auto;margin: 30px 0;height:400px">
        </ul> -->
        <el-steps direction="vertical" :active="0" :space="200">
          <el-step v-for="(item, index) in historys" :title="item.time" :key="index" icon="iconfont icon-luyin">
            <template slot="description">
              <div style="white-space: pre-wrap">{{ item.history }}</div>
            </template>
            <template slot="icon">
              <i class="el-icon-info" v-if="index == 0" style="font-size: 24px"></i>
              <i class="el-icon-success" v-else style="font-size: 24px"></i>
            </template>
          </el-step>
        </el-steps>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="width: 100px" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import {
    cancelPlanCourse,
    getPlanCourse,
    getStudentList,
    getStudentDeliverHoursVo,
    sendPlanCourse,
    selectStudent,
    updateStudent,
    modifyRemarks,
    getTrialclass,
    getStudentContactInfoDetail,
    findTeacherById,
    changestudyTeacher,
    changePlaceTeacher,
    findTeachersByDeliverMerchant
  } from '@/api/paikeManage/LearnManager';
  import {
    allotLearnTube,
    belongDeliverAndAllDeliver,
    getLearnTubeList,
    getTimetable,
    submitAssign,
    updateStudentIsEnable,
    getTableTitleSet,
    setTableList,
    bvstatusListOne
  } from '@/api/paikeManage/classCard';
  import { getTeamList, changeTeam, getNeedTime, getFormalHistory, getLeaderTeamList } from '@/api/orderManage';
  import FileSaver from 'file-saver';
  import XLSX from 'xlsx';
  import LeaveProcessingDialog from '../pclass/components/LeaveProcessingDialog.vue';
  import paikeDialog from './components/paikeDialog.vue';
  import translateDialog from './components/translateDialog.vue';
  import sumlookDialog from './components/sumlookDialog.vue';
  import studentTransfer from './components/studentTransfer.vue';
  import testReport from './components/testReport.vue';
  import ls from '@/api/sessionStorage';
  import { agentDeliverFinance, studentDeliverCourseInfo } from '@/api/FinanceApi/Finance';
  import dayjs from 'dayjs';
  import { selAllTeacher, updateStudentBinding, xueguanListApi } from '@/api/studentClass/changeList';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  export default {
    name: 'paike',
    components: {
      paikeDialog,
      sumlookDialog,
      LeaveProcessingDialog,
      translateDialog,
      studentTransfer,
      testReport,
      HeaderSettingsDialog
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        haveCourseHoursList: [
          {
            value: '0',
            label: '0课时'
          },
          {
            value: '1',
            label: '1-3课时'
          },
          {
            value: '2',
            label: '3课时以上'
          }
        ],
        optionsTecher: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '0',
            label: '未安排'
          },
          {
            value: '1',
            label: '已安排'
          }
        ],
        screenWidth: window.screen.width,
        formLabelWidth: '140px',
        formLabelWidthMax: '180px',
        showAgentDialog: false,
        agentHours: {},
        gradeNameArr: [
          '一年级',
          '二年级',
          '三年级',
          '四年级',
          '五年级',
          '六年级',
          '初一',
          '初二',
          '初三',
          '高一',
          '高二',
          '高三',
          '大一',
          '大二',
          '大三',
          '大四',
          '其他',
          '幼儿园'
        ],
        options: [
          {
            value2: 18,
            value1: '18',
            label: '幼儿园'
          },
          {
            value2: 1,
            value1: '1',
            label: '一年级'
          },
          {
            value1: '2',
            value2: 2,
            label: '二年级'
          },
          {
            value1: '3',
            value2: 3,
            label: '三年级'
          },
          {
            value1: '4',
            value2: 4,
            label: '四年级'
          },
          {
            value1: '5',
            value2: 5,
            label: '五年级'
          },
          {
            value1: '6',
            value2: 6,
            label: '六年级'
          },
          {
            value1: '7',
            value2: 7,
            label: '初一'
          },
          {
            value1: '8',
            value2: 8,
            label: '初二'
          },
          {
            value1: '9',
            value2: 9,
            label: '初三'
          },
          {
            value1: '10',
            value2: 10,
            label: '高一'
          },
          {
            value1: '11',
            value2: 11,
            label: '高二'
          },
          {
            value1: '12',
            value2: 12,
            label: '高三'
          },
          {
            value1: '13',
            value2: 13,
            label: '大一'
          },
          {
            value1: '14',
            value2: 14,
            label: '大二'
          },
          {
            value1: '15',
            value2: 15,
            label: '大三'
          },
          {
            value1: '16',
            value2: 16,
            label: '大四'
          },
          {
            value1: '17',
            value2: 17,
            label: '其他'
          }
        ],
        weeklist: [
          {
            value: 0,
            label: '星期一'
          },
          {
            value: 1,
            label: '星期二'
          },
          {
            value: 2,
            label: '星期三'
          },
          {
            value: 3,
            label: '星期四'
          },
          {
            value: 4,
            label: '星期五'
          },
          {
            value: 5,
            label: '星期六'
          },
          {
            value: 6,
            label: '星期天'
          }
        ],

        form: '',
        assign: {},
        deliverMerchantID: '',
        deliverMerchantCode: '',
        // 日期组件
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                // const end = new Date();
                // const start = new Date();
                // picker.$emit('pick', [start, end]);
                const temp = new Date();
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                const temp = new Date();
                temp.setTime(temp.getTime() - 3600 * 1000 * 24);
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '最近七天',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        },
        valueTeacher: '',
        isSubmit: false,
        teacherList: [],
        changeTeacherList: [],
        changeTeacherList1: [],
        teacherDialog: false,
        teacherForm: {
          type: 2,
          id: '',
          oldTeacher: '',
          newTeacherId: ''
        },
        rTeacherForm: {
          id: '',
          oldTeacher: '',
          newTeacherId: ''
        },
        value: '',
        value1: '{ value: item.value, label: item.label}',
        dialogVisible: false,
        timeAll: [],
        rowlist: [],
        paikeList: [],
        searchNum: {
          arrange: '',
          deliverName: '',
          merchantCodeOrMerchantPhone: '',
          // courseType: "",
          teamId: '',
          name: '',
          endTime: '',
          beginTime: '',
          teacherName: '',
          studentCode: '',
          teachingType: '',
          lastStudyTime: '',
          learnTubeTeacherName: '',
          isAssign: '',
          hasChat: '',
          pageNum: 1,
          pageSize: 10
        }, //搜索参数

        //学生信息
        studentInfo: {
          id: '',
          memberCode: '', //会员编号
          loginName: '', //登录账号
          loginPwd: '', //登录密码
          address: '', //地址
          regTime: '', //注册时间
          isEnable: '', //是否启用
          restrictedUse: '', //是否限制使用范围
          gender: '', //性别
          useModule: '', //使用模块
          isFormal: true, //是否正式学员
          dateOfBirth: '', //生日
          schoolType: '', //学校类别 普通学校 区重点 市重点 省重点
          province: '', //省
          city: '', //市
          area: '', //区

          realName: '', //学生名字
          studentCode: '', //学生编号
          grade: '', //学生年级
          school: '' //学生学校
        },
        studentInfoLabelWidth: '120px',

        translateDle: false, //打印按钮的抽屉

        StudentUpdateFn: false, //编辑按钮
        // dialogFormVisible: false,//编辑取消
        // updateStudentInfo1: false,//编辑确定

        tableLoading: false,
        isshow: false,
        childVisible: false, //是否展示抽屉
        islook: false,
        isSee: false,
        currentPage4: 10,
        total: null,
        idser: '', //集中交付id
        teacher: '',
        contentType: '',
        nullconiditon: {
          dateList: [],
          timeList: [
            {
              startTime: '',
              endTime: ''
            }
          ],
          teachingType: '',
          reviewTime: ['', ''],
          planCourseHours: '',
          haveCourseHours: ''
        },
        reviewTimes: [],
        classCardList: '',
        idList: [],
        allotLearnTubeList: {
          ids: '',
          type: 1,
          learnTube: ''
        },
        nameList: [],
        transferVisible: false,
        transferFrom: {},
        isAdmin: false,
        isTeamLeader: false,
        studentCourseInfo: '',
        tkedialog: false,
        exportLoading: false,
        tipVisible: false,
        showLockCourseVisible: false,
        lockCourseData: [],

        notesValue: '', // 备注
        studentCode: '', // 修改备注id
        deliverCode: '',
        dialogNotes: false, // 备注弹窗
        courseList: [],
        regTime: '',
        // isAssign: '',
        assignStatus: [
          {
            value: '0',
            label: '未指派'
          },
          {
            value: '1',
            label: '已指派'
          }
        ],

        reportVisible: false, // 试课报告
        reportList: {}, // 试课报告详情

        dialogAbutment: false, // 上课信息对接表
        abutmentList: {},
        coursrList: [],
        normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),
        reviewWeekList: [],

        dialogChoseTeacher: false,
        choseTeacherType: 0, //1上课 2复习
        updateTeacherItem: null,
        curChoseTeacher: '', //当前显示的值
        option: [],
        newChoseTeacher: '',
        //弹框选择教练
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          name: ''
        },
        //所属学管师
        manageTeacherArr: [],
        manageTeacherTotalPage: 0,
        manageTeacherLoad: false,
        manageTeacherObj: {
          pageNum: 1,
          pageSize: 10,
          realName: ''
        },

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '姓名',
            value: 'name'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '所属交付小组',
            value: 'teamName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          // {
          //   name: "课程类型",
          //   value: "curriculumName",
          // },
          {
            name: '首次上课时间',
            value: 'firstTime'
          },
          {
            name: '上课教练',
            value: 'studyTeacherName'
          },
          {
            name: '复习教练',
            value: 'reviewTeacherName'
          },
          {
            name: '代课教练',
            value: 'replaceTeacherName'
          },
          {
            name: '是否建群',
            value: 'hasChat'
          },
          {
            name: '创建时间',
            value: 'createTime'
          },
          {
            name: '联系方式',
            value: 'phone'
          },
          {
            name: '推荐手机号',
            value: 'referrerPhone'
          },
          {
            name: '门店账号',
            value: 'merchantCode'
          },
          {
            name: '门店负责人',
            value: 'merchantRealName'
          },
          {
            name: '门店手机号',
            value: 'merchantPhone'
          },
          {
            name: '已购学时',
            value: 'totalCourseHours'
          },
          // {
          //   name: "课程类型",
          //   value: "courseType",
          // },
          {
            name: '剩余学时',
            value: 'haveCourseHours'
          },
          {
            name: '剩余未交付课时',
            value: 'remainderCourseHours'
          },
          {
            name: '已购交付学时',
            value: 'totalDeliverHours'
          },
          {
            name: '已锁定学时',
            value: 'lockCourseHours'
          },
          // {
          //   name: '复习方式',
          //   value: 'reviewType'
          // },
          // {
          //   name: '已购复习学时（分钟）',
          //   value: 'totalReviewMinutes'
          // },
          // {
          //   name: '剩余复习学时（分钟）',
          //   value: 'haveReviewMinutes'
          // },
          {
            name: '复习时间',
            value: 'reviewWeek'
          },
          {
            name: '授课方式',
            value: 'teachingType'
          },
          {
            name: '所属学管师',
            value: 'learnTubeName'
          },
          {
            name: '课程期数',
            value: 'periods'
          },
          {
            name: '支付状态',
            value: 'payStatus'
          },
          {
            name: '状态',
            value: 'isEnable'
          },
          {
            name: '课程类型',
            value: 'curriculumName'
          }
        ],

        tableHeaderList: [], // 获取表头数据
        formattedData: [], // 新增一个用于存储格式化后结果的数组
        teamList: [],
        stufen: [],
        teamDialog: false,
        rTeacherDialog: false,
        leaderDialog: false,
        time1: '',
        time2: '',
        time3: '',
        teamForm: {
          type: 2,
          oldTeam: '',
          teamId: '',
          ids: [],
          firstTime: ''
        },
        teamObj: {
          pageNum: 1,
          pageSize: 50
        },
        optionTotal: 0,
        loadingTeam: false,
        pickerOptions1: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
        pickerOptions2: {
          selectableRange: '00:00:00 - 23:59:59'
        },
        pickerOptions3: {
          selectableRange: '00:00:00 - 23:59:59'
        },
        needTime: false,
        historys: [],
        dialogHistory: false,
        loadingHistory: false,
        leaderTeamList: []
      };
    },
    beforeCreate() {
      if (this.$route.query.token) {
        this.$router.push({
          path: '/'
        });
      }
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      // this.isTeamLeader = ls.getItem('rolesVal') === 'DeliverTeamLeader';
      this.isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';
      this.getManageTeacherList();
      this.getHeaderlist();
      if (this.isAdmin) {
        this.headerSettings.splice(6, 0, {
          name: '交付中心名称',
          value: 'deliverName'
        });
        this.headerSettings.splice(7, 0, {
          name: '交付中心编号',
          value: 'deliverMerchant'
        });
        this.headerSettings.splice(9, 0, {
          name: '推荐人备注',
          value: 'referrerRemark'
        });
        this.getTeamListFn();
        setTimeout(() => {
          this.initData();
        }, 200);
      } else if (this.isTeamLeader) {
        this.headerSettings = [
          {
            name: '姓名',
            value: 'name'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '所属交付小组',
            value: 'teamName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '首次上课时间',
            value: 'firstTime'
          },
          {
            name: '上课教练',
            value: 'studyTeacherName'
          },
          {
            name: '复习教练',
            value: 'reviewTeacherName'
          },
          {
            name: '是否建群',
            value: 'hasChat'
          },
          {
            name: '创建时间',
            value: 'createTime'
          },
          {
            name: '联系方式',
            value: 'phone'
          },
          {
            name: '已购鼎英语学时',
            value: 'totalCourseHours'
          },
          {
            name: '剩余鼎英语学时',
            value: 'haveCourseHours'
          },
          {
            name: '已购交付学时',
            value: 'totalDeliverHours'
          },
          {
            name: '已锁定鼎英语学时',
            value: 'lockCourseHours'
          },
          {
            name: '复习时间',
            value: 'reviewWeek'
          },
          {
            name: '授课方式',
            value: 'teachingType'
          },
          {
            name: '所属学管师',
            value: 'learnTubeName'
          },
          {
            name: '课程期数',
            value: 'periods'
          },
          {
            name: '支付状态',
            value: 'payStatus'
          }
        ];
        this.initTeamList();
      } else {
        this.getTeamListFn();
        setTimeout(() => {
          this.initData();
        }, 200);
      }
    },
    mounted() {
      this.getbvstatusList();
    },
    watch: {
      time1: function (val) {
        console.log(val);
        let now = new Date();
        let time = new Date(val).getTime();
        let today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        let timestampDate = new Date(time);
        if (today.getTime() === timestampDate.setHours(0, 0, 0, 0)) {
          // console.log('isToday');
          let nextTime = this.getThreeHour();
          console.log(nextTime, '==============');
          if (nextTime == '23:59:59' || nextTime == '00:00:00') {
            this.pickerOptions2 = {
              selectableRange: '00:00:00 - 23:59:59'
            };
          } else {
            this.pickerOptions2 = {
              selectableRange: `${nextTime} '- 23:59:59'`
            };
            // this.time2 = nextTime
            this.time2 = this.getThreeHourNoSecond();
          }
          // this.pickerOptions2.selectableRange=''
        } else {
          console.log('false');
          this.pickerOptions2 = {
            selectableRange: '00:00:00 - 23:59:59'
          };
        }
      }
    },
    methods: {
      async initNewData() {
        // 判断为null的时候赋空
        this.tableLoading = true;
        if (!this.timeAll) {
          this.timeAll = [];
        }
        this.searchNum.beginTime = this.timeAll[0];
        this.searchNum.endTime = this.timeAll[1];
        if (this.searchNum.beginTime == null && this.searchNum.endTime == null) {
          this.searchNum.beginTime = '';
          this.searchNum.endTime = '';
        }
        this.tableLoading = true;
        let { data } = await getStudentList(this.searchNum);
        this.paikeList = data.data;
        if (this.$refs.configurationTable) {
          this.$refs.configurationTable.$el.style.width = '99.99%';
        }
        this.tableLoading = false;
        this.total = Number(data.totalItems);
      },
      async initTeamList() {
        let { data } = await getLeaderTeamList();
        this.leaderTeamList = data;
        this.teamList = data;
        if (this.leaderTeamList.length < 1) {
          return this.$message.warning('您暂无小组');
        } else {
          this.searchNum.teamId = data.length > 0 ? data[0].id : '';
          setTimeout(() => {
            this.initNewData();
          }, 300);
        }
      },
      // 派单历史
      async getDetail(id) {
        this.loadingHistory = true;
        this.dialogHistory = true;
        let { data } = await getFormalHistory(id);
        // console.log(data)
        this.historys = data;
        setTimeout(() => {
          this.loadingHistory = false;
        }, 500);
      },
      editReplaceTeacher(row) {
        this.rTeacherForm.id = row.id;
        this.rTeacherForm.oldTeacher = row.replaceTeacherName;
        findTeachersByDeliverMerchant({
          deliverMerchant: row.deliverMerchant
        }).then(({ data }) => {
          console.log(data, '===================');
          this.changeTeacherList1 = data;
        });
        this.isSubmit = false;
        this.rTeacherDialog = true;
      },
      editTeacher(row) {
        this.teacherForm.id = row.id;
        this.teacherForm.oldTeacher = row.studyTeacherName;
        findTeacherById({ id: row.id, type: 2 }).then(({ data }) => {
          console.log(data, '===================');
          this.changeTeacherList = data;
        });
        this.isSubmit = false;
        this.teacherDialog = true;
      },
      submitChangeRTeacher() {
        // 处理输入事件
        let that = this;
        if (!that.rTeacherForm.newTeacherId) return that.$message.warning('请选择要更换的教练');
        let obj = {
          id: that.rTeacherForm.id,
          newTeacherId: that.rTeacherForm.newTeacherId
        };
        that.isSubmit = true;
        changePlaceTeacher(obj)
          .then((res) => {
            that.isSubmit = false;
            if (res.success) {
              that.$message.success('操作成功');
              that.initData();
              that.closeRTeacher();
            } else {
              that.$message.warning('操作失败');
            }
          })
          .catch(() => {
            that.isSubmit = false;
          });
      },
      submitChangeTeacher() {
        // 处理输入事件
        let that = this;
        if (!that.teacherForm.newTeacherId) return that.$message.warning('请选择要更换的教练');
        let obj = {
          id: that.teacherForm.id,
          newTeacherId: that.teacherForm.newTeacherId,
          type: 2
        };
        that.isSubmit = true;
        changestudyTeacher(obj)
          .then((res) => {
            that.isSubmit = false;
            if (res.success) {
              that.$message.success('操作成功');
              that.initData();
              that.closeTeacher();
            } else {
              that.$message.warning('操作失败');
            }
          })
          .catch(() => {
            that.isSubmit = false;
          });
      },
      closeRTeacher() {
        this.rTeacherForm = {
          id: '',
          oldTeacher: '',
          newTeacherId: ''
        };
        this.rTeacherDialog = false;
      },
      closeTeacher() {
        this.teacherForm = {
          type: 2,
          id: '',
          oldTeacher: '',
          newTeacherId: ''
        };
        this.teacherDialog = false;
      },
      getThreeHour() {
        let nowDate = new Date();
        nowDate.setHours(nowDate.getHours() + 3);
        let hours = nowDate.getHours().toString().padStart(2, '0');
        let minutes = nowDate.getMinutes().toString().padStart(2, '0');
        let seconds = nowDate.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
      },
      getThreeHourNoSecond() {
        let nowDate = new Date();
        nowDate.setHours(nowDate.getHours() + 3);
        let hours = nowDate.getHours().toString().padStart(2, '0');
        let minutes = nowDate.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      },
      // 复习时间
      getReviewWeek(week) {
        // console.log(JSON.parse(week),888);
        let that = this;
        let list = [];
        JSON.parse(week).forEach(function (item) {
          list.push(that.weeklist[item].label);
        });
        let str = list.join('、');
        return str;
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          // console.log(this.$refs.HeaderSettingsDialog.checkList)
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      getGrade(val) {
        // let index = 0;
        // debugger
        // console.log(this.gradeNameArr)
        // console.log('1111111111111111')
        // if (val <= 0) {
        //   index = 0;
        // } else if (val >= this.gradeNameArr.length) {
        //   index = this.gradeNameArr.length - 1;
        // } else {
        //   index = val;
        // }
        return this.gradeNameArr[val - 1];
      },
      selectFn(value) {
        this.studentInfo.grade = value;
      },
      selectFn1(studentInfo) {},
      selectFn3(studentInfo) {},

      // 重新指派
      async submitAss() {
        let data = await submitAssign(this.deliverMerchantCode[0], this.deliverMerchantID);
        console.log(data);
        if (data.success) {
          this.tkedialog = false;
          this.initData();
          this.deliverMerchantCode = '';
          this.deliverMerchantID = '';
          this.assign.belongDeliverName = '';
        }
      },
      //学员转移
      async transferStudent(row) {
        let obj = {
          ids: [row.id]
        };
        let { data } = await getNeedTime(JSON.stringify(obj));
        // this.needTime = data
        this.transferFrom.needTime = data;
        this.transferFrom.studentCode = row.studentCode;
        this.transferFrom.merchantCode = row.merchantCode;
        this.transferFrom.deliverMerchant = row.deliverMerchant;
        this.transferFrom.ids = row.id;
        this.transferFrom.curriculumId = row.curriculumId;
        this.transferVisible = true;
        this.$refs.studentTransfer.editDelivery();
      },
      warningStatus(val) {
        if (val.warningStatus == 0) {
          return '暂无';
        } else if (val.warningStatus == 1) {
          return '未处理';
        } else if (val.warningStatus == 2) {
          return '已处理';
        } else {
          return '暂无';
        }
      },
      async editTeam(row) {
        this.teamForm.oldTeam = row.teamName;
        this.teamForm.ids = [row.id];
        console.log(this.teamForm.ids, '===========');
        let obj = {
          ids: this.teamForm.ids
        };
        let { data } = await getNeedTime(JSON.stringify(obj));
        this.needTime = data;
        this.teamDialog = true;
      },
      async changeTeamFn() {
        let _this = this;
        if (this.idList.length < 1) {
          this.$message.warning('请选择数据');
        } else {
          console.log(this.idList);
          let needTime = await getNeedTime({
            ids: _this.idList
          });
          console.log(needTime, '===========');
          this.teamDialog = true;
        }
      },
      submitChange() {
        let that = this;
        let obj = {};

        if (this.idList.length > 0) {
          this.teamForm.ids = this.idList;
        }
        if (!this.teamForm.teamId) return this.$message.warning('请选择要更换的小组');
        if (!this.time1) return this.$message.warning('请选择日期！');
        if (!this.time2) return this.$message.warning('请选择时间！');
        if (this.needTime && this.reviewTimes.length < 1) return this.$message.error('请选择复习日期！');
        if (this.needTime && !this.time3) return this.$message.error('请选择复习时间！');
        that.isSubmit = true;
        let firstTime = `${this.time1} ${this.time2}`;
        obj = {
          ids: this.teamForm.ids,
          teamId: this.teamForm.teamId,
          type: this.teamForm.type,
          firstTime: firstTime,
          reviewTime: this.time2,
          reviewWeek: JSON.stringify(this.reviewTimes.sort())
        };
        // return console.log(obj)
        changeTeam(obj)
          .then((res) => {
            that.isSubmit = false;
            console.log(res);
            if (res.success) {
              this.$message.success('操作成功');
              this.initData();
              this.closeTeam();
            }
          })
          .catch((err) => {
            that.isSubmit = false;
          });
      },
      closeTeam() {
        this.teamForm = {
          type: 2,
          oldTeam: '',
          teamId: '',
          ids: [],
          firstTime: ''
        };
        this.time1 = '';
        this.time2 = '';
        this.teamDialog = false;
      },
      async editFn(id) {
        let data = await belongDeliverAndAllDeliver(id);
        this.deliverMerchantID = id;
        this.assign = data.data;
        console.log(data.data);
        this.tkedialog = true;
      },
      statusClass(warningStatus) {
        switch (warningStatus) {
          case 0:
            return '';
          case 1:
            return 'error';
        }
      },
      //更多下拉菜单触发
      handleCommand(command) {
        window.localStorage.setItem('studentCode', command.studentCode);
        window.localStorage.setItem('merchantCode', command.merchantCode);
        switch (command.command) {
          case 1: {
            //开通课程
            this.$router.push({
              path: '/students/areasOpenCourse'
            });
            break;
          }
          case 2: {
            //学员课程记录
            this.$router.push({
              path: '/students/areasStudentCourseRecord'
            });
            break;
          }
          case 3: {
            //学员销课记录
            this.$router.push({
              path: '/students/areasStudentCourseFlow'
            });
            break;
          }
          case 4: {
            //21天抗遗忘复习计划
            this.$router.push({
              path: '/students/areaStudentWordReviewPrint'
            });
            break;
          }
          case 5: {
            //学员词汇测试
            this.$router.push({
              path: '/students/areasStudentTestResultList'
            });
            break;
          }
          case 6: {
            //学员测验打印
            this.$router.push({
              path: '/students/areasStudentWordPrintList'
            });
            break;
          }

          case 7: {
            //数据查看
            this.lookBtn(command.row);
            break;
          }
          case 8: {
            //打印
            this.translateFn(command.row);
            break;
          }
          case 9: {
            //编辑
            this.updateStudentInfo(command.row);
            break;
          }
          case 10: {
            //打印
            this.agentDeliverHours(command.row);
            break;
          }
          case 11: {
            //开通全能听力课程
            this.$router.push({
              path: '/students/areasOpenListenCourse'
            });
            break;
          }
          case 12: {
            //开通超级阅读课程
            this.$router.push({
              path: '/students/areasOpenSuperReadCourse'
            });
            break;
          }
        }
      },
      enableChange(row) {
        let text = row.isEnable === 1 ? '开启' : '关闭';
        this.$confirm('确认要' + text + ' "' + row.name + '" 的账户吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            updateStudentIsEnable(row.id, row.isEnable).then((res) => {
              return res.code === 20000;
            });
          })
          .then(() => {
            this.initData();
            this.$message.success(text + '成功');
          })
          .catch(function () {
            row.isEnable = row.isEnable === 1 ? 0 : 1;
          });
      },
      getbvstatusList() {
        bvstatusListOne().then((res) => {
          this.courseList = res.data;
        });
      },
      beforeCommand(row, command) {
        return {
          row: row,
          studentCode: row.studentCode,
          merchantCode: row.merchantCode,
          command: command
        };
      },
      // 搜索
      initData01() {
        this.searchNum.pageNum = 1;
        this.searchNum.pageSize = 10;
        this.initData();
      },
      // 课程表按钮
      async classCard(row) {
        let search = JSON.parse(JSON.stringify(this.searchNum));
        search.studentCode = row.studentCode;
        search.pageNum = 1;
        search.name = '';
        let { data } = await getTimetable(search);
        if (data.data == '') {
          this.$message.error('该学生还没有课程安排，请先排课。');
        } else {
          this.$router.push({
            path: '/classCard',
            query: {
              classCard: row.studentCode
              // planId: row.planId
            }
          });
        }
      },

      agentDeliverHours(row) {
        studentDeliverCourseInfo(row.studentCode, row.merchantCode, row.deliverMerchant).then((res) => {
          this.studentCourseInfo = res.data;
          this.showAgentDialog = true;
          this.agentHours = row;
        });
      },
      async sendAgent() {
        this.agentHours.deliverMerchantCode = this.agentHours.deliverMerchant;
        this.agentHours.targetDeliverMerchantCode = this.agentHours.deliverMerchant;
        await agentDeliverFinance(this.agentHours);
        this.$message.success('成功');
        this.showAgentDialog = false;
        this.agentHours = {};
      },
      // 打印
      translateFn(row) {
        this.$refs.translate.studentCode = row.studentCode;
        this.$refs.translate.merchantCode = row.merchantCode;
        this.translateDle = true;
        this.$refs.translate.dayinDateList = row;
      },

      // 学管分配的按钮
      async stufenFn() {
        let _this = this;

        if (this.idList.length < 1) {
          this.$message.error('请选择数据');
        } else {
          var temp = true;
          let type = _this.stufen[0].curriculumId;
          _this.stufen.forEach((e) => {
            if (e.curriculumId != type) {
              temp = false;
            }
          });
          if (!temp) return _this.$message.error('不同类型的课程不能批量分配');
          let res = await getLearnTubeList({ curriculumId: type });
          this.teacherList = res.data;
          if (res.code == 20000) {
            _this.dialogVisible = true;
          }
        }
      },
      async sendFn() {
        this.allotLearnTubeList.ids = this.idList.toString();
        this.allotLearnTubeList.learnTube = this.valueTeacher;
        let res = await allotLearnTube(this.allotLearnTubeList);
        this.$message.success('操作成功');
        this.dialogVisible = false;
        this.initData();
      },
      // 多选
      studentXueguan(val) {
        // console.log(val)
        if (val.length == 1) {
          this.teamForm.oldTeam = val[0].teamName;
        }
        this.stufen = val;
        this.idList = val.map((item) => item.id);
        this.nameList = val.map((item) => item.name).join('/');
      },
      // 学员信息表
      studentFn(row) {
        this.$router.push({
          path: '/pclass/components/studentsList',
          query: {
            studentCode: row.studentCode,
            merchantCode: row.merchantCode
          }
        });
      },
      changeFn(row) {
        this.$router.push({
          path: '/pclass/components/changeClassList',
          query: {
            studentCode: row.studentCode,
            merchantCode: row.merchantCode
          }
        });
      },
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      // 学员列表接口
      async initData() {
        // 判断为null的时候赋空
        this.tableLoading = true;
        if (!this.timeAll) {
          this.timeAll = [];
        }
        this.searchNum.beginTime = this.timeAll[0];
        this.searchNum.endTime = this.timeAll[1];
        if (this.searchNum.beginTime == null && this.searchNum.endTime == null) {
          this.searchNum.beginTime = '';
          this.searchNum.endTime = '';
        }
        this.tableLoading = true;
        let { data } = await getStudentList(this.searchNum);
        this.paikeList = data.data;
        // this.paikeList.forEach((row) => {
        //   this.formattedData.push({
        //     ...row,
        //     formattedFirstTime: this.getFormatToService(row.firstTime, row.firstWeek),
        //   });
        // });
        if (this.$refs.configurationTable) {
          this.$refs.configurationTable.$el.style.width = '99.99%';
        }
        this.tableLoading = false;
        this.total = Number(data.totalItems);
      },

      //弹框关闭按钮
      handleClose(ele) {
        // console.log(11111111111111111111)
        // this[ele] = false;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      changeDrawer(v) {
        this.childVisible = v;
      },
      transferClose(val) {
        this.transferVisible = val;
      },

      // 排课按钮
      async paikeBtn(row) {
        let { data } = await getPlanCourse(row.id);
        console.log(data, '111111111111111');
        if (data.haveCourseHours == 0) return this.$message.warning('剩余可排交付学时不足，请充值');
        this.$refs.addRole.teacherNum.studentCode = row.studentCode;
        if (data && !!data.timeList && data.timeList.length > 0) {
          // 21天抗遗忘小时差
          data.reviewTime = data.reviewTime.split(',');
          let startTimes = dayjs(data.dateList[0] + data.reviewTime[0]);
          let endTimes = dayjs(data.dateList[0] + data.reviewTime[1]);
          let hourValue = endTimes.diff(startTimes, 'hour');
          this.$refs.addRole.hourValue = hourValue;
          this.$refs.addRole.coniditon = data;

          // 排课时间小时差
          for (let i = 0; i < data.timeList.length; i++) {
            let startTime = dayjs(data.dateList[0] + data.timeList[i].startTime);
            let endTime = dayjs(data.dateList[0] + data.timeList[i].endTime);
            let hours = endTime.diff(startTime, 'hour');
            data.timeList[i]['hourNum'] = hours;
            console.log(data.timeList);
          }
          this.$refs.addRole.coniditon.timeList = data.timeList;
          /*        this.$refs.addRole.details = data.teacherId.toString();
                this.$refs.addRole.TeacherList = [{
                  teacherId: data.teacherId,
                  teachName: data.teacherName
                }];*/
          this.$refs.addRole.coniditon.teachingType = data.teachingType;
        } else {
          this.$refs.addRole.coniditon = data;
          this.$refs.addRole.details = '';
          this.$refs.addRole.coniditon.dateList = [];
          this.$refs.addRole.coniditon.timeList = [
            {
              startTime: '',
              endTime: '',
              hourNum: 1
            }
          ];
          this.$refs.addRole.coniditon.reviewTime = ['', ''];
          this.$refs.addRole.coniditon.planCourseHours = 0;
          if (this.$refs.addRole.coniditon.teachingType === 0) {
            this.$refs.addRole.coniditon.teachingType = '';
          }
          this.$refs.addRole.TeacherList = [];
        }
        this.rowlist = row;
        console.log(this.rowlist);
        this.childVisible = true;
      },

      //排课计划发送
      sendBtn(row, type) {
        sendPlanCourse(row.planId, type).then((res) => {
          console.log(res);
          this.initData();
          this.$message.success('发送成功！');
        });
      },
      //取消排课
      cancelBtn(row) {
        cancelPlanCourse(row.planId).then((res) => {
          console.log(res);
          this.initData();
          this.$message.success('取消成功！');
        });
      },

      // 查看按钮
      async lookBtn(row) {
        this.idser = row.id;
        this.teacher = row.teacher;
        this.islook = true;
        this.contentType = row.contentType;
        this.$refs.sumlookDialog.rowList = row;
        this.$refs.sumlookDialog.reviewTotal.id = row.id;
        this.$refs.sumlookDialog.reviewTotal.planId = row.planId;
        this.$refs.sumlookDialog.getStudyfun();
      },
      seeBtn(row) {
        this.idser = row.id;
        this.isSee = true;
      },

      transDrawer(v) {
        this.translateDle = v;
      },
      //定义导出Excel表格事件
      exportExcel() {
        /* 从表生成工作簿对象 */
        var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'));
        /* 获取二进制字符串作为输出 */
        var wbout = XLSX.write(wb, {
          bookType: 'xlsx',
          bookSST: true,
          type: 'array'
        });
        try {
          FileSaver.saveAs(
            //Blob 对象表示一个不可变、原始数据的类文件对象。
            //Blob 表示的不一定是JavaScript原生格式的数据。
            //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
            //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
            new Blob([wbout], { type: 'application/octet-stream' }),
            //设置导出文件名称
            'sheetjs.xlsx'
          );
        } catch (e) {
          if (typeof console !== 'undefined') console.log(e, wbout);
        }
        return wbout;
      },

      //  打开已锁定课时详情
      showLockCourse(ele) {
        this.lockCourseData = [];
        let paramdata = {
          deliverMerchant: ele.deliverMerchant,
          merchantCode: ele.merchantCode,
          studentCode: ele.studentCode,
          curriculumId: ele.curriculumId
        };
        this.getStudentDeliverHoursVo(paramdata, ele.name);
      },

      //获取学员交付课时详情
      async getStudentDeliverHoursVo(data, name) {
        let result = await getStudentDeliverHoursVo(data);
        result.data.name = name;
        this.lockCourseData.push(result.data);
        this.showLockCourseVisible = true;
      },
      //重置
      rest() {
        this.timeAll = [];
        this.searchNum.pageNum = 1;
        this.$refs.searchNum.resetFields();
        this.manageTeacherArr = [];
        this.manageTeacherObj.pageNum = 1;
        this.manageTeacherObj.realName = '';
        if (this.isTeamLeader) {
          this.querydata.teamId = this.leaderTeamList[0].id;
        }
        this.getManageTeacherList();
        this.initData();
      },
      filterGrade(val) {
        let result = '';
        let arr = this.options.filter((i) => val == i.value1);
        if (arr.length > 0) {
          result = arr[0].value1;
        } else {
          result = '';
        }
        return result;
      },
      //编辑时查看信息
      async updateStudentInfo(row) {
        let result = await selectStudent(row.studentCode);
        // this.value1 = this.options[parseInt(result.data.grade)].value1;
        this.value1 = this.filterGrade(result.data.grade);
        // this.value=result.data.grade;
        this.studentInfo.studentCode = row.studentCode;
        this.studentInfo.realName = row.name;
        this.studentInfo.grade = result.data.grade;
        this.studentInfo.school = result.data.school;
        this.studentInfo.id = result.data.id;
        this.studentInfo.memberCode = result.data.memberCode;
        this.studentInfo.loginName = result.data.loginName;
        this.studentInfo.loginPwd = result.data.loginPwd;
        this.studentInfo.address = result.data.address;
        this.studentInfo.regTime = result.data.regTime;
        this.studentInfo.isEnable = result.data.isEnable;
        this.studentInfo.restrictedUse = result.data.restrictedUse;
        this.studentInfo.gender = result.data.gender;
        this.studentInfo.useModule = result.data.useModule;
        this.studentInfo.isFormal = result.data.isFormal;
        this.studentInfo.dateOfBirth = result.data.dateOfBirth;
        this.studentInfo.schoolType = result.data.schoolType;
        this.studentInfo.province = result.data.province;
        this.studentInfo.city = result.data.city;
        this.studentInfo.area = result.data.area;
        this.StudentUpdateFn = true;
      },

      async updateStudentInfo1() {
        let result = await updateStudent(this.studentInfo.studentCode, this.studentInfo.realName, this.studentInfo.grade, this.studentInfo.school);
        this.initData();
        this.StudentUpdateFn = false;
      },

      /*** 下拉加载* 所属学管师搜索* START*/
      loadmore_manageTeacher() {
        if (!this.manageTeacherLoad) {
          if (this.manageTeacherObj.pageNum < this.manageTeacherTotalPage) {
            this.manageTeacherObj.pageNum++;
            this.getManageTeacherList();
          }
        }
      },
      //学管师
      async getManageTeacherList() {
        let allData = await xueguanListApi(this.manageTeacherObj);
        this.manageTeacherTotalPage = allData.data.totalPage;
        this.manageTeacherArr = this.manageTeacherArr.concat(allData.data.data);
      },
      manageTeacherFilterValue(value) {
        console.log(value);
        this.manageTeacherArr = [];
        this.manageTeacherObj.pageNum = 1;
        this.manageTeacherObj.realName = value;
        this.getManageTeacherList();
      },
      clearSearchRecord() {
        setTimeout(() => {
          if (this.searchNum.learnTubeTeacherName == '') {
            this.manageTeacherArr = [];
            this.manageTeacherObj.pageNum = 1;
            this.manageTeacherObj.realName = '';
            this.getManageTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeManageTeacher(e) {
        if (e == '') {
          this.manageTeacherArr = [];
          this.manageTeacherObj.pageNum = 1;
          this.manageTeacherObj.realName = '';
          this.getManageTeacherList();
        }
      },
      /*** 下拉加载* 所属学管师搜索* END*/

      //教练选择
      openChoseTeacher(item, val, type) {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
        this.updateTeacherItem = item;
        this.curChoseTeacher = val;
        this.choseTeacherType = type;
        this.dialogChoseTeacher = true;
      },
      closeChoseTeacher() {
        this.newChoseTeacher = '';
        this.dialogChoseTeacher = false;
      },
      async getTeamListFn() {
        const { data } = await getTeamList(this.teamObj);
        // console.log(res);
        this.teamList = this.teamList.concat(data.data);
        // this.teamList = data.data
        this.optionTotal = data.totalItems;
      },
      loadmoreTeam() {
        if (!this.loadingTeam) {
          if (this.teamList.length == this.optionTotal) return; //节流防抖
          this.teamObj.pageNum++;
          this.getTeamListFn();
        }
      },
      /**
       * 下拉加载
       * 弹框分配老师
       */
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      async getTeacherList() {
        let allData = await selAllTeacher(this.selectObj);
        this.option = this.option.concat(allData.data.data);
        console.log(this.option);
      },
      async updateStudentBinding() {
        let typeArr = [this.choseTeacherType];
        if (this.choseTeacherType == 1 && this.updateTeacherItem.reviewTeacherName == '') {
          typeArr.push(2);
        }
        let data = {
          studentCode: this.updateTeacherItem.studentCode,
          teacherId: this.newChoseTeacher,
          type: typeArr
        };
        let allData = await updateStudentBinding(data);
        if (allData.success) {
          if (this.curChoseTeacher) {
            this.$message.success('修改成功！');
          } else {
            this.$message.success('选择成功！');
          }
          this.dialogChoseTeacher = false;
          this.newChoseTeacher = '';
          this.initData();
        }
      },
      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getTeacherList();
      },
      getCurChoseTeacherTitle() {
        let str2 = '上课';
        if (this.choseTeacherType === 2) {
          str2 = '复习';
        }
        return '当前' + str2 + '教练';
      },
      getDialogChoseTeacherTitle() {
        let str1 = '选择';
        if (this.curChoseTeacher) {
          str1 = '修改';
        }
        let str2 = '上课';
        if (this.choseTeacherType === 2) {
          str2 = '复习';
        }
        return str1 + str2 + '教练';
      },

      // 编辑备注
      openNotes(row) {
        this.notesValue = row.referrerRemark;
        this.studentCode = row.studentCode;
        this.deliverCode = row.deliverMerchant;
        this.dialogNotes = true;
      },

      //
      async editNotes() {
        let data = {
          referrerRemark: this.notesValue,
          studentCode: this.studentCode,
          deliverCode: this.deliverCode
        };
        await modifyRemarks(data).then((res) => {
          this.$message.success('操作成功');
          this.dialogNotes = false;
          this.initData();
        });
      },

      // 试课报告
      LessonTestReport(row) {
        console.log(row);
        getTrialclass(row.studentCode).then((res) => {
          this.reportList = res.data;
          this.reportList.curriculumName = row.curriculumName;
          this.reportVisible = true;
        });
      },

      changeReport(val) {
        this.reportVisible = val;
      },

      //上课信息对接表
      async fillTableNormalData(item) {
        const that = this;
        let data = {
          id: item.studentContactInfoId
        };
        let abutment = await getStudentContactInfoDetail(data);
        that.abutmentList = abutment.data;
        console.log(that.abutmentList, '==========================');
        // that.coursrList = that.abutmentList.courseProject ? JSON.parse(that.abutmentList.courseProject) : ''
        // that.reviewWeekList = JSON.parse(that.abutmentList.reviewWeek);
        that.coursrList = that.abutmentList.courseProject ? JSON.parse(that.abutmentList.courseProject) : '';
        that.reviewWeekList = that.abutmentList.reviewWeek ? JSON.parse(that.abutmentList.reviewWeek) : '';
        that.reviewWeekList.sort((a, b) => a - b);
      },

      getWeekName(week) {
        return this.normalWeekData[Number(week)];
      },
      openAbutment(row) {
        this.fillTableNormalData(row);
        this.dialogAbutment = true;
      },
      getFormatToService(date, week) {
        if (date) {
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
          } else {
            allStr = str.replace('&', '');
          }
          return allStr;
        }
        return '-';
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'paike',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'paike'
        };
        await getTableTitleSet(data).then((res) => {
          // console.log('走到这里了,1')
          // console.log(res)
          if (res.data) {
            // console.log(JSON.parse(res.data.value))
            this.tableHeaderList = [];
            JSON.parse(res.data.value).forEach((item) => {
              if (item) {
                this.tableHeaderList.push(item);
              }
            });
            // this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
            // console.log('走到这里了,2')
            // console.log(this.tableHeaderList)
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .inputclass {
    position: relative;
  }

  .btnclass {
    // position: absolute;
    // left: 1187px;
    // bottom: -41px;
    ::v-deep {
      .el-form-item__content {
        margin-left: 0px !important;
      }
    }
  }

  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
  }

  .el-icon-arrow-down {
    font-size: 12px;
  }

  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  // ::v-deep .el-dialog__header {
  //   text-align: center;
  //   font-weight: 900;
  // }

  // ::v-deep .el-dialog__body {
  //   padding: 0 2vw;
  //   height: 28vw;
  // }

  ::v-deep.notes-phone {
    .el-dialog__body {
      padding: 0 2vw;
      height: 15vw !important;
    }
  }

  ::v-deep.notes {
    .el-dialog__body {
      padding: 0 2vw;
      height: 4vw !important;
    }
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  .btnBox {
    display: inline-block;
    margin-right: 10px;
  }

  .detailTip {
    font-size: 12px;
    color: #46a6ff;
    margin-left: 10px;
    cursor: pointer;
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }

  ::v-deep.assgin-phone {
    .el-dialog__body {
      // padding: 0 2vw;
      margin-top: 30px;
      height: 40vw !important;
    }
  }

  ::v-deep.assgin {
    .el-dialog__body {
      // padding: 0 2vw;
      margin-top: 30px;
      height: 8vw !important;
    }
  }

  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }

  .week {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 7px 20px;
  }

  .vocabulary {
    position: absolute;
    top: 6px;
    right: -1px;
    height: 24px;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    border-radius: 3px;
    padding: 0 4px;
    background-color: #46a6ff;
  }

  .thesaurus {
    position: relative;
  }
</style>
