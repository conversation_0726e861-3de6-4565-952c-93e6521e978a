<template>
  <div>
    <!-- 试课奖励/工资配置 -->
    <div v-loading="tableLoading">
      <div class="box-card" style="text-align: center; margin-top: 150px; height: 30vh" v-if="trialWages.length <= 0">
        <div class="no_data" style="height: 100%">
          <el-image style="width: 100px; height: 100px" :src="url"></el-image>
          <div style="color: #999; margin-top: 20px">暂无数据</div>
          <el-button type="primary" size="small" style="margin-top: 25px" @click="openTearcherWages('add', null, true)">新增试课奖励</el-button>
        </div>
      </div>
      <div v-else>
        <el-button type="primary" size="small" style="margin-top: 15px" @click="openTearcherWages('add')">新增试课奖励</el-button>
        <div class="warning-title-css">
          <i class="el-icon-warning" style="color: #f89728"></i>
          <span>注:未设置课程类型的试课奖励，都按默认发!</span>
        </div>
        <div v-if="trialWages.length > 0">
          <el-table
            v-loading="tableLoading"
            :data="trialWages"
            style="width: 100%"
            id="out-table"
            :header-cell-style="{ background: '#e2eaf6' }"
            :cell-style="{ 'text-align': 'center' }"
          >
            <el-table-column
              v-for="(item, index) in tableHeaderList"
              :key="`${index}-${item.id}`"
              :prop="item.value"
              :label="item.name"
              header-align="center"
              :width="item.value == 'operate' ? 200 : ''"
            >
              <template v-slot="{ row, $index }">
                <div v-if="item.value == 'operate'">
                  <el-button type="primary" size="mini" icon="el-icon-edit" @click="openTearcherWages('update', row)">编辑</el-button>
                  <el-button type="danger" size="mini" icon="el-icon-delete" v-if="row.curriculumId != 'default'" @click="singleDelete(row.curriculumName, row, $index)">
                    删除
                  </el-button>
                </div>
                <!-- 工资 -->
                <span v-else-if="item.value == 'wage'">{{ Number(row.value) + Number(row.configState) }}</span>
                <!-- 状态 -->
                <span v-else-if="item.value == 'effectStatusDesc'" :class="{ green: row.effectStatusDesc == '生效中' }">{{ row[item.value] }}</span>
                <!-- 其他 -->
                <span v-else>{{ row[item.value] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleFormSizeChange"
            style="margin-top: 20px"
            @current-change="handleFormCurrentChange"
            :current-page="trialWagesQuery.pageNum"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="trialWagesQuery.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="trialWagesQuery.total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 试课/工资配置-新增弹窗 -->
    <el-dialog :title="`${isAdd ? '新增' : '编辑'}试课奖励`" :visible.sync="dialogVisibleAdd" width="33%" :before-close="handleCloseAdd">
      <el-row>
        <el-form label-width="120px" label-position="right" ref="addForm" :model="addForm" :rules="addFormRules" inline class="my-form">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-input disabled value="默认" v-if="addForm.curriculumId == 'default' || isDefault"></el-input>
            <!-- <el-select v-model="addForm.curriculumId" placeholder="请选择" v-else @change="changeCurriculumId"> -->
            <el-select v-model="addForm.curriculumId" placeholder="请选择" v-else>
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="试课奖励:" prop="expBonus">
            <el-row>
              <el-col :span="19">
                <el-input v-model="addForm.expBonus" @input="formatDecimal($event, 'addForm.expBonus')" placeholder="请输入试课奖励"></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/学员</el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAdd">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';
  import { getCurriculumExpCourseReward, addOrUpdateExpCourseRewardConfig, getOneToMoreClassList, deleteExpWageReward } from '@/api/FinanceApi/assistantWagesOneToMany';

  export default {
    name: 'OneToMany',
    data() {
      return {
        addFormRules: {
          courseName: {
            required: true,
            message: '请选择课程大类',
            trigger: 'blur'
          },
          expBonus: {
            required: true,
            message: '请输入试课奖励',
            trigger: 'blur'
          }
        },
        addForm: {
          curriculumId: '',
          expBonus: ''
        },
        isAdd: false, // 是否新增
        isDefault: false, // 是否为默认配置
        dialogVisibleAdd: false, // 新增弹窗
        url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
        tableLoading: false, // 表格加载状态
        trialWages: [], // 试课奖励列表
        tableHeaderList: [
          {
            name: '课程类型',
            value: 'curriculumName'
          },
          {
            name: '试课奖励/学员',
            value: 'expBonus'
          },
          {
            name: '状态',
            value: 'effectStatusDesc'
          },
          {
            name: '生效时间',
            value: 'effectTime'
          },
          {
            name: '操作',
            value: 'operate'
          }
        ],
        trialWagesQuery: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        courseList: [] // 课程类型
      };
    },
    created() {
      this.getbvstatusList();

      this.initData();
    },
    methods: {
      initData() {
        this.tableLoading = true;
        // 请求
        getCurriculumExpCourseReward({ pageNum: this.trialWagesQuery.pageNum, pageSize: this.trialWagesQuery.pageSize }).then((res) => {
          this.tableLoading = false;
          this.trialWages = res.data.data || [];
          this.trialWagesQuery.total = Number(res.data.totalItems);
        });
      },
      //   // 修改课程大类
      //   changeCurriculumId(e) {
      //     if (e) {
      //       this.addForm.name = this.courseList.filter((item) => item.id == e)[0].enName;
      //     }
      //   },
      // 确认新增
      handleAdd() {
        this.$refs.addForm.validate((valid) => {
          if (!valid) {
            return false;
          }
          addOrUpdateExpCourseRewardConfig(this.addForm)
            .then((result) => {
              if (this.isAdd) {
                this.$message.success('新增成功');
              } else {
                this.$message.success('编辑成功');
              }
              this.handleCloseAdd();
              this.initData();
            })
            .catch((err) => {});
        });
      },
      // 关闭新增上课时间
      handleCloseAdd() {
        this.dialogVisibleAdd = false;
        this.addForm = {
          curriculumId: '',
          expBonus: ''
        };
        // 清除校验
        this.$refs.addForm.clearValidate();
      },
      // 打开新增/编辑弹窗
      openTearcherWages(type, res, showDefault = false) {
        this.isDefault = false;
        if (type === 'add') {
          this.isAdd = true;
          if (showDefault) {
            this.isDefault = true;
            this.addForm.curriculumId = 'default';
          }
        } else if (type === 'update') {
          this.isAdd = false;

          this.addForm = { ...this.addForm, ...res };
        }
        this.dialogVisibleAdd = true;
      },
      singleDelete(name, row, index) {
        const that = this;
        console.log(name, row, index);
        this.$confirm(`确定删除《 ${name} 》课程类型的配置吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({
            lock: true,
            text: '删除中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          deleteExpWageReward(row.id)
            .then((res) => {
              loading.close();
              this.$message.success('删除成功');
              this.initData();
            })
            .catch((err) => {
              loading.close();
            });
        });
      },
      getbvstatusList() {
        getOneToMoreClassList({}).then((res) => {
          this.courseList = res.data;
          //   console.log('🚀 ~ bvstatusList ~ this.courseList:', this.courseList);
        });
      },
      //试课奖励分页
      handleFormSizeChange(val) {
        this.trialWagesQuery.pageSize = val;
        this.initData();
      },
      handleFormCurrentChange(val) {
        this.trialWagesQuery.pageNum = val;
        this.initData();
      },
      /**
       *
       * @param el 元素
       * @param path 路径 如：'addForm.value'
       */
      formatDecimal(el, path) {
        let value = JSON.parse(JSON.stringify(el));
        // 1. 移除非数字和非法字符
        let cleaned = value.replace(/[^\d.]/g, '');

        // 2. 分割整数和小数部分
        let parts = cleaned.split('.');
        if (parts.length > 1) {
          // 限制小数部分最多两位
          parts[1] = parts[1].slice(0, 2);
          cleaned = parts[0] + '.' + parts[1];
        }

        // 根据 digit 值创建路径数组
        let that = path.split('.');
        // 更新输入框的值
        setNestedValue(this, that, cleaned);
        // 递归函数来访问嵌套属性
        function setNestedValue(obj, path, value) {
          let i;
          for (i = 0; i < path.length - 1; i++) {
            obj = obj[path[i]];
          }
          obj[path[i]] = value;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .warning-title-css {
    line-height: 60px;
    span {
      display: inline-block;
      margin-left: 10px;
      color: #f89728;
    }
  }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
  }
</style>
