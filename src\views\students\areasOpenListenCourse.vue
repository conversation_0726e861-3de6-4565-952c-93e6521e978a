<!--开通全能听力课程-->
<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" class="SearchForm" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入课程名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程阶段:">
            <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" size="small" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="openCourse()">开通</el-button>
      </div>

      <el-table
        class="course-table"
        v-loading="tableLoading"
        ref="multipleTable"
        tooltip-effect="dark"
        :row-key="getRowKeys"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        :default-sort="{ prop: 'date', order: 'descending' }"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="courseCode" label="课程编号" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseName" label="课程名称" width="" sortable align="center"></el-table-column>
        <el-table-column prop="courseStageName" label="课程学段" width="" sortable align="center"></el-table-column>
        <el-table-column prop="textbookVersion" label="课程版本" width="" sortable align="center"></el-table-column>
        <el-table-column prop="coverUrl" label="课程封面" width="" sortable>
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image v-if="scope.row.coverUrl" class="table_list_pic" :src="scope.row.coverUrl" @click="openImg(scope.row)"></el-image>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="380px" :before-close="handleClose">
      <div class="coverimg">
        <el-image class="table_list_pic" :src="coverImg"></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import listeningCourseAPI from '@/api/omnipotentListening/listeningCourseAPI';
  import enTypes from '@/api/bstatus';
  import { pageParamNames } from '@/utils/constants';
  export default {
    data() {
      return {
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        submitOpenCourse: {
          studentCode: '',
          courseIdList: []
        },
        tableLoading: false,
        dataQuery: {
          courseCode: '',
          merchantCode: '',
          courseName: '',
          courseStage: ''
        },
        tableData: [], //表格数据

        courseStageType: [], //课程学段类型

        multipleSelection: [], //多选数据
        studentCode: '',
        merchantCode: '',
        //打开大图
        coverImg: '',
        dialogOpenimg: false
      };
    },

    created() {
      this.studentCode = window.localStorage.getItem('studentCode');
      this.merchantCode = window.localStorage.getItem('merchantCode');
      this.fetchData();
      //获取学段下拉框
      this.getCourseStage();
    },
    methods: {
      //获取学段
      getCourseStage() {
        enTypes.getEnumerationAggregation('CJYDCourseStage').then((res) => {
          this.courseStageType = res.data;
        });
      },
      getRowKeys(row) {
        return row.id;
      },
      // 多选的值
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },

      fetchData01() {
        this.tablePage = {
          currentPage: 1,
          size: this.tablePage.size,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },

      rest() {
        this.dataQuery = {
          courseCode: '',
          merchantCode: '',
          courseName: '',
          courseStage: ''
        };
        this.fetchData01();
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        that.dataQuery.pageNum = that.tablePage.currentPage;
        that.dataQuery.pageSize = that.tablePage.size;
        that.dataQuery.studentCode = that.studentCode;
        that.dataQuery.merchantCode = that.merchantCode;
        listeningCourseAPI.getListeningCourseOpen(that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name] || 0)));
        });
      },

      openCourse() {
        this.submitOpenCourse = {
          studentCode: '',
          merchantCode: '',
          courseIdList: []
        };
        if (this.multipleSelection.length <= 0) {
          this.$message('请选择课程');
          return false;
        }
        this.submitOpenCourse.studentCode = this.studentCode;
        this.submitOpenCourse.merchantCode = this.merchantCode;
        for (let i = 0; i < this.multipleSelection.length; i++) {
          this.submitOpenCourse.courseIdList.push(this.multipleSelection[i].id);
        }
        this.$confirm('确定操作吗?', '开课', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          listeningCourseAPI.openCourse(this.submitOpenCourse).then((res) => {
            this.submitOpenCourse = {
              studentCode: this.studentCode,
              merchantCode: this.merchantCode,
              courseIdList: []
            };
            this.$refs.multipleTable.clearSelection();
            this.multipleSelection = [];
            this.fetchData01();
            this.$message.success('开课成功');
          });
        });
      },

      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },

      openImg(row) {
        this.coverImg = row.coverUrl;
        this.dialogOpenimg = true;
      },
      handleClose() {
        this.dialogOpenimg = false;
      }
    }
  };
</script>

<style>
  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 5px 0;
    padding-left: 5px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;
  }
</style>
