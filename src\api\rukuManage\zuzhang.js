/**
 * “交付小组组长”相关接口
 */
import request from '@/utils/request'

/**
 * 交付中心配置列表查询
 * @param data
 */
export const getLeaderList = (data) => {
  return request({
    url: '/deliver/web/teamLeader/list/page',
    method: 'GET',
    params: data
  })
}
/**
 * 交付中心配置列表查询
 * @param data
 */
export const getMerchantName = (name) => {
  return request({
    url: `/deliver/web/merchant/searchDeliverInfo?name=${name}`,
    method: 'GET',
  })
}
/**
 * 交付中心配置列表查询详情
 * @param data
 */
export const getLeaderDetail = (id) => {
  return request({
    url: `/deliver/web/teamLeader/findById?id=${id}`,
    method: 'GET',
    // params: data
  })
}
/**
 * 交付中心配置添加交付小组组长
 * @param data
 */
export const addLeader = (data) => {
  return request({
    url: '/deliver/web/teamLeader/save',
    method: 'POST',
    data
  })
}
/**
 * 交付中心配置修改交付小组组长
 * @param data
 */
export const editLeader = (data) => {
  return request({
    url: '/deliver/web/teamLeader/update',
    method: 'POST',
    data
  })
}
/**
 * 交付中心配置修改交付小组组长
 * @param data
 */
export const delLeader = (id) => {
  return request({
    url: `/deliver/web/teamLeader/delete?id=${id}`,
    method: 'POST',
  })
}