/**
 * 学员测验打印-单词相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询
  getPrintClose(wordPrintCode, studentCode, merchantCode) {
    return request({
      url: '/znyy/deliver/student/printFinish',
      method: 'GET',
      params: {
        wordPrintCode: wordPrintCode,
        studentCode: studentCode,
        merchantCode: merchantCode
      }
    })
  },

  editEnable(data) {
    return request({
      url: '/znyy/areas/student/word/print/enable/edit/' + data,
      method: 'PUT',
    })
  }
}
