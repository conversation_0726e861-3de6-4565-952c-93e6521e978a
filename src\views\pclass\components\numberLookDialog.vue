<template>
  <div class="dialog">
    <div v-if="studyList1.curriculumName == '拼音法' || studyList1.curriculumName == '拼音法（高年级）'">
      <el-drawer title="数据查看" :visible.sync="lookstyle_" :direction="direction" @close="handleClose" style="margin-top: 15px" :size="screenWidth > 1300 ? '30%' : '80vw'">
        <el-row style="margin-top: 1vw; font-size: 22px">
          <el-col class="paike">
            日期：
            <span>{{ studyList1.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ studyList1.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            时间：
            <span>{{ studyList1.studyTime }}</span>
          </el-col>
          <el-col class="paike">
            实际时间：
            <span>{{ studyList1.actualStart }} 至 {{ studyList1.actualEnd }}</span>
          </el-col>
          <el-col class="paike">
            学习学时：
            <span>{{ studyList1.studyHour + '个小时' }}</span>
          </el-col>
          <div class="paike" v-if="studyList1.extendProperty.totalCourseHours">已购拼音法课时：{{ studyList1.extendProperty.totalCourseHours }}个小时</div>
          <div class="paike" v-if="studyList1.extendProperty.totalCourseHours">剩余拼音法课时：{{ studyList1.extendProperty.haveCourseHours }}个小时</div>
          <el-col class="paike">
            所学课程类型：
            <span>{{ studyList1.curriculumName }}{{ studyList1.extendProperty.courseTypeName }}</span>
          </el-col>
          <el-col class="paike">
            所学课程名称：
            <span>{{ studyList1.extendProperty.courseNames }}</span>
          </el-col>
          <el-col class="paike">
            学习元辅音个数：
            <span>{{ studyList1.extendProperty.consonantCounts }}</span>
          </el-col>
          <el-col class="paike">
            学习音节个数：
            <span>{{ studyList1.extendProperty.syllableCounts }}</span>
          </el-col>
          <el-col class="paike">
            学习单词个数：
            <span>{{ studyList1.extendProperty.wordCounts }}</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            教练评语：
            <el-input style="margin-top: 10px; width: 90%" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" v-model="studyList1.feedback" disabled></el-input>
            <div style="height: 20vh"></div>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
    <div v-if="studyList1.moduleType == 4">
      <el-drawer title="数据查看" :visible.sync="lookstyle_" :direction="direction" @close="handleClose" style="margin-top: 15px" :size="screenWidth > 1300 ? '30%' : '80vw'">
        <el-row style="margin-top: 1vw; font-size: 22px">
          <el-col class="paike">
            日期：
            <span>{{ studyList1.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ studyList1.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            实际时间：
            <span>{{ studyList1.actualStart }} 至 {{ studyList1.actualEnd }}</span>
          </el-col>
          <el-col class="paike">
            学习学时：
            <span>{{ studyList1.studyHour + '个小时' }}</span>
          </el-col>
          <el-col class="paike">
            课程类型：
            <span>{{ studyList1.listeningStatisticsDto.courseName }}</span>
          </el-col>
          <el-col class="paike">
            课程名称：
            <span>{{ listenName(studyList1.listeningStatisticsDto.courseList) }}</span>
          </el-col>
          <el-col class="paike">
            学习进度：
            <span>{{ listenProgress(studyList1.listeningStatisticsDto.progressList) }}</span>
          </el-col>
          <el-col class="paike">
            听力名称(正确率):
            <span>{{ listenArray(studyList1.listeningStatisticsDto.listeningList) }}</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            教练评语：
            <!-- <span>{{ studyList1.feedback }}</span> -->
            <el-input style="margin-top: 10px; width: 90%" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" v-model="studyList1.feedback" disabled></el-input>
            <div style="height: 20vh"></div>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
    <div v-else>
      <el-drawer title="数据查看" :visible.sync="lookstyle_" :direction="direction" @close="handleClose" style="margin-top: 6vw" :size="screenWidth > 1300 ? '30%' : '80vw'">
        <el-row style="margin-top: 1vw">
          <div style="margin-left: 20vw">
            <span class="Second" v-if="studyStatus">
              <button type="button" style="forecolor: blue" @click="studyFn">
                <span style="color: skyblue">日</span>
                /
                <span>总</span>
              </button>
            </span>
            <span class="Second" v-else>
              <button type="button" style="forecolor: blue" @click="studyBtn">
                <span style="color: skyblue">总</span>
                /
                <span>日</span>
              </button>
            </span>
          </div>
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            上学时间：
            <span>{{ studyList1.studyTime }}</span>
          </el-col>
          <template v-if="studyList1.moduleType != 3">
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              <!--          总报学时：-->
              已购鼎英语学时：
              <span>{{ studyList1.totalCourseHours }}小时</span>
              <!--          <span>{{ studyList1.totalCourse }}小时</span>-->
            </el-col>
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              <!--          剩余学时：-->
              剩余鼎英语学时：
              <span>{{ studyList1.leaveCourseHours }}小时</span>
              <!--          <span>{{ studyList1.haveCourseHours }}小时</span>-->
            </el-col>
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              所学内容：
              <span>{{ studyList1.studyContent }}</span>
            </el-col>
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              <el-col :span="4">所学词库：</el-col>
              <el-col :span="20" v-if="studyList1.studyBooks" style="white-space: pre-line">
                <div>{{ studyList1.studyBooks.replaceAll('、', '\n') }}</div>
              </el-col>
            </el-col>
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              <el-col :span="4">学习进度：</el-col>
              <el-col :span="20" v-if="studyList1.learnSchedule" style="white-space: pre-line">
                <div>{{ studyList1.learnSchedule.replaceAll(',', '\n') }}</div>
              </el-col>
            </el-col>
            <el-col class="paike" v-if="studyList1.curriculumName == '鼎英语'">
              学习效率：
              <span>{{ studyList1.studyRate }}</span>
            </el-col>
          </template>
          <template v-if="studyList1.moduleType == 3">
            <el-col class="paike">
              所学课程类型：
              <span>{{ studyList1.superReadCourseStatistics.courseName }}</span>
            </el-col>
            <el-col class="paike">
              <el-col :span="4">学习进度：</el-col>
              <el-col :span="20" style="white-space: pre-line">
                <div>{{ studyList1.superReadCourseStatistics.learningProgress + '%' }}</div>
              </el-col>
            </el-col>
            <el-col class="paike" style="padding-right: 50px">
              <div>学习关卡(正确率)：{{ rateMapArray(studyList1.superReadCourseStatistics.checkpointList) }}</div>
            </el-col>
            <el-col class="paike" style="padding-right: 50px">
              <div>所学课程名称进度：{{ mapArray(studyList1.superReadCourseStatistics.courseList) }}</div>
            </el-col>
          </template>

          <el-col class="paike" v-if="studyList1.isWord">
            复习词汇：
            <span>{{ studyList1.reviewWords }}个</span>
          </el-col>
          <el-col class="paike" v-if="studyList1.isWord">
            复习遗忘词汇：
            <span>{{ studyList1.forgetWords }}个</span>
          </el-col>
          <el-col class="paike" v-if="studyList1.isWord">
            复习遗忘率：
            <span>{{ studyList1.forgetRate }}%</span>
          </el-col>
          <el-col class="paike" v-if="studyList1.isWord">
            学新词汇：
            <span>{{ studyList1.newWords }}个</span>
          </el-col>
          <el-col class="paike" v-if="studyList1.isWord">
            学新遗忘词汇：
            <span>{{ studyList1.newForget }}个</span>
          </el-col>
          <el-col class="paike" v-if="studyList1.isWord">
            学新遗忘词汇率：
            <span>{{ studyList1.newForgetRate }}%</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus && studyList1.isWord">
            今日共识记词汇（复习遗忘词汇+学新词汇）：
            <span>{{ studyList1.todayWords }}个</span>
          </el-col>
          <el-col class="paike" v-if="CurriculumCodeArr.includes(studyList1.curriculumCode)">已购{{ studyList1.curriculumName }}学时:{{ studyList1.totalCourseHours }}</el-col>
          <el-col class="paike" v-if="CurriculumCodeArr.includes(studyList1.curriculumCode)">剩余{{ studyList1.curriculumName }}学时:{{ studyList1.leaveCourseHours }}</el-col>
          <!-- <el-col class="paike" v-if="studyList1.curriculumCode == 'XKT'">
            授课视频：
            <span>{{ studyList1.xktStatisticsDto.xktGradeName }}/{{ studyList1.xktStatisticsDto.xktCourseName }}/{{ studyList1.xktStatisticsDto.xktVideoName }}</span>
          </el-col> -->
          <el-col class="paike" v-if="XKTCurriculumCodeArr.includes(studyList1.curriculumCode)">
            授课视频：
            <div style="padding-right: 50px; word-break: break-all; overflow-wrap: break-word" v-for="item in studyList1.xktStatisticsDto.courseGradeVideos" :key="item.id">
              {{ item.gradeName }}/{{ item.courseName }}/{{ item.videoName }}
            </div>
          </el-col>
          <el-col class="paike" style="padding-right: 50px; word-break: break-all; overflow-wrap: break-word" v-if="studyStatus">
            教练评语：
            <span>{{ studyList1.feedback }}</span>
            <div style="height: 20vh"></div>
          </el-col>
        </el-row>
      </el-drawer>
    </div>
  </div>
</template>

<script>
  import { getFeedbackInfo, getTotalStatistics } from '@/api/paikeManage/LearnManager';
  import { getStudyDateList } from '@/api/paikeManage/classCard';
  import { CurriculumCodeArr, XKTCurriculumCodeArr } from '@/utils/constants.js';

  export default {
    //传值
    props: {
      //父组件向子组件传 drawer；这里默认会关闭状态
      lookstyle: {
        type: Boolean,
        default: false
      },
      //Drawer 打开的方向
      direction: {
        type: String,
        default: 'rtl'
      }
    },
    name: 'lookDialog',
    data() {
      return {
        screenWidth: window.screen.width,
        // loading: true,加载框用的
        drawer: false,
        activeName: 'second',
        studyStatus: true,
        day: true,
        studyList: {
          type: 2, //复习学习的反馈
          date: '',
          studentCode: ''
        },
        studyList1: '',
        fuxiList: '',
        teacher: '',
        reviewTotal: {
          id: '',
          planId: '',
          type: 1
        },
        XKTCurriculumCodeArr: XKTCurriculumCodeArr, //学考通相关课程大类
        CurriculumCodeArr: CurriculumCodeArr
      };
    },
    //计算属性
    computed: {
      lookstyle_: {
        get() {
          return this.lookstyle;
        },
        //值一改变就会调用set【可以用set方法去改变父组件的值】
        set(v) {
          //   console.log(v, 'v')
          this.$emit('lookDrawer', v);
        }
      }
    },
    methods: {
      rateMapArray(arr) {
        if (arr && arr.length > 0) {
          let newArr = [];
          newArr = arr.map((i) => {
            let str = '';
            let accuracyRate = i.accuracyRate ? i.accuracyRate + '%' : '-';
            let checkpointName = i.checkpointName;
            str = `${checkpointName}(${accuracyRate})`;
            //arr.push(str)
            return str;
          });
          return newArr.join(',');
          //return arr.map((i) => i.checkpointName + '（' + i.accuracyRate + '%' + '）').join(',');
        } else {
          return '-';
        }
      },
      mapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.courseName + '（' + i.checkpointNum + '）').join(' , ');
        } else {
          return '-';
        }
      },
      //听力数据处理
      listenProgress(progressList) {
        return progressList.map((item) => `${parseFloat(item.learningProgress)}%`).join(' , ');
      },
      listenName(progressList) {
        return progressList.map((item) => item.courseName).join(' ');
      },
      listenArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.listeningName + '（' + i.accuracyRate + '%）').join(' , ');
        } else {
          return '-';
        }
      },
      async studyFn() {
        this.studyStatus = false;
        this.reviewTotal.type = 1;
        let res = await getTotalStatistics(this.reviewTotal);
        this.studyList1 = res.data;
        console.log(this.studyList1, 'this.studyList1');
      },
      async studyBtn() {
        this.studyStatus = true;
        let res = await getFeedbackInfo(this.reviewTotal);
        this.studyList1 = res.data;
      },
      handleClose() {
        this.$emit('lookDrawer', false);
        this.studyStatus = true;
      },
      async numberLookFn() {
        let res = await getStudyDateList(this.studyList);
        this.fuxiList = res.data;
      },
      //分钟=>小时
      getDataHour(min) {
        if (min % 100 === 0) {
          return min / 100;
        } else {
          return (min / 100).toFixed(1);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .borders {
    margin: 1vw 1vw;
    // width: 28vw;
    height: 20vw;
    border: 1px solid #cac8c8;
    border-radius: 20px;
  }

  .paike {
    margin-bottom: 20px;
    margin-left: 2vw;

    &:first-child {
      margin-top: 0.5vw;
    }
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    // font-size: 22px;
    font-size: 30px; // 拼音法
    letter-spacing: 5px;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
    /* overflow-x: auto; */
  }
</style>
