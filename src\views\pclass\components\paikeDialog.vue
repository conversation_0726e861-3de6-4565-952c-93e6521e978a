<template>
  <el-form class="dialog" ref="ruleForm">
    <el-drawer title="排课" :show-close="false" :visible="childVisible_" :direction="direction" :size="screenWidth > 1300 ? '530px' : '85vw'">
      <el-row class="paikeTwo">
        <div style="display: flex">
          <div>
            <el-col class="paike" style="margin-bottom: 10px">
              学员姓名：
              <span>{{ rowlist.name }}</span>
            </el-col>
            <el-col class="paike" style="margin-bottom: 10px">
              学员编号：
              <span>{{ rowlist.studentCode }}</span>
            </el-col>
          </div>
          <div>
            <el-col class="paike">期数：{{ coniditon.periods }}</el-col>
          </div>
        </div>
        <el-col class="cc"></el-col>
      </el-row>
      <!-- 日期 -->
      <el-row class="paikeTwo">
        <el-col class="paike">
          <el-form-item label="日期:">
            <el-date-picker
              type="dates"
              value-format="yyyy-MM-dd"
              v-model="coniditon.dateList"
              placeholder="请选择"
              size="small"
              style="width: 185px"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 学习时间 -->
        <el-col class="paike" v-for="(item, index) in coniditon.timeList" :key="index">
          <el-form-item label="时间:">
            <el-time-picker
              v-model="coniditon.timeList[index].startTime"
              format="HH:mm"
              @change="updateEndTime(index)"
              value-format="HH:mm"
              style="width: 118px; margin-right: 10px"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00'
              }"
              placeholder="开始时间"
            ></el-time-picker>
            <el-input-number
              v-model="coniditon.timeList[index].hourNum"
              @change="updateEndTime"
              :min="1"
              :max="24"
              label="小时"
              size="mini"
              style="margin-right: 10px; width: 90px"
            ></el-input-number>
            <el-time-picker
              format="HH:mm"
              style="width: 118px"
              value-format="HH:mm"
              v-model="coniditon.timeList[index].endTime"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00'
              }"
              placeholder="结束时间"
            ></el-time-picker>
            <el-button type="primary" round size="mini" style="margin-left: 10px" @click="addItem()">增添</el-button>
            <el-button v-if="index !== 0" type="danger" size="mini" @click="deleteItem(item, index)">-</el-button>
          </el-form-item>
        </el-col>
        <!-- 复习时间 -->
        <!-- <el-col class="paike">
          <el-form-item label="21天抗遗忘复习时间:">
            <el-time-picker v-model="coniditon.reviewTime[0]" @change="updateReviewTime" :clearable="false" format="HH:mm"
              value-format="HH:mm" style="width: 108px; margin-right: 15px" :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="开始时间">
            </el-time-picker>
            <el-input-number v-model="hourValue" :min="1" :max="24" label="小时" size="mini"
              style="margin-right: 10px;width: 90px;" @change="updateReviewTime"></el-input-number>

            <el-time-picker format="HH:mm" style="width: 108px" value-format="HH:mm" v-model="coniditon.reviewTime[1]"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="结束时间">
            </el-time-picker>
          </el-form-item>
        </el-col> -->
        <el-col class="cc"></el-col>
      </el-row>

      <el-row>
        <el-col class="paike">
          <el-form-item label="授课方式:">
            <el-select v-model="coniditon.teachingType" placeholder="请选择" style="width: 180px">
              <el-option label="远程" :value="1"></el-option>
              <el-option label="线下" :value="2"></el-option>
              <el-option label="远程和线下" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col class="paike">
          <el-form-item label="选择老师:">
            <el-select :loading="teacherLoading" @visible-change="teacherChange" v-model="details" placeholder="请选择" filterable style="width: 180px">
              <el-option v-for="item in TeacherList" :key="item.teacherId" :label="item.teachName" :value="item.teacherId"></el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="选择课程学段:">
            <el-select v-model="coniditon.courseStage" placeholder="请选择" @change="changeCourseStage">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="选择课程模块:">
            <el-select v-model="coniditon.courseModule" @change="changeCourse" placeholder="请选择" filterable
              style="width: 180px">
              <el-option v-for="item in courseList" :key="item.name" :label="item.label" :value="item.name">
              </el-option>
            </el-select>
          </el-form-item> -->

          <!-- <el-form-item label="选择课程模块:" v-if="roles[0].val == 'learnManager'">
            <el-select
              v-model="coniditon.courseModule"
              @change="changeCourse"
              placeholder="请选择"
              filterable
              style="width: 180px"
            >
              <el-option
                v-for="item in courseList"
                :key="item.name"
                :label="item.label"
                :value="item.name"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- {{ condition.hasDeliverClass }} -->
          <el-row v-if="coniditon.hasDeliverClass">
            <el-col>
              <el-form-item label="班级:">
                <el-input-number v-model="deliverClass" :min="1" :max="999" label="班"></el-input-number>
                <!-- <el-input
                  v-model="deliverClass"
                  :min="1"
                  :max="999"
                  size="small"
                  maxlength="3"
                  @input="handleInput"
                  style="width: 180px"
                  class="input"
                  type="number"
                  placeholder=""
                >
                  <template slot="suffix">班</template>
                </el-input> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="已排交付学时:">{{ coniditon.planCourseHours }}时</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="剩余交付学时:">
                <span>{{ coniditon.haveCourseHours }}时</span>
                <!-- <br /> -->
                <!-- <span style="font-size: 14px; color: red">剩余学时不影响您的排课计划哦</span> -->
              </el-form-item>
            </el-col>
          </el-row>

          <div class="xubtn" type="flex" justify="center">
            <el-button class="del" @click="handleClose" style="margin-right: 1vw">取消</el-button>
            <span style="width: 50px"></span>
            <el-button type="primary" v-if="!sendBtn" @click="quedingBtn">确定</el-button>
            <!-- <el-button type="success" v-if="sendBtn && coniditon.studentSource === 1" @click="quedingBtn">
              确定并发送给门店
            </el-button>
            <el-button type="success" v-if="sendBtn && coniditon.studentSource === 2" @click="quedingBtn">
              确定并发送给门店和家长
            </el-button> -->
          </div>
        </el-col>
      </el-row>
    </el-drawer>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="27%">
      <el-collapse v-model="timeListVisible">
        <el-collapse-item name="1">
          <template slot="title">
            <el-tooltip class="item" effect="dark" :content="timeListVisible.length > 0 ? '点击收起停服时间' : '点击查看停服时间'" placement="top-end" style="width: 100%">
              <div style="font-size: 16px">该排课时间存在停服计划，请确认是否排课？</div>
            </el-tooltip>
          </template>
          <div style="height: auto; overflow: auto; white-space: nowrap; line-height: 30px; max-height: 130px">
            <div style="font-size: 16px">停服时间：</div>
            <div style="font-size: 16px; text-align: center" v-for="(item, index) in offServiceTimeList" :key="index">{{ item.split('/')[0] }} -- {{ item.split('/')[1] }}</div>
          </div>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="planCourse">确 定</el-button>
      </span>
    </el-dialog>
  </el-form>
</template>

<script>
  import { addPlanCourse, screenTeacherListAndHours, isTimeConflicts } from '@/api/paikeManage/LearnManager';
  import { getCourseclassList } from '@/api/FinanceApi/assistantWages';
  import enTypes from '@/api/student/bstatus';

  import dayjs from 'dayjs';
  import { mapGetters } from 'vuex';

  export default {
    name: 'paikeButton',
    props: {
      // 控制弹窗显示
      childVisible: {
        type: Boolean,
        default: false //这里默认为false
      },
      // 学员列表的信息viewTime
      rowlist: {
        default: false //这里默认为false
      }
    },
    data() {
      return {
        timeListVisible: ['1'],
        offServiceTimeList: [],
        dialogVisible: false,
        screenWidth: window.screen.width,
        value: '',
        tableData: '',
        itemcount: 1,
        drawer: false,
        direction: 'rtl',
        teacherNum: {
          id: '',
          teacherId: '',
          studentCode: ''
        },
        coniditon: {
          dateList: [],
          timeList: [
            {
              startTime: '',
              endTime: '',
              hourNum: 1
            }
          ],
          teachingType: '',
          reviewTime: ['', ''],
          planCourseHours: '',
          haveCourseHours: '',
          courseModule: '',
          courseStage: ''
        }, //列表老师用的
        TeacherList: [],
        timer: '',
        val: '',
        pickerOptions: {
          disabledDate: this.disabledDate
        },
        teacherLoading: false,
        details: '',
        beforeHours: 0,
        sendBtn: false,
        hourValue: 1,
        hourNum: 1,

        courseList: [], // 课程列表
        courseModuleList: [],
        courseModule: '', // 课程模块

        courseStageType: [], // 课程学段
        deliverClass: ''
      };
    },
    created() {
      let isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';
      if (!isTeamLeader) {
        this.getCourseList();
      }
      this.getStady();
      // this.coniditon.reviewTime[1] ='18:29'
    },
    mounted() {
      if (this.role == 3) {
        this.chartY = '9.5%';
      } else {
        this.chartY = '18%';
      }
      // console.log(this.coniditon.timeList)
    },
    computed: {
      childVisible_: {
        get() {
          return this.childVisible;
        },
        //值变化的时候会被调用
        set(v) {
          this.$emit('changeDrawer', false);
        }
      },

      ...mapGetters(['roles'])
    },
    /*  watch: {
      coniditon: {
        handler() {
          if (this.coniditon.dateList.length === 0) return;
          let hashEmpty = this.coniditon.timeList.some((item) => {
            return !item.startTime || !item.endTime;
          });
          if (hashEmpty) return;
          if (!this.coniditon.teachingType) return;
          // 获取老师列表
          if (this.timer != null) clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            this.getTeacherList();
          }, 500);
        },
        deep: true,
      },
    },
    rowlist(list) {
      console.log(list)
      this.coniditon.dateList=[list.firstTime.split(' ')[0]]
      this.coniditon.timeList[0].startTime=list.firstTime.split(' ')[1]
      this.updateEndTime(1)
    }*/
    methods: {
      handleInput(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.deliverClass = '';
        }
      },
      // 日期选择
      updateEndTime(index) {
        // console.log(this.coniditon.timeList)
        if (this.coniditon.dateList.length == 0) {
          this.$message.error('请先选择日期');
          return;
        }

        for (let index = 0; index < this.coniditon.timeList.length; index++) {
          let startTime = this.coniditon.dateList[0] + ' ' + this.coniditon.timeList[index].startTime;
          let time = dayjs(startTime).add(this.coniditon.timeList[index].hourNum, 'hour');
          //获取当前时间格式化
          let endTime = dayjs(time).format('HH:mm'); //年月日
          this.coniditon.timeList[index].endTime = endTime;
        }
      },
      // 21天抗遗忘
      updateReviewTime() {
        if (this.coniditon.dateList.length == 0) {
          this.$message.error('请先选择日期');
          return;
        }

        let startTime = this.coniditon.dateList[0] + ' ' + this.coniditon.reviewTime[0];
        let time = dayjs(startTime).add(this.hourValue, 'hour');
        //获取当前时间格式化
        let endTime = dayjs(time).format('HH:mm'); //年月日
        this.coniditon.reviewTime[1] = endTime;
        console.log(endTime);
        this.$forceUpdate();
      },
      disabledDate(time) {
        // time 是new Date
        let flag = false;
        if (this.coniditon.dateList) {
          for (let i = 0; i < this.coniditon.dateList.length; i++) {
            if (this.formatDate(time) === this.coniditon.dateList[i]) {
              flag = true;
            }
          }
        }
        return flag ? false : time.getTime() < Date.now() - 3600 * 1000 * 24;
      },
      formatDate(date) {
        let y = date.getFullYear();
        let m = date.getMonth() + 1;
        m = m < 10 ? '0' + m : m;
        let d = date.getDate();
        d = d < 10 ? '0' + d : d;
        return y + '-' + m + '-' + d;
      },
      teacherType() {
        if (this.coniditon.teachingType === '1') {
          return '男';
        } else if (this.coniditon.teachingType === '2') {
          return '女';
        }
      },
      // 关闭弹窗
      handleClose() {
        console.log('关闭弹窗');
        this.childVisible_ = false;
        this.coniditon.dateList = [];
        this.coniditon.timeList[0].endTime = '';
        this.coniditon.timeList[0].startTime = '';
        this.coniditon.reviewTime = ['', ''];
        this.coniditon.haveCourseHours = '';
        this.coniditon.planCourseHours = '';
        this.coniditon.teacherId = '';
        this.coniditon.teachingType = '';
        this.details = '';
        this.beforeHours = 0;
        this.hourValue = 1;
        this.sendBtn = false;
      },
      async quedingBtn() {
        let btn = this.sendBtn;
        let isSuccess = await this.getTeacherList();
        if (!isSuccess) {
          return;
        }

        if (btn !== this.sendBtn) {
          this.$message.warning('已刷新请再次确认');
          return;
        }
        if (this.coniditon.planCourseHours - 0 > this.coniditon.haveCourseHours - 0) {
          return this.$message.warning('剩余可排交付课时不足，请充值');
        }
        this.teacherNum.dateList = this.coniditon.dateList;
        this.teacherNum.timeList = this.coniditon.timeList;
        this.teacherNum.teachingType = this.coniditon.teachingType;
        this.teacherNum.id = this.rowlist.id;
        this.teacherNum.teacherId = this.details;
        this.teacherNum.courseModule = this.coniditon.courseModule;
        this.teacherNum.courseStage = this.coniditon.courseStage;
        this.teacherNum.curriculumId = this.rowlist.curriculumId;
        this.teacherNum.deliverClass = this.deliverClass;
        if (this.coniditon.reviewTime[0] == '' || this.coniditon.reviewTime[1] == '') {
          // this.$message.error("21天抗遗忘复习时间不能为空");
          // return false;
        }
        this.teacherNum.reviewTime = this.coniditon.reviewTime;
        //查看排课时间和停服时间是否有冲突
        console.log(this.teacherNum);
        let timeList = [];
        for (let i = 0; i < this.teacherNum.dateList.length; i++) {
          for (let j = 0; j < this.teacherNum.timeList.length; j++) {
            timeList.push(
              this.teacherNum.dateList[i] +
                ' ' +
                this.teacherNum.timeList[j].startTime +
                ':00' +
                '/' +
                this.teacherNum.dateList[i] +
                ' ' +
                this.teacherNum.timeList[j].endTime +
                ':00'
            );
          }
        }
        // console.log(timeList);
        let res = await isTimeConflicts(timeList);
        if (res.data.length == 0) {
          this.planCourse();
        } else {
          this.offServiceTimeList = res.data;
          this.dialogVisible = true;
        }
      },
      //关闭弹窗
      closeDialog() {
        this.dialogVisible = false;
        this.$message({
          type: 'info',
          message: '已取消排课'
        });
      },
      //排课
      async planCourse() {
        this.dialogVisible = false;
        let res = await addPlanCourse(this.teacherNum);
        this.$message.success('排课成功');
        this.childVisible_ = false;
        //重新获取列表
        this.$emit('updateList');
      },
      //新增方法
      addItem(length) {
        if (length >= 10) {
          this.$message({
            type: 'warning',
            message: '最多可新增10条!'
          });
        } else {
          this.coniditon.timeList.push({
            endTime: '',
            startTime: '',
            hourNum: 1
          });
        }
      },
      //删除方法
      deleteItem(item, index) {
        this.coniditon.timeList.splice(index, 1);
      },
      teacherChange(b) {
        if (b) {
          this.getTeacherList();
        }
      },

      // 获取老师列表
      async getTeacherList() {
        if (!this.coniditon.dateList) {
          this.$message.error('请选择日期！');
          return;
        }
        let hashEmpty = this.coniditon.timeList.some((item) => {
          return !item.startTime || !item.endTime;
        });
        if (hashEmpty) {
          this.$message.error('请填写完整学习时间!');
          return;
        }
        if (!this.coniditon.teachingType) {
          this.$message.error('请选择授课方式！');
          return;
        }
        this.teacherLoading = true;
        this.coniditon.studentCode = this.teacherNum.studentCode;
        this.coniditon.isExp = false;
        let { data } = await screenTeacherListAndHours(this.coniditon);
        this.teacherLoading = false;
        this.TeacherList = data.teacherList;
        this.coniditon.planCourseHours = data.beforeHours;
        // this.coniditon.planCourseHours = data.planCourseHours
        this.beforeHours = data.beforeHours;
        if (!this.TeacherList || this.TeacherList.length === 0) {
          this.details = '';
          this.$message.error('学员的课程时间内没有可用教练！');
        }
        // if (data.beforeHours > this.coniditon.haveCourseHours) {
        //   //学时不足则展示发送订单按钮
        //   // this.sendBtn = true;
        // }
        return true;
      },

      // 课程模块
      getCourseList() {
        let that = this;
        getCourseclassList().then((res) => {
          that.courseModuleList = res.data || [];
          for (let i = 0; i < that.courseModuleList.length; i++) {
            if (that.courseModuleList[i].name == 'WORD') {
              that.courseModuleList[i]['label'] = '单词';
            } else if (that.courseModuleList[i].name == 'NOVICE_GRAMMAR') {
              that.courseModuleList[i]['label'] = '小学语法';
            } else if (that.courseModuleList[i].name == 'INTERMEDIATE_GRAMMAR') {
              that.courseModuleList[i]['label'] = '初中语法';
            } else if (that.courseModuleList[i].name == 'ADVANCED_GRAMMAR') {
              that.courseModuleList[i]['label'] = '高中语法';
            } else {
              that.courseModuleList[i]['label'] = '阅读';
            }
          }
          that.courseList = that.courseModuleList;
          // console.log(that.courseList)
        });
      },

      // 选择课程模块
      changeCourse(e) {
        this.coniditon.courseModule = e;
        this.$forceUpdate();
      },

      //获取学段下拉框
      getStady() {
        var enType = 'CourseStage';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseStageType = res.data;
        });
      },

      changeCourseStage(e) {
        this.courseList = this.courseModuleList;
        if (this.coniditon.courseModule) {
          this.coniditon.courseModule = '';
        }
        if (e == 'XiaoXue') {
          this.courseList = this.courseList.filter((item) => {
            return item.name != 'INTERMEDIATE_GRAMMAR' && item.name != 'ADVANCED_GRAMMAR';
          });
        } else if (e == 'ChuZhong') {
          this.courseList = this.courseList.filter((item) => {
            return item.name != 'NOVICE_GRAMMAR' && item.name != 'ADVANCED_GRAMMAR';
          });
        } else if (e == 'GaoZhong') {
          this.courseList = this.courseList.filter((item) => {
            return item.name != 'NOVICE_GRAMMAR' && item.name != 'INTERMEDIATE_GRAMMAR';
          });
        } else {
          this.courseList = this.courseList.filter((item) => {
            return item.name == 'WORD';
          });
        }
        console.log(this.courseList);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .xubtn {
    margin-left: 7vw;
  }

  .paike {
    margin-left: 1vw;
    margin-right: 1vw;

    &:last-child {
      margin-bottom: 30px;
    }

    &:first-child {
      margin-top: 30px;
    }
  }

  .paikeTwo {
    width: 97%;
  }

  .cc {
    height: 0;
    margin: 0 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    font-size: 22px;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
