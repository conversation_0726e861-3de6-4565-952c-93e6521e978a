/**
 * 登录相关接口
 */
import request from "@/utils/request";

export default {
  verificationCode(gloadId) {
    return request({
      url: "/new/security/login/verification/code/" + gloadId,
      method: "get",
    });
  },
  getLogo(id) {
    return request({
      url: "/znyy/operations/center/getAvatar?secondaryDomainName=" + id,
      method: "get",
    });
  },
  getJlbInfo() {
    return request({
      url: "/znyy/school/currentAdmin",
      method: "GET",
    });
  },
  loginByUsername(username, password, userType, code, appId, role) {
    return request({
      url: "/new/security/deliver/login",
      method: "get",
      params: { password, role, username, userType, code, appId },
    });
  },
  loginByNoCode(username, password, userType, code, appId, role) {
    return request({
      url: "/new/security/deliver/loginNoCode",
      method: "get",
      params: { password, role, username, userType, code, appId },
    });
  },
  logout() {
    return request({
      url: "/deliver/web/common/logout",
      method: "PUT",
    });
  },
  byLoginNameQueryRoleTag(loginName) {
    return request({
      url: "/deliver/web/common/getDeliverRole/" + loginName,
      method: "GET",
    });
  },
  getUserInfo(token) {
    return request({
      url: "/deliver/web/common/getUserInfo",
      method: "GET",
    });
  },
};
