import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const downloadListRouter = {
  path: '/downloadList/index',
  component: Layout,
  name: 'downloadList',
  meta: {
    perm: 'm:downloadList:index',
    title: '下载列表',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: 'downloadList_index',
      component: _import('downloadList/index'),
      name: 'downloadListIndex',
      meta: {
        perm: 'm:downloadList:index',
        title: '下载列表',
        icon: 'divisionList',
        noCache: false
      }
    }
  ]
};

export default downloadListRouter;
