import request from '@/utils/request'

// 新增学管师
export const addxueguanListApi = (data) => {
  return request({
    url: '/deliver/web/sysUser/addLearnManager',
    method: 'post',
    params: data
  })
}
// 学管师列表
export const xueguanListApi = (data) => {
  return request({
    url: '/deliver/web/sysUser/getLearnManagerList',
    method: 'get',
    params: data
  })
}
// 删除学管师
export const deleteLearnManager = (id,role) => {
  return request({
    url: '/deliver/web/sysUser/deleteLearnManager?role='+role,
    method: 'DELETE',
    data: id
  })
}
// 删除教练
export const deleteTeacher = (id) => {
  return request({
    url: '/deliver/web/teacher/deleteTeacher',
    method: 'DELETE',
    data: id
  })
}

// 获取学管师列表
export const getLearnTubeList = (data) => {
  return request({
    url: '/deliver/web/sysUser/getLearnTubeList',
    method: 'get',
    params: data
  })
}
