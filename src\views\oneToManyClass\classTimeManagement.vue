<!-- 班级上课时间管理 -->
<template>
  <div>
    <!-- 课程类型 -->
    <el-row style="margin: 20px 0 20px 20px">
      <el-radio-group v-model="courseType" size="medium" @change="handleTabsClick">
        <el-radio-button label="1">试课</el-radio-button>
        <el-radio-button label="2">正式课</el-radio-button>
      </el-radio-group>
    </el-row>
    <!-- 课程大类 -->
    <el-row justify="space-between" type="flex" style="margin: 20px 0 20px 20px">
      <el-col :span="10">
        <el-tabs v-model="curriculumId" @tab-click="handleTabsClick">
          <el-tab-pane :label="item.enName" :name="item.id" v-for="item in courseList" :key="item.id"></el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="addTime()" icon="el-icon-plus" style="margin: 15px 0">新增上课时间</el-button>
      </el-col>
    </el-row>
    <!-- 空数据 -->
    <el-row v-loading="tableLoading" type="flex" justify="center" align="middle" v-if="tableList.length <= 0">
      <div style="text-align: center; margin-top: 100px; height: 30vh">
        <div class="no_data" style="height: 100%">
          <el-image style="width: 100px; height: 100px" :src="url"></el-image>
          <div style="color: #999; margin: 20px 0 20px">暂无数据</div>
          <el-button type="primary" @click="initData01" icon="el-icon-refresh">刷新</el-button>
          <el-button type="primary" size="small" style="margin-top: 25px" @click="addTime()" icon="el-icon-plus">新增上课时间</el-button>
        </div>
      </div>
    </el-row>
    <div v-else>
      <el-table
        v-loading="tableLoading"
        :data="tableList"
        style="width: 100%"
        id="out-table"
        :header-cell-style="{ background: '#f5f7fa' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="item.value == 'operate' ? 200 : ''"
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'operate'">
              <el-button type="primary" size="mini" @click="editBtn(row)">编辑</el-button>
              <el-button type="danger" v-if="row.isEnable == 1" size="mini" @click="changeStatusBtn(row, 0)">停用</el-button>
              <el-button type="success" v-else size="mini" @click="changeStatusBtn(row, 1)">启用</el-button>
            </div>
            <!-- 周 -->
            <div v-else-if="item.value == 'dayOfWeek'">
              <!-- <span>{{ getWeek(row.dayOfWeek) }}</span> -->
              <span>{{ row.dayOfWeekName }}</span>
            </div>
            <!-- 年级 -->
            <div v-else-if="item.value == 'grade'">
              <span>
                <!-- {{ getGrade(row.grade) || '-' }} -->
                {{ row.gradeName || '-' }}
              </span>
            </div>
            <!-- 开始时间 -->
            <div v-else-if="item.value == 'startTime'">
              <span>{{ row.startTime + ' ~ ' + row.endTime }}</span>
            </div>
            <!-- 人数限制 -->
            <div v-else-if="item.value == 'studentCountMin'">
              <span style="padding-right: 10px">{{ row.studentCountMin + ' ~ ' + row.studentCountMax }}</span>
              <el-tag v-if="row.studentCountType == 1" type="" effect="dark" size="mini">单</el-tag>
              <el-tag v-else-if="row.studentCountType == 2" type="success" effect="dark" size="mini">双</el-tag>
              <el-tag v-else type="warning" effect="dark" size="mini">全</el-tag>
            </div>
            <!-- 状态 -->
            <div v-else-if="item.value == 'isEnable'">
              <el-tag v-if="row.isEnable == 1" type="success" effect="dark" size="mini" style="width: 55px">正常</el-tag>
              <el-tag v-else type="danger" effect="dark" size="mini" style="width: 55px">已停用</el-tag>
            </div>
            <!-- 其他 -->
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-row type="flex" justify="center" align="middle" style="height: 60px">
        <!-- 3个变量：每页数量、页码数、总数  -->
        <!-- 2个事件：页码切换事件、每页数量切换事件-->
        <el-pagination
          v-if="tableIshow"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchNum.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="searchNum.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </el-row>
    </div>
    <!-- 新增班级上课时间 -->
    <el-dialog :title="`${isAdd ? '新增' : '编辑'}班级上课时间`" :visible.sync="dialogVisibleAdd" width="39%" :before-close="handleCloseAdd">
      <el-row>
        <el-form label-width="100px" label-position="left" style="text-align: left" ref="addForm" :model="addForm" :rules="addFormRules">
          <el-form-item label="类型:" prop="type">
            <el-select v-model="addForm.type" placeholder="请选择" :disabled="!isAdd">
              <el-option label="试课" :value="1"></el-option>
              <el-option label="正式课" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程大类:" prop="curriculumId">
            <el-select v-model="addForm.curriculumId" placeholder="请选择" :disabled="!isAdd">
              <el-option v-for="item in courseList" :key="item.enCode" :label="item.enName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="addForm.gradeList" placeholder="请选择" multiple @change="handleGradeChange">
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上课时间:" prop="week">
            <el-checkbox-group v-model="addForm.week" size="medium" @change="reviewWeekChange" v-if="weeklist.length > 0">
              <el-checkbox-button v-for="item in weeklist" :label="item.value" :key="item.value">
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="" prop="classTime">
            <el-row>
              <el-col :span="10">
                <el-time-select
                  placeholder="起始时间"
                  @change="changeTime"
                  v-model="addForm.startTime"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '22:30'
                  }"
                ></el-time-select>
              </el-col>
              <el-col :span="2" style="text-align: center">至</el-col>
              <el-col :span="10">
                <el-time-select
                  disabled
                  placeholder="结束时间"
                  v-model="addForm.endTime"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '23:30',
                    minTime: addForm.startTime
                  }"
                ></el-time-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" prop="studentCountType">
            <el-radio-group v-model="addForm.studentCountType" @change="handleAddFormStudentCountTypeChange">
              <el-radio :label="0">全部</el-radio>
              <el-radio :label="1">单数</el-radio>
              <el-radio :label="2">双数</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="人数限制:" prop="limitNum">
            <el-row>
              <el-col :span="10">
                <ElInputTextNumber
                  v-model="addForm.studentCountMin"
                  placeholder="开始值"
                  styleData="width: 100%; text-align: left"
                  :min="1"
                  :step="addForm.studentCountType ? 2 : 1"
                  @input="formatDecimal(addForm.studentCountMin, 'addForm.studentCountMin')"
                  @change="changelimitNum($event, 'studentCountMin')"
                ></ElInputTextNumber>
              </el-col>
              <el-col :span="2" style="text-align: center">一</el-col>
              <el-col :span="10">
                <ElInputTextNumber
                  v-model="addForm.studentCountMax"
                  placeholder="结束值"
                  styleData="width: 100%; text-align: left"
                  :min="addForm.studentCountMin || 1"
                  :step="addForm.studentCountType ? 2 : 1"
                  @input="formatDecimal(addForm.studentCountMax, 'addForm.studentCountMax')"
                  @change="changelimitNum($event, 'studentCountMax')"
                ></ElInputTextNumber>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="周成班上限:" prop="weekClassMax">
            <el-input-number v-model="addForm.weekClassMax" :min="1" step-strictly style="width: 100%; text-align: left"></el-input-number>
          </el-form-item>
          <!-- 提示 -->
          <el-form-item label=" ">
            <el-row>
              <el-col :span="1">
                <i class="el-icon-warning" style="color: #fbab16"></i>
              </el-col>
              <el-col :span="23">
                <b>
                  周成班上限例：周一、周四18：00-19：00周成班上限为5个，则系统自动成班上限最多为5个。超过的需要在下周一早上9点进行成班（达到上线管理员仍可在新班级列表-等待成班中进行手动成班）
                </b>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAdd">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import ElInputTextNumber from './components/ElInputTextNumber.vue';
  import { GradeType } from '@/api/studentClass/changeList';
  import { getClassTimeList, addClassTime, updateClassTimeStatus, updateClassTime, getOneToMoreClassList } from '@/api/oneToManyClass/classTimeManagement.js';
  import ls from '@/api/sessionStorage';

  export default {
    name: 'classTimeManagement',
    components: {
      HeaderSettingsDialog,
      ElInputTextNumber
    },
    data() {
      let classTimeValidate = (rule, value, callback) => {
        if (this.addForm.startTime && this.addForm.endTime) {
          callback();
        } else {
          callback(new Error('请完整填写上课时间'));
        }
      };
      let limitNumValidate = (rule, value, callback) => {
        if (this.addForm.studentCountMin && this.addForm.studentCountMax) {
          callback();
        } else {
          callback(new Error('请完整填写人数限制'));
        }
      };

      return {
        url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
        timestamp: null, // 防抖时间戳
        timeClash: false, // 时间冲突
        dialogVisibleAdd: false, // 新增班级上课时间弹窗
        isAdd: true, // 是否新增
        addFormRules: {
          type: { required: true, message: '请选择课程类型', trigger: 'change' },
          curriculumId: { required: true, message: '请选择课程大类', trigger: 'change' },
          grade: { required: true, message: '请选择年级', trigger: 'change' },
          weekClassMax: { required: true, message: '请输入周成班上限', trigger: 'change' },
          week: { required: true, message: '请选择周几', trigger: 'change' },
          classTime: { validator: classTimeValidate, trigger: 'blur' },
          limitNum: { required: true, validator: limitNumValidate, trigger: 'blur' },
          studentCountType: { required: true, message: '请选择单/双/全', trigger: 'change' }
        }, // 新增form验证规则

        addForm: {
          type: '',
          curriculumId: '',
          studentCountType: '',
          week: [],
          dayOfWeek: '',
          grade: '',
          startTime: '',
          endTime: '',
          studentCountMin: '',
          studentCountMax: '',
          weekClassMax: ''
        }, // 新增form
        // 年级列表
        gradeList: [],
        weeklist: [
          { value: 1, label: '周一' },
          { value: 2, label: '周二' },
          { value: 3, label: '周三' },
          { value: 4, label: '周四' },
          { value: 5, label: '周五' },
          { value: 6, label: '周六' },
          { value: 7, label: '周日' }
        ],
        searchNum: {
          pageNum: 1,
          pageSize: 10
        },
        total: 0, // 总数
        tableLoading: false, // 表格loading
        tableList: [],
        tableHeaderList: [
          { name: '周', value: 'dayOfWeek' },
          { name: '年级', value: 'grade' },
          { name: '开始时间-结束时间', value: 'startTime' },
          { name: '人数限制', value: 'studentCountMin' },
          { name: '周成班上限', value: 'weekClassMax' },
          { name: '状态', value: 'isEnable' },
          { name: '操作', value: 'operate' }
        ],
        courseType: '1',
        tableIshow: true, //分页是否显示
        curriculumId: '1223288510123233280', // 当前选中课程大类
        courseList: [] // 课程类型
      };
    },
    // },
    created() {
      this.getbvstatusList(); // 获取课程类型并初始化列表
      this.getGradeList(); // 获取年级列表
    },
    mounted() {},
    methods: {
      // 修改年级
      handleGradeChange(val) {
        this.addForm.grade = this.gradeList
          .filter((i) => val.includes(i.value))
          .map((i) => i.value)
          .join(','); // 将选中的值用逗号拼接成一个字符串

        console.log('🚀 ~ handleGradeChange ~ this.addForm.grade:', this.addForm.grade);
        return this.addForm.grade;
      },
      // 新增班级上课时间弹窗/选择周几格式化
      reviewWeekChange(val) {
        console.log(val);
        this.addForm.dayOfWeek = this.weeklist
          .filter((i) => val.includes(i.value))
          .map((i) => i.value)
          .join(',');
        console.log('🚀 ~ reviewWeekChange ~ this.addForm.dayOfWeek:', this.addForm.dayOfWeek);
        return this.addForm.dayOfWeek;
      },
      // 编辑班级上课时间弹窗/拿到周几值数组
      getWeek(val) {
        // let week = this.weeklist.filter((item) => {
        //   return val.includes(item.value);
        // });
        // week = week.map((item) => item.label).join(',');
        // return week;
        return this.weeklist.filter((i) => val.includes(i.value)).map((i) => i.value);
      },
      // 编辑班级上课时间弹窗/拿到年级值数组
      getGrade(val) {
        // let grade = this.gradeList.find((item) => {
        //   return item.value == val;
        // });
        let grade = this.gradeList.filter((i) => val.some((ii) => ii == i.value)).map((i) => i.value);
        // this.gradeList.find((item) => item.label == val);
        return grade;
      },
      // 切换状态
      /**
       *
       * @param row
       * @param status
       */
      changeStatusBtn(row, status) {
        // 状态修改
        if (status == 1) {
          this.updateStatus(row, status);
        } else if (status == 0) {
          this.$confirm('您确定要停用吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.updateStatus(row, status);
          });
        }
      },
      // 修改
      updateStatus(row, status) {
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        updateClassTimeStatus({ id: row.id, isEnable: status })
          .then((res) => {
            loading.close();
            this.$message({
              type: 'success',
              message: status ? '启用成功!' : '停用成功!'
            });
            this.initData(); // 刷新列表
          })
          .catch((e) => {
            loading.close();
          });
      },
      // 编辑按钮
      editBtn(row) {
        console.log('🚀 ~ editBtn ~ row:', row);
        // 编辑弹窗
        this.isAdd = false;
        // 回显
        let val = JSON.parse(JSON.stringify(row));
        // 格式化
        val.week = this.getWeek(val.dayOfWeek);
        val.dayOfWeek = this.reviewWeekChange(val.week);
        let grade = val.grade.split(',');
        val.gradeList = this.getGrade(grade);
        val.grade = this.handleGradeChange(val.gradeList);
        this.addForm = { ...this.addForm, ...val };
        this.$nextTick(() => {
          this.addForm.startTime = val.startTime;
          this.addForm.endTime = val.endTime;
        });
        // 打开弹窗
        console.log('🚀 ~ editBtn ~ this.addForm:', this.addForm);
        this.dialogVisibleAdd = true;
      },
      // 新增班级上课时间弹窗/选择开始时间
      changeTime(val) {
        console.log('🚀 ~ changeTime ~ val:', val);
        if (!val) return;
        let arr = val.split(':');
        this.addForm.endTime = (arr[0] - -1 + '').padStart(2, '0') + ':' + arr[1];
      },
      // 处理新增班级单/双数改变事件
      handleAddFormStudentCountTypeChange() {
        this.addForm.studentCountMin = '';
        this.addForm.studentCountMax = '';
      },
      changelimitNum(val, prop) {
        this.addForm[prop] = val = this.handleStudentCount(val);
        if (prop == 'studentCountMin') {
          if (Number(val) > Number(this.addForm.studentCountMax)) {
            this.addForm.studentCountMax = val;
          }
        } else if (prop == 'studentCountMax') {
          if (Number(val) < Number(this.addForm.studentCountMin)) {
            this.addForm.studentCountMax = this.addForm.studentCountMin;
          }
        }
      },
      // 切换课程大类tabs
      handleTabsClick() {
        console.log('🚀 ~ handleTabsClick ~ handleTabsClick:', this.curriculumId);
        // 调用接口获取课程列表
        this.initData();
      },
      // 关闭新增上课时间
      handleCloseAdd() {
        console.log('🚀 ~ handleCloseAdd ~ handleCloseAdd:');
        this.dialogVisibleAdd = false;
        this.addForm = {
          type: '',
          curriculumId: '',
          studentCountType: '',
          week: [],
          dayOfWeek: '',
          grade: '',
          startTime: '',
          endTime: '',
          studentCountMin: '',
          studentCountMax: '',
          weekClassMax: ''
        };
        // 清除校验
        this.$nextTick(() => {
          this.$refs.addForm.clearValidate();
        });
      },
      // 新增/修改上课时间
      handleAdd() {
        console.log('🚀 ~ handleAdd ~ this.addForm:', this.addForm);
        this.$refs.addForm.validate((valid) => {
          if (!valid) return;

          // console.log('🚀 ~ handleAdd ~ this.addForm:', this.addForm);
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          let ryEndTime;
          if (this.addForm.endTime == '24:00') {
            ryEndTime = '1970-01-01 23:59:59';
          } else {
            ryEndTime = '1970-01-01 ' + this.addForm.endTime + ':00';
          }
          let data = {
            id: this.addForm.id,
            type: this.addForm.type,
            curriculumId: this.addForm.curriculumId,
            ryStartTime: '1970-01-01 ' + this.addForm.startTime + ':00',
            ryEndTime: ryEndTime,
            studentCountMin: this.addForm.studentCountMin,
            studentCountMax: this.addForm.studentCountMax,
            studentCountType: this.addForm.studentCountType,
            weekClassMax: this.addForm.weekClassMax,
            dayOfWeek: this.addForm.dayOfWeek,
            grade: this.addForm.grade
          };
          console.log('🚀 ~ this.$refs.addForm.validate ~ data:', data);
          if (this.isAdd) {
            console.log('🚀 ~ this.$refs.addForm.validate ~ this.addForm:', this.addForm);
            // 新增
            addClassTime(data)
              .then((res) => {
                loading.close();

                if (res.data.success == false) {
                  this.$alert('该时间段已有配置，请重新选择时间', '提示', {
                    confirmButtonText: '确定',
                    type: 'warning',
                    callback: (action) => {}
                  });
                  return;
                }

                this.$message({
                  type: 'success',
                  message: '新增成功!'
                });
                this.handleCloseAdd(); // 关闭弹窗
                this.initData(); // 刷新列表
              })
              .catch((e) => {
                console.log('🚀 ~ this.$refs.addForm.validate ~ e:', e);
                loading.close();
              });
          } else {
            // 修改
            updateClassTime(data)
              .then((res) => {
                loading.close();

                if (res.data.success == false) {
                  this.$alert('该时间段已有配置，请重新选择时间', '提示', {
                    confirmButtonText: '确定',
                    type: 'warning',
                    callback: (action) => {}
                  });
                  return;
                }
                this.$message({
                  type: 'success',
                  message: '修改成功!'
                });
                this.handleCloseAdd(); // 关闭弹窗
                this.initData(); // 刷新列表
              })
              .catch((e) => {
                loading.close();
                this.$alert('该时间段已有配置，请重新选择时间', '提示', {
                  confirmButtonText: '确定',
                  type: 'warning',
                  callback: (action) => {}
                });
              });
          }
        });
      },
      // 打开新增上课时间
      addTime() {
        this.isAdd = true;
        this.dialogVisibleAdd = true;
        // 清除校验
        this.$nextTick(() => {
          this.$refs.addForm.clearValidate();
        });
      },
      // 重置搜索框
      rest() {
        this.$refs.query.resetFields();
        this.initData01();
      },
      // 搜索
      initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      async initData() {
        // 判断为null的时候赋空
        if (!this.timeAll) {
          this.timeAll = [];
        }
        // 时间戳防抖
        let timestamp = new Date().getTime();
        this.timestamp = timestamp;

        this.tableLoading = true;
        let { data } = await getClassTimeList({ ...this.searchNum, type: this.courseType, curriculumId: this.curriculumId });
        if (this.timestamp != timestamp) {
          console.log('🚀 ~ initData ~ 时间戳不一致，不请求数据');
          this.tableLoading = false;
          return;
        }
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.tableList = data.data;
        //
        this.tableList.forEach((i) => {
          // i.dayOfWeek.split(',').map((week) => {

          // })
          let week = i.dayOfWeek.split(',');
          let grade = i.grade.split(',');
          i.dayOfWeekName = this.weeklist
            .filter((item) => week.some((ii) => ii == item.value))
            .map((item) => item.label)
            .join(',');
          i.gradeName = this.gradeList
            .filter((item) => grade.some((ii) => ii == item.value))
            .map((item) => item.label)
            .join(',');
        });
        // console.log('🚀 ~ initData ~ this.tableList:', this.tableList, this.tableList.length > 0);
      },
      status(val) {
        switch (val.status) {
          case 8:
            return '已取消';
          case 9:
            return '已拒绝';
          case 11:
            return '待审核';
          default:
            return '已审核或未该审核';
        }
      },
      getbvstatusList() {
        getOneToMoreClassList().then((res) => {
          console.log('🚀 ~ getOneToMoreClassList ~ res:', res);
          this.courseList = res.data;
          this.curriculumId = this.courseList[0].id;
          this.initData();
        });
      },
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      // headerList() {
      //   if (this.tableHeaderList.length > 0) {
      //     this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
      //   }
      //   this.HeaderSettingsStyle = true;
      // },
      reset() {
        this.checkInfo = {
          auditReason: '',
          id: '',
          studentCode: '',
          merchantCode: ''
        };
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      /**
       *
       * @param val
       * @param path 路径 如：'addForm.value'
       */
      formatDecimal(val, path) {
        let value = JSON.parse(JSON.stringify(val));

        // 1. 移除非数字和非法字符
        let cleaned = value.replace(/[^\d.]/g, '');

        // 2. 分割整数和小数部分
        let parts = cleaned.split('.');
        if (parts.length > 1) {
          // 取整
          cleaned = parts[0];
        }
        // 根据 digit 值创建路径数组
        let that = path.split('.');
        // 递归函数来访问嵌套属性
        const setNestedValue = (obj, path, value) => {
          let i;
          for (i = 0; i < path.length - 1; i++) {
            obj = obj[path[i]];
          }
          obj[path[i]] = value;
        };
        // 更新输入框的值
        setNestedValue(this, that, cleaned);
      },
      handleStudentCount(value) {
        if (!value) return value;
        if (value.length > 5) {
          value = value.slice(0, 5);
        }
        if (this.addForm.studentCountType && this.addForm.studentCountType != 0) {
          if (value % 2 == 0) {
            // 偶数
            if (this.addForm.studentCountType == 1) {
              value = Number(value) + 1;
            }
          } else {
            // 奇数
            if (this.addForm.studentCountType == 2) {
              value = Number(value) + 1;
            }
          }
        }
        return value;
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  .my-input-number-suffix {
    height: 30px;
    position: relative;
    top: 1px;
    left: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .btn {
      width: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgb(245, 247, 250);
      &-up {
        height: 50%;
        border-radius: 0px 4px 0px 0px;
        border-left: 1px solid rgb(220, 223, 230);
        border-bottom: 1px solid rgb(220, 223, 230);
        &:hover {
          cursor: pointer;
        }
        &-icon {
          color: rgb(96, 98, 102);
        }
      }
      &-up-disabled {
        height: 50%;
        border-radius: 0px 4px 0px 0px;
        border-left: 1px solid rgb(220, 223, 230);
        border-bottom: 1px solid rgb(220, 223, 230);
        background: rgb(245, 247, 250);
        &:hover {
          cursor: not-allowed;
        }
      }
      &-down {
        height: 50%;
        border-radius: 0px 0px 4px 0px;
        border-left: 1px solid rgb(220, 223, 230);
      }
    }
  }

  ::v-deep.el-date-editor.el-input {
    width: 100%;
  }
  ::v-deep.el-input-number.is-controls-right .el-input__inner {
    text-align: left;
  }
  ::v-deep.el-select {
    width: 100%;
  }
  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
</style>
