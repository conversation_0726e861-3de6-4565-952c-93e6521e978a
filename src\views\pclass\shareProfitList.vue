<!--交付中心-试课学员管理-试课列表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="105px" ref="searchNum" :model="searchNum" :inline="true">
        <el-row style="margin-top: 1.3vw">
          <el-col :span="8" :xs="24">
            <el-form-item label="学员姓名:" prop="studentName">
              <el-input v-model="searchNum.studentName" clearable placeholder="请输入" @input="changeMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" @input="changeMessage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="到账时间:" prop="timeAll">
              <el-date-picker
                v-model="timeAll"
                format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24" v-if="isAdmin || show">
            <el-form-item label="教练老师：" prop="teacherId">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                clearable
                :filter-method="filterValue"
                v-model="searchNum.teacherId"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-button size="small" type="primary" icon="el-icon-search" @click="initData01" style="margin-left: 1vw">查询</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="restForm()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="warning" icon="el-icon-document-copy" @click="handleExportData" v-loading="exportLoading" style="margin: 10px 0 20px 20px">异步导出</el-button>
    <el-button type="primary" @click="headerList()" style="margin: 10px 0 20px 20px">列表显示属性</el-button>

    <el-card class="table_frame" shadow="never">
      <el-table :data="luyouclassCard" ref="grpNickNameTable" v-loading="tableLoading" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :width="item.value == 'lastStudyTime' || item.value == 'createTime' ? 200 : ''"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'teacherName'">
              <span>{{ row.teacherName ? row.teacherName : '-' }}</span>
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- <el-table :data="luyouclassCard" ref="grpNickNameTable" v-loading="tableLoading" id="out-table"
                :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
                <el-table-column prop="name" label="学员姓名" header-align="center" :key='1'></el-table-column>
                <el-table-column prop="studentCode" label="学员编号" header-align="center" :key='2'></el-table-column>
                <el-table-column prop="phone" label="手机号" header-align="center" :key='3' />
                <el-table-column prop="deliverMerchantName" label="交付中心" v-if="isAdmin" header-align="center"
                    :key='4'></el-table-column>
                <el-table-column prop="referrerName" label="推荐人" header-align="center" :key='5'></el-table-column>
                <el-table-column prop="referrerPhone" label="推荐人手机号" header-align="center" :key='6'></el-table-column>
                <el-table-column prop="teacherName" label="教练" header-align="center" :key='7' v-if="isAdmin || show">
                    <template slot-scope="scope">
                        <span>{{ scope.row.teacherName ? scope.row.teacherName : '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="到账时间" header-align="center" :key='8'></el-table-column>
                <el-table-column prop="lastStudyTime" label="试课时间" header-align="center" min-width="180" :key='9'>
                    <template slot-scope="scope">
                        <span>{{ scope.row.lastStudyTime }}</span>
                    </template>
                </el-table-column>
            </el-table> -->
    </el-card>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { selAllTeacher, getshareProfit, addExportData } from '@/api/studentClass/changeList';
  import { getDeliverCodes } from '@/api/FinanceApi/assistantWages';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';

  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';

  import { mapGetters } from 'vuex';
  import dayjs from 'dayjs';
  import ls from '@/api/sessionStorage';
  import moment from 'moment'; //导入文件
  moment.locale('zh-cn');

  export default {
    name: 'shareProfitList',
    components: {
      HeaderSettingsDialog
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        exportDisable: -1, //-1：未选择月数，0：超过一个月，1：可以导出
        exportLoading: false,
        ischeck: false,
        timeAll: [],

        tableLoading: false, //列表转圈是否开启
        searchNum: {
          searchName: '',
          studentCode: '',
          teacherId: '',
          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        isAdmin: false,
        total: null,
        luyouclassCard: [],
        value: '',
        option: [],
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          name: ''
        },
        show: false,

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '学员姓名',
            value: 'name'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '手机号',
            value: 'phone'
          },
          {
            name: '教练',
            value: 'teacherName'
          },

          {
            name: '推荐人',
            value: 'referrerName'
          },
          {
            name: '推荐人手机号',
            value: 'referrerPhone'
          },
          {
            name: '到账时间',
            value: 'createTime'
          },
          {
            name: '试课时间',
            value: 'lastStudyTime'
          }
        ],

        tableHeaderList: [] // 获取表头数据
      };
    },
    computed: {
      ...mapGetters(['roles'])
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      if (this.isAdmin) {
        this.headerSettings.splice(3, 0, {
          name: '交付中心',
          value: 'deliverMerchantName'
        });

        // this.headerSettings.splice(6, 0, {
        //   name: '教练',
        //   value: 'teacherName'
        // })
      }
      this.getTeacherList();
      this.initData();
      // this.getListShow();
      this.getHeaderlist();
    },
    methods: {
      //判断异步导出按钮的状态
      handleIsExportDisable() {
        /**
         * 判断异步导出按钮的状态
         * 1.判断是否选择了时间（不选 -1）
         * 2.判断是否大于一个月（大于 0）
         * 3.选择了时间，并且小于一个月（1）
         */

        const that = this;
        if (!this.timeAll || this.timeAll.length == 0) {
          that.exportDisable = -1;
          return;
        } else {
          that.searchNum.startTime = that.timeAll[0];
          that.searchNum.endTime = that.timeAll[1];
          // console.log(dayjs(that.searchNum.endTime).diff(dayjs(that.searchNum.startTime), 'month'));
        }
        //判断是否大于一个月

        let days = dayjs(that.searchNum.startTime).daysInMonth() - 1; // 获取开始月的天数

        if (dayjs(that.searchNum.endTime).diff(dayjs(that.searchNum.startTime), 'day') > days) {
          that.exportDisable = 0;
        } else {
          that.exportDisable = 1;
        }
      },
      // 导出
      handleExportData() {
        const that = this;
        this.handleIsExportDisable(); // 判断异步导出按钮的状态 //-1：未选择月数，0：超过一个月，1：可以导出
        if (that.exportDisable == -1) {
          that.$message({
            message: '请先选择时间，再进行数据导出，最多可选一个月',
            type: 'warning'
          });
          return;
        }
        if (that.exportDisable == 0) {
          that.$message({
            message: '最多可导出一个月数据，请重新选择时间',
            type: 'warning'
          });
          return;
        }

        this.exportLoading = true;
        addExportData(this.searchNum)
          .then((res) => {
            if (res.success) {
              that.exportLoading = false;
              that.$message({
                message: '该导出为异步导出，请前往下载中心即可下载',
                type: 'success'
              });
            }
          })
          .catch((err) => {
            this.exportLoading = false;
          });
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },

      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },

      // 获取教练
      async getTeacherList() {
        let allData = await selAllTeacher(this.selectObj);
        this.option = this.option.concat(allData.data.data);
      },

      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getTeacherList();
      },
      clearSearchRecord() {
        setTimeout(() => {
          if (this.searchNum.teacherId == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.name = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      },

      async initData01() {
        this.searchNum.pageNum = 1;
        this.searchNum.pageSize = 10;
        this.initData();
      },

      async initData() {
        this.tableLoading = true;
        if (!this.timeAll || this.timeAll.length == 0) {
          this.searchNum.startTime = '';
          this.searchNum.endTime = '';
        } else {
          this.searchNum.startTime = this.timeAll[0];
          this.searchNum.endTime = this.timeAll[1];
        }
        await getshareProfit(this.searchNum).then((res) => {
          this.luyouclassCard = res.data.data;
          this.tableLoading = false;
          this.total = Number(res.data.totalItems);
          this.$forceUpdate();
        });
      },

      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },

      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },

      //重置
      restForm() {
        console.log(this.searchNum);
        this.$refs.searchNum.resetFields();
        this.timeAll = [];
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.searchNum.pageNum = 1;
        this.getTeacherList();
        this.initData();
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },

      // getListShow() {
      //   getDeliverCodes().then(res => {
      //     this.show = res.data;
      //     if (!this.isAdmin && this.show) {
      //       this.headerSettings.splice(6, 0, {
      //         name: '教练',
      //         value: 'teacherName'
      //       })
      //     }
      //   })
      // },

      changeMessage() {
        this.$forceUpdate();
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'shareProfitList',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'shareProfitList'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            // this.tableHeaderList = JSON.parse(res.data.value);
            let Json = this.removeNullValues(res.data.value);
            this.tableHeaderList = JSON.parse(Json);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    margin-bottom: 2vh;
    background-color: rgba(255, 255, 255);
  }

  .table_frame {
    margin-top: 0.5vh;
    margin-bottom: 2vh;
    background-color: rgba(255, 255, 255);
  }

  ::v-deep .table_frame .el-card__body {
    padding: 20px 0 0 !important;
  }
</style>
