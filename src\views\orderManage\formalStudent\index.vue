<!--交付中心-接单管理-正式课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame" v-if="isAdmin">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="姓名：" label-width="120px" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="门店:" label-width="70px" prop="merchantCodeOrMerchantPhone">
              <el-input v-model.trim="querydata.merchantCodeOrMerchantPhone" size="small" placeholder="请输入门店账号或门店手机号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交付中心编号:" label-width="200px" prop="deliverMerchant">
              <el-input v-model.trim="querydata.deliverMerchant" @change="changeInput" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付中心名称：" label-width="120px" prop="deliverName">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                remote
                clearable
                v-model="querydata.deliverName"
                filterable
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="(item, index) in option" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="派单状态：" prop="dispatchOrderStatus">
              <el-select v-model="querydata.dispatchOrderStatus" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="进行中" value="1"></el-option>
                <el-option label="无人接单" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5" :xs="24">
            <el-form-item label="派单来源:" prop="deliverSource">
              <el-select v-model="querydata.deliverSource" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item,index) in deliverSources" :key="index" :label="item.name"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="4">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="querydata.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 交付小组组长 -->
    <div class="frame" v-else-if="isTeamLeader">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生来源:" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="系统派单" value="1"></el-option>
                <el-option label="门店派单" value="2"></el-option>
                <el-option label="总部派单" value="3"></el-option>
                <el-option label="试课派单" value="4"></el-option>
                <el-option label="变更交付小组" value="5"></el-option>
                <el-option label="学员转移" value="6"></el-option>
                <el-option label="主交付中心派单" value="7"></el-option>
                <el-option label="次交付中心派单" value="8"></el-option>
                <el-option label="轮排派单" value="9"></el-option>
                <el-option label="管理员处理" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付小组:" prop="team">
              <el-select v-model="querydata.teamId" clearable size="small" placeholder="请选择" style="width: 10vw" @change="handlechangeDownCompany">
                <el-option v-for="(item, index) in leaderTeamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 交付中心头部 -->
    <div class="frame" v-else>
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生来源:" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value="0"></el-option>
                <el-option label="系统派单" value="1"></el-option>
                <el-option label="门店派单" value="2"></el-option>
                <el-option label="总部派单" value="3"></el-option>
                <el-option label="试课派单" value="4"></el-option>
                <el-option label="变更交付小组" value="5"></el-option>
                <el-option label="学员转移" value="6"></el-option>
                <el-option label="主交付中心派单" value="7"></el-option>
                <el-option label="次交付中心派单" value="8"></el-option>
                <el-option label="轮排派单" value="9"></el-option>
                <el-option label="管理员处理" value="10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="5" :xs="24">
            <el-form-item label="派单来源:" prop="deliverSource">
              <el-select v-model="querydata.deliverSource" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item,index) in deliverSources" :key="index" :label="item.name"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="4">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="querydata.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 列表显示属性 -->

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <!-- 表格 -->

    <el-table
      :data="tableData"
      style="width: 100%"
      id="out-table"
      v-loading="tableLoading"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
      size="mini"
      fit
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        min-width="150"
        :width="item.value == 'operate' ? '300' : item.value == 'createTime' ? '250' : ''"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="openEdit(row)">上课信息对接表</el-button>
            <el-button type="warning" size="mini" v-if="isAdmin" @click="onAssign(row.id, 0)">指派</el-button>
            <el-button type="danger" size="mini" v-if="isAdmin" @click="onForceAssign(row.id, 1)">强制指派</el-button>

            <el-button type="warning" size="mini" v-if="!isAdmin && !isTeamLeader" @click="addGroup(row.id)">指派</el-button>
            <!-- <el-button type="danger" size="mini" v-if="!isAdmin&&row.studentSource=='系统派单'"
              @click="onReject(row)">拒绝</el-button> -->
          </div>
          <el-row type="flex" justify="center" align="middle" v-else-if="item.value == 'createTime'">
            <el-col :span="12" :offset="0">
              <div>{{ row[item.value] }}</div>
            </el-col>
            <el-col :span="6" :offset="0" v-if="isAdmin">
              <el-button type="text" @click="getDetail(row.id)">详情</el-button>
            </el-col>
          </el-row>
          <span v-else-if="item.value == 'residueTime'">
            <statistic
              v-if="row.timeOut && getTrueTime(row.timeOut)"
              ref="statistic"
              format="HH:mm:ss"
              :valueClass="statusClass(row.timeOut)"
              :value="getResidueTime(row.timeOut)"
              :key="'statistic' + row.timeOut"
              time-indices
            ></statistic>
            <!-- <span v-if="row.timeOut&&getTrueTime(row.timeOut)">{{getResidueTime(row['timeOut'])}}</span> -->
            <span v-else>{{ '-' }}</span>
          </span>
          <span v-else-if="item.value == 'deliverSource'">
            <span v-if="row.deliverSource != 0">
              {{ getSourse(row.deliverSource) }}
            </span>
            <span v-else>{{ '-' }}</span>
          </span>
          <span v-else>{{ row[item.value] ? row[item.value] : '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
      ></el-pagination>
    </el-row>
    <!-- 查看试课单 -->
    <el-dialog
      v-if="mathShow == 1"
      title="上课信息对接表"
      top="2vh"
      :visible.sync="dialogAbutment"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      :close-on-click-modal="false"
      center
      @close="closeEidt"
    >
      <div style="margin-bottom: 40px; margin-top: -30px; font-size: 15px">
        <span>创建时间：{{ abutmentList.submitTime ? abutmentList.submitTime : abutmentList.updateTime }}</span>
      </div>
      <el-form ref="abutmentList" :model="abutmentList" :label-width="screenWidth > 1300 ? '150px' : '70px'">
        <el-form-item label="学员姓名" prop="studentName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentCode }}</span>
          </div>
        </el-form-item>
        <el-form-item label="课程类型" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.curriculumName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="联系方式" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.mobile }}</span>
          </div>
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="充值课时" prop="rechargeHour" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.rechargeHour }}</span>
          </div>
        </el-form-item>
        <el-form-item v-if="!CurriculumCodeArr.includes(abutmentList.curriculumCode)" label="课程规划" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass thesaurus" style="margin: 10px 0 0" v-for="(item, index) in coursrList" :key="index">
            <span style="margin: 0 15px">{{ item.courseName }}</span>
            <div class="vocabulary" v-if="item.isFirstWordBase">首节课词库</div>
          </div>
        </el-form-item>
        <el-form-item label="上课时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-form-item label="" v-for="(val, index) in abutmentList.studyTimeList" :key="index" style="margin-bottom: 0 !important">
            <el-row style="margin-bottom: 10px; margin-left: 0">
              <el-col :span="8" :xs="24">
                <div class="timeClass" style="margin: 0 0">
                  <span style="margin: 0 15px">{{ getWeekName(val.usableWeek) }}</span>
                </div>
              </el-col>
              <el-col :span="16" :xs="24">
                <div class="timeClass">
                  <span style="margin: 0 15px">{{ val.startTime }}</span>
                  <span>至</span>
                  <span style="margin: 0 15px">{{ val.endTime }}</span>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form-item>
        <el-form-item
          label="复习时间"
          v-if="abutmentList.courseType == '鼎英语' || (abutmentList.curriculumName == '拼音法' && reviewWeekList.length > 0)"
          prop
          :style="{ width: screenWidth > 1300 ? '80%' : '100%' }"
          style="margin-top: 10px"
        >
          <el-row>
            <el-col :span="3" :xs="12" v-for="(item, index) in reviewWeekList" :key="index">
              <span class="week">{{ getWeekName(item) }}</span>
              <!-- <span class="week">{{ item }}</span> -->
            </el-col>
          </el-row>
          <div class="timeClass" style="margin: 10px 0 0" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <span style="margin: 0 15px">开始时间:</span>
            <span style="margin: 0 15px">{{ abutmentList.reviewTime }}</span>
          </div>
        </el-form-item>

        <div style="position: relative; display: flex; margin-bottom: 22px; align-items: center" v-if="abutmentList.firstTime">
          <div :style="{ width: screenWidth > 1300 ? '150px' : '85px' }" style="text-align: end; padding-right: 12px">
            首次上课时间
            <div style="color: #999999; font-size: 11px">限制24小时之后</div>
          </div>
          <div class="timeClass" style="margin-left: 0; line-height: 36px" :style="{ width: screenWidth > 1300 ? '36%' : '100%' }">
            <span style="margin: 0 15px">{{ getFormatToService(abutmentList.firstTime, abutmentList.firstWeek) }}</span>
          </div>
        </div>
        <el-form-item label="是否试课" prop="isExp" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isExp == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item
          label="词汇量检测"
          v-if="abutmentList.isExp === 1 && abutmentList.courseType == '鼎英语'"
          prop="wordBase"
          :style="{ width: screenWidth > 1300 ? '50%' : '100%' }"
        >
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.wordBase }}</span>
          </div>
        </el-form-item>
        <el-form-item label="是否新生" prop="isNewStudent" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isNewStudent == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 23px">{{ abutmentList.remark }}</span>
            <!-- <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" disabled /> -->
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeEidt">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 数学 -->
    <el-dialog
      v-if="mathShow == 2"
      title="数学上课信息对接表"
      top="2vh"
      :visible.sync="dialogAbutment"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      :close-on-click-modal="false"
      center
      @close="closeEidt"
    >
      <div style="margin-bottom: 40px; margin-top: -30px; font-size: 15px">
        <span>创建时间：{{ abutmentList.submitTime ? abutmentList.submitTime : abutmentList.updateTime }}</span>
      </div>
      <el-form ref="abutmentList" :model="abutmentList" :label-width="screenWidth > 1300 ? '150px' : '70px'">
        <el-form-item label="学员姓名" prop="studentName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学员编号" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.studentCode }}</span>
          </div>
        </el-form-item>
        <el-form-item label="课程类型" prop="studentCode" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.curriculumName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="联系方式" prop="mobile" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.mobile }}</span>
          </div>
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学科" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="版本" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ getGrade(abutmentList.grade) }}</span>
          </div>
        </el-form-item>
        <el-form-item label="充值课时" prop="rechargeHour" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.rechargeHour }}</span>
          </div>
        </el-form-item>
        <el-form-item v-if="!CurriculumCodeArr.includes(abutmentList.curriculumCode)" label="课程规划" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass thesaurus" style="margin: 10px 0 0" v-for="(item, index) in coursrList" :key="index">
            <span style="margin: 0 15px">{{ item.courseName }}</span>
            <div class="vocabulary" v-if="item.isFirstWordBase">首节课词库</div>
          </div>
        </el-form-item>
        <el-form-item label="上课时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-form-item label="" v-for="(val, index) in abutmentList.studyTimeList" :key="index" style="margin-bottom: 0 !important">
            <el-row style="margin-bottom: 10px; margin-left: 0">
              <el-col :span="8" :xs="24">
                <div class="timeClass" style="margin: 0 0">
                  <span style="margin: 0 15px">{{ getWeekName(val.usableWeek) }}</span>
                </div>
              </el-col>
              <el-col :span="16" :xs="24">
                <div class="timeClass">
                  <span style="margin: 0 15px">{{ val.startTime }}</span>
                  <span>至</span>
                  <span style="margin: 0 15px">{{ val.endTime }}</span>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form-item>
        <el-form-item
          label="复习时间"
          v-if="abutmentList.courseType == '鼎英语' || (abutmentList.curriculumName == '拼音法' && reviewWeekList.length > 0)"
          prop
          :style="{ width: screenWidth > 1300 ? '80%' : '100%' }"
          style="margin-top: 10px"
        >
          <el-row>
            <el-col :span="3" :xs="12" v-for="(item, index) in reviewWeekList" :key="index">
              <span class="week">{{ getWeekName(item) }}</span>
              <!-- <span class="week">{{ item }}</span> -->
            </el-col>
          </el-row>
          <div class="timeClass" style="margin: 10px 0 0" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <span style="margin: 0 15px">开始时间:</span>
            <span style="margin: 0 15px">{{ abutmentList.reviewTime }}</span>
          </div>
        </el-form-item>

        <div style="position: relative; display: flex; margin-bottom: 22px; align-items: center" v-if="abutmentList.firstTime">
          <div :style="{ width: screenWidth > 1300 ? '150px' : '85px' }" style="text-align: end; padding-right: 12px">
            首次上课时间
            <div style="color: #999999; font-size: 11px">限制24小时之后</div>
          </div>
          <div class="timeClass" style="margin-left: 0; line-height: 36px" :style="{ width: screenWidth > 1300 ? '36%' : '100%' }">
            <span style="margin: 0 15px">{{ getFormatToService(abutmentList.firstTime, abutmentList.firstWeek) }}</span>
          </div>
        </div>
        <el-form-item label="是否试课" prop="isExp" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isExp == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item
          label="词汇量检测"
          v-if="abutmentList.isExp === 1 && abutmentList.courseType == '鼎英语'"
          prop="wordBase"
          :style="{ width: screenWidth > 1300 ? '50%' : '100%' }"
        >
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.wordBase }}</span>
          </div>
        </el-form-item>
        <el-form-item label="是否新生" prop="isNewStudent" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ abutmentList.isNewStudent == 1 ? '是' : '否' }}</span>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark" :style="{ width: screenWidth > 1300 ? '70%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 23px">{{ abutmentList.remark }}</span>
            <!-- <el-input v-model="abutmentList.remark" type="textarea" placeholder="请输入" :rows="5" disabled /> -->
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeEidt">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 指派弹框 -->
    <!-- :width="screenWidth > 1300 ? '60%' : '90%'" -->
    <el-dialog :visible.sync="assignDialog" :width="screenWidth > 1300 ? '60%' : '90%'" :title="assignForm.isForced == 1 ? '强制指派交付中心' : '指派交付中心'">
      <el-form ref="form" label-width="150px">
        <el-form-item label="选择交付中心">
          <el-cascader
            v-model="assignForm.deliverMerchantCode"
            :options="deliverOptions"
            filterable
            :props="{
              label: 'deliverMerchantName',
              value: 'deliverMerchantCode'
            }"
            clearable
          ></el-cascader>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="end" style="margin-top: 2.5vh">
        <el-button type="primary" style="width: 100px" plain size="small" @click="closeAssgin">取消</el-button>
        <el-button type="primary" style="width: 100px" size="small" :loading="isSubmit" @click="submitAssign">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 流转指派历史弹框 -->
    <el-dialog title="历史派单记录" :visible.sync="dialogHistory" :close-on-click-modal="false" width="30%" center>
      <div style="overflow: auto; margin: 30px 0; height: 400px" v-loading="dialogLoading">
        <!-- <ul class="infinite-list" v-infinite-scroll="load" style="overflow:auto;margin: 30px 0;height:400px">
        </ul> -->
        <div v-if="historys.length >= 1">
          <el-steps direction="vertical" :active="0" :space="200">
            <el-step v-for="(item, index) in historys" :title="item.time" :key="index" icon="iconfont icon-luyin">
              <template slot="description">
                <div style="white-space: pre-wrap">{{ item.history }}</div>
              </template>
              <template slot="icon">
                <i class="el-icon-info" v-if="index == 0" style="font-size: 24px"></i>
                <i class="el-icon-success" v-else style="font-size: 24px"></i>
              </template>
            </el-step>
          </el-steps>
        </div>
        <div class="nomore" v-if="historys.length < 1">
          <el-image style="width: 6.25rem; height: 6.25rem" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
          <div style="color: #999; margin-top: 1.25rem">无数据</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" style="width: 100px" @click="closeDialog">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="指派交付小组" :visible.sync="dialogAddgroup" width="30%" @close="AddgroupClose">
      <span>
        选择交付小组
        <el-select v-model="groupForm.teamId" style="margin-left: 20px" placeholder="请选择" clearable filterable>
          <el-option v-for="item in groupList" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
        </el-select>
      </span>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button size="small" @click="dialogAddgroup = false">取消</el-button>
        <el-button size="small" type="primary" @click="AddgroupClick">确定</el-button>
      </span>
    </el-dialog>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import ls from '@/api/sessionStorage';
  import { pageParamNames } from '@/utils/constants';
  import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue';
  import { getTableTitleSet, setTableList, belongDeliverAndAllDeliver, bvstatusListOne } from '@/api/paikeManage/classCard';
  import { getFormalStudentList } from '@/api/orderManage';
  import { getStudentContactInfoDetail, queryStudentContactInfoDetail } from '@/api/paikeManage/LearnManager';

  import {
    rejectFormalStudent,
    getDurationConfig,
    getFormalHistory,
    assignFormalStudent,
    selectTeam,
    changeTeam2,
    getDurationConfigTimes,
    getLeaderTeamList
  } from '@/api/orderManage';
  import statistic from '../components/statistic.vue';
  import trialDate from '@/views/pclass/components/trialDate';
  import dayjs from 'dayjs';
  import { deliverlist } from '@/api/peizhi/peizhi';
  import { CurriculumCodeArr } from '@/utils/constants.js';
  export default {
    name: 'formalStudent',
    components: {
      HeaderSettingsDialog,
      trialDate,
      statistic
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        screenWidth: window.screen.width, //屏幕宽度
        mathShow: 0,
        // 搜索表单
        querydata: {
          studentName: '',
          studentCode: '',
          merchantCodeOrMerchantPhone: '',
          deliverMerchant: '',
          deliverName: '',
          dispatchStatus: '',
          // deliverSource: '',
          curriculumId: '',
          pageNum: 1,
          pageSize: 10 //页容量
        },
        total: null,
        tableData: [],
        tableLoading: false,
        isAdmin: false,
        isTeamLeader: false,
        abutmentList: {},
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        dialogAbutment: false, //上课信息对接表弹窗
        coursrList: [],
        reviewWeekList: [],
        gradeNameArr: [
          '一年级',
          '二年级',
          '三年级',
          '四年级',
          '五年级',
          '六年级',
          '初一',
          '初二',
          '初三',
          '高一',
          '高二',
          '高三',
          '大一',
          '大二',
          '大三',
          '大四',
          '幼儿园',
          '其他'
        ],
        // gradeNameArr: "一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他".split("_"),
        // normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),
        normalWeekData: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        option: [],
        optionTotal: 0,
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          deliverName: ''
        },
        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [],

        headerSettings1: [
          {
            name: '姓名',
            value: 'name'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '课程类型',
            value: 'courseType'
          },
          {
            name: '首次上课时间',
            value: 'firstTime'
          },
          {
            name: '派单交付小组',
            value: 'teamName'
          },
          {
            name: '派单时间',
            value: 'createTime'
          },
          {
            name: '剩余接单时间',
            value: 'residueTime'
          },
          {
            name: '派单来源',
            value: 'deliverSource'
          },
          {
            name: '学生来源',
            value: 'studentSource'
          }
        ],
        headerSettings2: [
          {
            name: '姓名',
            value: 'name'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '首次上课时间',
            value: 'firstTime'
          },
          {
            name: '家长联系方式',
            value: 'phone'
          },
          {
            name: '课程类型',
            value: 'courseType'
          },
          {
            name: '派单交付小组',
            value: 'teamName'
          },
          {
            name: '派单状态',
            value: 'dispatchOrderStatusStr'
          },
          {
            name: '派单时间',
            value: 'createTime'
          },
          {
            name: '派单来源',
            value: 'deliverSource'
          },
          {
            name: '剩余接单时间',
            value: 'residueTime'
          },
          {
            name: '学员来源',
            value: 'studentSource'
          },
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          },
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '门店账号',
            value: 'merchantCode'
          },
          {
            name: '门店名称',
            value: 'merchantName'
          },
          {
            name: '门店手机号',
            value: 'merchantPhone'
          }
        ],
        deliverSources: [
          { name: '管理员指定交付中心', value: 100 },
          { name: '指定交付小组派单', value: 101 },
          { name: '续课派单', value: 102 },
          { name: '试课转正课派单', value: 103 },
          { name: '交付中心直属门店派单', value: 104 },
          { name: '推荐人门店绑定主交付中心派单', value: 105 },
          { name: '推荐人门店绑定次交付中心派单', value: 106 },
          { name: '轮排派单', value: 107 },
          { name: '没有可用交付中心', value: 108 },
          { name: '未知派单', value: -1 }
        ],
        tableHeaderList: [], // 获取表头数据
        //指派的交付中心编号
        assignForm: {
          id: '',
          deliverMerchantCode: ''
        },
        deliverOptions: [],
        assignDialog: false,
        arrangementDuration: 0, //交付中心轮排配置时间
        dialogHistory: false,
        dialogLoading: false,
        historys: [],
        isSubmit: false,
        groupForm: {},
        groupList: [],
        dialogAddgroup: false,
        configTime: {
          deliverTime: '',
          spareDeliverTime: '',
          transferTime: ''
        },
        leaderTeamList: [],
        courseList: [],
        CurriculumCodeArr: CurriculumCodeArr
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      // this.isTeamLeader = ls.getItem('rolesVal') === 'DeliverTeamLeader'
      this.isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';

      this.headerSettings = this.isAdmin ? this.headerSettings2 : this.headerSettings1;

      this.getHeaderlist();
      this.getConfig();
      if (this.isAdmin) {
        this.getTeacherList();
        this.initData();
      } else if (this.isTeamLeader) {
        this.initTeamList();
      } else {
        this.getGroupList();
        this.initData();
      }
    },
    watch: {
      isAdmin: function (val) {
        this.headerSettings = val ? this.headerSettings2 : this.headerSettings1;
      }
    },

    computed: {
      ...mapGetters(['code'])
    },
    mounted() {
      this.getbvstatusList();
    },
    methods: {
      closeDialog() {
        this.historys = [];
        this.dialogHistory = false;
      },
      async initNewData() {
        let that = this;
        that.tableLoading = true;
        if (that.leaderTeamList.length < 1) {
          that.tableData = [];
        } else {
          let { data } = await getFormalStudentList(this.querydata);
          // console.log(data, '=======================')
          this.tableData = data.data;
          this.total = Number(data.totalItems);
          // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
          that.tableLoading = false;
          this.$forceUpdate();
        }
        that.tableLoading = false;
        this.$forceUpdate();
      },
      async initTeamList() {
        let { data } = await getLeaderTeamList();
        this.leaderTeamList = data;

        if (this.leaderTeamList.length < 1) {
          return this.$message.warning('您暂无小组');
        } else {
          this.querydata.teamId = data.length > 0 ? data[0].id : '';
          setTimeout(() => {
            this.initNewData();
          }, 300);
        }
      },
      handlechangeDownCompany(e) {
        this.querydata.teamId = e;
        this.initNewData();
      },
      getbvstatusList() {
        bvstatusListOne().then((res) => {
          this.courseList = res.data;
        });
      },
      getSourse(value) {
        let arr = this.deliverSources.filter((i) => i.value == value);
        if (arr.length > 0) {
          return arr[0].name;
        } else {
          return '';
        }
      },
      searchData() {
        this.querydata.pageNum = 1;
        this.querydata.pageSize = 10;
        this.initData();
      },
      async getGroupList() {
        let res = await selectTeam(this.code);
        this.groupList = res.data;
      },
      // 增加接单交付小组新增
      addGroup(id) {
        this.groupForm.id = id;
        this.groupForm.type = 2;
        this.dialogAddgroup = true;
      },
      //确定增加交付小组
      async AddgroupClick() {
        await changeTeam2(this.groupForm);
        this.$message.success('指派成功');
        this.initData();
        this.dialogAddgroup = false;
      },
      // 增加交付小组弹窗关闭
      AddgroupClose() {
        this.dialogAddgroup = false;
        this.groupForm = {};
      },
      //指派方法
      async onAssign(id, isForced) {
        this.assignForm.id = id;
        this.assignForm.isForced = isForced;
        const { data } = await belongDeliverAndAllDeliver(id);
        this.deliverOptions = data.deliverList;
        this.assignDialog = true;
      },
      async onForceAssign(id, isForced) {
        this.assignForm.id = id;
        this.assignForm.isForced = isForced;
        const { data } = await belongDeliverAndAllDeliver(id);
        this.deliverOptions = data.deliverList;
        this.assignDialog = true;
      },
      // 指派提交
      async submitAssign() {
        // console.log(this.assignForm)
        let data = {};
        this.isSubmit = true;
        if (this.assignForm.deliverMerchantCode.length > 0) {
          data = {
            id: this.assignForm.id,
            deliverMerchantCode: this.assignForm.deliverMerchantCode[0],
            isForced: this.assignForm.isForced
          };
        } else {
          this.isSubmit = false;
          return this.$message.warning('请选择交付中心');
        }
        try {
          await assignFormalStudent(data);
          this.$message.success('指派成功');
          this.isSubmit = false;
          this.initData();
          this.closeAssgin();
        } catch (error) {
          this.isSubmit = false;
          return this.$message.error(error);
        }
        // console.log(res)
      },
      // 拒绝指派
      async onReject(row) {
        await this.$confirm(`您确定要拒绝 ${row.name}(${row.studentCode})吗`);
        await rejectFormalStudent(row.id);
        this.$message.success('拒绝成功');
        await this.initData();
      },
      // 关闭指派弹框
      closeAssgin() {
        this.assignForm = {
          id: '',
          deliverMerchantCode: '' //指派的交付中心编号
        };
        this.assignDialog = false;
      },
      // 获取当前轮排配置
      async getConfig() {
        // let { data } = await getDurationConfig()
        let { data } = await getDurationConfigTimes();
        this.configTime = data;
        // console.log(data, '111111111111111111111111')
        this.arrangementDuration = Number(data.arrangementDuration);
      },
      // 派单历史
      async getDetail(id) {
        this.dialogHistory = true;

        this.dialogLoading = true;
        let { data } = await getFormalHistory(id);
        // console.log(data)
        this.historys = data;
        setTimeout(() => {
          this.dialogLoading = false;
        }, 500);
      },
      //上课信息对接表
      async fillTableNormalData(item) {
        // console.log(item)
        const that = this;
        let data = {
          id: item.studentContactInfoId
        };
        // let abutment = await getStudentContactInfoDetail(data);
        let abutment = await queryStudentContactInfoDetail(data); //获取鼎数学 上课信息表详情接口
        that.mathShow = 1;
        if (that.mathShow == 1) {
          // that.abutmentList = abutment.data.studentContactInfoDetailVo;
          that.abutmentList = abutment.data.studentContactInfoDetailVo;
          console.log('🚀 ~ fillTableNormalData ~ that.abutmentList:', that.abutmentList);
        } else {
          that.abutmentList = abutment.data;
        }
        // that.coursrList = JSON.parse(that.abutmentList.courseProject);
        that.coursrList = that.abutmentList.courseProject ? JSON.parse(that.abutmentList.courseProject) : '';
        that.reviewWeekList = that.abutmentList.reviewWeek ? JSON.parse(that.abutmentList.reviewWeek) : '';

        that.reviewWeekList.sort((a, b) => a - b);
      },

      getGrade(val) {
        return this.gradeNameArr[val - 1];
      },
      // 打开试课单
      openEdit(row) {
        this.fillTableNormalData(row);
        this.dialogAbutment = true;
      },
      closeEidt() {
        this.abutmentList = {};
        this.dialogAbutment = false;
      },
      getWeekName(week) {
        return this.normalWeekData[Number(week)];
      },
      getFormatToService(date, week) {
        if (date) {
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
          } else {
            allStr = str.replace('&', '');
          }
          return allStr;
        }
        return '-';
      },
      getTrueTime(time) {
        if (time) {
          let year = new Date(time).getFullYear();
          let newTime = new Date(time).getTime() - Date.now();
          if (year > 6000 || newTime < 0) {
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
      },
      // 时间转换方法
      getResidueTime(time) {
        if (time) {
          let time1 = new Date(time).getTime();
          // let time2 = Date.now()
          // let newTime = time1 - time2
          // if (newTime <= 0) {
          //   return 0
          // } else {
          //   return newTime
          // }
          return time1;
        } else {
          return 0;
        }
      },
      // 时间戳转换 HH:MM:SS
      getTimeFromTimestamp(timestamp) {
        const date = new Date(timestamp);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
      },

      // 动态class
      statusClass(time, source) {
        let normTime = 10 * 60 * 1000;
        let newTime = new Date(time).getTime() - Date.now();
        if (newTime <= normTime) {
          return 'error';
        } else {
          return 'normal';
        }
      },
      async initData() {
        let that = this;
        that.tableLoading = true;
        let { data } = await getFormalStudentList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(data[name])));
        that.tableLoading = false;

        this.$forceUpdate();
      },
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      //重置
      rest() {
        this.$refs.querydata.resetFields();
        this.querydata = {
          studentName: '',
          studentCode: '',
          merchantCode: '',
          deliverMerchant: '',
          deliverName: '',
          dispatchStatus: '',
          curriculumId: '',
          pageNum: 1,
          pageSize: 10 //页容量
        };
        if (this.isTeamLeader) {
          this.querydata.teamId = this.leaderTeamList.length ? this.leaderTeamList[0].id : '';
        }
        this.initData();
      },
      // 获取交付中心
      async getTeacherList() {
        let allData = await deliverlist(this.selectObj);
        this.option = this.option.concat(allData.data.data);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 改变交付中心编号事件
      changeInput(e) {
        if (!!e) {
          let arr = this.option.filter((i) => i.deliverMerchantCode == e);
          this.querydata.deliverName = arr.length > 0 ? arr[0].deliverMerchantName : this.querydata.deliverName;
        }
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 清除交付中心名称事件
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.deliverName == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.deliverName = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      // 改变交付中心名称事件
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.querydata.deliverMerchant = '';
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        } else {
          let arr = this.option.filter((i) => i.deliverMerchantName == e);
          this.querydata.deliverMerchant = arr[0].deliverMerchantCode;
        }
      },
      changeMessage() {
        this.$forceUpdate();
      },
      // 表头设置
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {};
        data = this.isAdmin
          ? {
              type: 'formalStudent-admin',
              value: JSON.stringify(arr)
            }
          : {
              type: 'formalStudent',
              value: JSON.stringify(arr)
            };
        this.setHeaderSettings(data);
      },
      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {};
        data = this.isAdmin ? { type: 'formalStudent-admin' } : { type: 'formalStudent' };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            // this.tableHeaderList = JSON.parse(res.data.value);
            let Json = this.removeNullValues(res.data.value);
            // console.log(Json)
            this.tableHeaderList = JSON.parse(Json);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  body {
    background-color: #f5f7fa;
  }

  .frame {
    // margin:  0 30px;
    background-color: rgba(255, 255, 255);
    padding: 20px;
  }

  .el-button--success {
    color: #ffffff;
    background-color: #6ed7c4;
    border-color: #6ed7c4;
  }

  .transferred {
    color: #ea2424;
  }

  .no_transferred {
    color: #1cb31c;
  }

  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }

  .week {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 7px 20px;
  }
  .vocabulary {
    position: absolute;
    top: 6px;
    right: -1px;

    height: 24px;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    border-radius: 3px;
    padding: 0 4px;
    background-color: #46a6ff;
  }

  .thesaurus {
    position: relative;
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
</style>
