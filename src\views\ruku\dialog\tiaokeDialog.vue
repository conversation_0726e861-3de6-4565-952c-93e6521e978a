<template>
  <div class="dialog">
    <el-drawer title="调课" :visible.sync="classCardstyle_" :direction="direction" @close="handleClose">
      <div>
        <el-row>
          <el-col style="margin-top:2vw;margin-left:2vw">学员名称：
            <el-select v-model="value1" placeholder="请选择" style="width: 13vw" @change="studenSel">
              <el-option v-for="item in studentList" :key="item.studentCode" :label="item.studentName"
                :value="item.studentCode">
              </el-option>
            </el-select>
          </el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">
            学员编号: {{planStudy.studentCode}}
          </el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">课程内容: {{planStudy.courseType}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">日期: {{ planStudy.dateWeek}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">时间: {{datatime1}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">复习时间: {{reviewTime1}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">授课方式:
            <span>
              <span v-if="planStudy.teachingType === 1">远程</span>
              <span v-if="planStudy.teachingType === 2">线下</span>
              <span v-if="planStudy.teachingType === 3">远程和线下</span>
            </span>
          </el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">已授学时: {{planStudy.finishCourseHours}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">剩余学时: {{planStudy.haveCourseHours}}</el-col>
          <el-col style="margin-top:2vw;margin-left:2vw">更换教练：
            <el-select v-model="value2" placeholder="请选择" style="width: 13vw" filterable>
              <el-option v-for="item in planStudy.teacherList" :key="item.teacherId" :label="item.teachName"
                :value="item.teacherId">
              </el-option>
            </el-select>
          </el-col>
          <el-col style="margin-top:2vw">
            <el-button style="width:4vw;margin-left:6.5vw" @click="quxiaoBtn=classCardstyle=false">取消</el-button>
            <el-button style="width:4vw;margin-left:6.5vw" type="primary" @click="querenFn">确定</el-button>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getStudentAdjust, updateStudentByTeacher } from '@/api/rukuManage/zhuTeacher'
export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    classCardstyle: {
      type: Boolean,
      default: false
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl"
    }
  },
  name: 'classcardDialog1',
  data() {
    return {
      value1: '',
      value2: '',
      planStudy: {
        studentCode: '',
        courseType: '',
        dateWeek: '',
        dateTime: '',
        reviewTime: '',
        teachingType: '',
        finishCourseHours: '',
        haveCourseHours: '',
        teacherList: ''
      },
      studentList: {},//接受授课类型用
      teacherid: '',
      studentCard: {
        studentCode: '',
        teacherId: ''
      },
      studentChange: {
        studentCode: '',
        teacherId: '',
        newTeacherId: ''
      },
      datatime1: '',
      reviewTime1: ''
    };
  },
  //计算属性
  computed: {
    classCardstyle_: {
      get() {
        return this.classCardstyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        // console.log(v, 'v')
        this.$emit('changeDrawer', v)
      }
    }
  },
  methods: {
    // 转文字
    async studenSel() {
      this.studentCard.studentCode = this.value1
      this.studentCard.teacherId = this.teacherid
      let { data } = await getStudentAdjust(this.studentCard)
      this.datatime1 = data.dateTime.toString()
      this.reviewTime1 = data.reviewTime.toString()
      this.planStudy = data
    },
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.studentChange.studentCode = '',
        this.studentChange.teacherId = '',
        this.planStudy = ''
      this.datatime1 = ''
      this.reviewTime1 = ''
      this.value1 = ''
      this.value2 = ''
      this.$emit("changeDrawer1", false);
      this.$emit("updateList");
    },
    // 更换教练
    async querenFn() {
      this.studentChange.studentCode = this.value1
      this.studentChange.teacherId = this.teacherid
      this.studentChange.newTeacherId = this.value2
      let res = await updateStudentByTeacher(this.studentChange)
      if (res.code == 20000) {
        this.$message.success("更换成功")
      }
      this.handleClose()
    }
  }
};
</script>


<style scoped>
div/deep/.el-drawer__container {
  position: relative;
  left: 0;
  top: 15%;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}
::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}
::v-deep :focus {
  outline: 0;
}
::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>q