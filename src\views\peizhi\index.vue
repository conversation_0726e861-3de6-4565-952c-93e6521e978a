<!--交付中心-交付中心配置-->
<template>
  <div>
    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="deliverMerchantName" min-width="130" label="交付中心" header-align="center" />
      <el-table-column prop="deliverMerchantCode" min-width="110" label="交付中心编号" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="editFn(scope.row.deliverMerchantCode)">绑定企业微信</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="scrmName" label="企业微信绑定人" header-align="center"></el-table-column>
      <el-table-column prop="maxDeliverNum" label="每月最多交付人数" header-align="center"></el-table-column>
      <el-table-column prop="thisMonthDeliverPeopleNum" label="本月已交付" header-align="center"></el-table-column>
      <el-table-column prop="lastMonthExceedDeliverPeopleNum" label="上月超出人数" header-align="center"></el-table-column>
      <el-table-column prop="maxExperienceNum" label="每月最多试课人数" header-align="center"></el-table-column>
      <el-table-column prop="thisMonthExperiencePeopleNum" label="本月已试课" header-align="center"></el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="querydate1.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="querydate1.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>

    <!-- 编辑弹框 -->
    <el-dialog :visible.sync="tkedialog" width="60%" title="交付中心配置" @opened="opened">
      <el-form ref="form" :model="form" label-width="150px">
        <el-form-item hidden="hidden" label="交付中心编号" prop="deliverMerchantCode" style="width: 50%;">
          <el-input v-model="form.deliverMerchantCode" />
        </el-form-item>
        <el-form-item label="交付中心" prop="deliverMerchantName" style="width: 50%;">
          <el-input v-model="form.deliverMerchantName" disabled />
        </el-form-item>
        <el-form-item label="选择个人" prop="username" style="width: 50%;">
          <el-input v-model="form.username" @input="filterName" />
        </el-form-item>
        <el-form-item style="width: 50%;">
          <div class="tree" id="tree">
            <el-tree ref="tree" v-show="tkedialog" :data="userTree" node-key="id" :props="props1" show-checkbox
              check-strictly v-loading="loading" @check-change="handleCheckedChange" :filter-node-method="filterNode"
              class="virtual-scroll-tree" virtual-scroll :height="300">
              <div slot-scope="{node,data}">
                <span>{{ node.label }}</span>
                <span v-if="data.qywechatUserId">{{ `(${data.qywechatUserId})` }}</span>
              </div>
            </el-tree>
          </div>
        </el-form-item>

      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right:1.5vw;" size="small" @click="submit">
          确定
        </el-button>
        <el-button @click="handleClose" size="small">取 消</el-button>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { cityDetail, deliverlist2, detail, edit, level, getUserTree, bindQyWechat } from "@/api/peizhi/peizhi"
import { CodeToText } from 'element-china-area-data'
import ls from "@/api/sessionStorage";

export default {
  name: "index.vue",
  data() {
    return {
      provice: '',
      cities: '',
      cascaderIdx: 0,
      props: { multiple: true },
      options: [],
      options01: [],
      form: {
        deliverMerchantCode: '',
        deliverMerchantName: '',
        username: '',
        userId: '',
        qywechatUserId: ''
      },
      classCardstyle: false, //抽屉状态
      direction: 'rtl',//超哪边打开
      value1: '',
      tkedialog: false,
      city: false,
      btndialog: false,
      querydate1: {
        dateTime: '',
        mobile: '',
        name: '',
        status: '',
        teachingType: '',
        pageNum: 1,
        pageSize: 10 //页容量
      },
      timeAll: [],
      childVisible: false, //是否展示抽屉
      total: null,
      teacher: "",
      contentType: "",
      teacherTime: false,
      id: '',
      getTeacherStudentList: {
        teacherId: ''
      },
      isAdmin: false,
      tableData: [],
      userTree: [],
      props1: {
        label: 'name',
        children: 'children'
      },
      loading: false,
    };
  },
  watch: {
    'form.id'(newVal) {
      this.cascaderIdx++
    },
    'form.username'(val) {
      this.loading = true
      setTimeout(() => {
        this.$refs.tree.filter(val);
      }, 500);
      setTimeout(() => {
        this.loading = false

      }, 1000);
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
    this.initData()
    this.level()
    // this.getUsers()
  },
  mounted() {
    this.getUsers()
  },
  methods: {
    filterName(e) {
      // console.log(e);
      this.loading = true
      setTimeout(() => {
        this.$refs.tree.filter(e);
      }, 500);
      this.loading = false
    },
    // 判断数组对象是否完全相同。
    objectsArraysAreEqual(arr1, arr2) {
      // 检查数组长度
      if (arr1.length !== arr2.length) return false;

      // 遍历数组元素
      for (let i = 0; i < arr1.length; i++) {
        // 检查当前元素是否为对象
        if (typeof arr1[i] !== 'object' || typeof arr2[i] !== 'object') {
          return false;
        }

        // 遍历对象的属性
        const keys1 = Object.keys(arr1[i]);
        const keys2 = Object.keys(arr2[i]);
        if (keys1.length !== keys2.length) return false;

        for (let j = 0; j < keys1.length; j++) {
          if (arr1[i][keys1[j]] !== arr2[i][keys1[j]]) {
            return false;
          }
        }
      }

      return true;
    },
    filterNode(value, data) {
      this.loading = true
      if (!value) {
        this.loading = false

        return true
      } else {
        this.loading = false
        return data.name.indexOf(value) !== -1;
      }

    },
    opened() {
      if (this.form.userId) {
        this.$refs.tree.setCheckedKeys([this.form.userId])
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    handleClose() {
      this.form = {
        deliverMerchantCode: '',
        deliverMerchantName: '',
        username: '',
        userId: '',
        qywechatUserId: ''
      }
      this.$refs.tree.setCheckedKeys([])
      this.loading = false
      this.tkedialog = false
    },
    handleCheckedChange(data, checked, indeterminate) {
      if (checked) {
        this.$refs.tree.setCheckedKeys([data.id])
        this.form.username = data.name
        this.form.userId = data.id
        this.form.qywechatUserId = data.qywechatUserId
        // console.log(this.form.username)
      }
      // if (data.isUser) {
      //   if (checked) {
      //     this.$refs.tree.setCheckedKeys([data.id])
      //     this.form.username = data.name
      //     // console.log(this.form.username)
      //   }
      // } else {
      //   this.$refs.tree.setCheckedKeys([])
      // }
    },
    async getUsers() {
      // this.loading = true
      let res = await getUserTree()
      // console.log(res, '================')
      if (this.userTree.length < 1) {
        this.userTree = res.data
      } else {
        let isSame = this.objectsArraysAreEqual(res, this.userTree)
        if (!isSame) {
          this.userTree = res.data
        }
      }
      // this.userTree = res.data
      // setTimeout(() => {
      //   this.loading = false
      // }, 2000);
    },
    getCodeToText(codeStr, codeArray) {
      if (null === codeStr && null === codeArray) {
        return null;
      } else if (null === codeArray) {
        codeArray = codeStr.split(",");
      }
      let area = [];
      switch (codeArray.length) {
        case 1:
          area.push(CodeToText[codeArray[0]]);
          break;
        case 2:
          area.push(CodeToText[codeArray[0]])
          area.push(CodeToText[codeArray[1]])
          break;
        case 3:
          area.push(CodeToText[codeArray[0]])
          area.push(CodeToText[codeArray[1]])
          area.push(CodeToText[codeArray[2]])
          break;
        default:
          break;
      }
      this.addForm.province = area[0]
      this.addForm.city = area[1]
      this.addForm.area = area[2]
      return area;
    },
    async submit() {
      let obj = {
        merchantCode: this.form.deliverMerchantCode,
        userId: this.form.userId,
        qywechatUserId: this.form.qywechatUserId,
      }
      let data = await bindQyWechat(obj)
      if (data.success) {
        this.$message.success("操作成功")
        this.handleClose()
        this.initData()
      }
    },

    async level() {
      let data = await level()
      this.options = data.data
      this.options01 = data.data.slice(0)

    },
    async initData() {
      let data = await deliverlist2(this.querydate1)
      // console.log(data)
      this.tableData = data.data.data
      this.total = Number(data.data.totalItems)
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.querydate1.pageSize = val;
      console.log(val)
      this.initData();
    },
    async editFn(code) {
      this.tkedialog = true
      this.loading = true
      setTimeout(() => {
        this.getUsers()
      }, 0)
      let data = await detail(code)
      // this.form = data.data
      this.form.deliverMerchantCode = data.data.deliverMerchantCode
      this.form.deliverMerchantName = data.data.deliverMerchantName
      this.form.username = data.data.scrmName ? data.data.scrmName : ''
      this.form.userId = data.data.scrmUserId ? data.data.scrmUserId : ''
      this.form.qywechatUserId = data.data.qywechatUserId ? data.data.qywechatUserId : ''
      setTimeout(() => {
        this.loading = false
      }, 500);
    },
    handleCurrentChange(val) {
      this.querydate1.pageNum = val;
      this.initData();
    },
    changeDrawer1(v) {
      this.classCardstyle = v;
    },
  }
};
</script>

<style lang="scss" scoped>
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

body {
  background-color: #f5f7fa;
}

.frame {
  margin: 30px;
  background-color: rgba(255, 255, 255);
}

.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #6ed7c4;
}

.provice button {
  margin-right: 20px;
  margin-bottom: 20px;
  color: #7ac756;
  border: 1px solid rgba(122, 199, 86, 1);
  background: rgba(238, 248, 232, 1);
  padding: 3px 5px;
  border-radius: 3px;
}

.city button {
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(56, 148, 255, 1);
  color: rgba(56, 148, 255, 1);
  border-radius: 3px;
  padding: 3px 5px;
  background-color: rgba(255, 255, 255, 1);
}
.tree {
  height: 300px;
  overflow-y: auto;
}
::v-deepp .virtual-scroll-tree .el-tree-node__content {
  height: auto; /* 根据实际情况调整 */
}
</style>
