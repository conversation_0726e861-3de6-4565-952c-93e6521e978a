<!--交付中心-接单管理-交付小组配置-->
<template>
  <div>
    <div class="frame" v-if="isAdmin">
      <el-form label-width="7.5rem" ref="querydata" :model="querydata">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item label="交付中心编号:">
              <!-- <el-input v-model="querydata.merchantCode" size="small" placeholder=""></el-input> -->
              <el-input v-model="querydata.merchantCode" @change="changeInput" size="small" placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="交付中心名称:">
              <!-- <el-input v-model="querydata.merchantName" size="small" placeholder=""></el-input> -->
              <el-select
                v-el-select-loadmore="handleLoadmore3"
                :loading="loadingShip3"
                remote
                clearable
                v-model="querydata.merchantName"
                filterable
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="(item, index) in options" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 1.25rem">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div style="height: 1.25rem; background: #f7f8fc; margin-bottom: 1.875rem"></div>
    <div class="top">
      <h4>交付小组配置</h4>
      <el-button type="primary" size="small" @click="editList = true">新增交付小组</el-button>
    </div>
    <!-- 暂无数据 -->
    <div class="nomore" v-if="tableData && tableData.length < 1">
      <el-image style="width: 6.25rem; height: 6.25rem" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
      <div style="color: #999; margin-top: 1.25rem">无数据</div>
    </div>
    <!-- 表格 -->
    <div class="table" v-if="tableData && tableData.length > 0">
      <el-table
        :data="tableData"
        style="width: 100%"
        id="out-table"
        :header-cell-style="getRowClass"
        :cell-style="{ 'text-align': 'center' }"
        :key="itemKey"
        :span-method="objectSpanMethod"
        v-loading="loading"
      >
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="'team' + index"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="
            item.value == 'deliverId' || item.value == 'operate' || item.value == 'learnTeacherNum'
              ? '150px'
              : item.value == 'deliverTeamMembers'
              ? '180px'
              : item.value == 'totalTeacherNum'
              ? '200px'
              : '120px'
          "
        >
          <template v-slot="{ row }">
            <!-- 按钮 -->
            <div v-if="item.value == 'operate'">
              <el-button type="primary" size="mini" @click="editFn(row)">编辑</el-button>
              <el-button type="danger " size="mini" @click="delFn(row)">删除</el-button>
            </div>
            <div v-else-if="item.value == 'percent'">
              <span>{{ row.percent == 0 ? 0 : row.percent + '%' }}</span>
            </div>
            <div v-else-if="item.value == 'experiencePercent'">
              <span>{{ row.experiencePercent == 0 ? 0 : row.experiencePercent + '%' }}</span>
            </div>
            <div v-else-if="item.value == 'bvStatuses'">
              <span>{{ mapEnName(row.bvStatuses) }}</span>
            </div>
            <div v-else-if="item.value == 'type'">
              <span>{{ transFormType(row.type) }}</span>
            </div>
            <div v-else-if="item.value == 'deliveryTeamLeader'">
              <span v-if="row.deliveryTeamLeader">{{ row.deliveryTeamLeader.name }}</span>
              <span v-else>{{ '-' }}</span>
              <!-- <i class="el-icon-edit" style="color:#409eff;margin-left:.625rem" @click="editFn(row)" v-if="isAdmin"></i> -->
            </div>
            <div v-else-if="item.value == 'deliverTeamMembers'">
              <span>{{ mapName(row.deliverTeamMembers) }}</span>
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <el-row type="flex" justify="center" align="middle" style="height: 3.75rem">
        <!-- 3个变量：每页数量、页码数、总数  -->
        <!-- 2个事件：页码切换事件、每页数量切换事件-->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="querydata.pageNum"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="querydata.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </el-row>
    </div>

    <!-- 编辑交付小组 -->
    <el-dialog :visible.sync="editList" width="30%" :title="editForm.id ? '编辑交付小组' : '新增交付小组'" @close="closeEdit" :close-on-click-modal="false" @open="openDialog">
      <el-form ref="editForm" :model="editForm" label-width="7.5rem" :rules="rules">
        <el-form-item label="交付中心编号" prop="merchantCode" v-if="isAdmin" label-width="7.5rem">
          <!-- <el-input v-model="editForm.merchantCode" placeholder="请输入" style="width:85%" /> -->
          <el-select
            v-el-select-loadmore="handleLoadmore4"
            :loading="loadingShip4"
            remote
            clearable
            v-model="editForm.merchantCode"
            filterable
            reserve-keyword
            placeholder="请选择"
            @input="changeMessage1"
            @blur="clearSearchRecord1"
            @change="changeDeliver"
            style="width: 85%"
            :disabled="!!editForm.id"
          >
            <el-option
              v-for="(item, index) in options1"
              :key="index"
              :label="item.deliverMerchantCode + '(' + item.deliverMerchantName + ')'"
              :value="item.deliverMerchantCode"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属交付小组" prop="teamName">
          <el-input v-model="editForm.teamName" placeholder="请输入" style="width: 85%" />
        </el-form-item>
        <el-form-item label="课程类型" prop="bvStatuses">
          <el-select
            v-el-select-loadmore="handleLoadmore"
            :loading="loadingShip"
            v-model="editForm.bvStatuses"
            filterable
            multiple
            placeholder="请选择"
            style="width: 85%"
            ref="course"
          >
            <el-option v-for="(item, index) in courseData" :key="index" :label="item.enName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接单类型" prop="type">
          <el-select v-model="editForm.type" multiple placeholder="请选择" style="width: 85%">
            <el-option v-for="item in types" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-row v-if="editForm.type && editForm.type.includes('1')">
          <el-col :span="7">
            <el-form-item label="交付中心试课承单量" label-width="10rem"></el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="experienceAmount" label-width="3.125rem" label="单量">
              <el-input v-model="editForm.experienceAmount" type="number" @input="handleInput1" placeholder="请输入">
                <template slot="suffix">单</template>
              </el-input>
              <!-- <el-input-number v-model="editForm.amount" :min="1"></el-input-number>
              <span>单</span> -->
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item prop="experiencePercent" label-width="3.125rem" label="比例">
              <el-input v-model="editForm.experiencePercent" type="number" style="width: 65%" @input="handleInput2" placeholder="请输入">
                <template slot="suffix">%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="editForm.type && editForm.type.includes('2')">
          <el-col :span="7">
            <el-form-item label="交付中心正式课承单量" label-width="10rem"></el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="amount" label-width="3.125rem" label="单量">
              <el-input v-model="editForm.amount" type="number" @input="handleInput3" placeholder="请输入">
                <template slot="suffix">单</template>
              </el-input>
              <!-- <el-input-number v-model="editForm.amount" :min="1"></el-input-number>
              <span>单</span> -->
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item prop="percent" label-width="3.125rem" label="比例">
              <el-input v-model="editForm.percent" type="number" style="width: 65%" @input="handleInput4" placeholder="请输入">
                <template slot="suffix">%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="交付小组组长" prop="deliveryTeamLeader">
          <el-select v-el-select-loadmore="handleLoadmore1" :loading="loadingShip1" v-model="editForm.deliveryTeamLeader" placeholder="请选择" style="width: 85%">
            <el-option v-for="(item, index) in teamLeaders" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交付小组成员" prop="deliverTeamMembers">
          <el-select :loading="loadingShip2" v-model="editForm.deliverTeamMembers" multiple placeholder="请选择" filterable style="width: 85%">
            <el-option v-for="(item, index) in teacherList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-row type="flex" justify="end" style="margin-top: 2.5vh">
          <el-button plain size="small" @click="closeEdit" style="width: 6.25rem">取消</el-button>
          <el-button type="primary" style="margin-right: 6.25rem; width: 6.25rem" :loading="isSubmit" size="small" @click="onSubmit">确定</el-button>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import { deliverlist } from '@/api/peizhi/peizhi';
  import { getTeamList, getTeamDetail, getCourseCateList, getTeacherList, getAllTeacherList, addTeam, editTeam, delTeam } from '@/api/orderManage';
  import { getLeaderList } from '@/api/rukuManage/zuzhang';
  export default {
    name: 'deliveryTeam',
    components: {},
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        screenWidth: window.screen.width, //屏幕宽度
        editList: false, //试课单弹窗
        editForm: {}, //试课单表单
        // 搜索表单
        querydata: {
          merchantCode: '',
          merchantName: '',
          pageNum: 1,
          pageSize: 10 //页容量
        },
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 1000
        },
        optionTotal: 0,
        loadingShip1: false,
        selectObj1: {
          pageNum: 1,
          pageSize: 100,
          merchantCode: ''
        },
        optionTotal1: 0,
        loadingShip2: false,
        selectObj2: {
          merchantCode: ''
        },
        optionTotal2: 0,
        options02: [],
        loadingShip3: false,
        selectObj3: {
          pageNum: 1,
          pageSize: 100
        },
        optionTotal3: 0,
        options: [],
        loadingShip4: false,
        selectObj4: {
          pageNum: 1,
          pageSize: 100
        },
        optionTotal4: 0,
        options1: [],
        total: null,
        tableData: [],
        isAdmin: false,
        option: [],
        courseData: [],
        headerSettings: [],
        types: [
          {
            value: '1',
            label: '试课'
          },
          {
            value: '2',
            label: '正式课'
          }
        ],
        teamLeaders: [],
        teacherList: [],
        tableHeaderList: [], // 获取表头数据
        tableHeaderList1: [
          {
            name: 'ID',
            value: 'deliverId'
          },
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '交付中心编号',
            value: 'merchantCode'
          },
          {
            name: '所属交付小组',
            value: 'teamName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '课程类型',
            value: 'bvStatuses'
          },
          {
            name: '接单类型',
            value: 'type' //1试课 2正式课
          },
          // {
          //   name: '交付中心承单量',
          //   value: 'amount'
          // },
          // {
          //   name: '比例',
          //   value: 'percent'
          // },
          {
            name: '正式课承单量',
            value: 'amount'
          },
          {
            name: '正式课比例',
            value: 'percent'
          },
          {
            name: '试课承单量',
            value: 'experienceAmount'
          },
          {
            name: '试课比例',
            value: 'experiencePercent'
          },
          {
            name: '正式课教练数量',
            value: 'learnTeacherNum'
          },
          {
            name: '正式课+试课教练数量',
            value: 'totalTeacherNum'
          },
          {
            name: '服务合伙人数量',
            value: 'bindSchoolCount'
          },

          {
            name: '交付小组组长',
            value: 'deliveryTeamLeader'
          },
          {
            name: '交付小组成员(教练)',
            value: 'deliverTeamMembers'
          }
        ], // 获取表头数据
        tableHeaderList2: [
          {
            name: 'ID',
            value: 'id'
          },
          {
            name: '所属交付小组',
            value: 'teamName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '课程类型',
            value: 'bvStatuses'
          },
          {
            name: '接单类型',
            value: 'type' //1试课 2正式课
          },
          {
            name: '正式课承单量',
            value: 'amount'
          },
          {
            name: '正式课比例',
            value: 'percent'
          },
          {
            name: '试课承单量',
            value: 'experienceAmount'
          },
          {
            name: '试课比例',
            value: 'experiencePercent'
          },
          {
            name: '正式课教练数量',
            value: 'learnTeacherNum'
          },
          {
            name: '正式课+试课教练数量',
            value: 'totalTeacherNum'
          },
          {
            name: '服务合伙人数量',
            value: 'bindSchoolCount'
          },
          {
            name: '交付小组组长',
            value: 'deliveryTeamLeader'
          },
          {
            name: '交付小组成员(教练)',
            value: 'deliverTeamMembers'
          }
        ], // 获取表头数据
        rules: {
          merchantCode: [{ required: true, message: '请输入交付中心编号', trigger: 'blur' }],
          teamName: [{ required: true, message: '请输入所属交付小组', trigger: 'blur' }],
          bvStatuses: [{ required: true, message: '请选择课程类型', trigger: 'blur' }],
          type: [{ required: true, message: '请选择接单类型', trigger: 'blur' }],
          amount: [{ required: true, message: '请输入正式课承单量', trigger: 'blur' }],
          percent: [{ required: true, message: '请输入正式课比例', trigger: 'blur' }],
          experienceAmount: [{ required: true, message: '请输入试课承单量', trigger: 'blur' }],
          experiencePercent: [{ required: true, message: '请输入试课比例', trigger: 'blur' }],
          deliveryTeamLeader: [{ required: true, message: '请选择交付小组组长', trigger: 'blur' }],
          deliverTeamMembers: [{ required: true, message: '请选择交付小组成员', trigger: 'blur' }]
        },
        SUBbvStatuses: [],
        itemKey: 0,
        //ID
        spanArr_id: [],
        idName: null,
        // 交付中心名称
        spanArr_delName: [],
        delName: null,
        //交付中心编号
        spanArr_delCode: [],
        codeName: null,
        isSubmit: false,
        loading: false
      };
    },
    watch: {
      editForm: {
        deep: true,
        immediate: true,
        handler(val) {
          if (parseInt(val.amount) < 1) {
            val.amount = 0;
          }
          if (parseInt(val.percent) < 1) {
            val.percent = 0;
          }
        }
      }
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      this.tableHeaderList = this.isAdmin ? this.tableHeaderList1 : this.tableHeaderList2;
      this.initData();
      if (this.isAdmin) {
        this.getDeliverList();
      }
    },
    methods: {
      searchData() {
        this.querydata.pageNum = 1;
        this.querydata.pageSize = 10;
        this.initData();
      },
      //获取合并配置明细
      getSpanArr(data) {
        console.log(data);

        let self = this;
        self.spanArr_id = [];
        self.idName = 0; // 初始计数为0

        self.spanArr_delName = [];
        self.delName = 0; // 初始计数为0

        self.spanArr_delCode = [];
        self.codeName = 0; // 初始计数为0
        // 循环遍历表体数据
        for (let i = 0; i < data.length; i++) {
          if (i == 0) {
            // 先设置第一项
            self.spanArr_id.push(1); // 初为1，若下一项和此项相同，就往spanArr_name数组中追加0
            self.idName = 0; // 初始计数为0

            self.spanArr_delName.push(1); // 初为1，若下一项和此项相同，就往spanArr_dep_name数组中追加0
            self.delName = 0; // 初始计数为0

            self.spanArr_delCode.push(1); // 初为1，若下一项和此项相同，就往spanArr_dep_name数组中追加0
            self.codeName = 0; // 初始计数为0
          } else {
            // 判断当前项与上项的设备类别是否相同，因为是合并这一列的单元格
            //ID列
            if (data[i].deliverId == data[i - 1].deliverId) {
              // 如果相等
              self.spanArr_id[self.idName] += 1; // 增加计数
              self.spanArr_id.push(0); // 相等就往spanArrp_name数组中追加0
            } else {
              self.spanArr_id.push(1); // 不等就往spanArr_name数组中追加1
              self.idName = i; // 将索引赋值为计数
              // console.log("索引", i, self.idName);
            }

            //交付中心编号列
            if (data[i].deliverName == data[i - 1].deliverName) {
              // 如果相等
              self.spanArr_delName[self.delName] += 1; // 增加计数
              self.spanArr_delName.push(0); // 相等就往spanArr_dep_name数组中追加0
              // console.log("索引", i, self.delName);
            } else {
              self.spanArr_delName.push(1); // 不等就往spanArr_dep_name数组中追加1
              self.delName = i; // 将索引赋值为计数
              // console.log("索引", i, self.delName);
            }

            //交付中心名称列
            if (data[i].merchantCode == data[i - 1].merchantCode) {
              // 如果相等
              self.spanArr_delCode[self.codeName] += 1; // 增加计数
              self.spanArr_delCode.push(0); // 相等就往spanArr_dep_name数组中追加0
              // console.log("索引", i, self.codeName);
            } else {
              self.spanArr_delCode.push(1); // 不等就往spanArr_dep_name数组中追加1
              self.codeName = i; // 将索引赋值为计数
              // console.log("索引", i, self.codeName);
            }
            //继续列，就继续if() else, 但是要重新声明 数据列添加的新数组 你妈的
          }
        }
        console.log(this.tableData);
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        let self = this;

        if (this.isAdmin) {
          //id列
          //columnIndex 给第二列做单元格合并。0是第一列，1是第二列，2是第三列。
          if (columnIndex === 0) {
            const rowCell = self.spanArr_id[rowIndex];
            if (rowCell > 0) {
              const colCell = 1;
              return {
                rowspan: rowCell,
                colspan: colCell
              };
            } else {
              // 清除原有的单元格，必须要加，否则就会出现单元格会被横着挤到后面了！！！
              // 本例中数据是写死的不会出现，数据若是动态后端获取的，就会出现了！！！
              return {
                rowspan: 0,
                colspan: 0
              };
            }
          }
          //部门列
          // columnIndex 给第二列做单元格合并。0是第一列，1是第二列，2是第三列。
          if (columnIndex === 1) {
            // console.log("单元格数组，若下一项为0，则代表合并上一项", self.spanArr_delName);
            const rowCell_dep = self.spanArr_delName[rowIndex];
            if (rowCell_dep > 0) {
              const colCell_dep = 1;
              // console.log(`动态竖向合并单元格, 第${colCell_dep}列，竖向合并${rowCell_dep}个单元格 `);
              return {
                rowspan: rowCell_dep,
                colspan: colCell_dep
              };
            } else {
              // 清除原有的单元格，必须要加，否则就会出现单元格会被横着挤到后面了！！！
              // 本例中数据是写死的不会出现，数据若是动态后端获取的，就会出现了！！！
              return {
                rowspan: 0,
                colspan: 0
              };
            }
          }

          if (columnIndex === 2) {
            // console.log("单元格数组，若下一项为0，则代表合并上一项", self.spanArr_delName);
            const rowCell_dep = self.spanArr_delName[rowIndex];
            if (rowCell_dep > 0) {
              const colCell_dep = 1;
              // console.log(`动态竖向合并单元格, 第${colCell_dep}列，竖向合并${rowCell_dep}个单元格 `);
              return {
                rowspan: rowCell_dep,
                colspan: colCell_dep
              };
            } else {
              // 清除原有的单元格，必须要加，否则就会出现单元格会被横着挤到后面了！！！
              // 本例中数据是写死的不会出现，数据若是动态后端获取的，就会出现了！！！
              return {
                rowspan: 0,
                colspan: 0
              };
            }
          }

          //继续列，就继续if() else 你妈的
        } else {
          return;
        }
      },
      searchTeacher() {},
      // 初始化交付小组成员列表
      async getTeacherList() {
        let allData = await getAllTeacherList(this.selectObj2);
        // this.teacherList = this.teacherList.concat(allData.data.data);
        this.teacherList = allData.data;
        this.optionTotal2 = Number(allData.length);
      },
      // 初始化交付小组组长列表
      async initLeader() {
        let allData = await getLeaderList(this.selectObj1);
        this.teamLeaders = this.teamLeaders.concat(allData.data.data);
        this.optionTotal1 = Number(allData.data.totalPage);
      },
      //  获取课程类型列表长度
      async getCourseList() {
        let allData = await getCourseCateList(this.selectObj);
        let newArr = allData.data.data.filter((i) => i.isEnable != 0);
        this.courseData = this.courseData.concat(newArr);
        this.optionTotal = Number(allData.data.totalPage);
      },
      transformStatuses() {
        let arr = [];
        for (let i = 0; i < this.editForm.bvStatuses.length; i++) {
          let fil = this.courseData.filter((item) => item.id == this.editForm.bvStatuses[i]);
          // console.log(fil);
          if (fil.length) {
            arr.push({
              enName: fil[0].enName,
              id: fil[0].id
            });
          }
        }
        this.SUBbvStatuses = arr;
      },
      handleInput1(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.editForm.experienceAmount = '';
        }
      },
      handleInput2(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.editForm.experiencePercent = '';
        }
      },
      handleInput3(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.editForm.amount = '';
        }
      },
      handleInput4(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.editForm.percent = '';
        }
      },

      onSubmit() {
        let that = this;
        // return console.log(213123123213213)
        that.$refs.editForm.validate(async (vaild) => {
          if (vaild) {
            let obj = {};
            that.isSubmit = true;
            let TeamLeade = { id: that.editForm.deliveryTeamLeader };
            let members = that.editForm.deliverTeamMembers.map((i) => {
              return { id: i };
            });
            that.transformStatuses();
            obj = that.editForm.id
              ? {
                  id: that.editForm.id,
                  teamLeaderId: TeamLeade.id,
                  teamName: that.editForm.teamName,
                  merchantCode: that.editForm.merchantCode,
                  amount: that.editForm.amount,
                  percent: that.editForm.percent,
                  experienceAmount: that.editForm.experienceAmount,
                  experiencePercent: that.editForm.experiencePercent,
                  type: that.editForm.type.join(','),
                  deliveryTeamLeader: TeamLeade,
                  deliverTeamMembers: members,
                  bvStatuses: that.SUBbvStatuses
                }
              : {
                  teamLeaderId: TeamLeade.id,
                  teamName: that.editForm.teamName,
                  merchantCode: that.editForm.merchantCode,
                  amount: that.editForm.amount,
                  percent: that.editForm.percent,
                  experienceAmount: that.editForm.experienceAmount,
                  experiencePercent: that.editForm.experiencePercent,
                  type: that.editForm.type.join(','),
                  deliveryTeamLeader: TeamLeade,
                  deliverTeamMembers: members,
                  bvStatuses: that.SUBbvStatuses
                };
            if (that.editForm.type == 1) {
              delete obj.amount;
              delete obj.percent;
            } else if (that.editForm.type == 2) {
              delete obj.experienceAmount;
              delete obj.experiencePercent;
            }
            try {
              that.editForm.id ? await editTeam(obj) : await addTeam(obj);
              that.$message.success('操作成功');
              setTimeout(() => {
                that.initData();
                that.closeEdit();
              }, 500);
            } catch (error) {
              this.isSubmit = false;
              return this.$message.error(error);
            }
          } else {
            that.isSubmit = false;
            that.$message.error({ showClose: true, message: '请按规范填写数据', type: 'error' });
            return;
          }
        });
      },
      delFn(row) {
        console.log(row);
        let that = this;
        const h = that.$createElement;
        that
          .$confirm('提示', {
            title: '提示',
            message: h('p', null, [
              h('div', null, '是否删除交付小组 '),
              h('div', { style: 'color: red' }, '删除前请确认交付小组内无学员!!!'),
              h('div', { style: 'color: red' }, '请务必确认好!!!')
            ]),
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            delTeam({
              id: row.id
            }).then((res) => {
              that.$message.success('操作成功');
              that.initData();
            });
          });
      },
      async editFn(row) {
        console.log(row);
        let { data } = await getTeamDetail(row.id);
        let newArr = JSON.parse(JSON.stringify(data)); //深拷贝一份
        newArr.type = newArr.type.split(',');
        newArr.bvStatuses = newArr.bvStatuses && newArr.bvStatuses.map((i) => i.id);
        newArr.deliverTeamMembers = newArr.deliverTeamMembers && newArr.deliverTeamMembers.map((i) => i.id);
        if (newArr.deliveryTeamLeader) {
          newArr.deliveryTeamLeader = newArr.deliveryTeamLeader.id;
        }
        this.editForm = newArr;
        // this.selectObj2.deliverMerchant = this.editForm.merchantCode
        setTimeout(() => {
          this.editList = true;
        }, 500);
      },
      openDialog() {
        // 打开弹框回调-初始化列表
        this.teacherList = [];
        this.teamLeaders = [];
        this.courseData = [];
        this.options1 = [];
        if (this.isAdmin) {
          this.getDeliverList1();
          if (this.editForm.merchantCode) {
            this.selectObj1.merchantCode = this.editForm.merchantCode;
            this.selectObj2.merchantCode = this.editForm.merchantCode;
            this.initLeader();
            this.getTeacherList();
          } else {
            this.teamLeaders = [];
            this.teacherList = [];
          }
        } else {
          this.initLeader();
          this.getTeacherList();
        }
        this.getCourseList();
        // this.initLeader()
        // this.getTeacherList()
        setTimeout(() => {
          this.editList = true;
        }, 500);
      },
      closeEdit() {
        // 关闭回调
        this.$refs.editForm.clearValidate();
        this.$refs.editForm.resetFields();
        this.isSubmit = false;
        this.teacherList = [];
        this.teamLeaders = [];
        this.courseData = [];
        this.selectObj = {
          pageNum: 1,
          pageSize: 100
        };
        this.selectObj1 = {
          pageNum: 1,
          pageSize: 100,
          merchantCode: ''
        };
        this.selectObj2 = {
          merchantCode: ''
        };
        this.selectObj3 = {
          pageNum: 1,
          pageSize: 100
        };
        this.selectObj4 = {
          pageNum: 1,
          pageSize: 100
        };
        this.editForm = {};
        this.editList = false;
      },
      //
      async initData() {
        this.loading = true;
        let { data } = await getTeamList(this.querydata);
        let newArr = data.data;
        this.tableData = newArr ?? [];
        this.total = Number(data.totalItems);
        this.itemKey = Math.random();
        this.$forceUpdate();
        this.getSpanArr(this.tableData);
        this.loading = false;
      },
      //重置
      rest() {
        this.querydata = {
          merchantCode: '',
          merchantName: '',
          pageNum: 1,
          pageSize: 10 //页容量
        };
        this.initData();
      },
      // 获取交付中心
      async getDeliverList() {
        let allData = await deliverlist(this.selectObj3);
        this.options = this.options.concat(allData.data.data);
        this.optionTotal3 = Number(allData.data.totalPage);
      },
      async getDeliverList1() {
        let allData = await deliverlist(this.selectObj4);
        this.options1 = this.options1.concat(allData.data.data);
        this.optionTotal4 = Number(allData.data.totalPage);
      },
      // 下拉加载
      handleLoadmore3() {
        if (!this.loadingShip3) {
          if (this.selectObj3.pageNum == this.optionTotal3) return; //节流防抖
          this.selectObj3.pageNum++;
          this.getDeliverList();
        }
      },
      // 下拉加载
      handleLoadmore4() {
        if (!this.loadingShip4) {
          if (this.selectObj4.pageNum == this.optionTotal4) return; //节流防抖
          this.selectObj4.pageNum++;
          this.getDeliverList1();
        }
      },
      // 改变下拉框的值
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.merchantName == '') {
            this.options = [];
            this.selectObj3.pageNum = 1;
            this.getDeliverList();
          }
        }, 500);
        this.$forceUpdate();
      },
      // 改变下拉框的值
      clearSearchRecord1() {
        setTimeout(() => {
          if (this.editForm.merchantCode == '') {
            this.options1 = [];
            this.selectObj4.pageNum = 1;
            this.getDeliverList1();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeInput(e) {
        console.log(e);
        if (!!e) {
          let arr = this.options.filter((i) => i.deliverMerchantCode == e);
          this.querydata.merchantName = arr[0].deliverMerchantName;
        }
      },
      changeTeacher(e) {
        console.log(e);
        if (e == '') {
          this.options = [];
          this.querydata.merchantCode = '';
          this.selectObj.pageNum = 1;
          // this.getTeacherList();
          this.getDeliverList();
        } else {
          let arr = this.options.filter((i) => i.deliverMerchantName == e);
          this.querydata.merchantCode = arr[0].deliverMerchantCode;
        }
      },
      changeDeliver(e) {
        if (e == '') {
          this.options1 = [];
          this.selectObj1.merchantCode = e;
          this.selectObj2.merchantCode = e;
          this.teacherList = [];
          this.teamLeaders = [];
        } else {
          this.teamLeaders = [];
          this.teacherList = [];
          this.selectObj1.merchantCode = e;
          this.selectObj2.merchantCode = e;
          this.initLeader();
          this.getTeacherList();
        }
      },
      changeMessage() {
        this.$forceUpdate();
      },
      changeMessage1() {
        this.$forceUpdate();
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getCourseList();
        }
      },
      // 下拉加载
      handleLoadmore1() {
        if (!this.loadingShip1) {
          if (this.selectObj1.pageNum == this.optionTotal1) return; //节流防抖
          this.selectObj1.pageNum++;
          this.initLeader();
        }
      },
      // 下拉加载
      handleLoadmore2() {
        if (!this.loadingShip2) {
          if (this.selectObj2.pageNum == this.optionTotal2) return; //节流防抖
          this.selectObj2.pageNum++;
          this.getTeacherList();
        }
      },
      transFormType(str) {
        return str
          .split(',')
          .map((i) => (i = i == 1 ? '试课' : '正式课'))
          .join(',');
      },
      mapName(arr) {
        if (!arr) return '';
        return arr.map((i) => i.name).join(',');
      },
      mapEnName(arr) {
        if (!arr) return '';
        return arr.map((i) => i.enName).join(',');
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      }
    }
  };
</script>
<style lang="sass">
  // 隐藏el-input设置type为num的时候后面的上下箭头
   // appearance: none !important
  input::-webkit-inner-spin-button
    appearance: none !important

  input::-webkit-outer-spin-button
    appearance: none !important

  input[type="number"]
    appearance: textfield

  // 解决光标上移
  ::v-deep .el-input__inner
    line-height: .0625rem !important
</style>
<style lang="scss" scoped>
  body {
    background-color: #f5f7fa;
  }
  .table {
    padding: 0.625rem 0.625rem 0 0.625rem;
  }
  .top {
    padding: 0 0 0 1.25rem;
  }

  .el-button--success {
    color: #ffffff;
    background-color: #6ed7c4;
    border-color: #6ed7c4;
  }

  .transferred {
    color: #ea2424;
  }

  .no_transferred {
    color: #1cb31c;
  }
  ::v-deep .el-dialog__title {
    font-weight: 600;
    font-size: 1.125rem;
  }
  ::v-deep .el-input__suffix {
    top: 0;
  }
  ::v-deep .el-form-item--medium .el-form-item__label {
    text-align: left;
  }
  ::v-deep .el-input__suffix-inner {
    color: #000;
  }
  ::v-deep .el-dialog__headerbtn .el-dialog__close {
    border-radius: 50%;
    background: #ccc;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 12.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .frame {
    // margin:  0 1.875rem;
    background-color: rgba(255, 255, 255);
    padding: 1.25rem;
  }
</style>
