<template>
  <div>
    <el-row style="margin: 4vw 5vw" class="infomationAll">
      <!-- <div class="container">
        <span class="text">个人头像：</span>
      </div> -->
      <!-- 头像 -->
      <!-- <div style="margin-left:6.5vw;display:flex;width:20vw">
        <el-upload
          style="display:flex;"
          class="avatar-uploader"
          action="https:/placeholder.typicode.com/posts/"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <div style="display:flex">
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <el-avatar v-else shape="square" :size="120" :src="url"></el-avatar>
            <button class="buttonel" style="margin-top:5.5vw" @click="photoBtn">更换头像</button>
          </div>
        </el-upload>
      </div> -->
      <!-- 姓名 -->
      <div style="display: flex; margin-bottom: 0.5vw">
        <div class="container">
          <span class="text">姓名：</span>
        </div>
        <div>{{ inforList.realName }}</div>
      </div>

      <div>
        <div class="container">
          <span class="text">身份证证件号：</span>
        </div>
        <div>{{ inforList.idCard }}</div>
      </div>

      <div>
        <div class="container" style="margin-bottom: 0.3vw">
          <span class="text">银行卡号：</span>
        </div>
        <div>{{ inforList.bankCard }}</div>
      </div>

      <div>
        <div class="container" style="margin-bottom: 0.3vw">
          <span class="text" style="line-height: 0">手机号：</span>
        </div>
        <div>{{ inforList.mobile }}</div>
      </div>

      <div style="margin-top: 0.5vw">
        <div class="container">
          <span class="text">实名认证：</span>
        </div>
        <div v-show="signContractStatus == 1">已认证</div>
        <div v-show="signContractStatus != 1">未认证</div>
        <div v-show="signContractStatus != 1">
          <el-button type="primary" size="mini" class="WithdrawalFn" @click="toAuthenFn()">去认证</el-button>
        </div>
      </div>

      <div style="height: 2vw">
        <div class="container" style="line-height: 1.5vw">
          <span class="text">当前余额：</span>
        </div>
        <div style="line-height: 1.5vw">{{ inforList.balance }}</div>
        <div>
          <el-button type="primary" size="mini" class="WithdrawalFn" @click="toWithdraw()">提取</el-button>
        </div>
      </div>
      <div>
        <div class="container">
          <span class="text">课程剩余余额：</span>
        </div>
        <div>{{ inforList.courseBalance }}</div>
      </div>
    </el-row>

    <el-dialog v-show="dialogTableVisible" title="余额提取" :visible.sync="dialogTableVisible" style="margin: 10vh auto; width: 65vw">
      <div class="dialogMoney">
        <div style="display: flex; font-size: 18px; color: #000">
          <div class="text">当前余额：</div>
          <div>{{ inforList.balance }}</div>
        </div>

        <div style="display: flex">
          <div style="font-size: 18px; color: #000; line-height: 36px; margin-left: 1vw">提取：</div>
          <div>
            <el-input v-model="amount"></el-input>
          </div>
        </div>

        <!-- <div style="display:flex">
          <div style="font-size:18px;color:#000;line-height:36px">验证码：</div>
          <div style="display:flex">
            <el-input
              onkeyup="value=value.replace(/[^\d\.]/g,'')"
              maxlength="6"
              placeholder="请输入验证码"
              v-model="msgCheck"
            ></el-input>
            <el-button size="mini" class="telBtn" type="primary" @click="send" v-show="isSend" style="margin-left:.5vw">发送验证码</el-button>
            <el-button size="mini" class="telBtn" type="primary" v-show="!isSend" style="margin-left:.5vw">{{sendmsg}}</el-button>
          </div>
        </div> -->
      </div>
      <div style="margin: 2vw 0 0 40%">
        <el-button type="primary" style="margin-right: 1vw" size="small" @click="btnOk">确定</el-button>
        <el-button type="primary" size="small" @click="dialogTableVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { deliverWithdraw, getUserDetail } from '@/api/realName/information';
  import { getToken } from '@/utils/auth';
  import { codeApi, userCodeApi } from '@/api/realName/realName';
  export default {
    name: 'informationPerson',
    data() {
      return {
        second: 60, //获取验证码间隔时间60s
        timer: null, // 该变量是用来记录定时器的,防止点击的时候触发多个setInterval
        msgCheck: '', //短信验证码
        sendmsg: '发送验证码',
        isSend: true,
        dialogTableVisible: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        imageUrl: '',
        inforList: '',
        amount: '', //余额,
        signContractStatus: '' //判断是否实名的东西
      };
    },
    created() {
      this.initData();
    },
    methods: {
      // 去实名认证
      toAuthenFn() {
        // window.localStorage.setItem("telName", this.name);
        // this.$router.push({
        //   path: "/layout/personMe/realName",
        //   query: {
        //     name: name
        //   },
        // });

        let req = 'token=' + getToken();
        //需要编码两遍，避免出现+号等
        let encode = Base64.encode(Base64.encode(req));
        this.disabledF = false;
        if (window.location.host.split('.')[0] == 'deliver') {
          window.open('https://pay.dxznjy.com/pay/account?' + encode, '_blank');
        } else if (window.location.host.split('.')[0] == 'test-deliver') {
          window.open('https://test-pay.dxznjy.com/pay/account?' + encode, '_blank');
        } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
          window.open('http://*************:8000/pay/account?' + encode, '_blank');
        } else {
          let a = window.location.host.split('.')[0].slice(0, -1);
          let b = `https://${a}i.dxznjy.com/`;
          window.open(b + 'pay/account?' + encode, '_blank');
        }
      },
      async initData() {
        // 先获取usrcode
        let codeLin = await codeApi();
        let codecanshu = codeLin.data.find((item) => {
          if (item.userType == 'Member') {
            return item;
          }
        });
        // 再获取是否实名认证
        let res = await userCodeApi(codecanshu.userCode);
        this.signContractStatus = res.data.signContractStatus;
        let { data } = await getUserDetail();
        this.inforList = data;
      },
      async btnOk() {
        this.dialogTableVisible = false;
        let res = await deliverWithdraw(this.amount);
      },
      send() {
        this.isSend = false;
        let timer = 60;
        this.sendmsg = timer + 's';
        this.timeFun = setInterval(() => {
          --timer;
          this.sendmsg = timer + 's';
          if (timer == 0) {
            this.isSend = true;
            this.sendmsg = '重新发送';
            clearInterval(this.timeFun);
          }
        }, 1000);
      },
      toWithdraw() {
        let req = 'token=' + getToken();
        //需要编码两遍，避免出现+号等
        let encode = Base64.encode(Base64.encode(req));
        this.disabledF = false;
        // window.open("https://cm.ngrok.dxznjy.com/pay/account?" + encode, "_blank");
        // window.open("https://pay.dxznjy.com/pay/account?" + encode, "_blank");
        if (window.location.host.split('.')[0] == 'deliver') {
          window.open('https://pay.dxznjy.com/pay/account?' + encode, '_blank');
        } else if (window.location.host.split('.')[0] == 'test-deliver') {
          window.open('https://test-pay.dxznjy.com/pay/account?' + encode, '_blank');
        } else if (window.location.host.split('.')[0] == 'uat-deliver') {
          window.open('https://uat-dxpay.ngrok.dxznjy.com/pay/account?' + encode, '_blank');
        } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
          window.open('http://*************:8000/pay/account?' + encode, '_blank');
        } else {
          let a = window.location.host.split('.')[0].slice(0, -1);
          let b = `https://${a}i.dxznjy.com/`;
          window.open(b + 'pay/account?' + encode, '_blank');
        }
      },
      WithdrawalFn() {
        console.log(1);
      },
      photoBtn() {
        console.log(1);
      },
      handleAvatarSuccess(res, file) {
        this.imageUrl = URL.createObjectURL(file.raw);
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg';
        const isPNG = file.type === 'image/png';
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG && !isPNG) {
          this.$message.error('上传头像图片只能是 JPG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .WithdrawalFn {
    color: #fff;
    background-color: #169bd5;
    margin-left: 1vw;
    line-height: 63%;
    height: 90%;
    border-radius: 6px;
  }

  .container {
    direction: rtl;
    width: 6vw;
    margin-right: 0.5vw;
  }

  .text {
    direction: ltr;
    unicode-bidi: bidi-override;
  }

  .infomationAll div:nth-child(n + 2) {
    margin-bottom: 0.5vw;
    display: flex;
  }

  .buttonel {
    font-size: 16px;
    color: #37b7f5;
    border: none;
    box-shadow: none;
    background-color: #fff;
  }

  /* 图片上传用 */
  ::v-deep .avatar-uploader .el-upload {
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar {
    border-radius: 6px;
    width: 120px;
    height: 120px;
    display: block;
  }

  .dialogMoney > * {
    margin-top: 0.8vw;
  }

  ::v-deep .el-dialog__title {
    font-size: 22px;
    padding-left: 45%;
    font-weight: bold;
  }

  ::v-deep .el-dialog__body {
    padding: 0 2vw 1vw;
    height: 100%;
  }

  .telBtn {
    border-radius: 6px;
  }
</style>
