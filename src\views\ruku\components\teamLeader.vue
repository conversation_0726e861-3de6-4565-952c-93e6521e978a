<!--交付中心-入库管理-人员入库-学管师的页面-->
<template>
  <el-card v-if="isLeader">
    <el-form label-width="80px" class="frame" ref="queryData" :model="queryData">
      <el-row type="flex" justify="space-around">
        <el-col :span="3">
          <el-form-item label="姓名:" prop="name">
            <el-input v-model="queryData.name" placeholder="请输入" clearable size="mini"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="queryData.sex" clearable size="mini" placeholder="请选择">
              <el-option label="男" value="1"></el-option>
              <el-option label="女" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="isAdmin">
          <el-form-item label="交付中心编号:" label-width="120px">
            <!-- <el-input v-model="queryData.merchantCode" size="small" placeholder=""></el-input> -->
            <el-input v-model="queryData.merchantCode" @change="changeInput" size="mini" placeholder=""></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5" v-if="isAdmin">
          <el-form-item label="交付中心名称:" label-width="120px">
            <!-- <el-input v-model="queryData.merchantName" size="small" placeholder=""></el-input> -->
            <el-select v-el-select-loadmore="handleLoadmore" size="mini" :loading="loadingShip" remote clearable
              v-model="queryData.merchantName" filterable reserve-keyword placeholder="请选择" @input="changeMessage"
              @blur="clearSearchRecord" @change="changeTeacher">
              <el-option v-for="(item,index) in option" :key="index" :label="item.deliverMerchantName"
                :value="item.deliverMerchantName">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="联系方式:" prop="mobile">
            <el-input v-model="queryData.mobile" clearable placeholder="请输入" size="mini"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col style="margin-left:71vw"> -->
        <el-col :span="4">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
      <!-- <el-row type="flex" justify="end">
  
      </el-row> -->
    </el-form>
  </el-card>
</template>

<script>

import ls from "@/api/sessionStorage";
import { deliverlist } from "@/api/peizhi/peizhi"
export default {
  name: 'teamLeader',
  props: {
    isLeader: {
      // 定义接收的类型 还可以定义多种类型 [String, Undefined, Number]
      // 如果required为true,尽量type允许undefined类型，因为传递过来的参数是异步的。或者设置默认值。
      type: Boolean,
      // 定义是否必须传
      required: true
    }
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function () {
          //临界值的判断滑动到底部就触发
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      option: [],
      optionTotal: 0,
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
      },
      queryData: {
        merchantCode: '',
        merchantName: '',
        name: "",
        sex: "",
        mobile: "",
        pageNum: 1,
        pageSize: 10
      },
      leaderData: {
        totalItems: '',
        data: ''
      },
    };
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin';
    if (this.isAdmin) {
      this.getTeacherList()
    }
  },
  methods: {
    changeInput(e) {
      console.log(e)
      if (!!e) {
        let arr = this.option.filter(i => i.deliverMerchantCode == e)
        this.queryData.merchantName = arr[0].deliverMerchantName
      }
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        if (this.selectObj.pageNum == this.optionTotal) return//节流防抖
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    changeMessage() {
      this.$forceUpdate();
    },
    // 获取交付中心
    async getTeacherList() {
      let allData = await deliverlist(this.selectObj);
      this.option = this.option.concat(allData.data.data);
      this.optionTotal = Number(allData.data.totalPage)
    },
    clearSearchRecord() {
      setTimeout(() => {
        if (this.queryData.deliverMerchantName == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        }
      }, 500)
      this.$forceUpdate();
    },
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.queryData.merchantCode = ''
        this.selectObj.pageNum = 1;
        this.getTeacherList();
      } else {
        let arr = this.option.filter(i => i.deliverMerchantName == e)
        this.queryData.merchantCode = arr[0].deliverMerchantCode
      }
    },
    initData() {
      this.$emit('queryFn', this.queryData)
    },
    //重置
    rest() {
      this.$refs.queryData.resetFields();
      this.queryData = {
        merchantCode: '',
        merchantName: '',
        name: "",
        sex: "",
        mobile: "",
        pageNum: 1,
        pageSize: 10
      }
      this.$emit('queryFn', this.queryData)
    },
  }
};
</script>

<style>
</style>
