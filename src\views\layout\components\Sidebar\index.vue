<template>
  <!--路由菜单-->
  <div :class="{ 'has-logo': showLogo }">
    <div>21131</div>
    <!--  -->
    <logo
      v-if="JlbInfo && JlbInfo.customName && JlbInfo.logoEnable"
      :collapse="isCollapse"
    />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in permission_routers"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div
      class="dxlogo"
      v-if="!isCollapse && JlbInfo && JlbInfo.customName && JlbInfo.logoEnable"
    ></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/styles/variables.scss";
import ls from "@/api/sessionStorage";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters(["permission_routers", "sidebar", "JlbInfo"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  created() {
    let rolesVale = ls.getItem("rolesVal");
    // if (rolesVale !== "DeliveryCenter" && rolesVale !== "admin" && rolesVale !== "learnManager"&& rolesVale !== "JiaofuManager") {
    //   this.permission_routers[5].children.splice(1, 1)
    //   this.permission_routers.splice(7, 1)
    // }
    // if(rolesVale !== "admin"&& rolesVale !== "JiaofuManager"){
    //   console.log(this.permission_routers);
    //   this.permission_routers.splice(5, 1)
    //   this.permission_routers[7].children.splice(1, 1)
    // }
  },
};
</script>
<style scoped lang="scss">
.dxlogo {
  width: 126px;
  height: 38px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 16px;
  background: url("../../../../assets/<EMAIL>") no-repeat;
  background-size: contain;
}
</style>
