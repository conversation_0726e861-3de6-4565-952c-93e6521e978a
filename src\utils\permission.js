import store from '@/store'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export default function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    var perms = store.getters.perms
    var roles = store.getters.roles
    roles = roles.concat(perms)
    let hasPermission = false;
    out: for (let i = 0; i < value.length; i++) {
      for (let j = 0; j < roles.length; j++) {
        if (roles[j].val == value[i]) {
          hasPermission = true;
          break out;
        }
      }
    }
    if (!hasPermission) {
      return false
    }
    return true
  } else {
    return false
  }
}
