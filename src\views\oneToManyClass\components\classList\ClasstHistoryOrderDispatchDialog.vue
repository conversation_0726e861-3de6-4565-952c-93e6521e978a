<!-- 一对多-班级列表-历史派单记录弹框 -->
<template>
  <!-- 历史派单记录弹框 -->
  <el-dialog title="历史派单记录" :visible.sync="historyOrderDispatchDialogVisible" width="30%" center>
    <div style="overflow: auto; margin: 30px 0; height: 400px" v-loading="loading">
      <el-steps direction="vertical" :active="0" :space="200" v-if="valueData.length > 0">
        <el-step v-for="(item, index) in valueData" :title="item.time" :key="'historys-order-dispatch-' + index" icon="iconfont icon-luyin">
          <template slot="description">
            <div style="white-space: pre-wrap">{{ item.history }}</div>
          </template>
          <template slot="icon">
            <i class="el-icon-info" v-if="index == 0" style="font-size: 24px"></i>
            <i class="el-icon-success" v-else style="font-size: 24px"></i>
          </template>
        </el-step>
      </el-steps>
      <div v-else style="height: 400px; text-align: center; font-size: 23px; line-height: 400px">暂无数据</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" style="width: 100px" @click="historyOrderDispatchDialogVisible = false">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { getHistory } from '@/api/oneToManyClass/classList';
  export default {
    name: 'ClasstHistoryOrderDispatchDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      classId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        loading: false,
        valueData: []
      };
    },
    computed: {
      historyOrderDispatchDialogVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      }
    },
    watch: {
      historyOrderDispatchDialogVisible(val) {
        if (val) {
          this.loading = true;
          this.getHistoryOrderDispatchRecord();
        }
      }
    },
    methods: {
      getHistoryOrderDispatchRecord() {
        getHistory(this.classId)
          .then((res) => {
            this.valueData = res.data;
            setTimeout(() => {
              this.loading = false;
            }, 500);
          })
          .catch(() => {
            setTimeout(() => {
              this.historyOrderDispatchDialogVisible = false;
              this.loading = false;
            }, 500);
          });
      }
    }
  };
</script>

<style></style>
