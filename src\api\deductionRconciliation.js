//deductionRconciliation
//'合同扣款对账'相关接口api
import request from '@/utils/request';

export default {
    //合同扣款对账-列表
  queryList({currentPage,size}) {
    return request({
      url: '/znyy/fundConversionEsignMonthPay/page',
      method: 'get',
      params: {
        pageNum: currentPage,
        pageSize: size
      }
    });
  },
  //门店详细数据
  queryStoreDetails({currentPage,size},id) {
    return request({
      url: '/znyy/fundConversionEsignMonthPay/getDetails',
      method: 'get',
      params: {
        pageNum: currentPage,
        pageSize: size,
        id:id
      }
    });
  },
  //去支付
  goPay(id) {
    return request({
      url: '/znyy/fundConversionEsignMonthPay/monthPay',
      method: 'get',
      params: {
        id:id
      }
    });
  }
};