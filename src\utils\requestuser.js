import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'

// var a=Cookies.get('name');
// console.log(a)

// create an axios instance
const service = axios.create({
  // baseURL: 'https://api.ngrok.dxznjy.com/',
  // baseURL: 'https://jzcpay.ngrok.dxznjy.com/',
  // baseURL: 'http://192.168.5.66:8081/',
  //baseURL: 'http://wy.ngrok.dxznjy.com/',
  // baseURL: 'https://dxcs.ngrok.dxznjy.com/',
  baseURL: 'https://gateway.dxznjy.com/',

  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 20000, // request timeout
  withCredentials: true // 使前台能够保存cookie
})

// request interceptor
service.interceptors.request.use(config => {
  // do some
  // console.log(Cookies.get('tokenuser'))
  config.headers['www-cid'] = 'dx_znyy_resource'
  // config.headers['x-www-iap-assertion'] = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  //   if (Cookies.get('tokenuser')) {
  //     config.headers['x-www-iap-assertion'] = Cookies.get('tokenuser')
  //   }
  return config
},
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */

  /**
   *
   *     if (res.code !== 20000) {
   Message({
   message: res.message || 'Error',
   type: 'error',
   duration: 5 * 1000
   })

   // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
   if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
   // to re-login
   MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
   confirmButtonText: 'Re-Login',
   cancelButtonText: 'Cancel',
   type: 'warning'
   }).then(() => {
   store.dispatch('user/resetToken').then(() => {
   location.reload()
   })
   })
   }
   return Promise.reject(new Error(res.message || 'Error'))
   */
  response => {
    const res = response.data
    // if(response.data=="Blob"){
    //   return res
    // }
    // if the custom code is not 20000, it is judged as an error.

    if (res.code == 20000 || response.status === 200) {
      return res
    } else {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // to re-login
        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
          confirmButtonText: 'Re-Login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    }
  },
  error => {
    // if (401 === 401) {
    //   router.replace('/login')
    //   removeToken()
    //   resetRouter()
    //   Cookies.remove('tokenuser')
    // }
    // Message({ message: error.response + error.response.data.message, type: 'error', duration: 3000 })
    // return Promise.reject('error')
  }
)

export default service
