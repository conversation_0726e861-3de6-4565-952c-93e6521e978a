/**
 * 单词水平相关接口
 */
import request from '@/utils/request'

export default {
  schoolPage(pageNum, pageSize, data) {
    return request({
      url: '/znyy/school/page/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  convertToOperationsSchool(merchantCode) {
    return request({
      url: '/znyy/school/update/school/type',
      method: 'PUT',
      params:{'merchantCode':merchantCode}
    })
  },
  openEnable(id,isEnable) {
    return request({
      url: '/znyy/school/update/enable/status/' + id+"/"+isEnable,
      method: 'PUT',
    })
  },

  schoolCheck(data){
    return request({
      url: '/znyy/school/update/check/status',
      method: 'POST',
      data
    })
  },

  schoolDetail(id){
    return request({
      url: '/znyy/school/detail/'+id,
      method: 'GET',
    })
  },
  schoolUpdateLogin(data){
    return request({
      url: '/znyy/bSysConfig/update/account',
      method: 'PUT',
      data
    })
  },
  //直营门店新增
  addSchool(data) {
    return request({
      url: '/znyy/school/save',
      method: 'POST',
      data
    })
  },

  // 直营门店流程图开启
  startAndTakeUserTaskByAddSchool(data) {
    return request({
      url: '/activiti/flowOnlineOperation/startAndTakeUserTask/newAddDirectSchool',
      method: 'POST',
      data
    })
  },

  getCurrentAdmin(){
    return request({
      url: '/znyy/school/currentAdmin',
      method: 'GET',
    })
  },
  exportSchool(data){
    return request({
      url: '/znyy/school/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })
  },
  openAccountAmount(id){
    return request({
      url: '/znyy/school/openAccountAmount/' + id,
      method: 'PUT',
    })
  }

}
