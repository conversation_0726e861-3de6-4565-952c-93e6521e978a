<!--交付中心-试课学员管理-试课列表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="105px" ref="searchNum" :model="searchNum" :inline="true">
        <!-- 1 -->
        <el-row style="margin-top: 1.3vw">
          <el-col :span="8" :xs="24">
            <el-form-item label="姓名:" prop="studentName">
              <el-input v-model="searchNum.studentName" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="订单号:" prop="orderNo">
              <el-input v-model="searchNum.orderNo" clearable placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="教练老师：" prop="teacherId">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                :filter-method="filterValue"
                clearable
                v-model="searchNum.teacherId"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="推荐人:" prop="referrerNameOrPhone">
              <el-input v-model="searchNum.referrerNameOrPhone" clearable placeholder="请输入推荐人姓名或手机号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24" v-if="roles[0].val == 'DeliveryCenter' && dataShow">
            <el-form-item label="扣绩效:" prop="isDeductWage">
              <el-select v-model="searchNum.isDeductWage" clearable placeholder="请选择">
                <el-option v-for="item in deductionStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="isAdmin" :xs="24">
            <el-form-item label="交付中心编号:" prop="deliverMerchant">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isAdmin" :xs="24">
            <el-form-item label="交付中心名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isAdmin" :xs="24">
            <el-form-item label="课程状态:" prop="courseStatus">
              <el-select v-model="searchNum.courseStatus" clearable placeholder="请选择">
                <el-option label="待排课" value="1"></el-option>
                <el-option label="已排课" value="2"></el-option>
                <el-option label="已完成" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="8" v-if="isAdmin" :xs="24">
            <el-form-item label="支付状态:" prop="payStatus">
              <el-select v-model="searchNum.payStatus" clearable placeholder="请选择">
                <el-option label="未支付" value="1"></el-option>
                <el-option label="已支付" value="2"></el-option>
                <el-option label="已退款" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8" :xs="24">
            <el-form-item label="时间:" prop="timeAll">
              <el-date-picker
                v-model="timeAll"
                format="yyyy-MM-dd HH:mm:ss"
                style="width: 360px"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-button size="small" type="primary" icon="el-icon-search" @click="initData01">查询</el-button>
            <el-button size="small" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-col>
          <!-- <el-col :span="8" v-if="isAdmin" :xs="24">
            <el-button size="small" type="primary" icon="el-icon-search" @click="initData01">查询</el-button>
            <el-button size="small" type="primary" icon="el-icon-search" @click="rest()">重置</el-button>
          </el-col> -->
        </el-row>
      </el-form>
    </el-card>
    <el-card class="frame" shadow="never">
      <!-- <el-row type="flex" justify="end">
        <span style="margin-right:6vw">
          <el-button  @click="exportExcel">导出</el-button>
        </span>
      </el-row> -->
      <el-row type="flex" justify="">
        <span style="margin-right: 3vw" v-if="isAdmin">
          <!-- <el-button type="warning" plain round @click="openReservation">预约人数设置</el-button> -->
          <el-button type="warning" plain round @click="openReservation">预约人数查看</el-button>
        </span>
        <span style="margin-right: 3vw" v-if="isAdmin">
          <el-button type="success" plain round @click="openCreate">创建试课单</el-button>
        </span>
        <span style="display: flex; align-items: center; margin-right: 3vw" v-if="isAdmin">
          <el-button plain round type="primary" @click="allSelectTerminal">批量支付</el-button>
        </span>
        <el-button type="primary" @click="headerList()">列表显示属性</el-button>
      </el-row>
    </el-card>

    <el-table
      :data="luyouclassCard"
      v-loading="tableLoading"
      style="width: 100%"
      id="out-table"
      ref="grpNickNameTable"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="isAdmin" prop="date" width="50" type="selection" align="center" :selectable="handleDisable"></el-table-column>
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value != 'operate' ? (item.value == 'lastStudyTime' || item.value == 'expectTime' ? 200 : item.value == 'remark' ? 300 : '120') : 350"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="studentFn(row)" style="margin-bottom: 10px">学员信息表</el-button>
            <el-button type="primary" size="mini" @click="changeClassFn(row)" style="margin-bottom: 10px">咨询记录表</el-button>
            <el-button v-if="row.payStatus != 3" type="primary" size="mini" @click="expericeClass(row)" style="margin-bottom: 10px">试课排课</el-button>
            <el-button type="primary" size="mini" @click="dataLookFn(row)" style="margin-bottom: 10px">数据查看</el-button>
            <el-button type="primary" size="mini" @click="edit(row)" style="margin-bottom: 10px">编辑</el-button>
            <el-button type="warning" size="mini" @click="deduct(row)" v-if="roles[0].val == 'DeliveryCenter' && row.canDeductWage" :disabled="!row.canDeductWage">
              扣绩效
            </el-button>
          </div>

          <div v-else-if="item.value == 'courseStatus'">
            <el-tag v-if="row.courseStatus == '已排课'">已排课</el-tag>
            <el-tag type="success" v-if="row.courseStatus == '已完成'">已完成</el-tag>
            <el-tag type="info" v-if="row.courseStatus == '已退款'">已退款</el-tag>
            <el-tag type="warning" v-if="row.courseStatus == '待排课'">待排课</el-tag>
          </div>

          <div v-else-if="item.value == 'name' && isAdmin">
            <el-button v-if="row.deliverMerchant == 'A0001' && row.payStatus != 3" type="primary" size="mini" @click="editFn(row, false)">指派</el-button>
            <div v-if="row.deliverMerchant != 'A0001'">
              <span
                v-if="row.isNewAssign == 0 && row.payStatus != 3 && row.deliverMerchant !== '门店自行交付' && (row.courseStatus == '待排课' || row.courseStatus == '已排课')"
                style="color: #46a6ff; cursor: pointer"
                @click="editFn(row, true)"
              >
                {{ row.deliverName }}
              </span>
              <span v-else>{{ row.deliverName }}</span>
            </div>
          </div>

          <div v-else-if="item.value == 'experienceObject'">
            <span>{{ row.experienceObject != 0 ? (row.experienceObject == 1 ? 'B端' : 'C端') : '无' }}</span>
          </div>

          <div v-else-if="item.value == 'clientName'">
            <span>{{ row.experienceObject == 1 && row.clientName ? row.clientName : '-' }}</span>
          </div>

          <div v-else-if="item.value == 'expectTime'">
            <div v-if="row.expectTime">
              <el-button v-if="!row.lastStudyTime" type="danger" plain size="mini">
                {{ row.expectTime }}
              </el-button>
              <el-button v-else type="success" plain size="mini">
                {{ row.lastStudyTime }}
              </el-button>
            </div>
          </div>

          <div v-else-if="item.value == 'isSend' && isAdmin">
            <span>{{ row.isSend ? '是' : '否' }}</span>
          </div>

          <div v-else-if="item.value == 'payStatus'">
            <el-tag type="danger" v-if="row.payStatus === 1">未支付</el-tag>
            <el-tag type="success" v-if="row.payStatus === 2">已支付</el-tag>
            <el-tag type="info" v-if="row.payStatus === 3">已退款</el-tag>
          </div>

          <div v-else-if="item.value == 'wage'">
            <span :style="{ color: row.isDeductWage ? '#ff6d6d' : '' }">{{ row.wage || 0 }}</span>
          </div>

          <div v-else-if="item.value == 'remark'">
            <el-tooltip class="item" effect="dark" :content="row.remark" placement="top">
              <div class="hidden-row">{{ row.remark }}</div>
            </el-tooltip>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <!-- 指派弹框 -->
    <el-dialog :visible.sync="tkedialog" :width="screenWidth > 1300 ? '60%' : '90%'" :title="isREAssign ? '重新指派交付中心' : '指派交付中心'">
      <el-form ref="form" :model="assign" label-width="150px">
        <el-form-item v-if="!isREAssign" label="应属交付中心" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="assign.belongDeliverName" />
        </el-form-item>
        <el-form-item label="重新指派交付中心">
          <el-cascader
            v-model="deliverMerchantCode"
            :options="assign.deliverList"
            filterable
            :props="{
              label: 'deliverMerchantName',
              value: 'deliverMerchantCode'
            }"
            clearable
          ></el-cascader>
        </el-form-item>

        <!-- <el-form-item v-if="!isREAssign" label="是否发送红包" prop="isSend">
          <el-radio-group v-model="assign.isSend">
            <el-radio label="true">是</el-radio>
            <el-radio label="false">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="submitAss">确定</el-button>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="classList" :width="screenWidth > 1300 ? '60%' : '90%'" title="创建试课单" :before-close="closeClass" :close-on-click-modal="false">
      <!-- 无推荐人 -->
      <div>
        <el-form ref="addForm" :rules="rules" :model="addForm" :label-width="screenWidth > 1300 ? '150px' : '120px'">
          <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-input v-model="addForm.realName" />
          </el-form-item>
          <el-form-item label="学员手机号" prop="phone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-input v-model="addForm.phone" />
          </el-form-item>
          <el-form-item label="来源备注" prop="studentSource" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-input v-model="addForm.studentSource" />
          </el-form-item>
          <el-form-item label="学员年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select v-model="addForm.grade" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="期望课程时间" prop="expectTime">
            <!-- <el-date-picker v-model="addForm.expectTime" type="datetime" placeholder="选择日期"
                  :style="{ width: screenWidth > 1300 ? '50%' : '100%' }" format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker> -->
            <div style="display: flex">
              <el-date-picker v-model="expectTime1" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
              <el-time-select
                placeholder="选择时间"
                format="HH:mm:ss"
                v-model="expectTime2"
                :picker-options="{
                  step: '00:30',
                  start: '08:30',
                  end: '23:30'
                }"
                @change="changeTime"
              ></el-time-select>
            </div>
          </el-form-item>
          <el-form-item label="学员所在区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-cascader v-model="addForm.address" :options="options02" @change="handleChange" />
          </el-form-item>

          <el-form-item label="学员性别" prop="gender" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-radio-group v-model="addForm.gender">
              <el-radio label="1">男</el-radio>
              <el-radio label="2">女</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="ischeck" label="" prop="" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-input v-model="addForm.refName" style="border: none" :style="{ width: screenWidth > 1300 ? '250px' : '100%' }" readonly="readonly" />
          </el-form-item>
          <el-form-item label="指派交付中心" prop="deliverMerchant" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-select v-model="addForm.deliverMerchant" placeholder="请选择">
              <el-option v-for="item in allDelivers" :key="item.deliverMerchantCode" :label="item.deliverMerchantName" :value="item.deliverMerchantCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="体验需求" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="addForm.remark"></el-input>
          </el-form-item>
        </el-form>
        <!-- 
        <el-dialog title="" :visible.sync="codeShow" width="15%" :before-close="codeDialogClose" append-to-body
          :close-on-click-modal=false :show-close=false class="codeDialog" @open="getQrCode">
          <div class="image">
            <el-image style="width: 150px; height: 150px;margin-bottom:20px;" :src="qrCode" fit="fill"></el-image>
            <div class="color">
              <span>请让</span>
              <span style="color: red">家长</span>
              <span>扫一扫，添加上课小助理</span>
            </div>
            <div class="color">否则无法提交试课单</div>
          </div>
          <el-row :gutter="20" type="flex" justify="center">
            <el-button size="mini" style="width:100px;border-radius:10px;" @click="codeDialogClose">取 消</el-button>
            <el-button size="mini" style="width:100px;border-radius:10px;" type="primary"
              @click="downloadByBlob">保存二维码</el-button>
          </el-row>

        </el-dialog> -->
        <el-row type="flex" justify="center" style="margin-top: 2.5vh">
          <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="submitAdd('addForm', 0)">立即创建</el-button>
          <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeClass">取消</el-button>
        </el-row>
      </div>
      <!--           <el-tabs v-model="isReffer" type="card" @tab-click="handleTabsEdit">
            <el-tab-pane v-for="(item, index) in editableTabs" :key="index" :label="item.title" :name="item.name">
              无推荐人
              <div v-show="isReffer == '1'">
                <el-form ref="addForm" :rules="rules" :model="addForm"
                  :label-width="screenWidth > 1300 ? '150px' : '120px'">
                  <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addForm.realName" />
                  </el-form-item>
                  <el-form-item label="学员手机号" prop="phone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addForm.phone" />
                  </el-form-item>
                  <el-form-item label="来源备注" prop="studentSource" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addForm.studentSource" />
                  </el-form-item>
                  <el-form-item label="学员年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-select v-model="addForm.grade" placeholder="请选择">
                      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="期望课程时间" prop="expectTime">
                   <el-date-picker v-model="addForm.expectTime" type="datetime" placeholder="选择日期"
                      :style="{ width: screenWidth > 1300 ? '50%' : '100%' }" format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker> 
                    <div style="display:flex">
                      <el-date-picker v-model="expectTime1" type="date" value-format="yyyy-MM-dd"
                        placeholder="选择日期"></el-date-picker>
                      <el-time-select placeholder="选择时间" format="HH:mm:ss" v-model="expectTime2" :picker-options="{
          step: '00:30',
          start:'08:30',
          end:'23:30'
        }" @change="changeTime">
                      </el-time-select>
                    </div>
                  </el-form-item>
                  <el-form-item label="学员所在区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-cascader v-model="addForm.address" :options="options02" @change="handleChange" />
                  </el-form-item>

                  <el-form-item label="学员性别" prop="gender" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-radio-group v-model="addForm.gender">
                      <el-radio label="1">男</el-radio>
                      <el-radio label="2">女</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item v-if="ischeck" label="" prop="" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addForm.refName" style="border: none;"
                      :style="{ width: screenWidth > 1300 ? '250px' : '100%' }" readonly="readonly" />
                  </el-form-item>
                  <el-form-item label="指派交付中心" prop="deliverMerchant"
                    :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-select v-model="addForm.deliverMerchant" placeholder="请选择">
                      <el-option v-for="item in allDelivers" :key="item.deliverMerchantCode"
                        :label="item.deliverMerchantName" :value="item.deliverMerchantCode">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="备注" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="addForm.remark">
                    </el-input>
                  </el-form-item>
                </el-form>

                <el-dialog title="" :visible.sync="codeShow" width="15%" :before-close="codeDialogClose" append-to-body
                  :close-on-click-modal=false :show-close=false class="codeDialog" @open="getQrCode">
                  <div class="image">
                    <el-image style="width: 150px; height: 150px;margin-bottom:20px;" :src="qrCode" fit="fill"></el-image>
                    <div class="color">
                      <span>请让</span>
                      <span style="color: red">家长</span>
                      <span>扫一扫，添加上课小助理</span>
                    </div>
                    <div class="color">否则无法提交试课单</div>
                  </div>
                  <el-row :gutter="20" type="flex" justify="center">
                    <el-button size="mini" style="width:100px;border-radius:10px;" @click="codeDialogClose">取 消</el-button>
                    <el-button size="mini" style="width:100px;border-radius:10px;" type="primary"
                      @click="downloadByBlob">保存二维码</el-button>
                  </el-row>

                </el-dialog>
                <el-row type="flex" justify="center" style="margin-top: 2.5vh">
                  <el-button type="primary" style="margin-right:1.5vw;" size="small" @click="submitAdd('addForm', 0)">
                    立即创建
                  </el-button>
                  <el-button type="primary" style="margin-right:1.5vw;" size="small" @click="closeClass">
                    取消
                  </el-button>
                </el-row>
              </div>

              有推荐人
              <div v-show="isReffer == '2'">
                <el-form ref="addFormReffer" :rules="rulesReffer" :model="addFormReffer"
                  :label-width="screenWidth > 1300 ? '150px' : '120px'">
                  <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addFormReffer.realName" />
                  </el-form-item>
                  <el-form-item label="家长联系方式" prop="phone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addFormReffer.phone" />
                  </el-form-item>

                  <el-form-item label="推荐人姓名" prop="referrerName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addFormReffer.referrerName" />
                  </el-form-item>
                  <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-input v-model="addFormReffer.referrerPhone"
                      :style="{ width: screenWidth > 1300 ? '250px' : '100%' }" />
                  </el-form-item>
                  <el-form-item label="是否发送红包" prop="sendPacket" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
                    <el-radio-group v-model="addFormReffer.sendPacket">
                      <el-radio label="true">是</el-radio>
                      <el-radio label="false">否</el-radio>
                    </el-radio-group>
                  </el-form-item>

                </el-form>
                <el-row type="flex" justify="center" style="margin-top: 2.5vh">
                  <el-button type="primary" style="margin-right:1.5vw;" size="small" @click="submitAdd('addFormReffer', 1)">
                    立即创建
                  </el-button>
                  <el-button type="primary" style="margin-right:1.5vw;" size="small" @click="closeClass">
                    取消
                  </el-button>
                </el-row>
              </div>

            </el-tab-pane>
          </el-tabs>
           -->
    </el-dialog>

    <!-- 编辑弹框 -->
    <el-dialog :visible.sync="editList" :width="screenWidth > 1300 ? '60%' : '90%'" title="编辑" @close="closeEdit">
      <el-form ref="editForm" :rules="editRules" :model="editForm" :label-width="screenWidth > 1300 ? '150px' : '110px'">
        <el-form-item label="学员姓名" prop="realName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.realName" />
        </el-form-item>
        <el-form-item label="区域" prop="address" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-cascader v-model="editForm.address" :options="options02" @change="handleChanges" />
        </el-form-item>
        <el-form-item label="英语分数" prop="score" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.score" placeholder="请输入" />
        </el-form-item>
        <!-- <el-form-item label="期望试课时间" prop="expectTime" style="width: 50%;">
          <el-date-picker v-model="editForm.expectTime" type="datetime" placeholder="选择日期时间" @change="getExpectTime">
          </el-date-picker>
          <div style="color: #999;">填写限制24小时之后</div>
        </el-form-item> -->
        <el-form-item label="预约试课时间" prop="expectTime">
          <div style="display: flex; align-items: center">
            <el-form-item prop="dateslot">
              <trialDate v-if="addVisible" ref="triaTime" @onChild="onChild" :dateTime="dateTime"></trialDate>
            </el-form-item>
            <el-form-item prop="timeslot">
              <el-select v-model="timeslot" placeholder="请选择试课时间段" style="margin-left: 10px" @change="changeTimeslot">
                <el-option v-for="item in timeList" :key="item.id" :label="item.startTime + '~' + item.endTime" :value="item.startTime"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="试课对象" prop="experienceObject" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.experienceObject">
            <el-radio :label="1">B端</el-radio>
            <el-radio :label="2">C端</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="客户姓名"
          prop="clientName"
          :style="{ width: screenWidth > 1300 ? '50%' : '100%' }"
          v-if="editForm.experienceObject == 1 || shows"
          :rules="editForm.experienceObject == 1 || shows ? editRules.target : [{ required: false }]"
        >
          <el-input v-model="editForm.clientName" placeholder="请输入" />
        </el-form-item>
        <!-- <el-form-item label="是否需要咨询" prop="residentialSchool" style="width: 50%;">
          <el-radio-group v-model="editForm.residentialSchool" @input="inputAsk">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="当前咨询师" prop="counselor" style="width: 50%;" v-if="editForm.residentialSchool==0|| shows"  :rules="editForm.residentialSchool==0|| shows?editRules.target:[{ required: false}]">
          <el-input v-model="editForm.counselor" placeholder="请输入咨询师姓名" :rows="5" />
        </el-form-item> -->
        <el-form-item label="咨询师" prop="counselor" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.counselor">
            <el-radio :label="'1'">上级推荐人</el-radio>
            <el-radio :label="'0'">自己</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="英语课外辅导" prop="classInstruction" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.classInstruction">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="体验需求" prop="remark" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.remark" type="textarea" placeholder="请输入" :rows="5" />
        </el-form-item>
        <el-form-item label="年级" prop="grade" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-select v-model="editForm.grade" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐人姓名" prop="referrerName" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerName" />
        </el-form-item>
        <el-form-item label="推荐人手机号" prop="referrerPhone" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input v-model="editForm.referrerPhone" :style="{ width: screenWidth > 1300 ? '83%' : '100%' }" />
        </el-form-item>
        <el-form-item label="提交时间" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-date-picker readonly v-model="editForm.createTime" type="datetime" placeholder="选择日期时间" style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="客户类型" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.customerType">
            <el-radio label="1">家长</el-radio>
            <el-radio label="2">意向客户</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="是否需要咨询" prop style="width: 50%;">
          <el-radio-group v-model="editForm.isAsk">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="是否成交" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.isDeal">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="成交金额" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-input-number :min="0" :max="9999999" v-model="editForm.dealPrice" style="width: 150px" />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="submitEdit('editForm')">确定</el-button>
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeEdit">取消</el-button>
      </el-row>
    </el-dialog>
    <ExperienceClassDialog @TrialClassLister="TrialClassLister" :TrialClassStyle="TrialClassStyle" :direction="direction" ref="ExperienceClassDialog" @updateList="initData" />
    <dataLookDialog @dataLooker="dataLooker" :dataLookerStyle="dataLookerStyle" :direction="direction" ref="dataLookDialog" />

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />

    <!-- 预约人数设置 -->
    <el-dialog title="预约人数设置" :visible.sync="dialogVisible" :width="screenWidth > 1300 ? '35%' : '80%'" center>
      <div :class="screenWidth > 1300 ? 'grid-container' : 'grid-container-one'">
        <div v-for="(item, index) in timeList" :key="index">
          <div style="display: flex; align-items: center">
            <span style="margin-right: 20px">{{ item.startTime + ' ~' + item.endTime }}</span>
            <!-- <el-input-number v-model="item.canReserveNum" :min="0" label="请选择" size="mini"></el-input-number> -->
            <el-input-number v-model="item.canReserveNum" :min="0" label="请选择" size="mini" disabled :controls="false"></el-input-number>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <!-- <el-button type="primary" @click="setupReservationTime">确 定</el-button> -->
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 扣绩效 -->
    <el-dialog title="扣绩效" :visible.sync="dialogPerformance" width="30%" :close-on-click-modal="false">
      <el-form :model="deduction" :rules="deductionRules" ref="deduction" label-width="100px" class="demo-ruleForm">
        <el-form-item label="扣款金额：" prop="deductWage">
          <div style="display: flex">
            <el-input type="number" :min="0" v-model="deduction.deductWage" placeholder="请输入扣款金额"></el-input>
            <span style="margin-left: 20px">元</span>
          </div>
        </el-form-item>
        <el-form-item label="扣款原因：" prop="deductReason">
          <el-input type="textarea" v-model="deduction.deductReason" placeholder="请输入扣款原因" :rows="5"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm">取 消</el-button>
          <el-button type="primary" @click="getDeduction">确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import {
    selAllTeacher,
    allDeliver,
    batchPayExperience,
    belongDeliverAndAllDeliver,
    check,
    createCourse,
    getExperienceList,
    getNoPayExperienceList,
    GradeType,
    submitAssign,
    update,
    updateDetail,
    selAllExperienceUsableTime,
    setExperienceUsableTimeNum,
    deductingPerformance,
    getScheduleTime
  } from '@/api/studentClass/changeList';
  import { getDeliverCodes } from '@/api/FinanceApi/assistantWages';
  import ExperienceClassDialog from './components/ExperienceClassDialog.vue';
  import HeaderSettingsDialog from './components/HeaderSettingsDialog.vue';
  import dataLookDialog from './components/dataLookDialog.vue';
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';
  import { CodeToText, regionData, TextToCode } from 'element-china-area-data';
  import FileSaver from 'file-saver';
  import XLSX from 'xlsx';
  import ls from '@/api/sessionStorage';
  import { getToken } from '@/utils/auth';
  import dayjs from 'dayjs';
  import trialDate from './components/trialDate.vue';
  import { mapGetters } from 'vuex';
  import moment from 'moment';
  //导入文件
  moment.locale('zh-cn');

  export default {
    name: 'TrialClassListNopay',
    components: {
      ExperienceClassDialog,
      dataLookDialog,
      trialDate,
      HeaderSettingsDialog
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },

    data() {
      var validateTime = (rule, value, callback) => {
        if (!this.expectTime1) {
          callback(new Error('请选择日期!'));
        } else if (!this.expectTime2) {
          callback(new Error('请选择时间!'));
        } else {
          callback();
        }
      };
      return {
        screenWidth: window.screen.width,
        ischeck: false,
        allDelivers: [],
        options: {},
        options02: regionData,
        selectedOptions: [],
        form: '',
        askOption: [
          {
            value: '1',
            label: '是'
          },
          {
            value: '2',
            label: '否'
          }
        ],
        isReffer: '1',
        editableTabs: [
          {
            title: '无推荐人',
            name: '1'
          },
          {
            title: '有推荐人',
            name: '2'
          }
        ],
        editForm: {},
        classList: false,
        editList: false,
        expectTime1: '',
        expectTime2: '',
        addForm: {
          realName: '',
          phone: '',
          studentSource: '',
          grade: '',
          expectTime: '',
          address: '',
          gender: '',
          referrerName: '',
          referrerPhone: '',
          deliverMerchant: '',
          refName: ''
        },
        addFormReffer: {
          realName: '',
          phone: '',
          referrerName: '',
          grade: 0,
          referrerPhone: '',
          sendPacket: '' //默认不发送红包
        },
        assign: {},
        tableLoading: false, //列表转圈是否开启
        lookstyle: false,
        islook: false,
        TrialClassStyle: false, //抽屉状态 1
        dataLookerStyle: false,
        deliverMerchantID: '',
        deliverMerchantCode: '',
        direction: 'rtl', //超哪边打开
        searchNum: {
          studentName: '',
          studentCode: '',
          orderNo: '',
          teacherId: '',
          referrerNameOrPhone: '',
          isDeductWage: '',
          deliverMerchant: '',
          deliverName: '',
          courseStatus: '',
          curriculumId: '',
          // payStatus: "",
          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        isAdmin: false,
        teacher: '',
        timeAll: [],
        total: null,
        tkedialog: false, //总部指派弹窗是否显示
        isREAssign: false, //默认false  false是指派  true重新指派
        luyouclassCard: [],
        editRowData: {}, //选择编辑的当前数据
        rules: {
          realName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: function (rule, value, callback) {
                if (/^1[345789]\d{9}$/.test(value) == false) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          studentSource: [{ required: true, message: '请输入来源', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
          expectTime: [
            { validator: validateTime, trigger: 'change' }
            // { required: true, message: "请选择时间", trigger: "change" },
          ],
          address: [{ required: true, message: '请选择区域', trigger: 'change' }],
          gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
          referrerName: [{ required: true, message: '请输入推荐人姓名', trigger: 'blur' }],
          referrerPhone: [{ required: true, message: '请填写推荐人手机号', trigger: 'blur' }]
        },

        editRules: {
          realName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: function (rule, value, callback) {
                if (/^1[345789]\d{9}$/.test(value) == false) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],

          // expectTime: [
          //   { required: true, message: '请选择期望试课时间', trigger: 'change' }
          // ],
          // dateslot:[{ required: true, message: '请选择试课日期', trigger: 'blur' }],
          // timeslot:[{ required: true, message: '请选择试课时间段', trigger: 'blur' }],
          // residentialSchool: [
          //   { required: true, message: '请选择是否咨询', trigger: 'change' }
          // ],
          experienceObject: [{ required: true, message: '请选择试课对象', trigger: 'change' }],
          clientName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
          counselor: [{ required: true, message: '请选择咨询师', trigger: 'change' }],
          classInstruction: [
            {
              required: true,
              message: '请选择是否有英语课外辅导',
              trigger: 'change'
            }
          ],
          studentSource: [{ required: true, message: '请输入来源', trigger: 'blur' }],
          grade: [{ required: true, message: '请选择年级', trigger: 'change' }],
          createTime: [{ required: true, message: '请选择提交时间', trigger: 'change' }],
          address: [{ required: true, message: '请选择区域', trigger: 'change' }],
          gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
          referrerPhone: [{ required: true, message: '请填写推荐人手机号', trigger: 'blur' }]
        },
        rulesReffer: {
          realName: [{ required: true, message: '请输入学员姓名', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            {
              validator: function (rule, value, callback) {
                if (/^1[345789]\d{9}$/.test(value) == false) {
                  callback(new Error('请输入正确的手机号'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          referrerName: [{ required: true, message: '请输入推荐人姓名', trigger: 'blur' }],
          referrerPhone: [{ required: true, message: '请填写推荐人手机号', trigger: 'blur' }],
          sendPacket: [{ required: true, message: '请选择性别', trigger: 'change' }]
        },

        expIdList: [],
        radio: '',
        delaytime: '', // 24 小时之后
        dialogVisible: false, // 时间预约
        value: '',
        timeslot: '', // 期望试课时间段
        dateslot: '',
        timeList: [],
        dateTime: '', // 编辑试课期望日期
        timeValue: '', // 编辑回显日期
        addVisible: false,
        shows: false, // 是否显示当前咨询师input框

        dialogPerformance: false,
        deduction: {
          studentCode: '', // 扣绩效id
          deductWage: '', // 扣绩效金额
          deductReason: '' // 扣款原因
        },
        deductionRules: {
          deductWage: [{ required: true, message: '请输入扣款金额', trigger: 'blur' }]
        },

        deductionStatus: [
          {
            value: 0,
            label: '未扣绩效'
          },
          {
            value: 1,
            label: '已扣绩效'
          }
        ],

        dataShow: false,

        option: [],
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          name: ''
        },

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '姓名',
            value: 'realName'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '课程类型',
            value: 'courseType'
          },
          {
            name: '课程状态',
            value: 'courseStatus'
          },
          {
            name: '联系方式',
            value: 'phone'
          },
          {
            name: '学员来源',
            value: 'studentSource'
          },
          {
            name: '试课对象',
            value: 'experienceObject'
          },
          {
            name: '客户姓名',
            value: 'clientName'
          },
          {
            name: '教练老师',
            value: 'teacherName'
          },
          {
            name: '试课时间',
            value: 'expectTime'
          },
          {
            name: '推荐人',
            value: 'referrerName'
          },
          {
            name: '推荐人手机号',
            value: 'referrerPhone'
          },
          {
            name: '支付状态',
            value: 'payStatus'
          },
          {
            name: '订单号',
            value: 'orderNo'
          },
          {
            name: '体验需求',
            value: 'remark'
          }
        ],

        orderId: '',
        tableHeaderList: [], // 获取表头数据
        courseList: [],
        curriculumId: '',
        sureLoading: false
      };
    },
    computed: {
      ...mapGetters(['roles'])
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      if (this.isAdmin) {
        this.headerSettings.splice(6, 0, {
          name: '交付中心名称',
          value: 'name'
        });
        this.headerSettings.splice(7, 0, {
          name: '交付中心编号',
          value: 'deliverMerchant'
        });
        // this.headerSettings.splice(15, 0, {
        //   name: "是否发送红包",
        //   value: "isSend",
        // });
      }
      this.getTeacherList();
      this.initData();
      this.GradeType();
      this.allDeliver();
      this.getListShow();
      this.getHeaderlist();
    },
    mounted() {
      this.getbvstatusList();
    },
    methods: {
      getbvstatusList() {
        bvstatusList({}).then((res) => {
          this.courseList = res.data;
          this.curriculumId = res.data.length > 0 ? res.data[0].id : '';
          this.searchNum.curriculumId = res.data.length > 0 ? res.data[0].id : '';
        });
      },
      changeTime(e) {
        this.expectTime2 = e + ':00';
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      /**
       * 下拉加载
       */
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },

      async getTeacherList() {
        let allData = await selAllTeacher(this.selectObj);
        this.option = this.option.concat(allData.data.data);
      },

      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getTeacherList();
      },

      clearSearchRecord() {
        setTimeout(() => {
          if (this.searchNum.teacherId == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.name = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      },
      handleTabsEdit(e) {
        this.validateClear(this.$refs['addForm']);
        this.validateClear(this.$refs['addFormReffer']);
      },

      getCodeToText(codeStr, codeArray, type) {
        if (codeStr === null && codeArray === null) {
          return null;
        } else if (codeArray === null) {
          codeArray = codeStr.split(',');
        }
        let area = [];
        switch (codeArray.length) {
          case 1:
            area.push(CodeToText[codeArray[0]]);
            break;
          case 2:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            break;
          case 3:
            area.push(CodeToText[codeArray[0]]);
            area.push(CodeToText[codeArray[1]]);
            area.push(CodeToText[codeArray[2]]);
            break;
          default:
            break;
        }
        // 新增
        if (type == 'add') {
          this.addForm.province = area[0];
          this.addForm.city = area[1];
          this.addForm.area = area[2];
        } else {
          //编辑
          this.editForm.province = area[0];
          this.editForm.city = area[1];
          this.editForm.area = area[2];
        }
        return area;
      },
      closeClass() {
        let _this = this;
        this.addForm = {
          deliverMerchant: null,
          gender: '',
          address: '',
          expectTime: '',
          grade: null,
          refName: '',
          referrerName: ''
        };
        this.addFormReffer = {
          realName: '',
          phone: '',
          referrerName: '',
          grade: 1,
          referrerPhone: '',
          sendPacket: 'false'
        };
        this.classList = false;
        this.ischeck = false;
        setTimeout(() => {
          _this.$refs.addForm.clearValidate();
        }, 50);
        // if (this.isReffer == "1" && this.$refs["addForm"]) {
        //   _this.validateClear(_this.$refs["addForm"]);
        // } else if (this.isReffer == "2" && this.$refs["addFormReffer"]) {
        //   _this.validateClear(_this.$refs["addFormReffer"]);
        // }
      },

      validateClear(ref) {
        setTimeout(function () {
          if (!ref) return;
          for (let i = 0; i < ref.length; i++) {
            ref[i].clearValidate();
          }
        }, 50);
      },

      async editverification() {
        let data = await check(this.editForm.referrerPhone);
        this.editForm.referrerName = data.data.saveName;
        console.log(data);
      },
      async verification() {
        let data = await check(this.addForm.referrerPhone);
        this.addForm.refName = data.data.name;
        this.addForm.referrerName = data.data.saveName;
        if (data.success) {
          this.ischeck = true;
        }
      },
      async submitAddSuccess() {
        let dataParam;
        // if (this.isReffer == "1") {
        //   this.addForm.referrer = false;
        //   this.addForm.expectTime = this.expectTime1 + ' ' + this.expectTime2
        //   dataParam = this.addForm;
        // } else {
        //   this.addFormReffer.referrer = true;
        //   dataParam = this.addFormReffer;
        // }
        this.addForm.referrer = false;
        this.addForm.expectTime = this.expectTime1 + ' ' + this.expectTime2;
        dataParam = this.addForm;
        let data = await createCourse(dataParam);
        if (data.success) {
          this.closeClass();
          this.initData();
          this.$message({
            showClose: true,
            message: '提交成功',
            type: 'success'
          });
        }
        this.addForm = {
          deliverMerchant: ' ',
          gender: 0,
          address: 0,
          expectTime: 0,
          grade: ' '
        };
      },
      submitAdd(formName, index) {
        this.$refs['addForm'].validate((valid) => {
          if (valid) {
            this.submitAddSuccess();
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      getExpectTime() {
        this.delaytime = dayjs(this.editForm.expectTime).add(1, 'day');
        let basetime = 24 * 60 * 60 * 1000;
        if (this.delaytime - this.editForm.expectTime < basetime) {
          this.$message({
            showClose: true,
            message: '选择时间不得小于24小时',
            type: 'error'
          });
          this.editForm.expectTime = '';
        }
        this.editForm.expectTime = dayjs(this.editForm.expectTime).format('YYYY-MM-DD HH:mm:ss');
        // this.time = dayjs(endTimes).valueOf();
        console.log(this.editForm);
      },
      async submitEdit(formName) {
        let that = this;
        let date = moment().format('YYYY-MM-DD');
        console.log(that.dateslot);
        if (that.dateslot == '') {
          that.$message.error({ message: '请选择预约试课时间' });
          return;
        }
        if (that.timeslot == '') {
          that.$message.error({ message: '请选择预约试课时间段' });
          return;
        }
        if (moment(that.dateslot).isBefore(date, 'day')) {
          that.$message.error({ message: '预约时间已过，不可选择' });
          return;
        }
        that.$refs[formName].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '编辑中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            console.log(that.editForm);
            update(that.editForm)
              .then(() => {
                that.editList = false;
                that.editForm = {};
                loading.close();
                that.initData();
                that.$message({
                  showClose: true,
                  message: '操作成功',
                  type: 'success'
                });
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      handleDisable(row, index) {
        if (row.payStatus == 1) {
          return true;
        } else {
          return false;
        }
      },
      allSelectTerminal() {
        console.log('触发~~');
        let arrId = [];
        for (let i = 0; i < this.expIdList.length; i++) {
          arrId.push(this.expIdList[i].id);
        }
        console.log(arrId);
        if (arrId.length === 0) {
          this.$message.warning('请选择需要支付的试课单');
          return;
        }
        batchPayExperience(arrId).then((res) => {
          if (res.success) {
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##BROWSER##WEB';
            let params = JSON.stringify(res.data);
            let req = 'token=' + getToken() + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            // window.open("https://pay.dxznjy.com/product?" + encode, "_blank");
            if (window.location.host.split('.')[0] == 'deliver') {
              window.open('https://pay.dxznjy.com/product?' + encode, '_blank');
            } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
              window.open('http://*************:8000/product?' + encode, '_blank');
            } else {
              let a = window.location.host.split('.')[0].slice(0, -1);
              let b = `https://${a}i.dxznjy.com/`;
              window.open(b + 'product?' + encode, '_blank');
            }
          }
          // this.$refs.grpNickNameTable.clearSelection()
        });
      },
      handleSelectionChange(val) {
        this.expIdList = val;
      },
      async changeAsk(id, val) {
        let data = await updateDetail(id);
        this.editForm = data.data;
        this.editForm.isAsk = val;
        let datas = await update(this.editForm);
        if (datas.success) {
          this.editList = false;
          this.editForm = {};
          this.initData();
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          });
        }
      },
      async allDeliver() {
        let data = await allDeliver();
        data.data.unshift({
          deliverMerchantCode: null,
          deliverMerchantName: '不指定'
        });
        this.allDelivers = data.data;
      },
      handleChange(value) {
        this.getCodeToText(null, value, 'add');
      },
      handleChanges(value) {
        this.getCodeToText(null, value, 'edit');
      },
      async edit(row) {
        let data = await updateDetail(row.id);
        this.editForm = data.data;
        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }
        if (!this.editForm.expectTime) {
          this.$message({
            showClose: true,
            message: '请先填写试课单',
            type: 'error'
          });
          return;
        }
        console.log(this.editForm);
        this.addVisible = true;
        this.dateTime = this.editForm.expectTime.slice(0, 10);
        let times = this.editForm.expectTime.split(' ');
        let time;
        if (Number(times[1].slice(3, 5)) >= 30) {
          time = '30';
        } else {
          console.log(Number(times[1].slice(3, 5)));
          time = '00';
        }
        this.timeslot = times[1].slice(0, 3) + time;
        await this.getReservationTime();
        if (data.data.customerType == 1) {
          this.editForm.customerType = '1';
        } else if (data.data.customerType == 2) {
          this.editForm.customerType = '2';
        }
        if (data.data.isAsk == 1) {
          this.editForm.isAsk = '1';
        } else if (data.data.isAsk == 2) {
          this.editForm.isAsk = '2';
        }
        if (data.data.isDeal == 1) {
          this.editForm.isDeal = '1';
        } else if (data.data.isDeal == 2) {
          this.editForm.isDeal = '2';
        }
        this.editForm.grade = String(this.editForm.grade == 0 ? 1 : this.editForm.grade);
        if (this.editForm.province && this.editForm.city && this.editForm.area) {
          let arr = [];
          arr.push(this.editForm.province);
          arr.push(this.editForm.city);
          arr.push(this.editForm.area);
          if (this.editForm.province === this.editForm.city) {
            this.editForm.city = '市辖区';
          }
          if (TextToCode[this.editForm.province]) {
            this.editForm.address = TextToCode[this.editForm.province].code;
            if (TextToCode[this.editForm.province][this.editForm.city]) {
              this.editForm.address = TextToCode[this.editForm.province][this.editForm.city].code;
              if (TextToCode[this.editForm.province][this.editForm.city][this.editForm.area]) {
                this.editForm.address = TextToCode[this.editForm.province][this.editForm.city][this.editForm.area].code;
              }
            }
          }
        }
        this.editList = true;
        this.$refs.editForm.clearValidate();
      },
      async GradeType() {
        let data = await GradeType();
        this.options = data.data;
        console.log(data);
      },
      // 数据查看按钮
      dataLookFn(row) {
        // this.$refs.dataLookDialog.teacher = row.id
        this.$refs.dataLookDialog.experienceId = row.id;
        this.$refs.dataLookDialog.initData();
      },
      // 重新指派
      async submitAss() {
        //必须选择交付中心
        if (this.deliverMerchantCode == '') {
          this.$message({
            showClose: true,
            message: '请选择重新指派交付中心',
            type: 'warning'
          });
          return;
        }
        //判断是否选择发送红包
        /*       if (!this.assign.isSend) {
              this.$message({
                showClose: true,
                message: "请选择是否发送红包",
                type: "warning",
              });
              return;
            } */
        let paramUrl = '';
        if (this.isREAssign) {
          if (this.deliverMerchantCode[0] == this.editRowData.deliverMerchant) {
            this.$message({
              showClose: true,
              message: '请勿重新指派同一个交付中心',
              type: 'warning'
            });
            return;
          }
          paramUrl = `?deliverMerchantCode=${this.deliverMerchantCode[0]}&id=${this.deliverMerchantID}&isSend=${this.assign.isSend}&isNewAssign=1`;
        } else {
          paramUrl = `?deliverMerchantCode=${this.deliverMerchantCode[0]}&id=${this.deliverMerchantID}&isSend=${this.assign.isSend}`;
        }

        let data = await submitAssign(paramUrl);
        if (data.success) {
          this.$message({
            showClose: true,
            message: '指派成功',
            type: 'success'
          });
          this.tkedialog = false;
          this.deliverMerchantCode = '';
          this.deliverMerchantID = '';
          this.assign.belongDeliverName = '';
          this.assign.isSend = '';
          this.isREAssign = false;
          this.initData();
        }
      },
      async editFn(row, assignStatus) {
        this.editRowData = row;
        let data = await belongDeliverAndAllDeliver(row.id);
        this.deliverMerchantID = row.id;
        this.assign = data.data;
        this.isREAssign = assignStatus;
        //如果是重新指派
        if (assignStatus) {
          this.assign.isSend = row.isSend.toString();
        }
        this.tkedialog = true;
      },
      // 体验课程按钮
      async expericeClass(row) {
        this.$refs.ExperienceClassDialog.studentNaC.realName = row.realName;
        this.$refs.ExperienceClassDialog.studentNaC.studentCode = row.studentCode;
        this.$refs.ExperienceClassDialog.planList.id = row.id;
        //获取当前时间格式化
        if (row.expectTime) {
          let time = dayjs(row.expectTime).add(1, 'hour');
          this.$refs.ExperienceClassDialog.dateListl = row.expectTime.split(' ')[0];
          this.$refs.ExperienceClassDialog.coniditon.timeList[0].startTime = dayjs(row.expectTime).format('HH:mm');
          this.$refs.ExperienceClassDialog.coniditon.timeList[0].endTime = dayjs(time).format('HH:mm');
        }
        this.$refs.ExperienceClassDialog.expericeRlook(); //回显
        this.TrialClassStyle = true;
      },
      // 学员信息表
      studentFn(row) {
        this.$router.push({
          path: '/pclass/components/studentsList',
          query: {
            studentCode: row.studentCode,
            merchantCode: row.merchantCode
          }
        });
      },
      // 试课信息表
      changeClassFn(row) {
        // window.localStorage.setItem("studentCodeL", row.studentCode);
        // window.localStorage.setItem("merchantCodeL", row.merchantCode);
        this.$router.push({
          path: '/pclass/components/changeClassList',
          query: {
            studentCode: row.studentCode,
            merchantCode: row.merchantCode
          }
        });
      },
      async initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      async initData() {
        this.tableLoading = true;
        if (this.timeAll && this.timeAll.length > 0) {
          this.searchNum.startTime = this.timeAll[0];
          this.searchNum.endTime = this.timeAll[1];
        } else {
          this.searchNum.startTime = undefined;
          this.searchNum.endTime = undefined;
        }
        let { data } = await getNoPayExperienceList(this.searchNum);
        for (let index = 0; index < data.data.length; index++) {
          if (data.data[index].isAsk == 1) {
            data.data[index].isAsk = '1';
          } else if (data.data[index].isAsk == 2) {
            data.data[index].isAsk = '2';
          } else {
            data.data[index].isAsk = '';
          }
        }
        this.luyouclassCard = data.data;
        this.tableLoading = false;
        this.total = Number(data.totalItems);
      },
      async changeFn() {
        this.tkedialog = true;
      },
      TrialClassLister(v) {
        this.TrialClassStyle = v;
      },
      dataLooker(v) {
        console.log(v);
        this.dataLookerStyle = v;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      // 删除按钮
      delCard(row) {
        console.log(1);
      },
      //定义导出Excel表格事件
      exportExcel() {
        /* 从表生成工作簿对象 */
        var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'));
        /* 获取二进制字符串作为输出 */
        var wbout = XLSX.write(wb, {
          bookType: 'xlsx',
          bookSST: true,
          type: 'array'
        });
        try {
          FileSaver.saveAs(
            //Blob 对象表示一个不可变、原始数据的类文件对象。
            //Blob 表示的不一定是JavaScript原生格式的数据。
            //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
            //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
            new Blob([wbout], { type: 'application/octet-stream' }),
            //设置导出文件名称
            'sheetjs.xlsx'
          );
        } catch (e) {
          if (typeof console !== 'undefined') console.log(e, wbout);
        }
        return wbout;
      },
      //打开创建试课单弹窗
      openCreate() {
        this.closeClass();
        this.classList = true;
      },
      //重置
      rest() {
        this.timeAll = '';
        this.$refs.searchNum.resetFields();
        // this.searchNum.deliverMerchant = '';
        // this.searchNum.deliverName = '';
        // this.searchNum.courseStatus = '';
        // this.searchNum.referrerNameOrPhone = '';
        // this.searchNum.teacherId = '';
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.searchNum.curriculumId = this.courseList.length > 0 ? this.courseList[0].id : '';
        this.getTeacherList();
        this.initData();
      },

      changeTimeslot(e) {
        console.log(e);
        let that = this;
        let date = moment().format('YYYY-MM-DD') + ' ' + e;
        // let time = moment(date).add(1, 'hours').format("HH:mm");
        // that.timeslot = e + '~' + time;
        that.timeslot = e;
        if (this.dateslot != '') {
          this.editForm.expectTime = this.dateslot + ' ' + e + ':00';
          console.log(this.editForm.expectTime);
        }
      },

      onChild(e) {
        console.log('获取子组件传过来的值：', e);
        this.dateslot = e;
        if (this.dateslot != '' && this.timeslot != '') {
          this.editForm.expectTime = this.dateslot + ' ' + this.timeslot + ':00';
          console.log(this.editForm.expectTime);
        }
      },

      // 打开预约人数设置弹窗
      openReservation() {
        this.getReservationTime();
        this.dialogVisible = true;
      },

      // 获取所有可预约时间段人数
      async getReservationTime() {
        await selAllExperienceUsableTime()
          .then((res) => {
            console.log(res);
            this.timeList = res.data;
          })
          .catch((err) => {});
      },

      // 获取所有可预约时间段人数
      async setupReservationTime() {
        this.timeList.forEach((item) => {
          delete item.startTime;
          delete item.endTime;
        });
        console.log(this.timeList);
        const loading = this.$loading({
          lock: true,
          text: '预约人数设置',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        await setExperienceUsableTimeNum(this.timeList)
          .then((res) => {
            console.log(res);
            loading.close();
            this.dialogVisible = false;
            this.$message.success('操作成功');
          })
          .catch((err) => {
            loading.close();
          });
      },

      closeEdit() {
        this.$refs.editForm.clearValidate();
        this.editList = false;
        this.addVisible = false;
      },

      // 关闭组件（为了每次打开组件从新调用）
      addClose() {
        this.addVisible = false;
      },

      inputAsk(e) {
        console.log(e);
        if (e == 0) {
          this.shows = true;
        } else {
          this.shows = false;
        }
      },

      // 扣绩效弹窗
      deduct(row) {
        this.deduction.deductWage = '';
        this.deduction.deductReason = '';
        this.deduction.studentCode = row.studentCode;
        this.dialogPerformance = true;
      },

      // deductPrice(e) {
      //   // this.deductionAmount = e;
      //   // this.$forceUpdate();
      // },

      // deductReason(e) {
      //   // this.deductionReason = e;
      //   // this.$forceUpdate();
      // },
      //扣绩效
      async getDeduction() {
        this.$refs['deduction'].validate((valid) => {
          if (valid) {
            if (this.deduction.deductWage == 0) {
              this.$message.error('扣款金额必须大于0');
              return;
            }
            deductingPerformance(this.deduction).then((res) => {
              this.$message({
                type: 'success',
                message: '操作成功'
              });
              this.$refs['deduction'].resetFields();
              this.dialogPerformance = false;
              this.initData();
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },

      resetForm() {
        this.dialogPerformance = false;
        this.$refs['deduction'].resetFields();
      },

      getListShow() {
        getDeliverCodes().then((res) => {
          this.dataShow = res.data;
          if (this.dataShow) {
            this.headerSettings.splice(this.headerSettings - 1, 0, {
              name: '工资',
              value: 'wage'
            });
          }
        });
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'TrialClassList',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },
      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'TrialClassList'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            // this.tableHeaderList = JSON.parse(res.data.value);
            let Json = this.removeNullValues(res.data.value);
            this.tableHeaderList = JSON.parse(Json);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
          this.$forceUpdate();
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
          this.$forceUpdate();
        });
      },

      // 获取
      async getClassTime() {
        await getScheduleTime(this.orderId)
          .then((res) => {
            console.log('时间11111111111');
            console.log(res);
            console.log('时间11111111111');
          })
          .catch((err) => {
            console.log(err);
          });
      },

      changeStatus(e) {
        console.log(e);
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .inpufn {
    position: relative;
  }

  .btnfn {
    position: absolute;
    left: 1427px;
    bottom: -18px;
  }

  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    /* 一行两列 */
    gap: 20px;
    /* 列之间的距离 */
  }

  .grid-container-one {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    /* 一行两列 */
    gap: 20px;
  }

  .hidden-row {
    white-space: nowrap; /* 阻止文本换行 */
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
    width: 100%;
  }
</style>
