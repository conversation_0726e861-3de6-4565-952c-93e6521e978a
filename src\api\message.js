/**
 * “消息配置”相关接口
 */
import request from '@/utils/request'

/**
 * 消息---列表查询
 * @param data
 */
export const getMsgList = (data) => {
  return request({
    url: '/deliver/message/template/find/page',
    method: 'GET',
    params: data
  })
}
/**
 * 消息---保存
 * @param data
 */
export const saveMsgList = (data) => {
  return request({
    url: '/deliver/message/template/save',
    method: 'POST',
    data
  })
}
/**
 * 消息---保存
 * @param data
 */
export const getGroupList = (data) => {
  return request({
    url: '/deliver/qywechatChat/list',
    method: 'GET',
    params: data
  })
}
