import request from '@/utils/request'
// 拿usercode
export const codeApi = () => {
    return request({
        url: 'mps/account/list/login/code',
        method: 'get',
    })
}
// 验证是否实名认证
export const userCodeApi = (id) => {
    return request({
        url: 'mps/user/info/user/code?userCode='+ id,
        method: 'get',
    })
}
// 绑身份证
export const verifiedApi = (data) => {
    return request({
        url: 'mps/auth/verified',
        method: 'put',
        data: data
    })
}
// 绑定银行卡验证码
export const minApi = (data) => {
    return request({
        url: 'mps/auth/bind/card',
        method: 'put',
        data: data
    })
}

// 绑定银行卡
export const bankApi = (data) => {
    return request({
        url: 'mps/auth/bind/card/confirm',
        method: 'put',
        data: data
    })
}

// 绑定手机验证码
export const telApi = (data) => {
    return request({
        url: 'mps/auth/bind/phone/sms/use/bank',
        method: 'get',
        params: data
    })
}

// 绑定手机
export const telbangdingApi = (data) => {
    return request({
        url: 'mps/auth/bind/phone/use/bank',
        method: 'put',
        data: data
    })
}


// 跳转签约
export const contractApi = (data) => {
    return request({
        url: 'mps/auth/sign/contract',
        method: 'get',
        params: data
    })
}