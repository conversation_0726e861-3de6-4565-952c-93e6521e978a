<!--交付中心-入库管理-预备教练审核-->
<template>
  <div>
    <!-- 头部 -->
    <PreCoach :zhuisfor="zhuisfor" @Searchlist="searchfun" @zhuSou="zhuSou" ref="teacher" />
    <xueguanshi :xueisTo="xueisTo" ref="dialog" @Xuechalist="xuechafun" @totalNum="totalNum" />
    <teamLeader :isLeader="isLeader" ref="teamLeader" @queryFn="queryFn" @totalNum="totalNum1" />
    <!-- body部 -->
    <div class="bodyex">
      <div style="text-align: right; margin-right: 20px">
        <el-button type="primary" @click="uploadQRCode">上传二维码</el-button>
      </div>
      <!-- 表格教练 -->
      <el-table
        v-loading="tableLoading"
        :data="zhusearchList"
        @selection-change="zhujiaoBtn"
        v-if="zhuisfor"
        style="height: 20%"
        class="twotable"
        :cell-style="{ 'text-align': 'center' }"
        ref="teacherTable"
        key="table1"
      >
        <el-table-column width="60" type="selection" align="center"></el-table-column>
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="
            item.value == 'deliverMerchantCode' || item.value == 'personality' || item.value == 'name' || item.value == 'mobile'
              ? 160
              : item.value == 'gradeStage' || item.value == 'prepareCount' || item.value == 'teachModule' || item.value == 'examinerName'
              ? '240'
              : ''
          "
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'operate'">
              <el-button type="primary" size="mini" @click="bianji(row)" :disabled="row.status == 0">审核</el-button>
            </div>

            <div v-else-if="item.value == 'sex'">
              <span>{{ row.sex == 1 ? '男' : '女' }}</span>
            </div>
            <div v-else-if="item.value == 'gradeStage'">
              <div v-if="getSplit(row.gradeStage)">
                <div v-for="(i, idx) in getSplit(row.gradeStage)" :key="idx">{{ i }}</div>
              </div>
              <div v-else>
                {{ row.gradeStage }}
              </div>
            </div>
            <div v-else-if="item.value == 'teachModule'">
              <div v-if="getSplit(row.teachModule)">
                <div v-for="(i, idx) in getSplit(row.teachModule)" :key="idx">{{ i }}</div>
              </div>
              <div v-else>
                {{ row.teachModule ? row.teachModule : '-' }}
              </div>
            </div>
            <div v-else-if="item.value == 'prepareCount'">
              <span>{{ row.prepareCount }}次/{{ row.prepareTotalMinutes }}分钟</span>
              <div v-if="row.prepareCount != '0'" @click="showPrepareDetails(row)" class="detailTip">详情</div>
            </div>

            <div v-else-if="item.value == 'studyStudentCount'">
              <span>{{ row.studyStudentCount }}</span>
              <span v-if="row.studyStudentCount != '0'" @click="showStudent(1, row)" class="detailTip">详情</span>
            </div>

            <div v-else-if="item.value == 'replaceStudentCount'">
              <span>{{ row.replaceStudentCount }}</span>
              <span v-if="row.replaceStudentCount != '0'" @click="showStudent(3, row)" class="detailTip">详情</span>
            </div>

            <div v-else-if="item.value == 'reviewStudentCount'">
              <span>{{ row.reviewStudentCount }}</span>
              <span v-if="row.reviewStudentCount != '0'" @click="showStudent(2, row)" class="detailTip">详情</span>
            </div>

            <div v-else-if="item.value == 'level'">
              <el-tag :type="row.level != 3 ? (row.level == 2 ? '' : 'success') : 'warning'" v-if="row.levelName && (row.level == 1 || row.level == 2 || row.level == 3)">
                {{ row.levelName }}
              </el-tag>
              <el-tag type="danger" v-if="row.levelName && row.level == 4">{{ row.levelName }}</el-tag>
              <el-tag class="tages" v-if="row.levelName && row.level == 5">{{ row.levelName }}</el-tag>
              <span v-if="!row.levelName && !row.level">-</span>
              <!-- 4.2.2 -->
              <el-button type="primary" icon="el-icon-edit" circle size="mini" @click="levelLoading(row)" v-if="levelaFrom.length > 1"></el-button>
            </div>

            <div v-else-if="item.value == 'teachingType'">
              <span>{{ row.teachingType != 0 ? (row.teachingType == 1 ? '线下' : '远程和线下') : '远程' }}</span>
            </div>

            <div v-else-if="item.value == 'postType'">
              {{ row.postType == 1 ? '全职' : '兼职' }}
            </div>

            <div v-else-if="item.value == 'experience'">
              <el-tag type="success" v-if="row.experience === 1">是</el-tag>
              <el-tag type="danger" v-else>否</el-tag>
            </div>

            <div v-else-if="item.value == 'isEnable'">
              <el-switch v-model="row.isEnable" :active-value="1" :inactive-value="0" active-color="#13ce66" @change="enableChange(row)"></el-switch>
            </div>

            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 表格学管 -->
      <el-table
        v-loading="tableLoading1"
        :data="xgsList.data"
        v-if="xueisTo"
        border
        class="twotable"
        @selection-change="xueguanBtnNum"
        :cell-style="{ 'text-align': 'center' }"
        ref="studentManagementTable"
        key="table2"
      >
        <el-table-column prop="date" width="80" type="selection" align="center"></el-table-column>
        <el-table-column prop="realName" label="学管姓名" width="160" header-align="center"></el-table-column>
        <el-table-column prop="sex" label="性别" width="160" header-align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="联系方式" width="400" header-align="center"></el-table-column>
      </el-table>

      <!-- 表格组长 -->
      <el-table
        v-loading="tableLoading2"
        :data="leaderData.data"
        v-if="isLeader"
        class="twotable"
        :cell-style="{ 'text-align': 'center' }"
        ref="studentManagementTable1"
        key="table3"
      >
        <el-table-column prop="name" label="姓名" header-align="center"></el-table-column>
        <el-table-column prop="sex" label="性别" header-align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="merchantName" label="交付中心名称" header-align="center"></el-table-column>
        <el-table-column prop="merchantCode" label="交付中心编号" header-align="center"></el-table-column>
        <el-table-column label="操作" header-align="center">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="eidtFn(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" v-if="scope.row.isEnable == 0" @click="delFn(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="联系方式" width="110" header-align="center"></el-table-column>
        <el-table-column prop="personality" label="性格特点" header-align="center"></el-table-column>
        <el-table-column prop="isEnable" label="是否绑定交付小组" header-align="center">
          <template slot-scope="scope">
            <span :class="statusClass(scope.row.isEnable)">{{ scope.row.isEnable == 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="comment" label="备注" header-align="center"></el-table-column>
      </el-table>
      <!-- 教练分页器 -->
      <el-row v-show="zhuisfor" type="flex" justify="center" align="middle" style="height: 60px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="zhusearchList.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="zhusearchList.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="zhusearchList.totalItems"
        ></el-pagination>
      </el-row>
      <!-- 学管的分页器 -->
      <el-row v-show="xueisTo" type="flex" justify="center" align="middle" style="height: 60px">
        <el-pagination
          @size-change="handleSizeChange1"
          @current-change="handleCurrentChange1"
          :current-page="xgsList.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="xgsList.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="xgsList.totalItems"
        ></el-pagination>
      </el-row>
      <!-- 组长的分页器 -->
      <el-row v-show="isLeader" type="flex" justify="center" align="middle" style="height: 60px">
        <el-pagination
          @size-change="handleSizeChange2"
          @current-change="handleCurrentChange2"
          :current-page="leaderData.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="leaderData.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="leaderData.totalItems"
        ></el-pagination>
      </el-row>
    </div>
    <!-- 上传二维码弹窗 -->
    <el-dialog :visible.sync="QRcodeDialog" width="30%" center :close-on-press-escape="false" :close-on-click-modal="false" @close="QRcodeCancel">
      <el-form>
        <el-form-item prop="weComImgUrl" label="企微二维码：" label-width="120px">
          <MyUpload :isKbOrMb="3000000" @handleSuccess="handlePicSuccess" @handleRemove="handlePicRemove" :fullUrl="true" :file-list="fileList" :limit="1"></MyUpload>
        </el-form-item>
        <el-form-item prop="coverClassUrl" label="班级群二维码：" label-width="120px">
          <MyUpload :isKbOrMb="300000" @handleSuccess="handlePicClassSuccess" @handleRemove="handlePicClassRemove" :fullUrl="true" :file-list="fileClassList" :limit="1"></MyUpload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="QRcodeCancel">取 消</el-button>
        <el-button type="primary" @click="QRcodeSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import FileSaver from 'file-saver';
import XLSX from 'xlsx';
import { searchTeacherApi, searchTeacherPrepareRecordList, searchTeacherStudentList, updateTeacherIsEnable, checkWechat } from '@/api/rukuManage/zhuTeacher';
import { getTeacherLevel, updateTeacherLevel } from '@/api/zhujiao';
import { deleteLearnManager, deleteTeacher, xueguanListApi } from '@/api/rukuManage/xueTeacher';
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
import { getLeaderList, delLeader } from '@/api/rukuManage/zuzhang';
import PreCoach from './components/preCoach.vue';
import xueguanshi from './components/xueguanshi.vue';
import teamLeader from './components/teamLeader.vue';
import ls from '@/api/sessionStorage';
import { mapGetters } from 'vuex';
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
import dayjs from 'dayjs';
import MyUpload from '@/components/Upload/MyUpload.vue';
import preparatoryCoachAPI from '@/api/rukuManage/preparatoryCoach';
import { filter } from 'lodash';

export default {
  name: 'preparationCoachReview',
  components: { PreCoach, xueguanshi, HeaderSettingsDialog, teamLeader, MyUpload },
  data() {
    return {
      rules: {
        levela: [
          {
            required: true,
            message: '教练等级不能为空',
            trigger: ['blur', 'change']
          }
        ]
      },
      levalForm: {
        levelName: '',
        level: '',
        levela: ''
      },
      levela: '',
      levelaFrom: {},
      dialogVisible: false,

      screenWidth: window.screen.width,
      // 点谁谁true，默认都false
      tableLoading: false,
      tableLoading1: false,
      tableLoading2: false,
      zhuisfor: true,
      xueisTo: false,
      isLeader: false,
      buttonzhu: 'primary', //按钮变色用
      buttonxue: '', //按钮变色用
      buttonzu: '', //按钮变色用
      zhusearchList: [],
      pageTeacherList: {
        name: '',
        totalItems: 0,
        sex: '',
        gradeStage: '',
        mobile: '',
        teachModule: '',
        teachingType: '',
        postType: '',
        pageNum: 1,
        pageSize: 10
      }, //获取页面的参数
      xueguanList: {
        realName: '',
        sex: '',
        mobile: '',
        pageNum: 1,
        pageSize: 10
      }, //学管师页面渲染
      xgsList: {},
      leaderData: {}, //学管师页面渲染
      leadList: [], //组长列表
      leaderPage: {
        name: '',
        sex: '',
        mobile: '',
        pageNum: 1,
        pageSize: 10
      },
      userIdList: '', //学管师userId集合,
      ids: '', //教练主键id集合
      isLearnManager: false,
      isAdmin: false,
      accountNumber: '***********',
      deliveryCenter: '***********',

      detailsChoseTeacher: null,
      choseDetailsStudentType: 0, //1:上课 2:代课 3:复习

      prepareHeight: 400,
      isRefPrepare: false,
      prepareList: [],
      dialogStudent: false,
      studentList: [],
      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '教练姓名',
          value: 'name'
        },
        {
          name: '性别',
          value: 'sex'
        },

        {
          name: '联系方式',
          value: 'mobile'
        },
        {
          name: '考核官',
          value: 'examinerName'
        },
        {
          name: '授课方式',
          value: 'teachingType'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '交付中心编号',
          value: 'deliverMerchantCode'
        },
        {
          name: '性格特点',
          value: 'personality'
        },
        {
          name: '所教模块',
          value: 'teachModule'
        },
        {
          name: '毕业院校',
          value: 'graduate'
        },
        {
          name: '课程类型',
          value: 'curriculumStr'
        },
        {
          name: '所教学段',
          value: 'gradeStage'
        },
        {
          name: '词汇水平',
          value: 'vocabulary'
        },
        {
          name: '可否试课',
          value: 'experience'
        },
        {
          name: '所学专业',
          value: 'major'
        },
        {
          name: '岗位类型',
          value: 'postType'
        }
      ],
      xueguanDelList: [], //选中的学管师列表
      tableHeaderList: [], // 获取表头数据
      wechats: [],
      QRcodeDialog: false,
      weComImgUrl: '', // 企微二维码
      coverClassUrl: '', // 班级群二维码
      fileList: [], // 上传图片已有图片列表
      fileClassList: [] // 上传图片班级群图片列表
    };
  },

  updated() {
    // console.log("updated")
    if (!this.isRefPrepare && this.$refs['scrollPrepare']) {
      console.log('scrollPrepare');
      this.isRefPrepare = true;
      this.$refs['scrollPrepare'].wrap.onscroll = (e) => {
        if (this.$refs['scrollPrepare'].wrap.scrollTop === this.$refs.scrollPrepare.wrap.scrollHeight - this.prepareHeight) {
          console.log('滑动到底啦');
        }
      };
    }
  },

  created() {
    this.isLearnManager = ls.getItem('rolesVal') === 'learnManager';
    this.isAdmin = ls.getItem('rolesVal') === 'admin';
    if (this.nick == this.accountNumber) {
      this.isAdmin = true;
    }
    this.initData();
    this.getHeaderlist();
    this.getTeacherLevel();
  },
  computed: {
    ...mapGetters(['nick'])
  },
  methods: {
    resetQRCode() {
      this.weComImgUrl = '';
      this.coverClassUrl = '';
      this.fileList = [];
      this.fileClassList = [];
    },
    /* 上传二维码 */
    uploadQRCode() {
      preparatoryCoachAPI.weComList().then((res) => {
        console.log(res, '获取二维码');
        this.fileList = res.data.weComImgUrl ? [{ url: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/' + res.data.weComImgUrl }] : [];
        this.fileClassList = res.data.classGroupImgUrl ? [{ url: 'http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com/' + res.data.classGroupImgUrl }] : [];
        this.weComImgUrl = res.data.weComImgUrl;
        this.classGroupImgUrl = res.data.classGroupImgUrl;
      });

      this.QRcodeDialog = true;
    },
    handlePicSuccess(url) {
      this.weComImgUrl = url;
    },
    handlePicRemove() {
      this.weComImgUrl = '';
    },
    handlePicClassSuccess(url) {
      this.classGroupImgUrl = url;
    },
    handlePicClassRemove() {
      this.classGroupImgUrl = '';
    },
    QRcodeSubmit() {
      if (!this.weComImgUrl || !this.classGroupImgUrl) {
        this.$message.error('请检查两张二维码是否都上传了~');
        return false;
      }
      console.log(this.weComImgUrl, this.classGroupImgUrl, '提交二维码');
      preparatoryCoachAPI
        .weComSave({
          weComImgUrl: this.weComImgUrl,
          classGroupImgUrl: this.classGroupImgUrl
        })
        .then((res) => {
          console.log(res, '上传二维码');
          if (res.code === 20000) {
            this.$message.success('上传成功');
            this.resetQRCode();
            this.initData();
          } else {
            this.$message.error(res.message);
          }
        });
      this.QRcodeDialog = false;
    },
    QRcodeCancel() {
      this.resetQRCode();
      this.QRcodeDialog = false;
    },
    getSplit(str) {
      let result = null;
      if (str) {
        let arr = str.split(';');
        let newArr = [];
        if (arr.length > 0) {
          newArr = arr.filter((i) => i != '');
          result = newArr;
          return result;
        } else {
          return null;
        }
      } else {
        return null;
      }
    },
    change(val) {
      // this.$set(this.levela, val)
      this.levalForm.levela = val;
      this.levela = val;
      // this.$forceUpdate()
    },
    ruleFormFalse(a) {
      this.resetForm(a);
      this.dialogVisible = false;
      this.initData();
    },
    //清空校验
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    //修改教练等级
    updateTeacherLevel(b) {
      // console.log(this.levalForm.level,555);
      // console.log(this.levalForm.levela,888);
      if (this.levalForm.level == this.levela) {
        this.$message({
          message: '当前教练等级与修改等级不能一致',
          type: 'error'
        });
      } else if (this.levela == '') {
        this.$message({
          message: '教练等级不能为空',
          type: 'error'
        });
      } else {
        let data = {
          level: this.levela,
          teacherId: this.levalForm.id
        };
        updateTeacherLevel(data).then((res) => {
          // console.log(res);
          if (res.code == 20000) {
            this.$message({
              message: res.message,
              type: 'success'
            });
            this.resetForm(b);
            this.initData();
          } else {
            this.$message({
              message: res.message,
              type: 'error'
            });
          }
        });
        this.dialogVisible = false;
      }
    },
    //教练等级修改弹窗控制
    levelLoading(t) {
      this.dialogVisible = true;
      console.log(t);
      this.levalForm = t;
      this.getTeacherLevel();
    },
    //获取教练等级
    //   async getTeacherLevelConfig() {
    //   this.tableLoading = true
    //   let { res } = await getTeacherLevelConfig();
    //  console.log(res,2222);
    // },
    getTeacherLevel() {
      getTeacherLevel().then((res) => {
        // console.log(res);
        this.levelaFrom = res.data;
      });
    },
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        console.log('获取的表头数据', this.$refs.HeaderSettingsDialog.checkList);
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    bianji(row) {
      let ceshi = row.id;
      this.$router.push({
        path: '/ruku/preparationCoachReview/index',
        query: {
          id: ceshi
        }
      });
    },
    zhujiaoBtn(val) {
      this.ids = val.map((item) => item.id);
    },
    xueguanBtnNum(val) {
      this.userIdList = val.map((item) => item.userId);
      this.xueguanDelList = val;
    },
    async delBtnxue() {
      var role;
      if (this.xueguanDelList.length < 1) {
        this.$message.error('请选择删除数据');
      } else {
        role = this.xueguanDelList[0].role;
        console.log(role);
        if (this.xueguanDelList.find((e) => e.role != role)) return this.$message.error('请选择相同类型学管师');

        await this.$confirm('您确定要删除吗?');

        await deleteLearnManager(this.userIdList, role);
        this.$message.success('删除成功');
      }
      let { data } = await xueguanListApi(this.xueguanList);
      for (let key in data) {
        if (key != 'data') {
          data[key] = parseInt(data[key]);
        }
      }
      this.xgsList = data;
      this.xgsList.totalItems = Number(data.totalItems);
    },
    async delBtnzhu() {
      if (this.ids.length < 1) {
        this.$message.error('请选择删除数据');
      } else {
        await this.$confirm('您确定要删除吗?');
        await deleteTeacher(this.ids);
        this.$message.success('删除成功');
      }
      this.initData();
    },
    // 教练分页
    handleSizeChange(val) {
      this.pageTeacherList.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.pageTeacherList.pageNum = val;
      this.$refs.teacher.zhuSou.pageNum = val;
      this.$refs.teacher.initData();
      console.log(this.pageTeacherList, 'this.pageTeacherList');
      // this.initData();
    },
    // 学管分页
    handleSizeChange1(val) {
      this.xueguanList.pageSize = val;
      this.xueguanBtn();
    },
    handleCurrentChange1(val) {
      this.xueguanList.pageNum = val;
      this.xueguanBtn();
    },
    // 学管分页
    handleSizeChange2(val) {
      this.leaderPage.pageSize = val;
      this.initLeader();
    },
    handleCurrentChange2(val) {
      this.leaderPage.pageNum = val;
      this.initLeader();
    },
    // 数字转换文字
    sexl(val) {
      if (val.sex == '1') {
        return '男';
      } else if (val.sex == '2') {
        return '女';
      }
    },
    teachingType(val) {
      if (val.teachingType == 0) {
        return '远程';
      } else if (val.teachingType == 1) {
        return '线下';
      } else if (val.teachingType == 2) {
        return '远程和线下';
      }
    },
    postType(val) {
      if (val.postType == 1) {
        return '全职';
      } else if (val.postType == 2) {
        return '兼职';
      }
    },
    // 教练接口
    async initData() {
      this.tableLoading = true;
      if ((this.pageTeacherList.curriculumId && this.pageTeacherList.curriculumId.length != 19) || !this.pageTeacherList.curriculumId) {
        delete this.pageTeacherList.curriculumId;
      }
      let { data } = await preparatoryCoachAPI.teacherPageAPI(this.pageTeacherList);
      // let { data } = await searchTeacherApi(this.pageTeacherList);
      console.log(data, 'datata');
      // for (let key in data) {
      //   if (key != 'data') {
      //     data[key] = parseInt(data[key]);
      //   }
      // }
      console.log(data, 2222222);
      this.zhusearchList = data;
      console.log(this.zhusearchList, 'eeeeeeeeeeeeeee');
      // this.pageTeacherList.totalItems = Number(data.totalItems)
      this.tableLoading = false;
    },
    searchfun(val) {
      // console.log(val,5555);
      this.zhusearchList = val;
      this.initData();
      this.pageTeacherList.totalItems = Number(val.totalItems);
    },
    zhuSou(val) {
      this.pageTeacherList = val;
      this.initData();
    },
    xuechafun(val) {
      this.xueguanList = val;
      this.xueguanBtn();
    },
    queryFn(val) {
      this.leaderPage = val;
      this.initLeader();
    },
    totalNum(val) {
      console.log(val);
    },
    totalNum1(val) {
      console.log(val);
    },
    // 全选
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    zhujiaocc() {
      this.zhuisfor = true;
      this.xueisTo = false;
      this.isLeader = false;
      this.buttonzhu = 'primary';
      this.buttonxue = '';
      this.buttonzu = '';
    },
    eidtFn(row) {
      console.log(row);
      this.$router.push({
        path: '/ruku/zuzhang/index',
        query: {
          data: row
        }
      });
    },
    // 删除交付小组组长
    async delFn(row) {
      await this.$confirm('您确定要删除吗?');
      await delLeader(row.id);
      this.$message.success('删除成功');
      this.initLeader();
    },
    // 点击交付小组组长tab按钮
    clickLeader() {
      // this.leaderData = {}
      this.zhuisfor = false;
      this.xueisTo = false;
      this.buttonzu = 'primary';
      this.buttonxue = '';
      this.buttonzhu = '';
      // 请求接口
      this.initLeader();
      this.isLeader = true;
      // this.$forceUpdate()
    },
    // 动态class
    statusClass(status) {
      switch (status) {
        case 0:
          return 'error';
        case 1:
          return 'normal';
        case 2:
          return '';
      }
    },
    // 初始化交付小组组长列表
    async initLeader() {
      this.tableLoading2 = true;
      let { data } = await getLeaderList(this.leaderPage);
      for (let key in data) {
        if (key != 'data') {
          data[key] = parseInt(data[key]);
        }
      }
      this.leaderData = data;
      this.tableLoading2 = false;
    },
    // 学管接口
    async xueguancc() {
      this.xueguanList.pageNum = 1;
      this.xueguanList.pageSize = 10;
      this.xueguanBtn();
    },
    async xueguanBtn() {
      this.tableLoading1 = true;
      // this.xgsList = {}
      this.zhuisfor = false;
      this.isLeader = false;
      this.buttonxue = 'primary';
      this.buttonzhu = '';
      this.buttonzu = '';
      let { data } = await xueguanListApi(this.xueguanList);
      for (let key in data) {
        if (key != 'data') {
          data[key] = parseInt(data[key]);
        }
      }
      this.xgsList = data;
      this.xgsList.totalItems = Number(data.totalItems);
      this.xueisTo = true;
      this.tableLoading1 = false;
      this.$forceUpdate();
    },
    QualityClick() {
      this.$router.push('/ruku/zhujiao/index');
    },
    leaderClick() {
      this.$router.push('/ruku/zuzhang/index');
    },
    xueguanClick() {
      this.$router.push('/ruku/xueguan/index');
    },
    //定义导出Excel表格事件
    exportExcel() {
      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'));
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      });
      try {
        FileSaver.saveAs(
          //Blob 对象表示一个不可变、原始数据的类文件对象。
          //Blob 表示的不一定是JavaScript原生格式的数据。
          //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
          //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
          new Blob([wbout], { type: 'application/octet-stream' }),
          //设置导出文件名称
          'sheetjs.xlsx'
        );
      } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbout);
      }
      return wbout;
    },

    enableChange(row) {
      let text = row.isEnable === 1 ? '开启' : '关闭';
      this.$confirm('确认要' + text + ' "' + row.name + '" 的账户吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function () {
          updateTeacherIsEnable({ id: row.id, isEnable: row.isEnable }).then((res) => {
            return res.code === 20000;
          });
        })
        .then(() => {
          this.initData();
          this.$message.success(text + '成功');
        })
        .catch(function () {
          row.isEnable = row.isEnable === 1 ? 0 : 1;
        });
    },

    getStudentDetailsTitle() {
      const type = this.choseDetailsStudentType === 1 ? '上课学员' : this.choseDetailsStudentType === 2 ? '复习学员' : this.choseDetailsStudentType === 3 ? '代课学员' : '';
      return this.detailsChoseTeacher ? this.detailsChoseTeacher.name + ' - ' + type : type;
    },
    //显示学员详情 1//上课  2//复习 3//代课
    showStudent(type, val) {
      this.choseDetailsStudentType = type;
      this.detailsChoseTeacher = val;
      this.getStudentList();
      this.dialogStudent = true;
    },
    //请求教练学员
    async getStudentList() {
      let { data } = await searchTeacherStudentList(this.detailsChoseTeacher.id, this.choseDetailsStudentType);
      this.studentList = data;
    },
    closeStudentDialog() {
      this.detailsChoseTeacher = null;
      this.dialogStudent = false;
    },
    //获取时分
    getTimeHourMin(time) {
      let hour = dayjs(time).format('HH:mm');
      return hour;
    },

    getSameDayListIndex(list, day) {
      for (let j = 0; j < list.length; j++) {
        if (list[j].date === day) {
          return j;
        }
      }
      return -1;
    },
    //显示备课详情
    showPrepareDetails(val) {
      this.detailsChoseTeacher = val;
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: 'person',
        value: JSON.stringify(arr)
      };
      this.setHeaderSettings(data);
    },
    removeNullValues(jsonStr) {
      const obj = JSON.parse(jsonStr);
      const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
      let newJson = cleanObj.filter((item) => item !== null);
      return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
    },
    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'person'
      };
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          let Json = this.removeNullValues(res.data.value);
          this.tableHeaderList = JSON.parse(Json);

          // 定义需要过滤的字段
          const fieldsToFilter = ['reviewStudentCount', 'level', 'replaceStudentCount', 'studyStudentCount', 'prepareCount'];

          // 过滤掉指定的表头
          this.tableHeaderList = this.tableHeaderList.filter((item) => !fieldsToFilter.includes(item.value));
        } else {
          if (!this.isAdmin) {
            this.headerSettings = this.headerSettings.filter((item) => item.value !== 'deliverMerchantCode');
          }
          this.tableHeaderList = this.headerSettings;
        }
      });
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then((res) => {
        this.$message.success('操作成功');
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
body {
  background-color: rgba(255, 255, 255);
}

.frame {
  background-color: rgba(255, 255, 255);
}

.bodyex {
  margin-top: 20px;
  height: 100%;
}

.twotable {
  margin-top: 10px;
  border-top: 1px solid rgba(223, 230, 236);
}

.active {
  display: none;
}

.active1 {
  display: none;
}
.active2 {
  display: none;
}
.detailTip {
  font-size: 12px;
  color: #46a6ff;
  margin-left: 10px;
  cursor: pointer;
}

::v-deep .el-timeline-item__timestamp {
  color: #000000 !important;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

// ::v-deep .level_style .el-tag--medium .el-tag--light{
//   background-color: #fff3fe !important;
//   border-color: #f5d1f1 !important;
//   color: #ee94ef !important;
// }
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}
.tages {
  background-color: rgba(201, 38, 219, 0.1) !important;
  border-color: #ef91fa !important;
  color: #e468f1 !important;
}

.is-circle {
  margin-left: 10px;
  color: rgb(24, 144, 255);
  background-color: transparent;
  border: none;
  font-size: 16px;
}

.checkTitle {
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 10px;
}
.checkNote {
  color: #a3a3a3;
  margin-bottom: 40px;
}
.nomore {
  width: 100%;
  height: 100%;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
