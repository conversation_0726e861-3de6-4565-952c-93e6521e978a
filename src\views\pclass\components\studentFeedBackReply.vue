<template>
  <div class="dialog">
    <el-dialog title="反馈回复" :visible.sync="studentFeedBackReplystyle_" :direction="directions" @close="handleClose">
      <div class="borders">
        <el-row>
          <el-col style="margin-top:2vw;margin-left:1vw">家长评分：{{ detail.parentScore }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">家长反馈:{{ detail.parentFeedback }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">推荐人评分：{{ detail.referrerScore }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">推荐人反馈:{{ detail.referrerFeedback }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">教练反馈：{{ detail.feedback }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">交付中心回复:</el-col>
          <el-input type="textarea" style="width:25vw" v-model="detail.deliverReply"></el-input>
          <el-col style="margin-top:3vw">
            <el-button style="width:6vw;margin-left:5vw" type="primary" @click="modifyBtn">保存</el-button>
            <el-button style="width:6vw;margin-left:5vw" type="primary"
              @click="quxiaoBtn=studentFeedBackReplystyle_=false">取消
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { replyStudentFeedback } from "@/api/paikeManage/studentFeedback";

export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    studentFeedBackDetailstyle: {
      type: Boolean,
      default: false
    },
    //Drawer 打开的方向
    directions: {
      type: String,
      default: "rtl"
    }
  },
  name: 'studentFeedBackReply',
  data() {
    return {
      value1: '',
      detail: {//回显接口数据
        parentScore: '',
        parentFeedback: '',
        referrerScore: '',
        referrerFeedback: '',
        feedback: '',
        deliverReply: ''
      },
      reply: {
        content: "",
        deliverCode: "",
        id: ""
      },
    };
  },
  //计算属性
  computed: {
    studentFeedBackReplystyle_: {
      get() {
        return this.studentFeedBackReplystyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit('lookDrawer', v)
      }
    }
  },
  methods: {
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.$emit("lookDrawer", false);
    },
    async modifyBtn() {//修改课程表列表
      this.reply.id = this.detail.id
      this.reply.deliverCode = this.detail.deliverMerchant
      this.reply.content = this.detail.deliverReply
      await replyStudentFeedback(this.reply),
        this.$message.success("修改成功")
      this.studentFeedBackReplystyle_ = false
      this.$emit("updateList");
    },
  }
};
</script>

<style lang="scss">
.borders {
  margin: 1vw 1vw;
  width: 28vw;
  height: 35vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
</style>


<style scoped>
div /deep/ .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>
