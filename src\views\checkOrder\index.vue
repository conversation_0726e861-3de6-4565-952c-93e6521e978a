<!--教练费退款审核-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px">
        <!-- 1 -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="姓名:">
              <el-input v-model="searchNum.name" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="学员编号:">
              <el-input v-model="searchNum.studentCode" :disabled="!!stuudentCode" clearable placeholder="请输入"
                size="small" style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核状态:">
              <el-select v-model="searchNum.checkStatus" clearable placeholder="请选择" style="width: 13vw">
                <el-option label="待审核" value="0"></el-option>
                <el-option label="已审核或该审核" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 2 -->
        <el-row>
          <el-col v-if="isAdmin" :span="8">
            <el-form-item label="门店编号:">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请输入门店编号" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="isAdmin ? 8 : 16">
            <el-form-item label="时间筛选:">
              <el-date-picker v-model="timeAll" style="width: 18vw" size="small" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
            <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">
              导出
            </el-button>
          </el-col>
        </el-row>
        <!-- 3 -->
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:20px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center" :width="item.value == 'operate' ? 200 : ''">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="danger" v-if="row.status == 11" size="mini" @click="LeaveBtn(row)">审核</el-button>
            <!-- <el-button type="primary" size="mini" v-else @click="paikeBtn(row)">调课</el-button>
              <el-button type="success" size="mini" @click="lookBtn(row)">数据查看</el-button>
              <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteStudy(row)">删除</el-button> -->
          </div>
          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ status(row) }}</span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表格 -->
    <!-- <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="merchantCode" label="门店编号" header-align="center" />
      <el-table-column prop="merchantName" label="所属门店" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center" width="200">
        <template v-slot="{ row }">
          <el-button type="danger" v-if="row.status == 11" size="mini" @click="LeaveBtn(row)">审核</el-button>
         
        </template>
      </el-table-column>
      <el-table-column prop="studentName" label="学员姓名" width="140" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="140" header-align="center"></el-table-column>
      <el-table-column prop="remark" label="说明" width="340" header-align="center"></el-table-column>
      <el-table-column prop="auditReason" label="退款/拒绝原因" width="180" header-align="center"></el-table-column>
      <el-table-column prop="status" label="审核状态" header-align="center" width="180" :formatter="status">
        <template slot-scope="scope">
          <span :class="statusClass(scope.row.status)">{{ status(scope.row) }}</span>
        </template>
      </el-table-column>
    </el-table> -->
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination v-if="tableIshow" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <dataLookDialog @dataLooker="dataLooker" :dataLookerStyle="dataLookerStyle" :direction="direction"
      ref="dataLookDialog" />
    <classCardDialog @changeDrawer="changeDrawer" :classCardstyle="classCardstyle" :direction="direction"
      @updateList="initData" ref="rowshuju" />
    <LeaveProcessingDialog @LeaveDialog="LeaveDialog" :LeaveStyle="LeaveStyle" :direction="direction" ref="LeaveDialog"
      @fMethod="paikeBtn" @qjstartTime="qjstartTime1" @qjendTime="qjendTime1" />
    <numberLookDialog @lookDrawer="lookDrawer" :lookstyle="lookstyle" :direction="direction" ref="numberLook" />

    <!-- 弹窗 -->
    <el-dialog title="审批" :visible.sync="open" width="70%" @close="cancel">
      <el-form label-width="120px" ref="checkForm" :model="checkInfo" :rules="rules">
        <el-form-item label="审批原因：">
          <el-input v-model.trim="checkInfo.auditReason" placeholder="请输入审批原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkWorkOrder">通过</el-button>
        <el-button @click="onDisagree">拒绝</el-button>
      </div>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import LeaveProcessingDialog from '../pclass/components/LeaveProcessingDialog.vue'
import classCardDialog from '../pclass/components/classCardDialog.vue'
import numberLookDialog from '../pclass/components/numberLookDialog.vue'
import { deletePlanStudy, getFeedbackInfo } from '@/api/paikeManage/LearnManager'
import dataLookDialog from '../pclass/components/dataLookDialog.vue'
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'
import { getTableTitleSet, setTableList, bvstatusList } from "@/api/paikeManage/classCard";

import {
  getAdjustInfo,
  getTimetable,
  checkOrderPass,
  studentStudyExport,
  getRefundReview
} from '@/api/paikeManage/checkOrder'
import ls from '@/api/sessionStorage'

export default {
  name: 'checkOrder',
  components: {
    classCardDialog,
    dataLookDialog,
    LeaveProcessingDialog,
    numberLookDialog,
    HeaderSettingsDialog
  },
  data() {
    return {
      checkInfo: {
        auditReason: "",
        id: '',
        studentCode: '',
        merchantCode: ''
      },
      rules: {
        auditReason: [
          { required: true, message: "审批原因不能为空", trigger: "blur" }
        ],
        id: [
          { required: true, message: "请先选择", trigger: "blur" }
        ],
      },
      open: false,
      stuudentCode: null,
      dataLookerStyle: false,
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date()
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const temp = new Date()
              temp.setTime(temp.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '最近七天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      qjstartTime: '',
      qjendTime: '',
      tableIshow: true,
      redLeave: [],
      lookstyle: false,
      LeaveStyle: false,
      classCardstyle: false, //抽屉状态
      LeaveId: '',
      searchNum: {
        checkStatus: '',
        name: '',
        endTime: '',
        startTime: '',
        teacherName: '',
        studentCode: '',
        studyStatus: '',
        lastStudyTime: '',
        pageNum: 1,
        pageSize: 10,
        planId: undefined,
        curriculumId: ''
      }, //搜索参数
      tableLoading: false,
      direction: 'rtl',//超哪边打开
      teacher: '',
      timeAll: [],
      contentType: '',
      total: null,
      luyouclassCard: [],
      leaveApplication: {
        id: '',
        type: 1
      },
      getFeedback: {
        //详情的参数
        id: '',
        type: '1'
      },
      isAdmin: false,
      auditReason: '',
      exportLoading: false,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '门店编号',
          value: 'merchantCode'
        },
        {
          name: '所属门店',
          value: 'merchantName'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '学员姓名',
          value: 'studentName'
        },
        // {
        //   name: '课程类型',
        //   value: 'curriculumName'
        // },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '说明',
          value: 'remark'
        },
        {
          name: '退款/拒绝原因',
          value: 'auditReason'
        },
        {
          name: '审核状态',
          value: 'status'
        }],

      tableHeaderList: [], // 获取表头数据
      courseList: []
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
    this.initData()
    this.stuudentCode = this.$route.query.classCard
    this.searchNum.planId = this.$route.query.planId
    this.searchNum.studentCode = this.stuudentCode ? this.stuudentCode : '';
    this.getHeaderlist();

  },
  mounted() {
    this.getbvstatusList()
  },
  methods: {
    getbvstatusList() {
      bvstatusList({}).then((res) => {
        this.courseList = res.data;
      });
    },
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    cancel() {
      this.open = false
      this.reset()
    },
    reset() {
      this.checkInfo = {
        auditReason: "",
        id: '',
        studentCode: '',
        merchantCode: ''
      }
    },
    /*
    * 同意审核
    * */
    checkWorkOrder() {
      if (this.checkInfo.auditReason == '') {
        this.$message.error('请输入退款原因');
        return;
      }

      getRefundReview(this.checkInfo.id, this.checkInfo.studentCode, this.checkInfo.merchantCode).then(res => {
        if (!res.data) {
          checkOrderPass(this.checkInfo.id, true, this.checkInfo.auditReason).then((res) => {
            this.$message({
              type: 'success',
              message: '已通过审核'
            })
            this.open = false;
            this.initData()
          })
        }
      })

    },

    /*
    * 不同意批量审批
    * */
    onDisagree() {
      checkOrderPass(this.checkInfo.id, false, this.checkInfo.auditReason).then((res) => {
        this.$message({
          type: 'error',
          message: '已拒绝退课'
        })
        this.open = false;
        this.initData()
      })
      //重置

    },

    rest() {
      this.searchNum.name = '';
      this.searchNum.studentCode = '';
      this.searchNum.checkStatus = '';
      this.searchNum.merchantCode = '';
      this.timeAll = '';
      this.initData01()
    },

    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData()
    },
    dataLooker(v) {
      this.dataLookerStyle = v
    },
    statusClass(status) {
      switch (status) {
        case 11:
          return 'error'
        case 8:
          return ''
        case 9:
          return ''
        default:
          return 'normal'
      }
    },
    qjstartTime1(v) {
      this.qjstartTime = v
    },
    qjendTime1(v) {
      this.qjendTime = v
    },
    // 学习课程表请假处理
    async LeaveBtn(row) {
      this.reset()
      this.checkInfo.id = row.id
      this.checkInfo.studentCode = row.studentCode
      this.checkInfo.merchantCode = row.merchantCode
      this.open = true
      // this.$confirm(row.remark, '确定通过退款审核', {
      //   confirmButtonText: '通过',
      //   distinguishCancelAndClose: true,
      //   cancelButtonText: '拒绝',
      //   cancelButtonClass: 'btnFalses',
      //   type: 'warning'
      // }).then(() => {
      //   getRefundReview(row.id, row.studentCode, row.merchantCode).then(res => {
      //     if (!res.data) {
      //       checkOrderPass(row.id, true,value).then((res) => {
      //         this.$message({
      //           type: 'success',
      //           message: '已通过审核'
      //         })
      //         this.initData()
      //       })
      //     }
      //   })
      // }).catch(action => {
      //   if (action === 'cancel') {
      //     checkOrderPass(row.id, false).then((res) => {
      //       this.$message({
      //         type: 'error',
      //         message: '已拒绝退课'
      //       })
      //       this.initData()
      //     })
      //   } else {
      //     this.$message({
      //       type: 'warning',
      //       message: '已取消操作'
      //     })
      //   }
      // })
    },
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = []
      }
      this.tableLoading = true
      this.searchNum.startTime = this.timeAll[0]
      this.searchNum.endTime = this.timeAll[1]
      this.tableLoading = true
      let { data } = await getTimetable(this.searchNum)
      this.tableLoading = false
      this.total = Number(data.totalItems)
      this.luyouclassCard = data.data
    },
    LeaveDialog(v) {
      this.LeaveStyle = v
    },
    changeDrawer(v) {
      this.classCardstyle = v
    },
    lookDrawer(v) {
      this.lookstyle = v
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return '远程'
      } else if (val.teachingType == 2) {
        return '线下'
      } else if (val.teachingType == 3) {
        return '远程和线下'
      } else {
        return '暂无'
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return '鼎英语'
      }
    },
    status(val) {
      switch (val.status) {
        case 8:
          return '已取消'
        case 9:
          return '已拒绝'
        case 11:
          return '待审核'
        default:
          return '已审核或未该审核'
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return '未上课'
      } else if (val.studyStatus == 2) {
        return '已上课'
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val
      this.initData()
    },
    // 编辑按钮
    async paikeBtn(row) {
      if (row) {
        let reslist = await getAdjustInfo(row.id)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = row
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.planStudy.studentCode = row.studentCode
        this.$refs.rowshuju.getTeachlist()
      } else {
        let reslist = await getAdjustInfo(this.LeaveId)
        this.$refs.rowshuju.classCardnum = reslist.data
        this.$refs.rowshuju.studentList = this.redLeave
        this.$refs.rowshuju.teacherId = reslist.data.teacherId
        this.classCardstyle = true
        this.$refs.rowshuju.getTeachlist()
        if (this.qjstartTime !== '' && this.qjstartTime != undefined && this.qjstartTime != null) {
          this.$refs.rowshuju.classCardnum.startStudyTime = this.qjstartTime
        }
        if (this.qjendTime !== '' && this.qjendTime != undefined && this.qjendTime != null) {
          this.$refs.rowshuju.classCardnum.endStudyTime = this.qjendTime
        }
      }
      // let reslist = await getAdjustInfo(row.id);
      // this.$refs.rowshuju.classCardnum = reslist.data;
      // this.$refs.rowshuju.studentList = this.redLeave;
      // this.$refs.rowshuju.teacherId = reslist.data.teacherId;
      // this.classCardstyle = true;
      // this.$refs.rowshuju.getTeachlist();
    },
    // 数据查看按钮
    async lookBtn(row) {
      if (row.experience) {
        this.$refs.dataLookDialog.experienceId = row.id
        this.$refs.dataLookDialog.initData()
      } else {
        this.$refs.numberLook.teacher = row.teacher
        this.$refs.numberLook.studyList.studentCode = row.studentCode
        this.getFeedback.id = row.id
        this.$refs.numberLook.reviewTotal.id = row.id
        this.$refs.numberLook.reviewTotal.planId = row.planId
        let res = await getFeedbackInfo(this.getFeedback)
        if (res.code == 20000) {
          this.lookstyle = true
        }
        this.$refs.numberLook.studyList1 = res.data
      }
    },
    //定义导出Excel表格事件
    exportExcel() {
      this.exportLoading = true
      studentStudyExport(this.searchNum).then(res => {
        console.log(window.URL.createObjectURL(res))
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url//获取服务器端的文件名
        link.setAttribute('download', '学习课程表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false
      })
    },
    //删除学习课程
    async deleteStudy(row) {
      await this.$confirm('您确定要删除学习课程吗?')
      await deletePlanStudy(row.id)
      this.$message.success('删除成功')
      await this.initData()
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "checkOrder",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'checkOrder'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
}
</script>

<style scoped>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

.btnFalses {
  background: #fff !important;
  color: #67c23a !important;
}

body {
  background-color: #f5f7fa;
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

.btnFalses {
  background: #fff !important;
  color: #67c23a !important;
}

// .el-button--success {
//   color: #ffffff;
//   background-color: #6ed7c4;
//   border-color: #6ed7c4;
// }
</style>
