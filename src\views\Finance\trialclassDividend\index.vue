<template>
  <div style="padding: 30px;">
    <div style="display: flex;justify-content: space-between;margin-bottom: 1vw;">
      <div>试课分润设置</div>
      <div style="margin-right: 26vw;"><i class="el-icon-warning"
          style="color: #ef9b42;margin-right: 10px;"></i>试课分润，教练填完反馈后即可分出</div>
    </div>
    <el-table :data="dividendList" border style="width: 70%" :header-cell-style="getRowClass">
      <el-table-column prop="" label="试课价格" width="" align="center">
        <template slot-scope="scope">
          <span>{{ (Number(scope.row.value) + Number(scope.row.configState)).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="推荐人分润" width="200" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.value).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="configState" label="交付中心分润" width="200" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.configState).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editDividend(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="display: flex;justify-content: space-between;margin-bottom: 1vw;margin-top: 4vw;">
      <div>试课奖励</div>
      <div style="margin-right: 26vw;">
        <i class="el-icon-warning" style="color: #ef9b42;margin-right: 10px;"></i>试课奖励，只要充值完学时就分出
      </div>
    </div>
    <el-table :data="wagesList" border style="width: 70%" :header-cell-style="getRowClass">
      <el-table-column prop="value" label="试课奖励" width="" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.value).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="editDividend(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <div style="margin-bottom: 1vw;margin-top: 4vw;">
      <div>抽佣设置</div>
    </div>
    <el-table :data="commissionList" border style="width: 70%" :header-cell-style="getRowClass">
      <el-table-column prop="value" label="上课抽佣（节）" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.value).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="commissionEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table> -->
    <!-- <div style="margin-bottom: 1vw;margin-top: 4vw;">
      <div>复习工资基本设置</div>
    </div>
    <div v-if="reviewWagesList.length==0" style="padding-left:250px;">
      <el-button type="primary" size="small" @click="reviewWagesEdit()">新增复习工资基本设置</el-button>
    </div>
    <el-table v-else :data="reviewWagesList" border style="width: 70%" :header-cell-style="getRowClass">
      <el-table-column prop="value" label="复习公约数" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.commonDivisor}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="公约数工资" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.commonDivisorSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="复习完成奖金" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.completeReward }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="reviewWagesEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex;justify-content: space-between;margin-bottom: 1vw;margin-top: 4vw; width:70%;">
      <div>复习完成率奖金设置（自然月）</div>
      <el-button type="primary"  v-if="reviewRate.length>0" size="small" @click="reviewEditOrAdd()">新增复习奖金设置</el-button>
    </div>
    <div v-if="reviewRate.length==0" style="padding-left:250px;">
      <el-button type="primary"  size="small" @click="reviewEditOrAdd()">新增复习奖金设置</el-button>
    </div>
    <el-table v-else :data="reviewRate" border style="width: 70%" :header-cell-style="getRowClass">
      <el-table-column prop="value" label="复习完成率" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.startingValue}}~{{scope.row.endValue}}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="value" label="奖金" align="center">
        <template slot-scope="scope">
          <span>{{ Number(scope.row.rewardAmount).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.$index>0"  type="danger" @click="delectReview(scope.$index,scope.row)" size="mini">删除</el-button>
          <el-button type="primary" size="small" @click="reviewEditOrAdd(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table> -->

    <el-dialog title="编辑试课分润" :visible.sync="dialogVisible" width="30%" :before-close="handleClose"
      :close-on-click-modal="false">
      <el-form ref="updateForm" :model="updateList" label-width="120px" :rules="rules">
        <el-form-item label="试课价格：">
          <el-input v-model="totalAmount" placeholder="请先输入推荐人和交付中心分润" style="width: 300px;" disabled></el-input><span
            style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item label="推荐人分润：" prop="pay">
          <el-input :min="0" type="number" v-model="updateList.pay" style="width: 300px;"
            @input="change"></el-input><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item label="交付中心分润：" prop="meritPay">
          <el-input :min="0" type="number" v-model="updateList.meritPay" style="width: 300px;"
            @input="change"></el-input><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updatePrice('updateForm')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="编辑试课奖励" :visible.sync="trialclassVisible" width="30%" :close-on-click-modal="false"
      :before-close="trialclassClose">
      <el-form ref="editForm" :model="updateList" label-width="100px" :rules="rewardRules">
        <el-form-item label="试课奖励：" prop="pay">
          <el-input :min="0" type="number" v-model="updateList.pay" style="width: 300px;" @input="change"
            placeholder="请先输入"></el-input><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="trialclassVisible = false">取消</el-button>
          <el-button type="primary" @click="updatePrice('editForm')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- <el-dialog title="抽佣设置" :visible.sync="commissionVisible" width="30%" :close-on-click-modal="false"
      :before-close="commissionClose">
      <el-form ref="commissionForm" :model="editCommissionList" label-width="160px" :rules="commissionRules">
        <el-form-item label="上课抽佣（节）：" prop="pay">
          <el-input :min="0" type="number" v-model="editCommissionList.pay" style="width: 300px;" @input="change"
            placeholder="请先输入"></el-input><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="commissionVisible = false">取消</el-button>
          <el-button type="primary" @click="updateCommissionList">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <!-- 复习工资基本设置 -->
    <!-- <el-dialog :title="reviewWagesTitle" :visible.sync="reviewWagesVisible"  width="560px" :close-on-click-modal="false"  :before-close="reviewWagesClose">
      <el-form ref="reviewWagesFrom" :model="reviewWagesInfo" label-width="120px">
        <el-form-item label="复习公约数：" :rules="[{ required: true, message: '请输入公约数', trigger: 'blur' },
            {validator,trigger: 'blur' } ]" prop="commonDivisor">
          <el-input-number :min="0" type="number" :precision="0"  controls-position="right" v-model="reviewWagesInfo.commonDivisor" style="width: 300px;" 
            placeholder="请输入"></el-input-number><span style="margin-left: 20px;">个</span>
        </el-form-item>
        <el-form-item label="公约数工资："  :rules="[ { required: true, message: '请输入公约数工资', trigger: 'blur' },
           { validator, trigger: 'blur'} ]" prop="commonDivisorSalary">
          <el-input-number :min="0" type="number"  :precision="2"  controls-position="right" v-model="reviewWagesInfo.commonDivisorSalary" style="width: 300px;"
            placeholder="请输入"></el-input-number><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item label="完成复习奖金：" :rules="[{ required: true, message: '请输入复习奖金', trigger: 'blur' },{ validator, trigger: 'blur'}]" prop="completeReward">
          <el-input-number :min="0" type="number"  :precision="2"  controls-position="right" v-model="reviewWagesInfo.completeReward" style="width: 300px;" 
            placeholder="请输入"></el-input-number><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>    
          <el-button @click="reviewWagesClose">取消</el-button>
          <el-button type="primary" @click="updatereviewWagesList">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <!-- 复习奖金设置 -->
    <!-- <el-dialog :title="reviewTitle" :visible.sync="reviewVisible" width="560px" :close-on-click-modal="false" :before-close="reviewClose">
      <el-form ref="reviewFrom" :model="reviewInfo" label-width="120px">
        <el-form-item label="起始值：" prop="startingValue" :rules="[{ required: true, message: '请输入起始值', trigger: 'blur' }, {validator,trigger: 'blur' } ]">
          <el-input-number :min="0" type="number" :max="100"  controls-position="right" v-model="reviewInfo.startingValue" style="width: 300px;" placeholder="请输入"></el-input-number><span style="margin-left: 20px;">%</span>
        </el-form-item>
        <el-form-item label="结束值：" prop="endValue" :rules="[{ required: true, message: '请输入结束值', trigger: 'blur' }, {validator,trigger: 'blur' } ]">
          <el-input-number :min="0" type="number"  :max="100" controls-position="right" v-model="reviewInfo.endValue" style="width: 300px;" placeholder="请输入"></el-input-number><span style="margin-left: 20px;">%</span>
        </el-form-item>
        <el-form-item label="奖金金额：" prop="rewardAmount" :rules="[{ required: true, message: '请输入奖金金额', trigger: 'blur' }, {validator,trigger: 'blur' } ]">
          <el-input-number :min="0" type="number" controls-position="right" :precision="2"  v-model="reviewInfo.rewardAmount" style="width: 300px;" placeholder="请输入"></el-input-number><span style="margin-left: 20px;">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="reviewClose">取消</el-button>
          <el-button type="primary" @click="updatereviewList">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog> -->
    <!-- <el-dialog  class="wages" :visible.sync="reviewDelVisible"  width="560px">
      <div class="del-content-css">
        <div class="delect-title-css">
          <i class="el-icon-delete"></i>
          <span>删除</span>
        </div>
        <div class="content-delect-css">
          <span>删除复习完成率{{ delReviewInfo.startingValue }}%~{{ delReviewInfo.endValue }}%,奖金{{ delReviewInfo.rewardAmount }}</span>
        </div>
        <div class="del-bottom-css">
          <i class="el-icon-warning" style="color:#E6A23C;"></i>
          <span>删除后，教练将不获得奖金</span>
        </div>
      </div>
      <div style="text-align: right; padding-right:20px;padding-bottom:20px;">
        <el-button size="small" @click="reviewDelVisible=false">取消</el-button>
        <el-button size="small" type="primary" @click="delectReviewData">确定</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import { getDividendsetting, updateDividend, getCommissionList, updateCommission ,saveReviewSalaryConfig,getReviewSalaryConfig,getReviewRewardConfig,saveReviewRewardConfig ,deleteReviewRewardConfig} from "@/api/FinanceApi/trialclassDividend";
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      dialogVisible: false,
      wagesList: [], // 试课奖励列表
      dialogAdd: false,
      formInline: '',
      value: "",
      screenHeight: window.screen.height,
      url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
      show: false, // 判断登录用户是否有权限
      trialclassVisible: false,
      dividendList: [],
      updateList: {
        name: '',
        pay: '',
        meritPay: '',
        id: ''
      },
      totalAmount: '',
      rules: {
        pay: [
          { required: true, message: '请输入推荐人分润', trigger: 'blur' }
        ],
        meritPay: [
          { required: true, message: '请输入交付中心分润', trigger: 'blur' }
        ]
      },
      dialogStatus: false, // 弹窗类型
      trialclassVisible: false,
      rewardRules: {
        pay: [
          { required: true, message: '请输入试课奖励', trigger: 'blur' }
        ]
      },
      // accountNumber: '***********',
      commissionVisible: false,
      reviewDelVisible:false,
      commissionList: [],
      reviewWagesList:[],
      reviewRate:[],
      delReviewInfo:{},
      reviewWagesVisible:false,
      reviewWagesInfo:{},
      reviewWagesTitle:'新增复习工资基本设置',
      reviewVisible:false,
      reviewInfo:{},
      reviewTitle:'新增复习奖金设置',
      commissionRules: {
        pay: [
          { required: true, message: '请输入上课抽佣', trigger: 'blur' }
        ],
        meritPay: [
          { required: true, message: '请输入复习抽佣', trigger: 'blur' }
        ]
      },
      editCommissionList: {
        id: '',
        pay: '',
        meritPay: ''
      },
    };
  },
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  created() { },
  mounted() {
    // if (this.name != this.accountNumber) {
    //     this.initData();
    //     this.commissionInit();
    // } else {
    //     this.$message.error('您暂无该权限');
    // }
    this.initData();
    this.commissionInit();
    this.getReviewSalaryConfig()
    this.getReviewRewardConfig()
  },
  methods: {
    // validator(rule, value, callback) {
    //   console.log(value)
    //   console.log('...................................................')
    //   console.log(rule)
    //   if (value == 0) {
    //     if(rule.field =='endValue'||rule.field == 'startingValue' || rule.field == 'commonDivisor'){
    //       callback(new Error('数量不能为 0'));
    //     }else{
    //       callback(new Error('金额不能为 0'));
    //     }
        
    //   } else {
    //     callback();
    //   }
    // },
    change(e) {
      this.$forceUpdate();
      this.totalAmount = (Number(this.updateList.pay) + Number(this.updateList.meritPay)).toFixed(2);
    },
    changePerformance(e) {
      this.$forceUpdate();
    },
    async initData() {
      let that = this;
      that.dividendList = [];
      that.wagesList = [];
      await getDividendsetting().then((res) => {
        let list = res.data;
        if (list.length != 0) {
          // 分润设置
          for (var i = 0; i < list.length; i++) {
            if (list[i].name == 'EXP_REF_DELIVER_PROFIT') {
              this.dividendList.push(list[i]);
            } else {
              this.wagesList.push(list[i]);
            }
          }
        }
      });
    },
    // async getReviewSalaryConfig(){
    //   await getReviewSalaryConfig().then((res) => {
    //     if(res.data.id){
    //       this.reviewWagesList = [res.data]||[];
    //     }else{
    //       this.reviewWagesList=[]
    //     }
    //   });
    // },
    // async getReviewRewardConfig(){
    //   await getReviewRewardConfig().then((res) => {
    //     if(res.data){
    //       this.reviewRate = res.data||[];
    //     }
    //   });
    // },

    // 抽佣
    async commissionInit() {
      await getCommissionList().then((res) => {
        this.commissionList = res.data;
      });
    },

    changePrice(e) {
      this.$forceUpdate();
    },

    editDividend(row) {
      this.updateList.name = row.name;
      this.updateList.pay = row.value;
      this.updateList.id = row.id;
      if (this.updateList.name == 'EXP_REF_DELIVER_PROFIT') {
        this.updateList.meritPay = row.configState;
        this.dialogVisible = true;
      } else {
        this.updateList.meritPay = '0';
        this.trialclassVisible = true;
      }
      this.totalAmount = (Number(this.updateList.pay) + Number(this.updateList.meritPay)).toFixed(2);
    },

    updatePrice(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '加载中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          updateDividend(this.updateList).then(res => {
            loading.close()
            this.$message.success("操作成功");
            this.$refs[formName].resetFields();
            this.dialogVisible = false;
            this.trialclassVisible = false;
            this.initData();
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },

    handleClose() {
      this.$refs['updateForm'].resetFields();
      this.dialogVisible = false;
    },


    trialclassClose() {
      this.$refs['editForm'].resetFields();
      this.trialclassVisible = false;
    },

    commissionClose() {
      this.$refs['commissionForm'].resetFields();
      this.commissionVisible = false;
    },
    // reviewClose(){
    //   this.$refs['reviewFrom'].resetFields();
    //   this.reviewVisible = false;
    // },
    // reviewWagesClose(){
    //   this.$refs['reviewWagesFrom'].resetFields();
    //   this.reviewWagesVisible = false;
    // },
    // reviewWagesEdit(row){
    //   this.reviewWagesVisible=true
    //   this.reviewWagesTitle='新增复习工资基本设置'
    //   this.reviewWagesInfo={}
    //   if(row){
    //     this.reviewWagesTitle='编辑复习工资基本设置'
    //     this.reviewWagesInfo={...row}
    //   }
    // },
    // reviewEditOrAdd(row){
    //   this.reviewVisible=true
    //   this.reviewTitle='新增复习奖金设置'
    //   this.reviewInfo={}
    //   if(row){
    //     this.reviewTitle='编辑复习奖金设置'
    //     this.reviewInfo={...row}
    //   }
    // },
    // //删除
    // delectReview(index,info){
    //   this.delReviewInfo={...info,index:index}
    //   this.reviewDelVisible=true
      
    // },
    // //删除弹窗确定
    // delectReviewData(){
    //   deleteReviewRewardConfig(this.delReviewInfo).then(res => {
    //     if(res.code==20000){
    //       this.reviewDelVisible=false
    //       this.reviewRate=res.data
    //       this.$message.success("删除成功");
    //     }
    //   })
    // },
    // 抽佣编辑
    commissionEdit(row) {
      console.log(row);
      this.commissionVisible = true;
      this.editCommissionList.id = row.id;
      this.editCommissionList.pay = row.value;
      this.editCommissionList.meritPay = row.configState;
      this.editCommissionList.name = 'CUT_CONFIG';
    },
    //新增编辑复习工资
    // updatereviewWagesList(){
    //   this.$refs['reviewWagesFrom'].validate((valid) => {
    //     if (valid) {
    //       const loading = this.$loading({
    //         lock: true,
    //         text: '编辑中...',
    //         spinner: 'el-icon-loading',
    //         background: 'rgba(0, 0, 0, 0.7)'
    //       })
    //       saveReviewSalaryConfig(this.reviewWagesInfo).then(res => {
    //         loading.close()
    //         this.$message.success("操作成功");
    //         this.$refs['reviewWagesFrom'].resetFields();
    //         this.reviewWagesVisible = false;
    //         if(res.data.id){
    //           this.reviewWagesList = [res.data]||[];
    //         }else{
    //           this.reviewWagesList=[]
    //         }
    //       }).catch(err => {
    //         loading.close();
    //       })
    //     } else {
    //       console.log('error submit!!');
    //       return false;
    //     }
    //   });
    // },
    // //新增编辑复习完成率奖金
    // updatereviewList(){
    //   this.$refs['reviewFrom'].validate((valid) => {
    //     if (valid) {
    //       const loading = this.$loading({
    //         lock: true,
    //         text: '编辑中...',
    //         spinner: 'el-icon-loading',
    //         background: 'rgba(0, 0, 0, 0.7)'
    //       })
    //       saveReviewRewardConfig(this.reviewInfo).then(res => {
    //         loading.close()
    //         this.$message.success("操作成功");
    //         this.$refs['reviewFrom'].resetFields();
    //         this.reviewVisible = false;
    //         if(res.data){
    //           this.reviewRate = res.data||[];
    //         }
    //       }).catch(err => {
    //         loading.close();
    //       })
    //     } else {
    //       console.log('error submit!!');
    //       return false;
    //     }
    //   });
    // },
    updateCommissionList() {
      this.$refs['commissionForm'].validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '编辑中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          updateCommission(this.editCommissionList).then(res => {
            loading.close()
            this.$message.success("操作成功");
            this.$refs['commissionForm'].resetFields();
            this.commissionVisible = false;
            this.commissionInit();
          }).catch(err => {
            loading.close();
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#e2eaf6'
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

.wages ::v-deep.el-dialog__body {
  padding: 0 !important;
}
::v-deep.is-controls-right .el-input-number__increase{
  display: none !important;
}
::v-deep.is-controls-right .el-input__inner{
  text-align: left !important;
  padding-left: 20 !important;
}
::v-deep.is-controls-right .el-input__inner{
  padding-left: 20 !important;
}
::v-deep.is-controls-right .el-input-number__decrease{
  display: none !important;
}
// .del-content-css{
//   padding-bottom: 40px;
//   padding-left: 30px;
//   span{
//     display: inline-block;
//     margin-left: 5px;
//   }
//   .delect-title-css{
//     color:#F56C6C;
//     font-size: 20px;
//   }
//   .content-delect-css{
//       margin-top: 20px;
//   }
//   .del-bottom-css{
//     margin-top: 12px;
//     span{
//       color:#F56C6C;
//     }
//   }
// }
.dialog-top-padding{
  padding-top: -30px;
}
.price {
  display: flex;
  justify-content: space-between;
  width: 280px;
  color: #c0c4cc;
  padding: 0 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  border: 1px solid #dfe4ed;
}

.amount {
  width: 200px;
  height: 36px;
  margin-left: 4px;
  line-height: 36px;
  padding: 0 15px;
  color: #c0c4cc;
  background-color: #f5f7fa;
  border-radius: 5px;
  box-sizing: border-box;
  border: 1px solid #dfe4ed;
}

.course {
  width: 300px;
  color: #c0c4cc;
  padding: 0 15px;
  background-color: #f5f7fa;
  border-radius: 5px;
  border: 1px solid #dfe4ed;
}

.no_data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

::v-deep .el-card__body {
  height: 100%;
}
</style>
