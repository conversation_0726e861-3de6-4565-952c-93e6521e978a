import request from '@/utils/request'

// 教练工资流水列表
export const coachTurnoverList = (data) => {
  return request({
    url: '/deliver/teacherWagesFlow/queryTeacherWagesFlow',
    method: 'POST',
    data
  })
}
// /deliver​/web​/review​/wage​/count​/getReviewWageCount

// 根据商户号获取教练复习工资统计
export const getReviewWageCount = () => {
  return request({
    url: '/deliver/web/review/wage/count/getReviewWageCount',
    method: 'GET',
  })
}

// 新增教练复习分润补款充值订单
export const reviewSalary = (data) => {
  return request({
    url: '/deliver/web/recharge/order/save/reviewSalary',
    method: 'POST',
    data
  })
}

// 校验是否充值过
export const getCheckRecharge = () => {
  // 发送GET请求，请求地址为'/deliver/web/recharge/order/check/recharge'
  return request({
    url: '/deliver/web/recharge/order/check/recharge?businessTag=DELIVER_EXPERIENCE_SUBSIDY_PAYMENT',
    method: 'GET',
  })
}
// 获取交付中心月度应发试课补贴,返回金额字符串
export const getMonthAllowance = () => {
  return request({
    url: '/deliver/web/experience/allowance/getMonthAllowance',
    method: 'GET',
  })
}
//试课补贴
export const allowanceSave = (data) => {
  return request({
    url: '/deliver/web/recharge/order/save/experience/allowance',
    method: 'POST',
    data
  })
}
// /web/review/wage/pool/page
export const getReviewpage= (data) => {
  return request({
    url: '/deliver/web/review/wage/pool/page',
    method: 'GET',
    params:data
  })
}







