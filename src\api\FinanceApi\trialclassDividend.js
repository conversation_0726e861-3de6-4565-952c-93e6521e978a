import request from '@/utils/request'

// 获取分润设置列表
export const getDividendsetting = () => {
  return request({
    url: '/znyy/deliver/wage/getProfitConfig',
    method: 'GET'
  })
}

export const updateDividend = (data) => {
  return request({
    url: '/znyy/deliver/wage/updateProfitConfig',
    method: 'POST',
    data
  })
}


// 抽佣设置列表
export const getCommissionList = () => {
  return request({
    url: '/znyy/deliver/wage/getCutConfig',
    method: 'GET'
  })
}

// 抽佣设置编辑
export const updateCommission = (data) => {
  return request({
    url: '/znyy/deliver/wage/updateCutConfig',
    method: 'POST',
    data
  })
}

// 保存或更新教练复习工资基本设置
export const saveReviewSalaryConfig = (data) => {
  return request({
    url: '/deliver/web/review/salary/saveReviewSalaryConfig',
    method: 'POST',
    data
  })
}
// /
//获取教练复习工资基本设置
export const getReviewSalaryConfig = () => {
  return request({
    url: '/deliver/web/review/salary/getReviewSalaryConfig',
    method: 'GET'
  })
}
//保存或更新教练复习奖金设置
export const saveReviewRewardConfig = (data) => {
  return request({
    url: '/deliver/web/review/salary/saveReviewRewardConfig',
    method: 'POST',
    data
  })
}
// 获取教练复习奖金设置
export const getReviewRewardConfig = () => {
  return request({
    url: '/deliver/web/review/salary/getReviewRewardConfig',
    method: 'GET'
  })
}
//删除教练复习奖金数据
export const deleteReviewRewardConfig = (data) => {
  return request({
    url: '/deliver/web/review/salary/deleteReviewRewardConfig?id='+data.id,
    method: 'POST',
    data
  })
}
