/**
 * 忘记密码相关接口
 */
import request from '@/utils/request';

export default {
  // //发送短信
  // sendSmg(mobile){
  //   return request({
  //     url: '/znyy/user/sms/midify/pwd/'+ mobile,
  //     method: 'POST'
  //   })
  // },
  //发送短信
  sendSmg(mobile, code) {
    const data = { mobile };
    if (code !== undefined && code !== null && code !== '') {
      data.code = code;
    }
    return request({
      url: '/new/security/sms/back/forget',
      method: 'POST',
      data
    });
  },
  // 验证短信
  verifySms(params) {
    return request({
      url: '/new/security/captcha/image/slide',
      method: 'get',
      params
    });
  },
  // 重置密码
  pswReset(data) {
    return request({
      url: '/znyy/user/merchant/update/pwd',
      method: 'PUT',
      data
    });
  }
};
