<template>
  <div v-loading="loading">
    <el-form ref="editForm" :model="editForm" :label-width="screenWidth > 1300 ? '150px' : '110px'">
      <el-form-item label="学员姓名" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.realName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="区域" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.province }}/{{ editForm.city }}/{{ editForm.area }}</span>
        </div>
      </el-form-item>
      <el-form-item label="课程类型" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.curriculumName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="期望上课时间" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.extendTime }}</span>
        </div>
      </el-form-item>
      <!-- <el-form-item label="试课对象" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">
              {{ editForm.experienceObject == 1 ? 'B端' : editForm.experienceObject == 2 ? 'C端' : '无' }}
            </span>
          </div>
        </el-form-item> -->
      <el-form-item label="试课对象" prop="experienceObject" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-radio-group v-model="editForm.experienceObject" disabled>
          <el-radio :label="1">B端</el-radio>
          <el-radio :label="2">C端</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="咨询师" prop="counselor" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-radio-group v-model="editForm.counselor" disabled>
          <el-radio :label="'1'">上级推荐人</el-radio>
          <el-radio :label="'0'">自己</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="体验需求" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <div class="timeClass" style="margin: 0 0">
            <span style="display: block; word-wrap: break-word; margin: 5px 10px; min-height: 23px">{{ editForm.remark }}</span>
          </div>
        </div>
      </el-form-item>
      <!-- <el-form-item v-if="editForm.curriculumName == '鼎英语'" label="英语课外辅导" prop="classInstruction" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <el-radio-group v-model="editForm.classInstruction" disabled>
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      <div v-if="editForm.curriculumCode == 'MATH'">
        <el-form-item label="版本" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ editForm.versionName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="学科" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ editForm.disciplineName }}</span>
          </div>
        </el-form-item>
        <el-form-item label="当前年级" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">{{ editForm.gradeName }}</span>
          </div>
        </el-form-item>
      </div>

      <el-form-item label="试课人年级" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ getGrade(editForm.grade) }}</span>
        </div>
      </el-form-item>
      <el-form-item label="推荐人姓名" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.referrerName }}</span>
        </div>
      </el-form-item>
      <el-form-item label="推荐人手机号" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.referrerPhone }}</span>
        </div>
      </el-form-item>
      <el-form-item label="提交时间" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.createTime }}</span>
        </div>
      </el-form-item>
      <!-- <el-form-item label="客户类型" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">
              {{ editForm.customerType == 1 ? '家长' : editForm.customerType == 2 ? '意向客户' : '无' }}
            </span>
          </div>
        </el-form-item>
        <el-form-item label="是否成交" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
          <div class="timeClass" style="margin: 0 0">
            <span style="margin: 0 15px">
              {{ editForm.customerType == 1 ? '是' : editForm.customerType == 2 ? '否' : '无' }}
            </span>
          </div>
        </el-form-item> -->
      <!-- <el-form-item label="客户类型" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-radio-group v-model="editForm.customerType" disabled>
          <el-radio label="1">家长</el-radio>
          <el-radio label="2">意向客户</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否成交" prop :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <el-radio-group v-model="editForm.isDeal" disabled>
          <el-radio label="1">是</el-radio>
          <el-radio label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="成交金额" :style="{ width: screenWidth > 1300 ? '50%' : '100%' }">
        <div class="timeClass" style="margin: 0 0">
          <span style="margin: 0 15px">{{ editForm.dealPrice }}元</span>
        </div>
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script>
  import { updateDetail, queryStudentExperienceDetail, GradeType } from '@/api/studentClass/changeList';
  import orderApi from '@/api/oneToManyClass/DispatchInProgress';

  export default {
    props: {
      /**
       * 请求类型 1-1v1 2-1v多
       */
      reqType: {
        type: Number | String,
        required: false,
        default: 1
      },
      id: {
        type: Number | String,
        required: true
      }
    },
    data() {
      return {
        loading: false,
        editForm: {
          versionName: '',
          disciplineName: '',
          gradeName: ''
        },
        screenWidth: window.innerWidth, // 屏幕宽度
        // 年级列表
        options: [
          { value: '1', label: '一年级' },
          { value: '2', label: '二年级' },
          { value: '3', label: '三年级' },
          { value: '4', label: '四年级' },
          { value: '5', label: '五年级' },
          { value: '6', label: '六年级' },
          { value: '7', label: '七年级' },
          { value: '8', label: '八年级' },
          { value: '9', label: '九年级' },
          { value: '10', label: '高中一年级' },
          { value: '11', label: '高中二年级' },
          { value: '12', label: '高中三年级' },
          { value: '13', label: '大一' },
          { value: '14', label: '大二' },
          { value: '15', label: '大三' },
          { value: '16', label: '大四' },
          { value: '17', label: '其他' },
          { value: '18', label: '幼儿园' },
          { value: '19', label: '雅思' },
          { value: '20', label: '托福' }
        ],
        weeklist: [
          { value: 1, label: '周一' },
          { value: 2, label: '周二' },
          { value: 3, label: '周三' },
          { value: 4, label: '周四' },
          { value: 5, label: '周五' },
          { value: 6, label: '周六' },
          { value: 7, label: '周日' }
        ]
      };
    },
    watch: {
      id: {
        immediate: true,
        handler(val) {
          this.fillTableNormalData();
        }
      }
    },
    created() {
      this.getGradeList(); // 获取年级列表
    },
    methods: {
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.options = res.data.map((item) => {
            return { value: item.value, label: item.label };
          });
        });
      },
      // 拿到年级
      getGrade(val) {
        let grade = this.options.find((item) => {
          return item.value == val;
        });

        return grade && grade.label;
      },
      //试课单
      async fillTableNormalData() {
        if (!this.id) {
          this.$message({
            message: '没有找到该试课单id',
            type: 'error'
          });
          this.$emit('close');
          return;
        }
        this.editForm = {};
        // console.log(item)

        const that = this;
        this.loading = true;
        let data;
        if (this.reqType == 1) {
          data = await updateDetail(this.id);
          console.log('1111111111111111111111a11', data);
          if (data?.data?.curriculumCode === 'MATH') {
            this.editForm.curriculumCode = 'MATH';
            console.log('进来了，重新请求试课单接口');
            let dataDetail = await queryStudentExperienceDetail(data?.data?.orderId); // 获取数学-试课单详情
            this.editForm.versionName = dataDetail.data.mathExtendedFieldDto.versionName;
            this.editForm.disciplineName = dataDetail.data.mathExtendedFieldDto.disciplineName;
            this.editForm.gradeName = dataDetail.data.mathExtendedFieldDto.gradeName;
            console.log('🚀 ~ 数学fillTableNormalData ~ this.editForm:', this.editForm);
          }
        } else {
          data = await orderApi.getClassCourseListT(this.id);
          console.log('22222222222222', data);
          if (data?.data?.curriculumCode === 'MATH') {
            this.editForm.curriculumCode = 'MATH';
            console.log('进来了，重新请求试课单接口');
            let dataDetail = await queryStudentExperienceDetail(data?.data?.orderNo); // 获取数学-试课单详情
            this.editForm.versionName = dataDetail.data.mathExtendedFieldDto.versionName;
            this.editForm.disciplineName = dataDetail.data.mathExtendedFieldDto.disciplineName;
            this.editForm.gradeName = dataDetail.data.mathExtendedFieldDto.gradeName;
            console.log('🚀 ~ 数学fillTableNormalData ~ this.editForm:', this.editForm);
          }
        }

        this.loading = false;
        if (!data.data.experienceObject) {
          this.$message({
            showClose: true,
            message: '请先填写试课单',
            type: 'error'
          });
          this.$emit('close');
          return;
        }
        this.loading = false;
        data.data.grade = data.data.grade * 1;
        // this.editForm = data.data;
        this.editForm = Object.assign(data.data, this.editForm);
        console.log('🚀 ~ 试课单fillTableNormalData ~ this.editForm:', this.editForm);

        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }

        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }
        console.log(this.editForm);
        this.addVisible = true;
        if (this.editForm.customerType == 1) {
          this.editForm.customerType = '1';
        } else if (this.editForm.customerType == 2) {
          this.editForm.customerType = '2';
        }
        if (this.editForm.isAsk == 1) {
          this.editForm.isAsk = '1';
        } else if (this.editForm.isAsk == 2) {
          this.editForm.isAsk = '2';
        }
        if (this.editForm.isDeal == 1) {
          this.editForm.isDeal = '1';
        } else if (this.editForm.isDeal == 2) {
          this.editForm.isDeal = '2';
        }
        `--------------------`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
    ::v-deep.el-date-editor.el-input {
      width: 100%;
    }
    ::v-deep.el-input-number.is-controls-right .el-input__inner {
      text-align: left;
    }
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }
</style>
