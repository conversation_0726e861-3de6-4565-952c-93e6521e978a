import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const transferOrderRouter = {
  //修改此资转订单路由路径需要将'src\views\login\index.vue'中login方法和'src\permission.js'中的资转订单路由路径一并修改
  path: '/transferOrder/index',
  component: Layout,
  name: 'transferOrder',
  meta: {
    perm: 'm:transferOrder:index',
    title: '资转订单',
    icon: 'market_flow',
    noCache: false
  },
  children: [
    {
      path: 'transferOrder_index',
      component: _import('transferOrder/index'),
      name: 'transferOrderIndex',
      meta: {
        perm: 'm:transferOrder:index',
        title: '资转订单',
        icon: 'market_flow',
        noCache: false
      }
    }
  ]
};

export default transferOrderRouter;
