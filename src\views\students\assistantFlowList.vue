<!--学员流水记录-->
<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left" ref="searchValue"
      :model="dataQuery">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="交易流水号：" prop="flowCode">
            <el-input id="flowCode" v-model="dataQuery.flowCode" name="id" placeholder="请输入交易流水号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号：" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员名称：" prop="realName">
            <el-input id="realName" v-model="dataQuery.realName" name="id" placeholder="请输入学员名称：" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="变动方式：" prop="direction">
            <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;" clearable>
              <el-option v-for="item in [{ value: 1, label: '入' }, { value: 0, label: '出' }]" :key="item.value"
                :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="变动类型：" prop="flowType">
            <el-select v-model="dataQuery.flowType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in financeAccountsType" :key="index" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="变动时间：" prop="RegTime">
            <el-date-picker style="width: 100%;" v-model="RegTime" type="daterange" value-format="yyyy-MM-dd HH:mm:ss"
              unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- <el-form-item label="商户类型：">
        <el-select v-model="dataQuery.merchantRoleTag" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{value:'Agent',label:'市级服务商'},{value:'Dealer',label:'托管中心'},{value:'Market',label:'散户'}]" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="变动方式：">
       <el-select v-model="dataQuery.direction" placeholder="全部" style="width: 185px;">
          <el-option v-for="item in [{value:1,label:'入'},{value:0,label:'出'}]" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="变动类型：">
        <el-select v-model="dataQuery.accountType" filterable value-key="value" placeholder="请选择" >
            <el-option v-for="(item,index) in financeAccountsType" :key="index" :label="item.label" :value="item.value" />
          </el-select>
      </el-form-item> -->


    </el-form>
    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="exportFlow()" v-loading="exportLoading">导出
      </el-button>
      <el-button type="primary" @click="headerList()" v-if="show" style="margin:20px 0 20px 20px">列表显示属性</el-button>
    </el-form>

    <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" default-expand-all :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`"
            :prop="item.value" :label="item.name" header-align="center" :width="item.value!='remark'?((item.value== 'operate'|| item.value=='flowCode' || item.value=='endTime')?200:''):300">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="warning" size="mini" v-if="row.typeOfChange === '出' && currentAdmin.roleTag === 'admin'"
              @click="converterToDeliver(row)">转为交付学时
            </el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- <el-table class="period-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" default-expand-all :header-cell-style="getRowClass" :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column prop="flowCode" label="交易流水号" width="180" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="100"></el-table-column>
      <el-table-column label="操作" width="126">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" v-if="scope.row.typeOfChange === '出' && currentAdmin.roleTag === 'admin'"
            @click="converterToDeliver(scope.row)">转为交付学时
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="merchantName" label="学员名称" width="200" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="realName" label="学员名称"></el-table-column>
      <el-table-column prop="typeOfChange" label="变动方式"></el-table-column>
      <el-table-column prop="flowTypeName" label="变动类型"></el-table-column>
      <el-table-column prop="beforeHours" label="变动前学时（节）" width="140"></el-table-column>
      <el-table-column prop="userHours" label="变动学时（节）" width="120"></el-table-column>
      <el-table-column prop="afterHours" label="变动后学时（节）" width="140"></el-table-column>
      <el-table-column prop="endTime" label="变动时间" width="170"></el-table-column>
      <el-table-column prop="remark" label="变动描述" width="360" :show-overflow-tooltip="true"></el-table-column>
    </el-table> -->
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems"/>

  </div>
</template>

<script>
import
merchantAccountFlowApi
  from '@/api/merchantAccountFlow'
import enTypes from '@/api/bstatus'
import {
  pageParamNames
} from '@/utils/constants'
import schoolList from '@/api/schoolList'
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";

import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: 'assistantFlowList',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      tableLoading: false,
      exportLoading: false,
      classHoursBeforeTheChange: 0,
      changeOfClassHours: 0,
      classHoursAfterTheChange: 0,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [{
        name: 1,
        title: '1111'
      }],
      currentAdmin: {},
      RegTime: [],
      dataQuery: {
        flowCode:null,
        studentCode:null,
        realName:null,
        direction:null,
        flowType:null,
      },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      financeAccountsType: [],

      
      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '交易流水号',
          value: 'flowCode'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '学员名称',
          value: 'realName'
        },
        
        {
          name: '变动方式',
          value: 'typeOfChange'
        },
        {
          name: '变动类型',
          value: 'flowTypeName'
        },
        {
          name: '变动前学时（节）',
          value: 'beforeHours'
        },
        {
          name: '变动学时（节）',
          value: 'userHours'
        },
        {
          name: '变动后学时（节）',
          value: 'afterHours'
        },
        {
          name: '变动时间',
          value: 'endTime'
        },
        {
          name: '变动描述',
          value: 'remark'
        }],
      tableHeaderList: [], // 获取表头数据

      show:true

    }
  },
  created() {
    this.getCurrentAdmin();
    this.fetchData();
    //获取变动类型
    this.getFinanceAccountsType();
    this.getHeaderlist();

  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    getCurrentAdmin() {
      schoolList.getCurrentAdmin().then((res) => {
        this.currentAdmin = res.data
      })
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData()
    },
    // 查询提现列表
    fetchData() {
      const that = this
      that.tableLoading = true
      var a = that.RegTime
      if (a.length > 0) {
        that.dataQuery.startDate = a[0]
        console.log(that.dataQuery.startDate = a[0])
        that.dataQuery.endDate = a[1]
      } else {
        that.dataQuery.startDate = ''
        that.dataQuery.endDate = ''
      }
      merchantAccountFlowApi
        .studentCourseFlowList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.show = true;
          that.tableData = res.data.data
          that.tableLoading = false
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          )
        }).catch(err=>{
          console.log(err)
          that.show = false;
        })
      merchantAccountFlowApi.detailsOfStudentCourseFlowChanges(that.tablePage.currentPage,
        that.tablePage.size,
        that.dataQuery).then(res => {
          that.classHoursBeforeTheChange = res.data.classHoursBeforeTheChange
          that.changeOfClassHours = res.data.changeOfClassHours
          that.classHoursAfterTheChange = res.data.classHoursAfterTheChange
        })
    },
    //新增操作
    clickAdd() {
      this.dialogVisible = true
    },
    // 打开修改登陆账号
    openLogin() {
      this.showLoginAccount = true
    },
    closeLogin() {
      this.showLoginAccount = false
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val
      this.fetchData()
    },
    async converterToDeliver(row) {
      await this.$confirm("您确定要将学员<<" + row.realName + ">>在" + row.endTime + "的学习记录转为集中交付流水吗?");
      merchantAccountFlowApi.toDeliver(row.id).then(res => {
        this.$message.success("转换成功")
      })
    },
    //导出
    exportFlow() {
      const that = this
      that.exportLoading = true
      //
      merchantAccountFlowApi.studentSimpleExecl(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '课程流水表.xls')
        document.body.appendChild(link)
        link.click()
        that.exportLoading = false
      })
    },
    //获取变动类型
    getFinanceAccountsType() {
      var enType = 'CourseAccountsType'
      enTypes.getEnumerationAggregation(enType).then(res => {
        this.financeAccountsType = res.data
      })
    },

    //重置
    rest() {
      this.$refs.searchValue.resetFields();
      this.RegTime= []
      this.fetchData();
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },

     // 接收子组件选择的表头数据
     selectedItems(arr) {
      let data = {
        type: "assistantFlowList",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'assistantFlowList'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
}
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
