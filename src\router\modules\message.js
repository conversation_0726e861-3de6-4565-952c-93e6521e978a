
import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const messageRouter = {
  path: '/message/messageSetting',
  component: Layout,
  name: 'message',
  meta: {
    perm: 'm:message:messageSetting',
    title: '消息管理',
    icon: 'divisionList',
    noCache: true
  },
  alwaysShow: true,
  children: [
    {
      path: 'message_index',
      component: _import('message/messageSetting'),
      name: 'messageSetting',

      meta: {
        perm: 'm:message:messageSetting',
        title: '消息配置',
        icon: 'divisionList',
        noCache: true
      }
    }
  ]
}

export default messageRouter
