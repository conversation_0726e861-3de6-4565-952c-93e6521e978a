<!--交付中心-入库管理-教练管理-->
zhu<PERSON><PERSON>
<template>
  <div>
    <div class="frame">
      <el-form label-width="90px" ref="querydate1" :model="querydate1">
        <!-- 1 -->
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label="教练名称：" prop="name">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                :filter-method="filterValue"
                clearable
                v-model="querydate1.name"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="教练名称:" prop="name">
              
              <el-input v-model="querydate1.name" clearable size="mini" placeholder="请选择" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="6">
            <el-form-item label="授学时间:">
              <el-date-picker
                size:mini
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                v-model="querydate1.dateTime"
                type="date"
                placeholder="选择日期时间">
              </el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
            <el-form-item label="状态:" prop="status">
              <el-select v-model="querydate1.status" clearable size="mini" placeholder="请选择" style="width: 10vw">
                <el-option label="注销" value="0"></el-option>
                <el-option label="正常" value="1"></el-option>
                <el-option label="待调课" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="授课方式:" prop="teachingType">
              <el-select v-model="querydate1.teachingType" clearable size="mini" placeholder="请选择" style="width: 10vw">
                <el-option label="远程" value="1"></el-option>
                <el-option label="线下" value="2"></el-option>
                <el-option label="远程和线下" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="账号:" prop="mobile">
              <el-input v-model="querydate1.mobile" clearable size="mini" placeholder="请输入" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付编号:" prop="deliverMerchant">
              <el-input v-model="querydate1.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付名称:" prop="deliverName">
              <el-input v-model="querydate1.deliverName" clearable placeholder="请输入交付中心名称" size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否转移:" prop="isTransfer">
              <el-select v-model="querydate1.isTransfer" clearable size="mini" placeholder="请选择" style="width: 10vw">
                <el-option label="未转移" value="0"></el-option>
                <el-option label="已转移" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="querydate1.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="isAdmin ? 6 : 18" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <!-- <el-table-column prop="deliverMerchant" min-width="110" label="交付中心编号" header-align="center" />
      <el-table-column prop="deliverName" min-width="130" label="交付中心名称" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center" width="300">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" v-if="isAdmin" @click="transfer(scope.row)"
            :disabled="scope.row.status == 0">转移</el-button>
          <el-button type="primary" size="mini" v-if="scope.row.status !== 0" @click="delFN(scope.row)"
            :disabled="scope.row.isTransfer == 1">注销</el-button>
          <el-button type="primary" size="mini" v-if="scope.row.status == 0" @click="startFn(scope.row)"
            :disabled="scope.row.isTransfer == 1">启用</el-button>
          <el-button type="primary" size="mini" v-if="scope.row.status !== 0" @click="changeClassFn(scope.row)"
            :disabled="scope.row.isTransfer == 1">调课
          </el-button>
        </template>
      </el-table-column> -->
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :width="item.value == 'operate' ? '300' : ''"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="warning" size="mini" v-if="isAdmin" @click="transfer(row)" :disabled="row.status == 0">转移</el-button>
            <el-button type="primary" size="mini" v-if="row.status !== 0" @click="delFN(row)" :disabled="row.isTransfer == 1">注销</el-button>
            <el-button type="primary" size="mini" v-if="row.status == 0" @click="startFn(row)" :disabled="row.isTransfer == 1">启用</el-button>
            <el-button type="primary" size="mini" v-if="row.status !== 0" @click="changeClassFn(row)" :disabled="row.isTransfer == 1">调课</el-button>
          </div>

          <div v-if="item.value == 'teachingType'">
            <span v-if="row.teachingType == 1 || row.teachingType == 2 || row.teachingType == 3">
              {{ row.teachingType != 1 ? (row.teachingType == 2 ? '线下' : '远程和线下') : '远程' }}
            </span>
            <span v-else>暂无</span>
          </div>

          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ status(row) }}</span>
          </div>

          <div v-else-if="item.value == 'isTransfer'">
            <span :class="row.isTransfer == 1 ? 'transferred' : 'no_transferred'">{{ row.isTransfer == 1 ? '已转移' : '未转移' }}</span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="teacherId" label="教练编号" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="name" label="教练名称" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="mobile" label="账号" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="lastStudyTime" label="授学时间" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="teachingType" label="授课方式" :formatter="teachingType"
        header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="planCourseHours" label="剩余授课/时" header-align="center"></el-table-column> -->
      <!-- <el-table-column prop="status" label="状态" header-align="center" :formatter="status">
        <template slot-scope="{ row }">
          <span :class="statusClass(row.status)">{{ status(row) }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="isTransfer" label="是否转移" header-align="center">
        <template slot-scope="scope">
          <span :class="scope.row.isTransfer == 1 ? 'transferred' : 'no_transferred'">{{ scope.row.isTransfer == 1 ? '已转移' : '未转移'
          }}</span>
        </template></el-table-column> -->
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="querydate1.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="querydate1.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 注销弹框 -->
    <el-dialog :visible.sync="btndialog" width="20%">
      <div style="text-align: center; font-size: 18px; color: #000">是否注销本账号</div>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="btnOk">确 定</el-button>
        <el-button @click="btndialog = false" size="small">取 消</el-button>
      </el-row>
    </el-dialog>
    <!-- 调课弹框 -->
    <el-dialog :visible.sync="tkedialog" width="20%">
      <div style="text-align: center; font-size: 18px; color: #000">此账号现有已排课程</div>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="(tkedialog = false), (classCardstyle = true)">调 课</el-button>
        <el-button @click="tkedialog = false" size="small">取 消</el-button>
      </el-row>
    </el-dialog>
    <tiaokeDialog @changeDrawer1="changeDrawer1" @updateList="initData" :classCardstyle="classCardstyle" :direction="direction" ref="studentcar" />

    <el-dialog title="" :visible.sync="promptVisible" width="24%" center>
      <div class="" style="text-align: center; padding-bottom: 30px">
        <i class="el-icon-warning" style="color: #f7b108; font-size: 60px"></i>
        <div style="margin-top: 20px; font-size: 17px">当前该教练还有未完成的课程，请联系取消后再转移</div>
      </div>
    </el-dialog>

    <el-dialog title="转移教练" :visible.sync="transferVisible" width="24%" center :close-on-click-modal="false">
      <el-form ref="transferForm" label-width="120px" :model="transferForm" :rules="rules" label-position="left">
        <el-form-item label="交付中心名称：" prop="deliverMerchant">
          <el-select v-model="transferForm.deliverMerchant" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterLists" :key="item.merchantCode" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item style="margin-top: 50px">
          <el-button type="primary" @click="submitForm">确定</el-button>
          <el-button @click="resetForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { cancelTeacher, getTeacherStudentList, teacherManagerList, belongDeliverAndAllDeliver, transferTeacher, teacherClass } from '@/api/rukuManage/zhuTeacher';
  import { selAllTeacher } from '@/api/studentClass/changeList';
  import tiaokeDialog from './dialog/tiaokeDialog.vue';
  import FileSaver from 'file-saver';
  import XLSX from 'xlsx';
  import ls from '@/api/sessionStorage';
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';

  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';

  export default {
    name: 'assistManage',
    components: {
      tiaokeDialog,
      HeaderSettingsDialog
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        classCardstyle: false, //抽屉状态
        direction: 'rtl', //超哪边打开
        value1: '',
        tkedialog: false,
        btndialog: false,
        querydate1: {
          dateTime: '',
          mobile: '',
          name: '',
          status: '',
          isTransfer: '',
          teachingType: '',
          pageNum: 1,
          pageSize: 10 //页容量
        },
        timeAll: [],
        childVisible: false, //是否展示抽屉
        total: null,
        teacher: '',
        contentType: '',
        tableData: [],
        teacherTime: false,
        id: '',
        getTeacherStudentList: {
          teacherId: ''
        },
        isAdmin: false,

        promptVisible: false,
        transferVisible: false,
        deliverCenterLists: {}, // 可指派交付中心
        transferForm: {
          teacherId: '',
          deliverMerchant: ''
        },

        rules: {
          deliverMerchant: [{ required: true, message: '请选择交付中心', trigger: 'change' }]
        },

        option: [],
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          name: ''
        },

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          },
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '教练编号',
            value: 'teacherId'
          },
          {
            name: '教练名称',
            value: 'name'
          },
          {
            name: '课程类型',
            value: 'curriculumName'
          },
          {
            name: '账号',
            value: 'mobile'
          },
          {
            name: '授课方式',
            value: 'teachingType'
          },
          {
            name: '状态',
            value: 'status'
          },
          {
            name: '是否转移',
            value: 'isTransfer'
          }
        ],

        tableHeaderList: [], // 获取表头数据
        courseList: []
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      this.initData();
      this.getTeacherList();
      this.getHeaderlist();
    },
    mounted() {
      this.getbvstatusList();
    },
    methods: {
      getbvstatusList() {
        bvstatusList({}).then((res) => {
          this.courseList = res.data;
        });
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      status(val) {
        if (val.status == 0) {
          return '已注销';
        } else if (val.status == 1) {
          return '正常';
        } else if (val.status == 2) {
          return '待调课';
        }
      },
      statusClass(status) {
        switch (status) {
          case 0:
            return '';
          case 1:
            return 'normal';
          case 2:
            return 'error';
        }
      },
      initData01() {
        (this.querydate1.pageNum = 1), (this.querydate1.pageSize = 10), this.initData();
      },
      async initData() {
        if ((this.querydate1.curriculumId && this.querydate1.curriculumId.length != 19) || !this.querydate1.curriculumId) {
          delete this.querydate1.curriculumId;
        }
        console.log(this.querydate1);

        let { data } = await teacherManagerList(this.querydate1);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
      },
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.querydate1.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.querydate1.pageNum = val;
        this.initData();
      },
      changeDrawer1(v) {
        this.classCardstyle = v;
      },
      // 注销按钮
      async delFN(row) {
        this.id = row.teacherId;
        this.btndialog = true;
      },
      // 启用
      async startFn(row) {
        this.id = row.teacherId;
        let res = await cancelTeacher(this.id);
        this.initData();
        this.$message.success('启用成功');
      },
      // 确认注销
      async btnOk() {
        this.btndialog = false;
        let res = await cancelTeacher(this.id);
        if (res.data == 1) {
          this.$message.success('注销成功');
        } else {
          this.tkedialog = true;
          this.getTeacherStudentList.teacherId = this.id;
          // debugger
          let { data } = await getTeacherStudentList(this.getTeacherStudentList);
          this.$refs.studentcar.teacherid = this.id;
          this.$refs.studentcar.studentList = data.filter((item) => item !== null);
          console.log(this.$refs.studentcar.studentList);
        }
        this.initData();
      },
      // 调课按钮
      async changeClassFn(row) {
        this.getTeacherStudentList.teacherId = row.teacherId;
        // debugger
        let { data } = await getTeacherStudentList(this.getTeacherStudentList);
        this.$refs.studentcar.teacherid = row.teacherId;
        const filteredArray = data.filter((item) => item !== null);
        this.$refs.studentcar.studentList = filteredArray;
        console.log(this.$refs.studentcar.studentList);
        this.classCardstyle = true;
      },

      // 转移教练
      async transfer(row) {
        let data = {
          teacherId: row.teacherId
        };
        let res = await teacherClass(data);
        console.log(res);
        if (res.data) {
          this.promptVisible = true;
        } else {
          this.transferForm.deliverMerchant = '';
          this.transferForm.teacherId = row.teacherId;
          this.transferVisible = true;
          this.editDelivery();
        }
      },

      submitForm() {
        let that = this;
        that.$refs['transferForm'].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '转移中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            transferTeacher(that.transferForm)
              .then(() => {
                that.transferVisible = false;
                loading.close();
                that.initData();
                that.$message({
                  showClose: true,
                  message: '操作成功',
                  type: 'success'
                });
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },

      resetForm() {
        this.$refs.transferForm.resetFields();
        this.transferVisible = false;
      },
      //定义导出Excel表格事件
      exportExcel() {
        /* 从表生成工作簿对象 */
        var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'));
        /* 获取二进制字符串作为输出 */
        var wbout = XLSX.write(wb, {
          bookType: 'xlsx',
          bookSST: true,
          type: 'array'
        });
        try {
          FileSaver.saveAs(
            //Blob 对象表示一个不可变、原始数据的类文件对象。
            //Blob 表示的不一定是JavaScript原生格式的数据。
            //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
            //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
            new Blob([wbout], { type: 'application/octet-stream' }),
            //设置导出文件名称
            'sheetjs.xlsx'
          );
        } catch (e) {
          if (typeof console !== 'undefined') console.log(e, wbout);
        }
        return wbout;
      },

      //重置
      rest() {
        this.$refs.querydate1.resetFields();
        this.initData();
      },

      // 获取可指派交付中心列表
      editDelivery() {
        belongDeliverAndAllDeliver().then((res) => {
          this.deliverCenterLists = res.data;
        });
      },

      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 获取教练
      async getTeacherList() {
        let allData = await selAllTeacher(this.selectObj);
        this.option = this.option.concat(allData.data.data);
      },

      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getTeacherList();
      },

      changeMessage() {
        this.$forceUpdate();
      },

      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydate1.name == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.name = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'assistManage',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'assistManage'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  body {
    background-color: #f5f7fa;
  }

  .frame {
    // margin:  0 30px;
    background-color: rgba(255, 255, 255);
    padding: 20px;
  }

  .el-button--success {
    color: #ffffff;
    background-color: #6ed7c4;
    border-color: #6ed7c4;
  }

  .transferred {
    color: #ea2424;
  }

  .no_transferred {
    color: #1cb31c;
  }
</style>
