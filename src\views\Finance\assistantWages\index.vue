<!-- 新班级列表 -->
<template>
  <div class="app-container">
    <!-- 课程类型 -->
    <el-row style="margin: 20px 0 20px 10px">
      <el-col :span="2.5">
        <el-radio-group v-model="courseType" size="medium" @change="handleTabsClick">
          <el-radio-button label="1">1v1</el-radio-button>
          <el-radio-button label="2">1v多</el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <!--  -->
    <OneToOne v-if="courseType == '1'"></OneToOne>
    <OneToMany v-if="courseType == '2'"></OneToMany>
  </div>
</template>

<script>
  import OneToOne from './components/OneToOne.vue'; // 工资管理 1v1
  import OneToMany from './components/OneToMany.vue'; // 工资管理 1v多

  export default {
    name: 'newClassList',
    components: {
      OneToOne,
      OneToMany
    },
    data() {
      return {
        courseType: '2' // 当前选中类型
      };
    },
    created() {},
    mounted() {},
    methods: {
      // 切换课程大类tabs
      handleTabsClick() {
        // console.log('🚀 ~ handleTabsClick ~ handleTabsClick:', this.courseType, this.orderStatus);
        // TODO: 调用接口获取课程列表
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  ::v-deep.el-date-editor.el-input {
    width: 100%;
  }
  ::v-deep.el-input-number.is-controls-right .el-input__inner {
    text-align: left;
  }
  ::v-deep.el-select {
    width: 100%;
  }
  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
</style>
