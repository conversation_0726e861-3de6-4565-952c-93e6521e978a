<!--交付中心-接单管理-试课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="4">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="学生来源：" prop="source">
              <el-select v-model="querydata.source" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option v-for="(item,index) in sourceOptions" :key="index" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="派单时间:" label-width="" prop="">
              <el-date-picker v-model="time1" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange" size="small" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                @change='changeTime'>
              </el-date-picker>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付中心名称：" label-width="120px" prop="deliverName">
              <el-select v-el-select-loadmore="handleLoadmore" :loading="loadingShip" remote clearable
                v-model="querydata.deliverName" filterable reserve-keyword placeholder="请选择" @input="changeMessage"
                @blur="clearSearchRecord" @change="changeTeacher">
                <el-option v-for="(item,index) in option" :key="index" :label="item.deliverMerchantName"
                  :value="item.deliverMerchantName">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :offset="0">
            <el-form-item label="交付中心编号:" label-width="100px" prop="deliverMerchant">
              <el-input v-model.trim="querydata.deliverMerchant" style="width:200px;" size="small" @change="changeInput"
                placeholder="请输入"></el-input>
              <!-- @change="changeInput" -->
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-row>
      <el-col :span="2" :offset="0">
        <!-- 列表显示属性 -->
        <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
      </el-col>
      <el-col :span="4" :offset="2" style="padding-top:20px;">
        <el-radio-group v-model="querydata.type" @change="changeRadio">
          <el-radio-button label="1">试课列表</el-radio-button>
          <el-radio-button label="2">正式课列表</el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-table :data="tableData" border style="width: 100%" id="out-table" v-loading="tableLoading "
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" size="mini" fit>
      <el-table-column v-for="(item, index) in tableHeaderList" :key="index" :prop="item.value" :label="item.name"
        header-align="center" min-width="150">
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="querydata.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>

    <!-- 流转指派历史弹框 -->
    <el-dialog title="历史派单记录" :close-on-click-modal=false :visible.sync="dialogHistory" width="30%" center>
      <div style="overflow:auto;margin: 30px 0;height:400px">
        <!-- <ul class="infinite-list" v-infinite-scroll="load" style="overflow:auto;margin: 30px 0;height:400px">
        </ul> -->
        <el-steps direction="vertical" :active="0" :space='200'>
          <el-step v-for="(item, index) in historys" :title="item.time" :key="index" icon="iconfont icon-luyin">
            <template slot="description">
              <div style="white-space: pre-wrap">{{item.history}}</div>
            </template>
            <template slot="icon">
              <i class="el-icon-info" v-if="index==0" style="font-size:24px;"></i>
              <i class="el-icon-success" v-else style="font-size:24px;"></i>
            </template>
          </el-step>
        </el-steps>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHistory = false">取 消</el-button>
        <el-button type="primary" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import ls from '@/api/sessionStorage'
import statistic from '../components/statistic.vue'
import { deliverlist } from "@/api/peizhi/peizhi"
import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue'
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard'
import { getHistory, getLossTable } from '@/api/orderManage'
import { CodeToText, regionData, TextToCode } from "element-china-area-data";
export default {
  name: 'testStudent',
  components: {
    HeaderSettingsDialog, statistic
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) {
            binding.value()
          }
        })
      }
    }
  },
  data() {
    return {
      isAdmin: false,
      screenWidth: window.screen.width, //屏幕宽度
      time1: [],
      loadingShip: false,
      option: [],
      optionTotal: 0,
      // 搜索表单
      querydata: {
        type: 1,
        source: '',
        studentName: '',
        studentCode: '',
        startTime: '',
        endTime: '',
        deliverMerchant: "",
        pageNum: 1,
        pageSize: 10 //页容量
      },
      sourceOptions: [
        {
          label: '全部', value: ''
        },
        {
          label: '轮排抢单', value: 1
        },
        {
          label: '合伙人派单', value: 2
        },
        {
          label: '总部派单', value: 3
        },
        {
          label: '变更交付小组派单', value: 4
        },
        {
          label: '主交付中心派单', value: 7
        },
        {
          label: '次交付中心派单', value: 8
        },
        {
          label: '轮排派单', value: 9
        },
        {
          label: '管理员处理', value: 10
        },
      ],
      // 表格数据总数量
      total: null,
      // 表格数据
      tableData: [],
      tableLoading: false,
      option: [],
      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '交付中心名称',
          value: 'deliverName'
        },
        {
          name: '交付中心编号',
          value: 'deliverMerchant'
        },
        {
          name: '课程类型',
          value: 'type'
        },
        {
          name: '课程时间',
          value: 'courseTime'
        },
        {
          name: '派单时间',
          value: 'dispatchTime'
        },
        {
          name: '学生来源',
          value: 'source'
        },
        {
          name: '转移理由',
          value: 'reason'
        }
      ],
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        deliverName: ''
      },
      tableHeaderList: [], // 获取表头数据
      dialogHistory: false,
      historys: [],
      isSubmit: false
    }
  },
  created() {
    this.getHeaderlist();
    this.getTeacherList();
    this.initData()
  },
  watch: {
    'querydata.type': function (val) {
      console.log(val)
      if (val == 1) {
        this.sourceOptions = [
          {
            label: '全部', value: ''
          },
          {
            label: '轮排抢单', value: 1
          },
          {
            label: '合伙人派单', value: 2
          },
          {
            label: '总部派单', value: 3
          },
          {
            label: '变更交付小组派单', value: 4
          },
          {
            label: '主交付中心派单', value: 7
          },
          {
            label: '次交付中心派单', value: 8
          },
          {
            label: '轮排派单', value: 9
          },
          {
            label: '管理员处理', value: 10
          },
        ]
      } else {
        this.sourceOptions = [
          {
            label: '全部', value: ''
          },
          {
            label: '轮排抢单', value: 1
          },
          {
            label: '合伙人派单', value: 2
          },
          {
            label: '总部派单', value: 3
          },
          {
            label: '试课派单', value: 4
          },
          {
            label: '变更交付小组派单', value: 5
          },
          {
            label: '学员转移', value: 6
          },
          {
            label: '主交付中心派单', value: 7
          },
          {
            label: '次交付中心派单', value: 8
          },
          {
            label: '轮排派单', value: 9
          },
          {
            label: '管理员处理', value: 10
          },
        ]
      }
    }
  },
  methods: {
    // 清除交付中心名称事件
    clearSearchRecord() {
      setTimeout(() => {
        if (this.querydata.deliverName == '') {
          this.option = []
          this.selectObj.pageNum = 1
          this.selectObj.deliverName = ''
          this.getTeacherList()
        }
      }, 500)
      this.$forceUpdate()
    },
    // 获取交付中心
    async getTeacherList() {
      if (this.selectObj.pageNum == this.optionTotal) return
      let allData = await deliverlist(this.selectObj)
      this.option = this.option.concat(allData.data.data);
      this.optionTotal = Number(allData.data.totalPage)
      // this.selectObj.pageNum++
    },
    // 改变交付中心编号事件
    changeInput(e) {
      if (!!e) {
        let arr = this.option.filter(i => i.deliverMerchantCode == e)
        this.querydata.deliverName = arr.length > 0 ? arr[0].deliverMerchantName : this.querydata.deliverName
      }
    },
    // 改变交付中心名称事件
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.querydata.deliverCode = ''
        this.selectObj.pageNum = 1;
        this.getTeacherList();
      } else {
        let arr = this.option.filter(i => i.deliverMerchantName == e)
        this.querydata.deliverMerchant = arr[0].deliverMerchantCode
      }
    },
    changeMessage() {
      this.$forceUpdate();
    },
    changeRadio(e) {
      console.log(e)
      this.querydata.type = e
      this.initData()
    },
    changeTime(e) {
      console.log(e);
      if (e) {
        this.querydata.startTime = e[0]
        this.querydata.endTime = e[1]
        this.initData()
      } else {
        this.querydata = {
          type: 1,
          source: '',
          studentName: '',
          studentCode: '',
          startTime: '',
          endTime: '',
          deliverMerchant: "",
          pageNum: 1,
          pageSize: 10 //页容量
        }
        this.initData()
      }
    },
    // 派单历史
    async getDetail(id) {
      let { data } = await getHistory(id)
      // console.log(data)
      this.historys = data
      setTimeout(() => {
        this.dialogHistory = true
      }, 500);
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        if (this.selectObj.pageNum == this.optionTotal) return//节流防抖
        this.selectObj.pageNum++
        this.getTeacherList()
      }
    },
    // 时间戳转换 HH:MM:SS
    getTimeFromTimestamp(timestamp) {
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    },
    closeEdit() {
      this.$refs.editForm.clearValidate()
      this.editList = false
      this.addVisible = false
    },
    searchData() {
      this.querydata.pageNum = 1
      this.querydata.pageSize = 10
      this.initData()
    },
    // 初始化表格
    async initData() {
      let that = this
      that.tableLoading = true
      let { data } = await getLossTable(this.querydata)
      console.log(data, '=======================')
      if (data && data.data) {
        this.tableData = data.data
        this.total = Number(data.totalItems)

      } else {
        this.tableData = []
        this.total = 0
      }
      that.tableLoading = false
      this.$forceUpdate()

    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },

    //重置
    rest() {
      this.$refs.querydata.resetFields()
      this.querydata = {
        type: 1,
        source: '',
        studentName: '',
        studentCode: '',
        startTime: '',
        endTime: '',
        deliverMerchant: "",
        pageNum: 1,
        pageSize: 10 //页容量
      }
      this.initData()
    },
    // 表头设置
    headerList() {
      if (this.tableHeaderList.length > 0) {
        // console.log(this.tableHeaderList)
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value) // 回显
      }
      this.HeaderSettingsStyle = true
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e
    },
    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {}
      data = {
        type: 'lossTable',
        value: JSON.stringify(arr)
      }
      this.setHeaderSettings(data)
    },
    // 获取表头设置
    async getHeaderlist() {
      let data = { type: 'lossTable' }
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value)
        } else {
          this.tableHeaderList = this.headerSettings
        }
      })
    },
    // 设置表头
    async setHeaderSettings(data) {
      console.log(data)
      await setTableList(data).then((res) => {
        this.$message.success('操作成功')
        this.HeaderSettingsStyle = false
        this.getHeaderlist()
      })
    },
    // 分页
    handleSizeChange(val) {
      this.querydata.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.querydata.pageNum = val
      this.initData()
    },
  }
}
</script>

<style lang="scss" scoped>
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

body {
  background-color: #f5f7fa;
}

.frame {
  // margin:  0 30px;
  background-color: rgba(255, 255, 255);
  padding: 20px;
}

.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #6ed7c4;
}

.transferred {
  color: #ea2424;
}

.no_transferred {
  color: #1cb31c;
}
</style>
