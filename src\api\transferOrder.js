/**
 * “资转订单”相关接口
 */
import request from '@/utils/request';
function removeEmptyProperties(obj) {
  // 创建一个新的对象来存储非空的属性
  const result = {};

  for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
          const value = obj[key];

          if (value !== null && value !== undefined && value !== '') {
              // 非空、非对象值或数组且长度大于0
              result[key] = value;
          }
      }
  }
  return result;
}
export default {
  queryList(queryParam, pageParam) {
    console.log(queryParam, pageParam);
    const result = removeEmptyProperties(queryParam);
    return request({
      url: '/deliver/assetTransfer/getAssetTransferList',
      method: 'get',
      params: {
        pageNum: pageParam.currentPage,
        pageSize: pageParam.size,
        ...result
      }
    });
  },
  goPay(data) {
    return request({
      url: '/deliver/assetTransfer/rechargeInfo',
      method: 'get',
      params: {
        ...data
      }
    });
  }
};
