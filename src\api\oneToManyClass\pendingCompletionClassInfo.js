// 一对多学员管理-待完善上课信息表
import request from '@/utils/request';
/**
 * 获取试课待完善上课信息表数据
 * @param {*} data
 * @returns
 */
export const getPendingCompletionClassInfoTrial = (data) => {
  return request({
    url: '/zx/exp/pageExperienceOrder',
    method: 'GET',
    params: { ...data, isOneToMore: 1 }
  });
};
/**
 * 提交试课待完善上课信息表
 * @param {*} data
 */
export const updateClassInfoTrial = (data) => {
  return request({
    url: '/deliver/web/experience/save',
    method: 'POST',
    data
  });
};
/**
 * 获取一对多上课时间列表
 * @param {*} data
 * @returns
 */
export const getStudyDateData = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/getClassTimeList',
    method: 'GET',
    params: data
  });
};
/**
 * 获取正课待完善上课信息表数据
 * @param {*} data
 * @returns
 */
export const getPendingCompletionClassInfoFormal = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/selStudentContactInfoPageOneToMuch',
    params: data
  });
};
