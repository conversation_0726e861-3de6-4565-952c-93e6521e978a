<template>
  <div class="dialog">
    <el-dialog title="反馈详情" :visible.sync="studentFeedBackDetailstyle_" :direction="direction" @close="handleClose">
      <el-form ref="form" :model="detail" label-width="130px">
        <el-form-item label="家长评分" prop style="width: 50%">
          <el-rate disabled v-model="detail.parentScore"></el-rate>
        </el-form-item>

        <el-form-item label="家长反馈" prop>
          <textarea disabled v-model="detail.parentFeedback" name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>

        <el-form-item v-if="detail.referrerScore > 0" label="推荐人评分" prop style="width: 50%">
          <el-rate disabled v-model="detail.referrerScore"></el-rate>
        </el-form-item>

        <el-form-item v-if="detail.referrerFeedback" label="推荐人反馈" prop>
          <textarea v-model="detail.referrerFeedback" disabled name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>

        <el-form-item label="教练反馈">
          <textarea v-model="detail.feedback" disabled name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>

        <el-form-item label="体验后的学习意愿" prop="">
          <textarea v-model="detail.studyIntention" disabled name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>

        <el-form-item label="学员信息状况反馈" prop="">
          <textarea v-model="detail.feedback" disabled name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>

        <el-form-item label="交付中心回复" prop="">
          <textarea v-model="detail.deliverReply" disabled name="" id="" cols="50" rows="8" style="border-color: rgba(187, 187, 187, 1)"></textarea>
        </el-form-item>
      </el-form>
      <!-- <el-row>
        <el-col style="margin-top:2vw;margin-left:1vw">家长评分：<el-rate v-model="detail.parentScore"  disabled></el-rate></el-col>
        <el-col style="margin-top:2vw;margin-left:1vw">家长反馈:{{ detail.parentFeedback }}</el-col>
        <el-col style="margin-top:2vw;margin-left:1vw">推荐人评分：{{ detail.referrerScore }}</el-col>
        <el-col style="margin-top:2vw;margin-left:1vw">推荐人反馈:{{ detail.referrerFeedback }}</el-col>
        <el-col style="margin-top:2vw;margin-left:1vw">教练反馈：{{ detail.feedback }}</el-col>
        <el-col style="margin-top:2vw;margin-left:1vw">交付中心回复:{{ detail.deliverReply }}</el-col>

      </el-row> -->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="studentFeedBackDetailstyle_ = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { replyStudentFeedback } from '@/api/paikeManage/studentFeedback';

  export default {
    //传值
    props: {
      //父组件向子组件传 drawer；这里默认会关闭状态
      studentFeedBackDetailstyle: {
        type: Boolean,
        default: false
      },
      //Drawer 打开的方向
      direction: {
        type: String,
        default: 'rtl'
      }
    },
    name: 'studentFeedBackDetail',
    data() {
      return {
        value1: '',
        detail: {
          //回显接口数据
          parentScore: '',
          parentFeedback: '',
          referrerScore: '',
          referrerFeedback: '',
          feedback: '',
          deliverReply: ''
        },
        reply: {
          content: '',
          deliverCode: '',
          id: ''
        }
      };
    },
    //计算属性
    computed: {
      studentFeedBackDetailstyle_: {
        get() {
          return this.studentFeedBackDetailstyle;
        },
        //值一改变就会调用set【可以用set方法去改变父组件的值】
        set(v) {
          //   console.log(v, 'v')
          this.$emit('changeDrawer', v);
        }
      }
    },
    methods: {
      //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
      handleClose() {
        this.$emit('changeDrawer', false);
      },
      async modifyBtn() {
        //修改课程表列表
        this.reply.id = this.detail.id;
        this.reply.deliverCode = this.detail.deliverMerchant;
        this.reply.content = this.detail.deliverReply;
        await replyStudentFeedback(this.reply), this.$message.success('修改成功');
        this.studentFeedBackDetailstyle_ = false;
        this.$emit('updateList');
      }
    }
  };
</script>

<style lang="scss">
  .borders {
    margin: 1vw 1vw;
    width: 28vw;
    height: 35vw;
    border: 1px solid #cac8c8;
    border-radius: 20px;
  }
</style>

<style scoped>
  div /deep/ .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    width: 100%;
  }
  .el-rate {
    height: 20px;
    line-height: 1;
    display: inline-block;
  }
  ::v-deep .el-drawer__header {
    color: #000;
    font-size: 22px;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow: auto;
    /* overflow-x: auto; */
  }
</style>
