<template>
  <div class="dialog">
    <el-drawer title="学员课程表" :visible.sync="classCardstyle_" :direction="direction" @close="handleClose"
      :size="screenWidth > 1300 ? '30%' : '80vw'">
      <div :class="screenWidth > 1300 ? 'borders':'borders-phone'">
        <el-row>
          <el-col style="margin-top:2vw;margin-left:1vw">学员名称：{{ classCardnum.studentName }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">
            课程名称:
            <el-select v-model="classCardnum.courseType" placeholder="请选择">
              <el-option label="鼎英语" :value="1"></el-option>
            </el-select>
          </el-col>

          <el-col style="margin-top:2vw;margin-left:1vw">
            选择日期:
            <el-date-picker v-model="classCardnum.dateTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date"
              :style="{width: screenWidth > 1300 ? '11vw' : '68%'}" placeholder="选择日期">
            </el-date-picker>
          </el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">课程时间:
            <el-time-picker v-model="classCardnum.startStudyTime" :clearable="false" format="HH:mm" value-format="HH:mm"
              :style="{width: screenWidth > 1300 ? '7vw' : '34%'}" style="margin-right: 1vw" :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="课程开始时间">
            </el-time-picker>
            <el-time-picker format="HH:mm" :clearable="false" :style="{width: screenWidth > 1300 ? '7vw' : '34%'}"
              value-format="HH:mm" v-model="classCardnum.endStudyTime" :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="课程结束时间">
            </el-time-picker>
          </el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">复习时间:
            <el-time-picker v-model="classCardnum.startReviewTime" :clearable="false" format="HH:mm"
              value-format="HH:mm" :style="{width: screenWidth > 1300 ? '7vw' : '34%'}" style="margin-right: 1vw"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="复习开始时间">
            </el-time-picker>
            <el-time-picker format="HH:mm" :clearable="false" :style="{width: screenWidth > 1300 ? '7vw' : '34%'}"
              value-format="HH:mm" v-model="classCardnum.endReviewTime" :picker-options="{
                selectableRange: '00:00:00 - 23:59:00',
              }" placeholder="复习结束时间">
            </el-time-picker>
          </el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">联系方式：{{ classCardnum.mobile }}</el-col>
          <el-col style="margin-top:2vw;margin-left:1vw">教练:
            <el-select v-model="teacherId" placeholder="请选择" @visible-change="getTeachlist"
              :style="{width: screenWidth > 1300 ? '13vw' : '50%'}">
              <el-option v-for="item in teacherList" :key="item.teacherId" :label="item.teachName"
                :value="item.teacherId">
              </el-option>
            </el-select>
          </el-col>
          <el-col style="margin-top:3vw">
            <el-button style="margin-left:5vw" type="primary" @click="modifyBtn">保存</el-button>
            <el-button style="margin-left:5vw" type="primary" @click="quxiaoBtn=classCardstyle_=false">取消
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { screenTeacherList } from "@/api/paikeManage/LearnManager";
import { adjustPlanStudy } from "@/api/paikeManage/classCard";

export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    classCardstyle: {
      type: Boolean,
      default: false
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl"
    }
  },
  name: 'classcardDialog',
  data() {
    return {
      screenWidth: window.screen.width,
      value1: '',
      zhujiaolist: {//教练获取数据
        timeList: [{ startTime: '', endTime: '' }],
        teachingType: '',
        dateList: []
      },
      classCardnum: {//回显接口数据
        courseType: '',
        startStudyTime: '',
        endStudyTime: '',
        startReviewTime: '',
        endReviewTime: '',
        teachingType: '',
        lastReviewTime: '',
        dateTime: [],
        teacherId: ''
      },
      planStudy: {
        startTime: '',
        endTime: '',
        reviewTime: ["", ""],
        dateTime: [],
        studyId: '',
        teacherId: '',
        teachingType: '',
        studentCode: ''
      },
      teacherId: '',
      studentList: {},//接受授课类型用
      teacherList: {},//排课获得的老师列表
    };
  },
  //计算属性
  computed: {
    classCardstyle_: {
      get() {
        return this.classCardstyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit('changeDrawer', v)
      }
    }
  },
  methods: {
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.$emit("changeDrawer", false);
    },
    async modifyBtn() {//修改课程表列表
      this.planStudy.dateTime = this.classCardnum.dateTime
      this.planStudy.startTime = this.classCardnum.startStudyTime
      this.planStudy.endTime = this.classCardnum.endStudyTime
      this.planStudy.reviewTime[0] = this.classCardnum.startReviewTime
      this.planStudy.reviewTime[1] = this.classCardnum.endReviewTime
      this.planStudy.studyId = this.classCardnum.id
      this.planStudy.teacherId = Number(this.teacherId)
      this.planStudy.teachingType = Number(this.studentList.teachingType)
      await adjustPlanStudy(this.planStudy),
        this.$message.success("修改成功")
      this.classCardstyle_ = false
      this.$emit("updateList");
    },
    async getTeachlist() {//教练的列表
      if (!this.classCardnum.dateTime) {
        this.$message.error("请选择日期！")
        return;
      }
      if (!this.classCardnum.startStudyTime || !this.classCardnum.endStudyTime) {
        this.$message.error("请选择课程时间！")
        return;
      }
      if (!this.classCardnum.startReviewTime || !this.classCardnum.endReviewTime) {
        this.$message.error("请选择复习时间！")
        return;
      }
      this.zhujiaolist.teachingType = this.studentList.teachingType//远程线下
      this.zhujiaolist.timeList[0].startTime = this.classCardnum.startStudyTime
      this.zhujiaolist.timeList[0].endTime = this.classCardnum.endStudyTime
      this.zhujiaolist.dateList = [this.classCardnum.dateTime]
      let { data } = await screenTeacherList(this.zhujiaolist)
      this.teacherList = data
      let flag = true;
      this.teacherList.forEach((item) => {
        if (item.teacherId === this.teacherId) {
          flag = false;
        }
      })
      if (flag) {
        this.teacherId = '';
      }
    },
    timestudy1() {
      if (this.classCardnum.endStudyTime != '') {
        this.getTeachlist()
      }
    },
    timestudy2() {
      if (this.classCardnum.startStudyTime != '') {
        this.getTeachlist()
      }
    },
  }
};
</script>

<style lang="scss">
.borders {
  margin: 1vw 1vw;
  width: 28vw;
  height: 35vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
.borders-phone {
  margin: 1vw 1vw;
  width: 100%;
  height: 80%;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
</style>


<style scoped>
div /deep/ .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>
