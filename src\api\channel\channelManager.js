/**
 * 渠道管理员接口
 */
import request from '@/utils/request'
export default {
  // 新增
  addChannerManager(data) {
    return request({
      url: '/znyy/channelManager/save',
      method: 'POST',
      data
    })
  },
  queryChannelManagerList(dataQuery,tablePage){
    return request({
      url: '/znyy/channelManager/getList',
      method: 'get',
      params: {
        name: dataQuery.name,
        realName: dataQuery.realName,
        pageNum: tablePage.currentPage,
        pageSize: tablePage.size
      }
    })
  },
  updateStatus(id,isEnable){
    return request({
      url: '/znyy/channelManager/updateStatus',
      method: 'get',
      params: {
        id: id,
        isEnable: isEnable
      }
    })
  },
  getDetail(id){
    return request({
      url: '/znyy/channelManager/getDetail',
      method: 'get',
      params: {
        id: id
      }
    })
  },
  updateChannerManager(data){
    return request({
      url: '/znyy/channelManager/updateChannerManager',
      method: 'POST',
      data
    })
  },
  addChannelAuthority(data){
    return request({
      url: '/znyy/channelAuthority/addChannelAuthority',
      method: 'POST',
      data
    })
  },
  getChannelList(){
    return request({
      url: '/znyy/channelManager/getChannelList',
      method: 'GET'
    })
  },
  getCompanyAndDivision(){
    return request({
      url: '/znyy/channelManager/getCompanyAndDivision',
      method: 'GET'
    })
  },
  //为渠道管理员指派分公司/事业部
  assignCD(channelManagerCode,merchantCode){
    return request({
      url: '/znyy/channelManager/assignCD',
      method: 'POST',
      params:{
        channelManagerCode: channelManagerCode,
        merchantCode: merchantCode
      }
    })
  }
}
