<!-- 一对多-班级列表排课-多时间段选择组件 -->
<template>
  <div class="time-list">
    <div v-for="(item, index) in timeList" :key="index" class="time-list-row">
      <div class="time-input" :style="{ width }">
        <el-time-select
          v-if="timeType == 'select'"
          v-model="timeList[index][startTime]"
          :disable="disable || disableStartTime"
          format="HH:mm"
          value-format="HH:mm"
          :picker-options="pickerOptions[index].startTime"
          :editable="false"
          placeholder="开始时间"
          @change="handleStartTimeChange(item, index)"
          class="time-input-time"
          :style="{ width: timeWidth }"
        ></el-time-select>
        <el-time-picker
          v-else
          v-model="timeList[index][startTime]"
          :disable="disable || disableStartTime"
          format="HH:mm"
          value-format="HH:mm"
          :picker-options="pickerOptions[index].startTime"
          :editable="false"
          placeholder="开始时间"
          @change="handleStartTimeChange(item, index)"
          class="time-input-time"
          :style="{ width: timeWidth }"
        ></el-time-picker>
        <el-input-number
          class="time-input-num"
          v-model="timeList[index][timeNum]"
          :min="1"
          :max="timeNumMax[index]"
          :disable="disable || disableTimeNum"
          @change="handleTimeNumChange(item, index)"
        ></el-input-number>
        <el-time-select
          v-if="timeType == 'select'"
          v-model="timeList[index][endTime]"
          :disable="disable || disableEndTime"
          format="HH:mm"
          value-format="HH:mm"
          :picker-options="pickerOptions[index].endTime"
          placeholder="结束时间"
          @change="handleEndTimeChange(item, index)"
          class="time-input-time"
          :style="{ width: timeWidth }"
        ></el-time-select>
        <el-time-picker
          v-else
          v-model="timeList[index][endTime]"
          :disable="disable || disableEndTime"
          format="HH:mm"
          value-format="HH:mm"
          :picker-options="pickerOptions[index].endTime"
          placeholder="结束时间"
          @change="handleEndTimeChange(item, index)"
          class="time-input-time"
          :style="{ width: timeWidth }"
        ></el-time-picker>
      </div>
      <span v-if="canAdd" class="time-list-row-btnGroup">
        <el-button v-if="timeList.length != 1" type="danger" size="mini" round @click="handleTimeListDelClick(index)">删除</el-button>
        <el-button v-if="index == timeList.length - 1" type="primary" size="mini" round @click="handleTimeListAddClick()">增添</el-button>
      </span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'BaseMultipleTimeRange',
    props: {
      timeType: {
        type: String,
        default: 'picker' //  select/picker
      },
      timeList: {
        type: Array,
        required: true,
        default: () => [
          {
            startTime: null,
            endTime: null,
            timeNum: 1
          }
        ]
      },
      startTime: {
        type: String,
        default: 'startTime'
      },
      timeNum: {
        type: String,
        default: 'timeNum'
      },
      endTime: {
        type: String,
        default: 'endTime'
      },
      canAdd: {
        type: Boolean,
        default: true
      },
      width: {
        type: String,
        default: '372px'
      },
      timeWidth: {
        type: String,
        default: 'auto'
      },
      disable: {
        type: Boolean,
        default: false
      },
      disableStartTime: {
        type: Boolean,
        default: false
      },
      disableTimeNum: {
        type: Boolean,
        default: false
      },
      disableEndTime: {
        type: Boolean,
        default: false
      },
      inhibitChange: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        timeNumMax: [],
        pickerOptions: [],
        oldTimeListLength: -1
      };
    },
    watch: {
      timeList: {
        handler(value) {
          if (Array.isArray(value)) {
            if (value.length == 0) {
              if (this.timeType == 'select') {
                this.pickerOptions.push({
                  startTime: { start: '00:00', step: '00:30', end: '22:30', maxTime: '' },
                  endTime: { start: '00:00', step: '00:30', end: '23:30', minTime: '' }
                });
              } else {
                this.pickerOptions.push({
                  startTime: { selectableRange: '00:00:00 - 23:59:00' },
                  endTime: { selectableRange: '00:00:00 - 23:59:00' }
                });
              }
              this.timeNumMax.push(1);
              this.oldTimeListLength = this.timeList.length;
              this.timeList.push({ [this.startTime]: null, [this.endTime]: null, [this.timeNum]: 1 });
              return;
            }
            let forNum = value.length;
            if (this.oldTimeListLength >= 0) {
              if (value.length > this.oldTimeListLength) {
                forNum = value.length - this.oldTimeListLength;
              } else if (value.length == this.oldTimeListLength || value.length < this.oldTimeListLength) {
                forNum = 0;
              }
            }
            for (let i = 0; i < forNum; i++) {
              if (this.timeType == 'select') {
                this.pickerOptions.push({
                  startTime: { start: '00:00', step: '00:30', end: '22:30', maxTime: '' },
                  endTime: { start: '00:00', step: '00:30', end: '23:30', minTime: '' }
                });
              } else {
                this.pickerOptions.push({
                  startTime: { selectableRange: '00:00:00 - 23:59:00' },
                  endTime: { selectableRange: '00:00:00 - 23:59:00' }
                });
              }
              let startTime = value[i][this.startTime] || '00:00';
              const startHour = Number(startTime.split(':')[0]);
              this.timeNumMax.push(23 - startHour);
            }
          }
        },
        immediate: true
      }
    },
    methods: {
      // 处理开始时间选择器改变
      handleStartTimeChange(item, index) {
        if (this.inhibitChange) {
          this.$emit('inhibitChange');
          this.timeList[index][this.startTime] = null;
          this.timeList[index][this.endTime] = null;
          return;
        }
        // 处理对应的结束时间可选范围
        let startTime = item[this.startTime];
        if (this.timeType == 'select') {
          this.pickerOptions[index].endTime.minTime = startTime || '';
        } else {
          this.pickerOptions[index].endTime.selectableRange = `${startTime || '00:00'}:00 - 23:59:00`;
        }
        if (startTime) {
          // 处理对应的小时输入框最大值
          const startTimeArray = startTime.split(':');
          const startHour = Number(startTimeArray[0]);
          const startMinute = Number(startTimeArray[1]);
          let timeNumMax = 23 - startHour;
          if (timeNumMax > 0) {
            // 计算最大可选时间段
            this.timeNumMax[index] = timeNumMax;
            const timeItem = this.timeList[index];
            if (this.timeNumMax[index] < timeItem[this.timeNum]) {
              timeItem[this.timeNum] = this.timeNumMax[index];
            }

            // 根据开始时间及小时数，回填对应的结束时间
            timeItem[this.endTime] = this.getHourMinuteAfterSpecifiedHour(startHour, startMinute, timeItem[this.timeNum]);
          } else {
            this.timeNumMax[index] = 1;
          }
        }
      },
      // 处理时间栏数字输入框改变事件
      handleTimeNumChange(item, index) {
        if (this.inhibitChange) {
          this.$emit('inhibitChange');
          this.timeList[index][this.timeNum] = null;
          return;
        }
        let timeNum = item[this.timeNum];
        const timeItem = this.timeList[index];
        const startTimeArray = timeItem[this.startTime].split(':');
        const startHour = Number(startTimeArray[0]);
        const startMinute = Number(startTimeArray[1]);
        timeItem[this.endTime] = this.getHourMinuteAfterSpecifiedHour(startHour, startMinute, timeNum); // 设置默认时间段
      },
      // 处理结束时间选择器改变
      handleEndTimeChange(item, index) {
        if (this.inhibitChange) {
          this.$emit('inhibitChange');
          this.timeList[index][this.startTime] = null;
          this.timeList[index][this.endTime] = null;
          return;
        }

        // 处理对应的开始时间可选范围
        let endTimeOption = item[this.endTime];
        if (this.timeType == 'select') {
          this.pickerOptions[index].startTime.maxTime = endTimeOption || '';
        } else {
          this.pickerOptions[index].startTime.selectableRange = `00:00:00 - ${endTimeOption || '23:59'}:00`;
        }

        // 处理结束时间改变时，同步更改小时数和小时数输入框最大值限制
        let startTime = item[this.startTime];
        if (startTime) {
          let [startHour] = startTime.split(':');
          let endTime = item[this.endTime];
          if (endTime) {
            let [endHour] = endTime.split(':');
            this.timeNumMax[index] = 23 - startHour;
            item[this.timeNum] = endHour - startHour;
          }
        }
      },
      // 获取指定时分后指定小时数的时分
      getHourMinuteAfterSpecifiedHour(hour, minute, num) {
        const date = new Date();
        date.setHours(hour, minute, 0, 0);
        date.setHours(date.getHours() + num);
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      },
      // 处理时间栏点击添加事件
      handleTimeListAddClick() {
        if (this.inhibitChange) {
          this.$emit('inhibitChange');
          return;
        }
        this.oldTimeListLength = this.timeList.length;
        this.timeList.push({ [this.startTime]: null, [this.endTime]: null, [this.timeNum]: 1 });
      },
      // 处理排时间栏点击删除事件
      handleTimeListDelClick(index) {
        if (this.inhibitChange) {
          this.$emit('inhibitChange');
          return;
        }
        this.oldTimeListLength = this.timeList.length;
        this.timeList.splice(index, 1);
        this.pickerOptions.splice(index, 1);
        this.timeNumMax.splice(index, 1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .time-list {
    display: flex;
    flex-direction: column;
    .time-list-row {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      .time-input {
        display: flex;
        flex-wrap: wrap;
        .time-input-time {
          min-width: 115px;
          flex: 1;
        }
        .time-input-num {
          min-width: 95px;
          margin: 0 6px;
          flex: 1;
        }
      }
      .time-list-row-btnGroup {
        min-width: 125px;
        margin-left: 6px;
      }
    }
  }
</style>
