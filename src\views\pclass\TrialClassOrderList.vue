<!--交付中心-试课学员管理-待完善试课信息表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="120px" ref="searchNum" :model="searchNum" :inline="true">
        <!-- 1 -->
        <el-row style="display: flex; flex-wrap: wrap; align-items: center">
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="联系方式:" prop="mobile">
              <el-input v-model="searchNum.mobile" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6" :xs="24">
            <el-form-item label="是否有推荐人:" prop="referrer">
              <el-select v-model="searchNum.referrer" clearable placeholder="请选择">
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="20">

          <el-col :span="6" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-button type="primary" icon="el-icon-search" style="margin-bottom: 22px; margin-left: 10vw" size="mini"
              @click="initData01">查询
            </el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- <div style="margin-bottom: 25px;">
        <el-button-group>
          <el-button size="medium" :type="type == 2 ? 'primary' : ''" @click="changeOver(2)">鼎校甄选试课单</el-button>
          <el-button size="medium" :type="type == 1 ? 'primary' : ''" @click="changeOver(1)">阿拉鼎试课单</el-button>
        </el-button-group>
      </div> -->
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin:20px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :width="item.value =='operate'? 200 : ''"
        :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="createTrialClass(row)">填写试课单</el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <!-- 创建弹框 -->
    <!-- :rules="rules" -->
    <CreateLessonTestForm ref="createLesson" @changeDrawer="changeDrawer" @refreshPage="initData" :options="options"
      :options02="options02" :addForm="addForm" :allDelivers="allDelivers" :createLessonStyle="createLessonStyle">
    </CreateLessonTestForm>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />

  </div>
</template>

<script>
import CreateLessonTestForm from "../pclass/components/CreateLessonTestForm.vue";
import { allDeliver, GradeType, getExperienceOrderZXList } from "@/api/studentClass/changeList";
import ls from "@/api/sessionStorage";
import { regionData } from 'element-china-area-data'
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'
import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard'

export default {
  name: "TrialClassOrderList",
  components: {
    CreateLessonTestForm, HeaderSettingsDialog
  },
  data() {
    return {
      type: 2, // 1阿拉鼎 2鼎校甄选
      reviewStyle: false,
      LeaveViewStyle: false,
      LeaveViewStyley: false,
      direction: "rtl", //超哪边打开
      // 日期组件
      tableLoading: false,
      luyouclassCard: [],
      total: null,
      searchNum: {
        mobile: "",
        name: "",
        referrer: "",
        curriculumId: '',
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      isAdmin: false,

      createData: "",    //弹窗接收参数


      allDelivers: [],
      createLessonStyle: false,
      options: {},
      options02: regionData,
      addForm: {
        orderId: '',
        expName: '',
        expPhone: '',
        grade: '',
        sex: "",
        expectTime: '',
        address: "",
        province: '',
        city: '',
        area: '',
        referrerPhone: '',
        referrerName: ''
      },
      /*     rules: {
            expName: [
              { required: true, message: '请输入试课人姓名', trigger: 'blur' },
            ],
            expPhone: [
              { required: true, message: '请输入试课人手机号', trigger: 'blur' },
              {
                validator: function (rule, value, callback) {
                  if (/^1[345789]\d{9}$/.test(value) == false) {
                    callback(new Error("请输入正确的手机号"));
                  } else {
                    callback();
                  }
                }, trigger: 'blur'
              },
            ],
            grade: [
              { required: true, message: '请选择年级', trigger: 'change' }
            ],
            expectTime: [
              // { required: true, message: '请选择时间', trigger: 'change' }
              { validator: validateTime, trigger: "change" },
            ],
            sex: [
              { required: true, message: '请选择性别', trigger: 'change' }
            ],
            referrerPhone: [
              { required: true, message: '请填写手机号', trigger: 'blur' },
            ],
            address: [
              { required: true, message: '请选择区域', trigger: 'change' }
            ]
          }, */

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '姓名',
          value: 'name'
        },
        {
          name: '联系方式',
          value: 'mobile'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '课程类型',
          value: 'curriculumName'
        },
        {
          name: '阿拉鼎单号',
          value: 'orderNo'
        },
        {
          name: '下单时间',
          value: 'createTime'
        },
        {
          name: '推荐人姓名',
          value: 'referrerName'
        },
        {
          name: '推荐人联系方式',
          value: 'referrerPhone'
        }],

      tableHeaderList: [], // 获取表头数据

      courseList: []
    };
  },
  //计算属性
  computed: {

  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
    this.initData();
    this.allDeliver();
    this.GradeType();
    this.getHeaderlist();
    this.getbvstatusList()
  },
  methods: {
    getbvstatusList() {
      bvstatusList({}).then((res) => {
        this.courseList = res.data;
        this.curriculumId = res.data.length > 0 ? res.data[0].id : ''
        this.searchNum.curriculumId = res.data.length > 0 ? res.data[0].id : ''
      });
    },
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },

    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },

    // 填写试课单
    createTrialClass(row) {
      console.log(row)
      this.addForm = {
        orderId: row.orderId,
        expName: row.name,
        expPhone: row.mobile,
        curriculumName: row.curriculumName,
        curriculumId: row.curriculumId,
        grade: '',
        sex: "",
        expectTime: '',
        province: '',
        city: '',
        area: '',
        referrerPhone: row.referrerPhone,
        referrerName: row.referrerName
      };
      this.$refs.createLesson.addForm = this.addForm;
      this.$refs.createLesson.address = '';
      this.createLessonStyle = true;
      // this.$refs.createLesson.getStatus(this.type)
    },

    async GradeType() {
      let data = await GradeType()
      this.options = data.data
      console.log(data)
    },

    changeDrawer(v) {
      if (!v) {
        this.addForm = {
          orderId: '',
          expName: '',
          expPhone: '',
          grade: '',
          sex: "",
          expectTime: '',
          address: "",
          province: '',
          city: '',
          area: '',
          referrerPhone: '',
          referrerName: ''
        }
      }
      this.createLessonStyle = v
    },

    LeaveDialog(v) {
      this.LeaveViewStyle = v;
    },
    LeaveyDialog(v) {
      this.LeaveViewStyley = v;
    },
    async initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData();
    },
    async initData() {
      // 判断为null的时候赋空
      this.tableLoading = true;
      let datalist = await getExperienceOrderZXList(this.searchNum);
      this.tableLoading = false;
      this.luyouclassCard = datalist.data.data;
      this.total = Number(datalist.data.totalItems);
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return "远程";
      } else if (val.teachingType == 2) {
        return "线下";
      } else if (val.teachingType == 3) {
        return "远程和线下";
      } else {
        return "暂无";
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return "鼎英语";
      }
    },

    async allDeliver() {
      let data = await allDeliver()
      data.data.unshift({ "deliverMerchantCode": null, "deliverMerchantName": '不指定' })
      this.allDelivers = data.data;
    },

    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },

    closeClass() { },

    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.initData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "TrialClassOrderList",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'TrialClassOrderList'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },

    // tab切换
    // changeOver(value) {
    //   this.type = value;
    //   this.initData01();
    // },
  }
};
</script>
<style lang="scss" scoped>
.redClass {
  color: red;
}

.GreenClass {
  color: green;
}
</style>
