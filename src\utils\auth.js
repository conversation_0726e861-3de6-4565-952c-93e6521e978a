import Cookies from 'js-cookie'

const TokenKey = 'deliver-Token'

const AddOrUpdate ="deliver-addOrUpdate"

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

//修改显示

export function getAddOrUpdate() {
  return Cookies.get(AddOrUpdate)
}

export function setAddOrUpdate(addOrUpdate) {
  return Cookies.set(AddOrUpdate, addOrUpdate)
}

export function removeAddOrUpdate() {
  return Cookies.remove(AddOrUpdate)
}


