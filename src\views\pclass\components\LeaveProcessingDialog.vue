<template>
  <div class="dialog">
    <el-drawer
      title="请假处理"
      :visible.sync="classCardstyle_"
      :direction="direction"
      @close="handleClose"
      :size="screenWidth > 1300 ? '30%' : '80vw'">
      <div :class="screenWidth > 1300 ? 'borders':'borders-phone'">
        <el-row>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >课程名称:鼎英语</el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">
            课程内容: {{ LeaveList.courseName }}
          </el-col>

          <el-col style="margin-top: 2vw; margin-left: 1vw">
            学员名称:{{ LeaveList.studentName }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >上学时间:{{ studyDate }}
            <span v-if="LeaveList.startTime">/</span>
            {{ LeaveList.studyDateTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"  v-if="LeaveList.startTime"
            >调整时间:{{LeaveList.date}}
            <span v-if="LeaveList.startTime">/</span>
            {{ LeaveList.startTime }}<span v-if="LeaveList.startTime">-</span>{{ LeaveList.endTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >请假原因:
            <div style="margin: 1vw">
              <el-input
                :style="{width:screenWidth>1300?'24vw':'80%'}"
                type="textarea"
                :rows="2"
                disabled
                placeholder="请输入内容"
                v-model="LeaveList.cause"
              >
              </el-input>
            </div>
          </el-col>
          <el-col style="margin-top: 2vw">
            <el-button
              style="margin-left: 5vw"
              type="primary"
              @click="paikeFnL()"
              >调课</el-button>
            <el-button
              style="margin-left: 5vw"
              type="primary"
              @click="quxiaoBtn = classCardstyle_ = false"
              >取消
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    LeaveStyle: {
      type: Boolean,
      default: false,
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl",
    },
  },
  name: "LeaveDialog",
  data() {
    return {
      screenWidth: window.screen.width,
      value1: "",
      textarea: "",
      teacherId: "",
      studentList: {}, //接受授课类型用
      teacherList: {}, //排课获得的老师列表
      LeaveList: [],
      studyDate:'',
    };
  },
  //计算属性
  computed: {
    classCardstyle_: {
      get() {
        return this.LeaveStyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("LeaveDialog", v);
      },
    },
  },
  methods: {
    paikeFnL() {
      this.$emit("LeaveDialog", false);
      this.$emit("fMethod");
      this.$emit('qjstartTime',this.LeaveList.startTime)
      this.$emit('qjendTime',this.LeaveList.endTime)
    },
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.$emit("LeaveDialog", false);
    },
  },
};
</script>

<style lang="scss">
.borders {
  margin: 1vw 1vw;
  width: 28vw;
  height: 35vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
.borders-phone {
  margin: 1vw 1vw;
  width: 100%;
  height: 100%;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
</style>


<style scoped>
::v-deep .el-textarea__inner {
  height: 6vw;
  margin-top: 0.2vw;
}
div /deep/ .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>
