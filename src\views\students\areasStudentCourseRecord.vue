<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="门店编号：">
            <el-input v-model="dataQuery.merchantCode" disabled placeholder="请输入门店编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" disabled placeholder="请输入学员编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" clearable placeholder="请输入登录账号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>

        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.studentName" clearable placeholder="请输入姓名" />
          </el-form-item>
        </el-col>

        <el-form-item label="状态:">
          <el-select v-model="dataQuery.isEnable" filterable value-key="value" placeholder="请选择"
            @change="check(dataQuery.isEnable)" clearable>
            <el-option v-for="(item, index) in statusList" :key="index" :label="item.value" :value="item.type" />
          </el-select>
        </el-form-item>

        <el-col :span="16" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-button type="primary" @click="headerList()" style="margin: 20px 0">列表显示属性</el-button>

    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :width="item.value == 'courseCode' || item.value == 'courseName' || item.value == 'courseCover' ? '160' : ''"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="warning" size="mini" icon="el-icon-switch-button" v-if="row.isEnable === '开通'" @click="updateStatus(row.id, 0)">暂停</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="row.isEnable === '暂停'" @click="updateStatus(row.id, 1)">开通</el-button>
            <el-button type="danger" size="mini" icon="el-icon-video-pause" v-if="row.isEnable === '归档'" @click="regainArchive(row.id, 1)">恢复存档</el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import studentRecordApi from '@/api/student/areasStudentCourseList';
  import { pageParamNames } from '@/utils/constants';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';

  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';

export default {
  name: 'areasStudentCourseRecord',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        loginName: '',
        merchantCode: '',
        studentName: '',
        isEnable: ''
      },
      statusList: [
        {
          type: 1,
          value: '开通'
        },
        {
          type: 0,
          value: '暂停'
        },
        {
          type: 2,
          value: '归档'
        }
      ], //状态
      statusFnList: {},

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '课程进度编号',
          value: 'scheduleCode'
        },
        {
          name: '门店编号',
          value: 'merchantCode'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '课程编号',
          value: 'courseCode'
        },
        {
          name: '学员名称',
          value: 'studentName'
        },
        {
          name: '课程分类',
          value: 'categoryName'
        },
        {
          name: '课程类型',
          value: 'bigClassName'
        },
        {
          name: '课程小类',
          value: 'courseLevelName'
        },
        {
          name: '课程名称',
          value: 'courseName'
        },
        {
          name: '课程学段',
          value: 'courseStageName'
        },
        {
          name: '教材版本',
          value: 'courseEditionName'
        },
        {
          name: '开通时间',
          value: 'lastTime'
        },
        {
          name: '学习中单词',
          value: 'studyIngWordCount'
        },
        {
          name: '学习进度',
          value: 'scheduleName'
        },
        {
          name: '学习轮次',
          value: 'learnProgress'
        },
        {
          name: '状态',
          value: 'isEnable'
        }],

      tableHeaderList: [], // 获取表头数据

    }
  },
  created() {
    this.fetchData01();
    // 获取上个页面的学员编号
    // this.dataQuery.studentCode= ls.getItem('areaStudentCode');
    this.dataQuery.studentCode = window.localStorage.getItem("studentCode");
    this.dataQuery.merchantCode = window.localStorage.getItem("merchantCode");

    this.getHeaderlist();

  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    //重置
    rest() {
      this.dataQuery.merchantCode = '';
      this.dataQuery.studentCode = '';
      this.dataQuery.loginName = '';
      this.dataQuery.studentName = '';
      this.dataQuery.isEnable = '';
      this.fetchData01()
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      studentRecordApi.courseRecordList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    exportStudentRecord() {
      const that = this;
      studentRecordApi.exportAreasStudentRecord(that.dataQuery).then(response => {
        console.log(response)
        if (!response) {
          this.$notify.error({
            title: '操作失败',
            message: '文件下载失败'
          })
        }
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url // 获取服务器端的文件名
        link.setAttribute('download', '课程记录表.xls')
        document.body.appendChild(link)
        link.click()
        this.exportLoading = false

      })

    },
    updateStatus(id, status) {
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.statusFnList.studentCode = window.localStorage.getItem("studentCode");
        this.statusFnList.merchantCode = window.localStorage.getItem("merchantCode");
        studentRecordApi.updateStatus(id, status, this.statusFnList).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功')
        })

      })

    },
    regainArchive(id, status) {
      const that = this;
      this.$confirm('确定操作吗?', '修改状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.statusFnList.studentCode = window.localStorage.getItem("studentCode");
        this.statusFnList.merchantCode = window.localStorage.getItem("merchantCode");
        studentRecordApi.updateStatus(id, status, this.statusFnList).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('修改成功')
        })

      })

    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'areasStudentCourseRecord',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'areasStudentCourseRecord'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
      //查看进度
      // enterChildrenList(studentCode) {
      //   const that = this;
      //   ls.setItem('areaStudentCode', studentCode);
      //   that.$router.push({
      //     path: "/student/studentCourseProgress",
      //     query: {
      //       studentCode:studentCode
      //     }
      //   });
      // },
    }
  };
</script>

<style>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .period-table td,
  .period-table th {
    text-align: center;
  }

  .mt20 {
    margin-top: 20px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  @media (max-width: 767px) {
    .el-message-box {
      width: 80% !important;
    }
  }
</style>
