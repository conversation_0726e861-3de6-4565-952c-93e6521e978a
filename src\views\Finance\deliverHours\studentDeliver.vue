  <!--学员集中交付流水-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" :model="searchNum" ref="searchNum">
        <!-- 1 -->
        <el-row>
          <el-col :span="6">
            <el-form-item label="订单号:" prop="orderId">
              <el-input v-model="searchNum.orderId" clearable placeholder="请选择" size="small"
                style="width:11vw"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入学员编号" size="small"
                style="width:11vw"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="时间筛选:" prop="timeAll">
              <el-date-picker v-model="timeAll" style="width: 16.5vw" size="small" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 4.2.2 -->
          <el-col :span="6">
            <el-form-item label="审核状态:" prop="status">
              <el-select v-model="searchNum.status" placeholder="请选择" clearable style="width:11vw"
                @change="selectChange">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span=" 6 ">
            <el-form-item label="门店商户号:" prop="merchantCode">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请输入门店商户号" size="small"
                style="width:11vw"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付中心编号:" prop="deliverMerchantCode">
              <el-input v-model="searchNum.deliverMerchantCode" clearable placeholder="请输入交付中心编号" size="small"
                style="width:11vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"
                style="width:11vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item  label="状态:" prop="deliverStatus">
              <el-select v-model="searchNum.deliverStatus" placeholder="请选择" clearable style="width:11vw">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" size="mini" icon="el-icon-search" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:20px">列表显示属性</el-button>
    <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center"
        :width="item.value != 'operate' ? (item.value == 'operate' || item.value == 'startTime' || item.value == 'endTime' || item.value == 'remark' ? 170 : '') : 160">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="warning" size="mini" @click="cancel(row)" v-if="row.status === '1' && isAdmin">退回
            </el-button>
            <el-button type="warning" size="mini" @click="shenCancel(row)" v-if="row.status === '1' && !isAdmin">申请退回
            </el-button>
            <el-tag size="medium" type="warning" :effect="dark" style="opacity: 0.5; cursor: not-allowed;" effect="dark" v-if="row.status === '3' && !isAdmin">审核中</el-tag>
            <el-button type="warning" size="mini" @click="cancel(row)" v-if="row.status === '3' && isAdmin">审核
            </el-button>
            <!-- <el-button type="success" size="mini" @click="LeaveBtn(row)">查看详情</el-button>
            <el-button type="primary" size="mini" v-else @click="paikeBtn(row)">调课</el-button>
            <el-button type="success" size="mini" @click="lookBtn(row)">数据查看</el-button>
            <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteStudy(row)">删除</el-button> -->
          </div>

          <div v-else-if="item.value == 'useHours'">
            {{ row.useHours / 100 }}/节
          </div>
          <div v-else-if="item.value == 'beforeHours'">
            {{ row.beforeHours / 100 }}/节
          </div>
          <div v-else-if="item.value == 'afterHours'">
            {{ row.afterHours / 100 }}/节
          </div>

          <div v-else-if="item.value == 'flowType'">
            <span>{{ flowType(row.flowType) }}</span>
          </div>

          <div v-else-if="item.value == 'status'">
            <span
              :class="flowStatusClass(row.status)">{{ row.flowType !== 'deductHours' ? '' : flowStatus(row.status) }}</span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import {
  backDeliver,
  checkbackDeliver,
  pageDeliverHoursList,
  pageStudentDeliverFlowChanges
} from '@/api/FinanceApi/Finance'
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard'

import FileSaver from 'file-saver'
import XLSX from 'xlsx'
import ls from '@/api/sessionStorage'
import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue'
import { log } from 'bpmn-js-token-simulation'

export default {
  name: 'studentDeliver',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      //审核状态
      options: [{
        value: '',
        label: '全部'
      }, {
        value: '0',
        label: '未审核'
      }, {
        value: '1',
        label: '已审核'
      }],
      statusOptions:[
        { value: '0', label: '已退' },
        { value: '1', label: '可退回' },
        { value: '2', label: '已反馈' },
        { value: '3', label: '待审核' },
      ],
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date()
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const temp = new Date()
              temp.setTime(temp.getTime() - 3600 * 1000 * 24)
              picker.$emit('pick', [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ])
            }
          },
          {
            text: '最近七天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      reviewStyle: false,
      LeaveViewStyle: false,
      direction: 'rtl', //超哪边打开
      LeaveId: '',
      searchNum: {
        status: '',
          endTime: '',
          startTime: '',
          merchantCode: '',
          studentCode: '',
          deliverMerchantCode: '',
          deliverName: '',
          deliverStatus: '',
          orderId: '',
          pageNum: 1,
          pageSize: 10
      }, //搜索参数
      tableLoading: false,
      teacher: '',
      timeAll: [],
      contentType: '',
      total: null,
      reviewLister: [],
      isAdmin: false,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '门店商户号',
          value: 'merchantCode'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '所属交付中心编号',
          value: 'deliverMerchantCode'
        },
        {
          name: '所属交付中心',
          value: 'deliverName'
        },
        {
          name: '变动数量（节）',
          value: 'useHours'
        },
        {
          name: '销课前学时数',
          value: 'beforeHours'
        },
        {
          name: '销课后学时数',
          value: 'afterHours'
        },
        {
          name: '开始时间',
          value: 'startTime'
        },
        {
          name: '结束时间',
          value: 'endTime'
        },
        {
          name: '变动类型',
          value: 'flowType'
        },
        {
          name: '状态',
          value: 'status'
        },
        {
          name: '备注',
          value: 'remark'
        }],

      tableHeaderList: [], // 获取表头数据

    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
    this.initData();
    this.getHeaderlist();
  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    selectChange(e) {
      // console.log(e, '审核状态改变');
    },
    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData()
    },
    // 列表数据
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = []
      }
      this.tableLoading = true
      this.searchNum.startTime = this.timeAll[0]
      this.searchNum.endTime = this.timeAll[1]
      // this.searchNum.flowType = 'deductHours'
      let { data } = await pageStudentDeliverFlowChanges(this.searchNum)
      this.tableLoading = false
      this.total = Number(data.totalItems)
      this.reviewLister = data.data
    },
    statusClass(status) {
      switch (status) {
        case 1:
          return ''
        case 2:
          return 'error'
      }
    },
    LeaveDialog(v) {
      this.LeaveViewStyle = v
    },
    reviewDialog(v) {
      this.reviewStyle = v
    },
    flowStatusClass(status) {
      switch (status) {
        case '0':
          return 'error'
        case '3':
          return 'warning'
        default:
          return 'normal'
      }
    },
    flowStatus(status) {

      switch (status) {
        case '0':
          return '已退'
        case '2':
          return '已反馈'
        case '3':
          return '待审核'
        default:
          return '可退回'
      }
    },
    flowType(status) {
      switch (status) {
        case 'CourseBack':
          return '退回'
        case 'deductHours':
          return '耗课'
        case 'toDeliverHours':
          return '充值'
        case 'ChargeCourse':
          return '扣除学时'
        default:
          status
      }
    },
    async shenCancel(data) {
      this.$prompt('请输入退回原因', '您确定要申请退回本次学习所耗学时吗', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        backDeliver(data.id, value).then(res => {
          this.$message({
            type: 'success',
            message: '申请成功'
          })
        })
        this.initData()
      }).catch(() => {
      })

    },
    async cancel(data) {
      if (data.status === '3') {
        this.$confirm('申请理由：' + data.checkRemark, '您确定要退回本次学习所耗学时吗', {
          confirmButtonText: '确定',
          cancelButtonText: '拒绝',
          type: 'warning'
        }).then(() => {
          checkbackDeliver(data.id, true).then(res => {
            this.$message({
              type: 'success',
              message: '退回成功'
            })
          })
          this.initData()
        }).catch(() => {
          checkbackDeliver(data.id, false).then(res => {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
          })
          this.initData()
        });
        return
      }
      this.$prompt('请输入退回原因', '您确定要退回本次学习所耗学时吗', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        backDeliver(data.id, value).then(res => {
          this.$message({
            type: 'success',
            message: '退回成功'
          })
        })
        this.initData()
      }).catch(() => {
      })

    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return '远程'
      } else if (val.teachingType == 2) {
        return '线下'
      } else if (val.teachingType == 3) {
        return '远程和线下'
      } else {
        return '暂无'
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return '未复习'
      } else if (val.studyStatus == 2) {
        return '已复习'
      }
    },
    status(val) {
      if (val.status == 1) {
        return '正常'
      } else if (val.status == 2) {
        return '请假'
      } else if (val.status == 3) {
        return '请假已处理'
      }
    },
    reviewStatus(val) {
      if (val.reviewStatus == 0) {
        return '未复习'
      } else if (val.reviewStatus == 2) {
        return '已复习'
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return '鼎英语'
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val
      this.initData()
    },
    //定义导出Excel表格事件
    exportExcel() {
      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'))
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array'
      })
      try {
        FileSaver.saveAs(
          //Blob 对象表示一个不可变、原始数据的类文件对象。
          //Blob 表示的不一定是JavaScript原生格式的数据。
          //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
          //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
          new Blob([wbout], { type: 'application/octet-stream' }),
          //设置导出文件名称
          'sheetjs.xlsx'
        )
      } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbout)
      }
      return wbout
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.timeAll = [];
      this.initData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "studentDeliver",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'studentDeliver'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

.warning {
  color: rgb(234, 155, 36);
}
</style>
