<template>
  <el-form class="dialog gauge_outfit">
    <el-dialog title="表单设置信息" :visible="formVisible" @close="handleClose" :size="screenWidth > 1300 ? '60%' : '50vw'">
      <div class="dialog-prompt">按照点击顺序排序</div>
      <el-row class="paikeTwo">
        <el-checkbox-group v-model="checkList" @change="handleCheckChange">
          <template v-for="(item, index) in headerSettings">
            <el-checkbox :label="item.value" :key="index">{{ item.name }}</el-checkbox>
          </template>
        </el-checkbox-group>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click="formVisible = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="formSelection">确 定</el-button>
      </span>
    </el-dialog>
  </el-form>
</template>

<script>
  export default {
    name: 'headerSettingsDialog',
    props: {
      // 控制弹窗显示
      HeaderSettingsStyle: {
        type: Boolean,
        default: false //这里默认为false
      },
      // 学员列表的信息viewTime
      rowlist: {
        default: false //这里默认为false
      },

      headerSettings: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        screenWidth: window.screen.width,
        coniditon: {
          //调老师列表用的
          dateList: [],
          teachingType: '',
          timeList: [
            {
              endTime: '',
              startTime: ''
            }
          ]
        },

        headerList: [], // 表单列表
        checkList: [],

        selectedItems: ''
      };
    },
    mounted() {},
    computed: {
      formVisible: {
        get() {
          return this.HeaderSettingsStyle;
        },
        //值变化的时候会被调用
        set(v) {
          this.checkList = [];
          this.$emit('HeaderSettingsLister', false);
        }
      }
    },
    methods: {
      formSelection() {
        if (this.checkList.length == 0) {
          this.$message.error('必须勾选至少一项表单');
          return;
        }
        this.$emit('selectedItems', this.selectedItems);
        this.formVisible = false;
      },

      handleCheckChange(value) {
        // 获取被选择的项目
        // console.log(this.checkList);
        // this.selectedItems = this.headerSettings.filter((item) => value.includes(item.value));
        this.selectedItems = this.checkList.map((item) => {
          const index = this.headerSettings.findIndex((setting) => setting.value === item);
          return index !== -1 ? this.headerSettings[index] : null;
        });
        // console.log(this.selectedItems);
      },

      // 回显
      async expericeRlook() {},

      // 获取老师列表
      async getTeacherList() {},
      // 关闭弹窗
      handleClose() {
        this.formVisible = false;
      },

      async quedingBtn() {}
    }
  };
</script>

<style lang="scss" scoped>
  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
  }

  .el-checkbox-group .el-checkbox {
    flex-basis: 20%;
    /* 一行5列，每列占20%宽度 */
  }

  ::v-deep .el-checkbox {
    margin-right: 0 !important;
    margin-top: 20px !important;
  }

  .dialog-prompt {
    position: absolute;
    top: 50px;
    left: 20px;
  }

  ::v-deep .el-dialog__footer {
    text-align: center !important;
  }

  .gauge_outfit ::v-deep .el-dialog {
    width: 60% !important;
  }
</style>
