/**
 * 学员转移相关接口
 */
import request from '@/utils/request'

// 搜索交付中心信息
export const searchDeliverInfo = (name) => {
  return request({
    url: '/deliver/web/merchant/searchDeliverInfo',
    method: 'GET',
    params: { name: name }
  })
}

// 学员转移
export const studentTransfer = (studentCode, merchantCode, deliverMerchant, toDeliverMerchant, firstTime, reviewTime, reviewWeek,curriculumId) => {
  return request({
    url: '/deliver/web/merchant/studentTransfer',
    method: 'POST',
    params: {
      studentCode: studentCode,
      merchantCode: merchantCode,
      deliverMerchant: deliverMerchant,
      toDeliverMerchant: toDeliverMerchant,
      firstTime: firstTime,
      reviewTime: reviewTime,
      reviewWeek: reviewWeek,
      curriculumId:curriculumId
    }
  })
}

// 获取指派中心
export const belongDeliverAndAllDeliver = () => {
  return request({
    url: "/znyy/school/queryDeliverList",
    method: "GET",
  });
}

