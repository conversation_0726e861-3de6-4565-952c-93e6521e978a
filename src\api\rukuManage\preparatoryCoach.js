import request from '@/utils/request';

export default {
  // 企微二维码列表 /train/web/weCom/getImage
  weComList(data) {
    return request({
      url: '/train/web/weCom/getImage',
      method: 'get',
      params: data
    });
  },

  // 企微二维码上传
  weComSave(data) {
    return request({
      url: '/train/web/weCom/save',
      method: 'post',
      data
    });
  },

  // 入库信息分页查询
  teacherPageAPI(data) {
    return request({
      url: '/train/web/teacher/page',
      method: 'get',
      params: data
    });
  }
};

// 入库信息详情

export const infoAPI = (id) => {
  return request({
    url: '/train/web/teacher/info',
    method: 'GET',
    params: { id: id }
  });
};
// 入库信息审核
export const checkAPI = (data) => {
  return request({
    url: '/train/web/teacher/check',
    method: 'POST',
    data
  });
};
