import request from '@/utils/request'

// 试课评价列表
export const getStudentFeedbackList = (data) => {
    return request({
        url: '/deliver/web/studentFeedback/list',
        method: 'GET',
        params:data
    })
}

// 查看试课反馈详情
export const getStudentFeedbackDetail = (id) => {
    return request({
        url: '/deliver/web/studentFeedback/detail',
        method: 'GET',
        params:{feedbackId: id}
    })
}
// 交付中心回复
export const replyStudentFeedback = (data) => {
    return request({
        url: '/deliver/web/studentFeedback/reply',
        method: 'post',
        data:data
    })
}
