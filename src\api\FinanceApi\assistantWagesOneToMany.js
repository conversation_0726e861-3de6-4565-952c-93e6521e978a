// 一对多工资管理
import request from '@/utils/request';

// 获取1v多课程大类
export const getOneToMoreClassList = () => {
  return request({
    url: '/znyy/curriculum/allNew',
    method: 'GET',
    params: {
      curriculumType: 1
    }
  });
};

/**
 * 正课
 */
// 教练正式课工资配置列表-鼎英语
export const getTeacherWageList = (data) => {
  return request({
    url: '/znyy/deliver/wage/findTeacherWageConfigPage',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 1
    }
  });
};
//教练正式课工资列表查询(除鼎英语)
export const getCurriculumTeacherWageConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/getCurriculumTeacherWageConfig',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 1
    }
  });
};
// 新增或编辑 教练正式课工资配置
export const addOrUpdateTeacherWageConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateCurriculumTeacherWageConfig',
    method: 'POST',
    data: {
      configGroup: 'TEACHER_WAGE_ONE_TO_MANY',
      ...data
    }
  });
};
// 删除 教练正式课工资配置
export const deleteCurriculumTeacherWageConfig = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteCurriculumTeacherWageConfig?id=' + id,
    method: 'POST'
  });
};

/**
 * 试课
 */

// 教练试课工资配置列表
export const getCurriculumExpCourse = (data) => {
  return request({
    url: '/znyy/deliver/wage/getCurriculumExpCourse',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 1
    }
  });
};

// 新增或编辑 教练试课工资配置
export const addOrUpdateExpCourseWageConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateCurriculumExpCourseConfig',
    method: 'POST',
    data: {
      configGroup: 'EXP_WAGE_REWARD_ONE_TO_MANY',
      ...data
    }
  });
};
// /znyy/deliver/wage/deleteCurriculumExpCourse
// 教练试课工资除鼎英语删除
export const getDeleteNoTeacher = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteCurriculumExpCourse?id=' + id,
    method: 'POST'
  });
};

/**
 * 试课奖励
 */

// 试课奖励查询
export const getCurriculumExpCourseReward = (data) => {
  return request({
    url: '/znyy/deliver/wage/findExpWageRewardPage',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 1
    }
  });
};

// 新增或编辑 试课奖励配置
export const addOrUpdateExpCourseRewardConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/saveOrUpdateExpWageReward',
    method: 'POST',
    params: {
      oneToManyType: 1
    },
    data: {
      configGroup: 'TEACHER_WAGE_ONE_TO_MANY',
      ...data
    }
  });
};
// 删除 试课奖励配置
export const deleteExpWageReward = (data) => {
  return request({
    url: '/znyy/deliver/wage/deleteExpWageReward?id=' + data,
    method: 'POST'
  });
};
