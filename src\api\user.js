/**
 * “用户管理”相关接口
 */
import request from '@/utils/request';

export default {
  /**
   * 查询角色
   * @param queryParam
   * @param pageParam
   */
  //获取新增用户角色
  queryRole() {
    return request({
      url: '/deliver/web/sysUser/getAddRoleList',
      method: 'GET'
    });
  },
  //获取用户列表
  queryUser(queryParam, pageParam) {
    return request({
      url: '/deliver/web/sysUser/getDeliverUserList',
      method: 'get',
      params: {
        mobile: queryParam.loginName,
        realName: queryParam.realName,
        roleId: queryParam.roleTag,
        pageNum: pageParam.currentPage,
        pageSize: pageParam.size
      }
    });
  },

  deleteUser(data) {
    return request({
      url: '/znyy/sys/user',
      method: 'delete',
      data
    });
  },
  /**
   * 新增用户
   */
  addNewUser(data) {
    return request({
      url: '/deliver/web/sysUser/saveDeliverUser',
      method: 'post',
      params: {
        mobile: data.mobile,
        realName: data.realName,
        pwd: data.pwd,
        confirmPwd: data.confirmPwd,
        merchantCode: data.merchantCode
      },
      data: data.roleValList
    });
  },
  /**
   * 新 编辑用户
   */
  editNewUser(data) {
    return request({
      url: '/deliver/web/sysUser/modifyDeliverUser',
      method: 'POST',
      params: {
        id: data.id,
        mobile: data.mobile,
        realName: data.realName,
        pwd: data.pwd,
        confirmPwd: data.confirmPwd
      },
      data: data.roleValList
    });
  },

  // 修改密码
  updatePwd(data) {
    return request({
      url: '/znyy/sys/user/pwd',
      method: 'patch',
      data
    })
  },
  // 获取手机号
  getPhone() {
    return request({
      url: '/znyy/sys/user/getLoginPhone',
      method: 'get'
    })
  }

};
