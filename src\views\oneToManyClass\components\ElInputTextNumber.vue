<!-- 输入框数字格式化组件 -->
<template>
  <el-input v-model="valueText" :placeholder="placeholder" :style="styleData" @input="$emit('input', $event)" @change="handleChange">
    <div v-if="controls" slot="suffix" class="my-input-number-suffix">
      <div @click="handleAddStep" class="btn btn-up" :class="{ 'btn-up-disabled': upDisabled }">
        <i class="el-icon-arrow-up" :class="{ 'btn-icon': !upDisabled }"></i>
      </div>
      <div @click="handleReduceStep" class="btn btn-down" :class="{ 'btn-down-disabled': downDisabled }">
        <i class="el-icon-arrow-down" :class="{ 'btn-icon': !downDisabled }"></i>
      </div>
    </div>
  </el-input>
</template>

<script>
  export default {
    name: 'ElInputNumber',
    model: {
      prop: 'value',
      event: 'input'
    },
    props: {
      value: {
        type: [Number, String],
        default: ''
      },
      styleData: {
        type: String,
        default: ''
      },
      min: {
        type: [Number, String],
        default: ''
      },
      max: {
        type: [Number, String],
        default: ''
      },
      step: {
        type: Number,
        default: 1
      },
      controls: {
        type: Boolean,
        default: true
      },
      placeholder: {
        type: String,
        default: ''
      }
    },
    computed: {
      valueText: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      },
      upDisabled() {
        if (this.max) {
          return this.handleOnlyNumber(this.value) + this.step > this.max;
        }
        return false;
      },
      downDisabled() {
        if (this.min) {
          return this.handleOnlyNumber(this.value) - this.step < this.min;
        }
        return false;
      }
    },
    methods: {
      handleChange(value) {
        if (this.max && this.handleOnlyNumber(this.value) > this.max) {
          value = String(this.max);
          this.$emit('input', value);
        } else if (this.min && this.handleOnlyNumber(this.value) < this.min) {
          value = String(this.min);
          this.$emit('input', value);
        }
        this.$emit('change', value);
      },
      handleAddStep() {
        if (this.upDisabled) return;
        let value;
        if (typeof this.value === 'string' && !this.value) {
          value = String(this.min || 1);
        } else {
          value = String(this.handleOnlyNumber(this.value) + this.step);
        }
        this.$emit('input', value);
        this.$emit('change', value);
      },
      handleReduceStep() {
        if (this.downDisabled) return;
        let value;
        if (typeof this.value === 'string' && !this.value) {
          value = String(this.min || 1);
        } else {
          value = String(this.handleOnlyNumber(this.value) - this.step);
        }
        this.$emit('input', value);
        this.$emit('change', value);
      },
      handleOnlyNumber(val) {
        if (typeof val !== 'string') {
          val = String(val);
        }
        return Number(val.replace(/[^\d.]/g, ''));
      }
    }
  };
</script>

<style lang="scss" scoped>
  .my-input-number-suffix {
    height: 30px;
    position: relative;
    top: 1px;
    left: 4px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .btn {
      width: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgb(245, 247, 250);
      &-icon {
        color: rgb(96, 98, 102);
      }
      &-up {
        height: 50%;
        border-radius: 0px 4px 0px 0px;
        border-left: 1px solid rgb(220, 223, 230);
        border-bottom: 1px solid rgb(220, 223, 230);
        &:hover {
          cursor: pointer;
        }
      }
      &-up-disabled {
        background: rgb(245, 247, 250);
        &:hover {
          cursor: not-allowed;
        }
      }
      &-down {
        height: 50%;
        border-radius: 0px 0px 4px 0px;
        border-left: 1px solid rgb(220, 223, 230);
      }
      &-down-disabled {
        background: rgb(245, 247, 250);
        &:hover {
          cursor: not-allowed;
        }
      }
    }
  }
</style>
