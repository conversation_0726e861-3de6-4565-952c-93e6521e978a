<!-- 交付中心-接单管理-承单量配置-1v多 -->
<template>
  <div style="padding: 0 0 20px 0">
    <!-- 申请中列表入口 -->
    <div class="notice">
      <div class="notice-title">
        <div class="notice-title1">承单量申请列表</div>
        <div class="notice-title2">如需修改：请先撤销后申请</div>
      </div>
      <div class="notice-btns">
        <el-button size="small" type="primary" @click="handleApplyListClick">查看详情</el-button>
      </div>
    </div>

    <!-- 申请按钮 -->
    <div class="btn-add" style="margin-bottom: 10px">
      <el-button size="small" type="primary" @click="clickAdd">承单量申请</el-button>
    </div>
    <el-table :data="orderSettingList" v-loading="orderSettingListLoading" style="width: 100%" :header-cell-style="getRowClass">
      <el-table-column align="center" prop="curriculumName" label="课程大类"></el-table-column>
      <el-table-column align="center" prop="experienceNum" label="试课承单量/班"></el-table-column>
      <el-table-column align="center" prop="learnNum" label="正式课承单量/班"></el-table-column>
      <el-table-column align="center" label="状态">
        <template slot-scope="{ row }">
          <span :class="statusClass(row.status)">{{ statusNameFormat(row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{ row }">
          <el-button type="primary" @click="handleDetailClick(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 承单量配置 -->
    <el-drawer :with-header="false" :visible.sync="orderSettingDrawerVisible" :wrapperClosable="false" direction="rtl" size="90%" :before-close="handleClose">
      <div class="setting">
        <div class="closeIcon" @click="handleClose">X</div>
        <h3>承单量{{ isDetail ? '详情' : '申请' }}</h3>
        <el-tabs v-model="orderSettingDrawerActiveTab" :before-leave="handleTabsChange" style="width: 90%; min-height: 80%">
          <el-tab-pane v-for="item in formList" :key="item.curriculumId" :label="item.curriculumName" :name="item.curriculumId" v-loading="orderSettingDrawerLoading">
            <el-form :model="item">
              <div style="display: flex">
                <el-form-item label="试课:" prop="experienceNum" style="display: flex; margin-right: 10%">
                  <el-input-number v-model="item.experienceNum" :min="0" size="small" step-strictly :disabled="isDetail" :controls="false"></el-input-number>
                  班
                </el-form-item>
                <el-form-item label="正课:" prop="learnNum" style="display: flex">
                  <el-input-number v-model="item.learnNum" :min="0" size="small" step-strictly :disabled="isDetail" :controls="false"></el-input-number>
                  班
                </el-form-item>
              </div>
              <div v-if="item.classTimeWeekVoList1 && item.classTimeWeekVoList1.length > 0">
                <h4>试课预约班级数设置</h4>
                <div v-for="(weeksItem, index) in item.classTimeWeekVoList1" :key="index" style="display: flex; align-items: flex-start; margin-top: 18px; line-height: 30px">
                  <div style="margin: 0 30px 18px 0; width: 355px">{{ weeksItem.dayOfWeek }}</div>
                  <div style="display: flex; flex-wrap: wrap; width: 1020px">
                    <el-form-item
                      v-for="timeItem in weeksItem.classTimeNumVoList"
                      :key="timeItem.id"
                      :label="timeItem.startTime + '-' + timeItem.endTime"
                      style="display: flex; margin-right: 20px"
                    >
                      <el-input-number v-model="timeItem.canReserveNum" :min="0" size="small" step-strictly :disabled="isDetail" :controls="false"></el-input-number>
                      班
                    </el-form-item>
                  </div>
                </div>
              </div>
              <div v-if="item.classTimeWeekVoList2 && item.classTimeWeekVoList2.length > 0">
                <h4>正式课预约班级数设置</h4>
                <div v-for="(weeksItem, index) in item.classTimeWeekVoList2" :key="index" style="display: flex; align-items: flex-start; margin-top: 18px; line-height: 30px">
                  <div style="margin: 0 30px 18px 0; width: 355px">{{ weeksItem.dayOfWeek }}</div>
                  <div style="display: flex; flex-wrap: wrap; width: 1020px">
                    <el-form-item
                      v-for="timeItem in weeksItem.classTimeNumVoList"
                      :key="timeItem.id"
                      :label="timeItem.startTime + '-' + timeItem.endTime"
                      style="display: flex; margin-right: 20px"
                    >
                      <el-input-number v-model="timeItem.canReserveNum" :min="0" size="small" step-strictly :disabled="isDetail" :controls="false"></el-input-number>
                      班
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <div v-if="isDetail" class="btns">
          <el-button type="primary" style="width: 100px" @click="orderSettingDrawerVisible = false">确定</el-button>
        </div>
        <div v-else class="btns">
          <el-button type="primary" plain style="width: 100px" @click="orderSettingDrawerVisible = false">取消</el-button>
          <el-button type="primary" :loading="applyLoading" style="width: 100px" @click="applyFn">申请</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog title="承单量申请列表" :visible.sync="applyListDialogVisible" width="60%" align="center">
      <div>
        <el-table :data="applyList" v-loading="applyListLoading" max-height="500" style="width: 100%; margin-bottom: 30px">
          <el-table-column align="center" prop="curriculumName" label="课程大类"></el-table-column>
          <el-table-column align="center" prop="experienceNum" label="试课承单量/班"></el-table-column>
          <el-table-column align="center" prop="learnNum" label="正式课承单量/班"></el-table-column>
          <el-table-column align="center" prop="applyTime" label="申请时间"></el-table-column>
          <el-table-column align="center" prop="status" label="状态">
            <template slot-scope="{ row }">
              <span :class="statusClass(row.status)">{{ statusNameFormat(row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="{ row }">
              <el-button v-if="row.status == 2" type="primary" @click="handleRevokeClick(row.id)">撤销</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="primary" style="width: 100px" @click="applyListDialogVisible = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import {
    getManyCurrentConfigData,
    getManyOperateConfigData,
    getOrderDetailByCurriculumData,
    saveToMany,
    getManyConfigDetailData,
    setRevokeOneToManyOrder
  } from '@/api/orderManageOneToMany';
  export default {
    name: 'OneToManyOrderSetting',
    data() {
      return {
        // 承单量申请中列表弹窗数据
        applyListDialogVisible: false,
        applyListLoading: false,
        applyList: [],
        curriculumList: [],

        // 承单量生效中列表数据
        orderSettingListLoading: false,
        orderSettingList: [],

        // 承单量申请及详情弹窗数据
        orderSettingDrawerVisible: false,
        orderSettingDrawerLoading: false,
        isDetail: false,
        orderSettingDrawerActiveTab: '',
        orderSettingDrawerActiveIndex: '',
        formList: [],
        applyLoading: false
      };
    },
    mounted() {
      this.getCurriculumList();
      this.getManyCurrentConfig();
    },
    methods: {
      // 获取一对多所有课程大类
      getCurriculumList() {
        getAllOneToManyCurriculumType().then((res) => {
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      //一对多-根据课程大类查询配置详情用以回显到新增弹窗
      getOrderDetailByCurriculum(item) {
        this.orderSettingDrawerLoading = true;
        getOrderDetailByCurriculumData(item.curriculumId)
          .then((res) => {
            item.experienceNum = res.data.experienceNum;
            item.learnNum = res.data.learnNum;
            item.classTimeWeekVoList1 = res.data.classTimeWeekVoList1 || [];
            item.classTimeWeekVoList2 = res.data.classTimeWeekVoList2 || [];
            this.orderSettingDrawerLoading = false;
          })
          .catch(() => {
            this.orderSettingDrawerLoading = false;
          });
      },
      // 获取生效中配置
      getManyCurrentConfig() {
        this.orderSettingListLoading = true;
        getManyCurrentConfigData().then((res) => {
          this.orderSettingList = res.data.data;
          this.orderSettingListLoading = false;
        });
      },
      // 处理切换tabs
      handleTabsChange(activeName) {
        if (this.applyLoading) return false;
        const activeIndex = this.formList.findIndex((item) => item.curriculumId === activeName);
        if (activeIndex == -1) return false;
        if (this.isDetail) {
          this.orderSettingDrawerActiveIndex = activeIndex;
          let item = this.formList[activeIndex];
          if (item.classTimeWeekVoList1.length == 0 && item.classTimeWeekVoList2.length == 0) {
            this.getManyConfigDetail(item.id);
          }
        } else {
          this.orderSettingDrawerActiveIndex = activeIndex;
          let item = this.formList[activeIndex];
          if (item.classTimeWeekVoList1.length == 0 && item.classTimeWeekVoList2.length == 0) {
            this.getOrderDetailByCurriculum(item);
          }
        }
      },
      // 查看详情
      handleDetailClick(row) {
        this.isDetail = true;
        this.formList = this.orderSettingList.map((each) => {
          const item = {
            id: each.id,
            curriculumId: each.curriculumId,
            curriculumName: each.curriculumName,
            experienceNum: each.experienceNum,
            learnNum: each.learnNum,
            classTimeWeekVoList1: [],
            classTimeWeekVoList2: []
          };
          return item;
        });
        const acitveIndex = this.formList.findIndex((each) => each.id == row.id);
        if (acitveIndex > -1) {
          this.orderSettingDrawerActiveIndex = acitveIndex;
          this.orderSettingDrawerActiveTab = this.formList[acitveIndex].curriculumId;
          this.getManyConfigDetail(row.id);
        }
      },
      // 获取承单量配置详情
      getManyConfigDetail(id) {
        this.orderSettingDrawerLoading = true;
        getManyConfigDetailData(id)
          .then((res) => {
            if (res.data.id) {
              const detailItem = this.formList[this.orderSettingDrawerActiveIndex];
              detailItem.experienceNum = res.data.experienceNum;
              detailItem.learnNum = res.data.learnNum;
              detailItem.classTimeWeekVoList1 = res.data.classTimeWeekVoList1 || [];
              detailItem.classTimeWeekVoList2 = res.data.classTimeWeekVoList2 || [];
              this.orderSettingDrawerVisible = true;
            }
            this.orderSettingDrawerLoading = false;
          })
          .catch(() => {
            this.orderSettingDrawerLoading = false;
          });
      },
      //添加操作
      clickAdd() {
        this.isDetail = false;
        if (this.curriculumList.length > 0) {
          this.orderSettingDrawerActiveIndex = 0;
          this.orderSettingDrawerActiveTab = this.curriculumList[0].value;
          this.formList = [];
          this.curriculumList.forEach((each, index) => {
            const item = {
              curriculumId: each.value,
              curriculumName: each.label,
              experienceNum: 0,
              learnNum: 0,
              classTimeWeekVoList1: [],
              classTimeWeekVoList2: []
            };
            if (index === 0) {
              this.getOrderDetailByCurriculum(item);
            }
            this.formList.push(item);
          });
          this.orderSettingDrawerVisible = true;
        } else {
          this.getCurriculumList();
        }
      },
      // 申请
      applyFn() {
        const submitItem = this.formList[this.orderSettingDrawerActiveIndex];
        const submitForm = {
          curriculumId: submitItem.curriculumId,
          curriculumName: submitItem.curriculumName,
          experienceNum: submitItem.experienceNum || 0,
          learnNum: submitItem.learnNum || 0,
          reserveNumList: []
        };
        submitForm.reserveNumList = submitForm.reserveNumList.concat(this.handleApplyClassTimeWeekData(submitItem.classTimeWeekVoList1));
        submitForm.reserveNumList = submitForm.reserveNumList.concat(this.handleApplyClassTimeWeekData(submitItem.classTimeWeekVoList2));
        this.$confirm('仅会申请当前选中的课程大类，确定要申请吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (submitForm.reserveNumList.length == 0) {
            this.$message.error('请等待管理员配置班级上课时间后再申请');
            return;
          }
          console.log('submitForm', submitForm);
          this.applyLoading = true;
          saveToMany(submitForm)
            .then(() => {
              this.$message.success('提交申请成功');
              this.applyLoading = false;
              this.handleClose();
            })
            .catch(() => {
              this.applyLoading = false;
            });
        });
      },
      // 处理承单量申请的试课/正课上课时间数据
      handleApplyClassTimeWeekData(arr) {
        const result = [];
        if (Array.isArray(arr)) {
          arr.forEach((weekItem) => {
            if (Array.isArray(weekItem.classTimeNumVoList)) {
              weekItem.classTimeNumVoList.forEach((timeItem) => {
                result.push({
                  classTimeConfigId: timeItem.id,
                  canReserveNum: timeItem.canReserveNum || 0
                });
              });
            }
          });
        }
        return result;
      },
      // 抽屉关闭
      handleClose() {
        this.orderSettingDrawerVisible = false;
        this.orderSettingDrawerLoading = false;
        this.applyLoading = false;
      },
      // 查看申请记录列表
      handleApplyListClick() {
        this.applyListDialogVisible = true;
        this.getManyOperateConfig();
      },
      // 获取审核中配置
      getManyOperateConfig() {
        this.applyListLoading = true;
        getManyOperateConfigData().then((res) => {
          this.applyList = res.data.data;
          this.applyListLoading = false;
        });
      },
      // 撤销申请中的承单量配置
      handleRevokeClick(id) {
        this.$confirm('确定撤销该申请？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            setRevokeOneToManyOrder(id).then(() => {
              this.$message.success('撤销成功');
              this.getManyOperateConfig();
            });
          })
          .catch(() => {
            this.$message.info('已取消撤销');
          });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      statusClass(status) {
        status = Number(status);
        switch (status) {
          case 1:
            return 'normal';
          case 2:
            return 'warning';
          case 3:
            return 'warning';
          case 4:
            return 'error';
        }
      },
      statusNameFormat(status) {
        status = Number(status);
        //状态 1:适用中 2审核中 3已撤销 4已拒绝
        switch (status) {
          case 1:
            return '生效中';
          case 2:
            return '审核中';
          case 3:
            return '已撤销';
          case 4:
            return '已拒绝';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-tabs__header {
    margin-top: 0 !important;
    margin-left: 0 !important;
  }
  .notice {
    // position: absolute;
    // top: 0;
    // left: 0;
    background-color: #ffefe8;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    .notice-title1 {
      color: #000;
      font-size: 14px;
    }
    .notice-title2 {
      color: #c3c3c3;
      font-size: 12px;
      margin-top: 10px;
    }
    .notice-btns {
      display: flex;
      align-items: center;
    }
    .closeIcon {
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
    }
  }
  .setting {
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow-y: auto;
    .closeIcon {
      position: absolute;
      right: 100px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
    }
  }
  ::v-deep .el-drawer__body {
    overflow-y: auto !important;
  }
  .table {
    width: 70%;
    display: grid;
    font-size: 14px;
    grid-template-columns: repeat(4, 1fr); /* 将容器分为4列，每列平均占据剩余空间 */
    grid-gap: 10px; /* 设置格子之间的间隔 */
    grid-row-gap: 35px;
  }
  .form {
    width: 65%;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 将容器分为4列，每列平均占据剩余空间 */
    grid-gap: 10px; /* 设置格子之间的间隔 */
    grid-row-gap: 35px;
  }

  // .tabs-container {
  //   width: 70%;
  // }

  // .tabs-container ::v-deep .el-tabs__nav-scroll::-webkit-scrollbar {
  //   display: none; /* 隐藏默认的滚动条 */
  // }
  // .tabs-container ::v-deep .el-tabs__nav-wrap {
  //   overflow: hidden; /* 隐藏超出部分 */
  // }
  // .tabs-container ::v-deep .el-tabs__nav-scroll {
  //   overflow-x: auto; /* 允许水平滚动 */
  // }

  .formItem {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-top: 10px;
    .input {
      // width: 5%;
      margin-left: 10px;
    }
    .label {
      text-align: right;
      width: 80px;
    }
  }
  ::v-deep .el-input__suffix {
    color: #000;
    top: 8px;
  }
  .btns {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .notice {
    // position: absolute;
    // top: 0;
    // left: 0;
    background-color: #ffefe8;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    .notice-title1 {
      color: #000;
      font-size: 14px;
    }
    .notice-title2 {
      color: #c3c3c3;
      font-size: 12px;
      margin-top: 10px;
    }
    .notice-btns {
      display: flex;
      align-items: center;
    }
    .closeIcon {
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
    }
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .warning {
    color: rgb(253, 184, 54);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    cursor: unset !important;
  }

  // 自定义表格边框
  ::v-deep #out-table .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:nth-child(1) {
    border-left: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:nth-child(2),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(5),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(8),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(11) {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__row td:nth-child(1) {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__row td:nth-child(0),
  ::v-deep #out-table .el-table__row td:nth-child(2),
  ::v-deep #out-table .el-table__row td:nth-child(5),
  ::v-deep #out-table .el-table__row td:nth-child(8),
  ::v-deep #out-table .el-table__row td:nth-child(11) {
    border-right: 1px solid #d3dce6 !important;
  }
</style>
