<template>
  <div class="app-container">
    <el-form
      :inline="true"
      class="container-card"
      label-width="110px"
      label-position="left"
    >
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" clearable disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="能力水平：">
            <el-select
              v-model="dataQuery.studyRank"
              placeholder="全部"
              style="width: 185px"
            >
              <el-option
                v-for="(item, index) in studyRank"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="fetchData01()"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh-left" @click="rest()"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px"
      >列表显示属性</el-button
    >
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 200 : ''"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        sortable
        :show-overflow-tooltip="item.value == 'levelDescription' ? true : false"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button
              type="success"
              size="mini"
              icon="el-icon-edit-outline"
              @click="jumpOpenCourse(row.id)"
              >打印</el-button
            >
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
import testResultApi from "@/api/student/areasStudentTestResultList";
import { pageParamNames } from "@/utils/constants";
import ls from "@/api/sessionStorage";
import HeaderSettingsDialog from "../pclass/components/HeaderSettingsDialog.vue";
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";

export default {
  name: "areasStudentTestResultList",
  components: {
    HeaderSettingsDialog,
  },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },
      tableData: [],
      dataQuery: {
        studentCode: "",
        merchantCode: "",
        startDate: "",
        endDate: "",
        loginName: "",
        realName: "",
      },
      studyRank: [],
      value1: "",
      exportLoading: false,
      stuudentCode: null,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: "学员编号",
          value: "studentCode",
        },
        {
          name: "登录账号",
          value: "loginName",
        },
        {
          name: "操作",
          value: "operate",
        },
        {
          name: "姓名",
          value: "realName",
        },
        {
          name: "词汇量",
          value: "wordLevel",
        },
        {
          name: "开始时间",
          value: "addTime",
        },
        {
          name: "结束时间",
          value: "endTime",
        },
        {
          name: "能力水平",
          value: "title",
        },
        {
          name: "测试结果",
          value: "levelDescription",
        },
      ],

      tableHeaderList: [], // 获取表头数据
    };
  },
  // watch: {
  //   '$route' () {
  //     this.fetchData();//我的初始化方法
  //   }
  // },
  created() {
    this.dataQuery.studentCode = window.localStorage.getItem("studentCode");
    this.dataQuery.merchantCode = window.localStorage.getItem("merchantCode");
    this.fetchData();
    this.getHeaderlist();
    // this.getStudyRank();
    // this.dataQuery.studentCode = this.stuudentCode ? this.stuudentCode : ''
  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(
          (item) => item.value
        ); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0];
      this.dataQuery.endDate = e[1];
    },

    getStudyRank() {
      testResultApi.getStudyRank().then((res) => {
        this.studyRank = res.data;
      });
    },
    //重置
    rest() {
      this.dataQuery.studentCode = "";
      this.dataQuery.loginName = "";
      this.dataQuery.realName = "";
      this.dataQuery.studyRank = "";

      this.fetchData01();
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null,
      };
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      that.tableLoading = true;
      testResultApi
        .testResultList(
          that.tablePage.currentPage,
          that.tablePage.size,
          that.dataQuery
        )
        .then((res) => {
          that.tableData = res.data.data;
          that.tableLoading = false;
          // 设置后台返回的分页参数
          pageParamNames.forEach((name) =>
            that.$set(that.tablePage, name, parseInt(res.data[name]))
          );
        });
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 跳转到测试结果
    jumpOpenCourse(id) {
      const that = this;
      ls.setItem("testId", id);
      that.$router.push({
        path: "/students/components/studentWordsTest",
        query: {
          id: id,
        },
      });
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "areasStudentTestResultList",
        value: JSON.stringify(arr),
      };
      this.setHeaderSettings(data);
    },

    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: "areasStudentTestResultList",
      };
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
          this.$forceUpdate();
        } else {
          this.tableHeaderList = this.headerSettings;
          this.$forceUpdate();
        }
      });
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then((res) => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      });
    },
  },
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}
</style>
