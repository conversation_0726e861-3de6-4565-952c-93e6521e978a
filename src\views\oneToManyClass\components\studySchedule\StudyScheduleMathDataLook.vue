<!-- 一对多-学习课程表-数据查看弹窗 -->
<template>
  <el-drawer :title="title" :visible="dataLookMathVisible" @close="handleClose" style="margin-top: 15px" :size="screenWidth > 1300 ? '35%' : '70vw'">
    <el-row v-loading="loading" class="paikeTwo" style="margin-top: 1vw">
      <el-col class="paike">班级名称：{{ dataList.className }}</el-col>
      <el-col class="paike">班级编号：{{ dataList.classCode }}</el-col>
      <el-col class="paike">年级：{{ dataList.grade }}</el-col>
      <el-col class="paike">日期：{{ dataList.date }}</el-col>
      <el-col class="paike">时间：{{ dataList.actualClassTime }}</el-col>
      <!-- <el-col class="paike">
        <div v-for="(item, index) in dataList.studentInfoDtoList" :key="index">
          学员：
          <div class="card">{{ item.studentName }}</div>
        </div>
      </el-col> -->
      <el-col class="paike">
        今日学习内容：
        <div class="card" v-if="dataList.mathFeedbackInfoDto && dataList.mathFeedbackInfoDto.courseType == 1">{{ '同步课 / ' + dataList.mathFeedbackInfoDto.courseName }}</div>
        <div class="card" v-else>{{ '错题带刷' }}</div>
        <div v-if="dataList.studyContent" class="card">{{ dataList.studyContent }}</div>
      </el-col>
      <el-col v-for="(item, index) in dataList.studentInfoDtoList" :key="index" class="paike">
        {{ item.studentName }}学习反馈：
        <div class="card">{{ item.feedBack }}</div>
        <div>
          <div v-if="dataList.mathFeedbackInfoDto && dataList.learningReportMap && dataList.mathFeedbackInfoDto.courseType == 1">
            <div class="paike">版本：{{ dataList.learningReportMap[item.studentCode].versionName }}</div>
            <div class="paike">学科学段：{{ dataList.learningReportMap[item.studentCode].subjectPeriodName }}</div>
            <div class="paike">整体表现：{{ `互动参与（主动回答${dataList.learningReportMap[item.studentCode].participateTime}次，小组讨论积极）` }}</div>
            <div class="paike">
              随堂训练成绩：{{ dataList.learningReportMap[item.studentCode].score
              }}{{ dataList.learningReportMap[item.studentCode].fullScore ? `（满分${dataList.learningReportMap[item.studentCode].fullScore}分）` : '' }}
            </div>
            <div class="paike">
              随堂训练用时：{{ formatSecondsToTime(dataList.learningReportMap[item.studentCode].usedExamTime)
              }}{{
                formatSecondsToTime(dataList.learningReportMap[item.studentCode].examTime)
                  ? `（测试时长${formatSecondsToTime(dataList.learningReportMap[item.studentCode].examTime)}）`
                  : ''
              }}
            </div>
            <div class="paike">
              整体掌握程度：{{
                (dataList.learningReportMap[item.studentCode].proficiencyLevel ? getProficiencyText(dataList.learningReportMap[item.studentCode].proficiencyLevel) : '--') +
                `（${dataList.learningReportMap[item.studentCode].proficiencyScore}%）`
              }}
            </div>
            <div class="paike">
              答题正确率：{{ dataList.learningReportMap[item.studentCode].answerRightRate + '%'
              }}{{ `（共${dataList.learningReportMap[item.studentCode].allQuestionNumber}题、正确${dataList.learningReportMap[item.studentCode].answerRightNumber}题）` }}
            </div>
            <div class="paike">知识点掌握情况：</div>
            <div
              style="margin-left: 30px; font-size: 16px; color: #000"
              v-for="(itemKnow, knowIndex) in dataList.learningReportMap[item.studentCode].knowledgeProficiencyList"
              :key="knowIndex"
            >
              <div style="margin: 10px 0">
                {{ itemKnow.knowledgeName }}(掌握状态：{{ getProficiencyText(itemKnow.proficiencyLevel) }},程度：{{ itemKnow.proficiencyScore + '%' }})
              </div>
            </div>
          </div>
          <div v-if="dataList.mathFeedbackInfoDto && dataList.learningReportMap && dataList.mathFeedbackInfoDto.courseType !== 1">
            <div class="paike">
              随堂训练用时：{{ formatSecondsToTime(dataList.learningReportMap[item.studentCode].usedExamTime)
              }}{{
                formatSecondsToTime(dataList.learningReportMap[item.studentCode].examTime)
                  ? `（测试时长${formatSecondsToTime(dataList.learningReportMap[item.studentCode].examTime)}）`
                  : ''
              }}
            </div>
            <div class="paike">
              整体掌握程度：{{
                (dataList.learningReportMap[item.studentCode].proficiencyLevel ? getProficiencyText(dataList.learningReportMap[item.studentCode].proficiencyLevel) : '--') +
                (dataList.learningReportMap[item.studentCode].proficiencyScore ? `（${dataList.learningReportMap[item.studentCode].proficiencyScore}%）` : '')
              }}
            </div>
            <div class="paike">
              答题正确率：{{ dataList.learningReportMap[item.studentCode].answerRightRate + '%'
              }}{{
                dataList.learningReportMap[item.studentCode].allQuestionNumber && dataList.learningReportMap[item.studentCode].answerRightNumber
                  ? `（共${dataList.learningReportMap[item.studentCode].allQuestionNumber}题、正确${dataList.learningReportMap[item.studentCode].answerRightNumber}题）`
                  : ''
              }}
            </div>
            <div class="paike">
              学前学后对比：学习前（{{ dataList.learningReportMap[item.studentCode].learnBeforeScore }}）、学习后（{{
                dataList.learningReportMap[item.studentCode].learnAfterScore
              }}）
            </div>
            <div class="paike">知识点掌握情况：</div>
            <div
              style="margin: 30px; font-size: 16px; color: #000"
              v-for="(itemKnow, knowIndex) in dataList.learningReportMap[item.studentCode].knowledgeProficiencyList"
              :key="knowIndex"
            >
              <div style="margin: 10px 0">
                {{ itemKnow.knowledgeName }}(掌握状态：{{ getProficiencyText(itemKnow.proficiencyLevel) }},程度：{{ itemKnow.proficiencyScore + '%' }})
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col class="paike" style="text-align: center">
        <el-button type="primary" size="large" @click="handleClose">确定</el-button>
      </el-col>
    </el-row>
  </el-drawer>
</template>

<script>
  import load from 'jszip/lib/load';

  export default {
    name: 'DataLookMathDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      dataList: {
        type: Object,
        default: () => {
          return {};
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        screenWidth: window.screen.width,
        title: '数据查看'
      };
    },
    computed: {
      dataLookMathVisible: {
        get() {
          return this.visible;
        },
        set(v) {
          this.$emit('update:visible', v);
        }
      }
    },
    methods: {
      formatSecondsToTime(totalSeconds) {
        if (!totalSeconds || isNaN(Number(totalSeconds))) return '00分00秒';

        const seconds = Number(totalSeconds);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        const pad = (num) => String(num).padStart(2, '0');

        return `${pad(minutes)}分${pad(remainingSeconds)}秒`;
      },
      // 方法
      getProficiencyText(level) {
        switch (level) {
          case 1:
            return '未掌握';
          case 2:
            return '良好';
          case 3:
            return '优秀';
          default:
            return '--';
        }
      },
      // 关闭弹窗
      handleClose() {
        this.dataLookMathVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .diyih {
    display: flex;
    justify-content: center;
    margin-bottom: 3vh;
  }

  .firste {
    font-size: 18px;
    font-weight: 900;
    margin-left: 5vw;
  }

  .Second {
    padding-left: 60px;
  }

  // 2行
  .dierr {
    display: flex;
    justify-content: space-between;
  }

  .nob {
    border: none;
    background: #fff;

    &:first-child {
      padding-left: 3vw;
    }

    &:last-child {
      padding-right: 3vw;
    }
  }

  .paike {
    margin-bottom: 20px;

    &:first-child {
      margin-top: 0.5vw;
    }
  }

  .card {
    padding: 10px;
    margin-top: 6px;
    border-radius: 10px;
    line-height: 25px;
    background-color: #ebeef5;
  }

  .paikeTwo {
    width: 80%;
    margin-left: 2vw;
  }

  .xubtn {
    margin-top: 10vh;
  }

  .cc {
    height: 0px;
    margin: 1vh 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }

  .active1 {
    font-weight: 900;
    font-size: 18px;
    border-bottom: 2px solid #000;
  }

  .dateArrClass > div ::after {
    content: '';
    position: absolute;
    right: 9px;
    top: 21px;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #fc3c39;
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-radius: 25px;
    height: 100%;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    // font-size: 22px;
    font-size: 30px; // 拼音法
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
    padding-top: 0;
    margin-top: 15px;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
