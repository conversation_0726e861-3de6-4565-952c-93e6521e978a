import request from '@/utils/request'

// 交付中心配置列表查询
export const deliverlist2 = (data) => {
  return request({
    url: '/deliver/web/deliverconfig/deliverlist',
    method: 'get',
    params: data
  })
}
export const deliverlist = (data) => {
  return request({
    url: '/deliver/web/deliverconfig/simpleList',
    method: 'get',
    params: data
  })
}

// 交付中心交付区域详情
export const cityDetail = (merchantCode) => {
  return request({
    url: '/deliver/web/areaconfig/detail?merchantCode=' + merchantCode,
    method: 'get',
  })
}

// 交付中心交付详情
export const detail = (data) => {
  return request({
    url: '/deliver/web/deliverconfig/detail?deliverMerchantCode=' + data,
    method: 'get',
  })
}

// 交付中心省市接口
export const level = () => {
  return request({
    url: "/v2/mall/queryAllRegion/two/level",
    method: 'get',
  })
}

// 交付中心编辑接口
export const edit = (data) => {
  return request({
    url: "/deliver/web/deliverconfig/edit",
    method: 'put',
    data
  })
}

// 查询部门及用户
export const getUserTree = () => {
  return request({
    url: "/scrm/qywechat/getUserTree",
    // url: "/deliver/web/deliverconfig/getUserTree",
    method: 'GET'
  })
}
// 查询部门及用户
export const bindQyWechat = (data) => {
  return request({
    url: "/deliver/web/deliverconfig/bindUser",
    method: 'PUT',
    params: data
  })
}