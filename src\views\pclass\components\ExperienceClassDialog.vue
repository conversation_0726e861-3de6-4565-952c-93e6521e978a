<template>
  <el-form class="dialog">
    <el-drawer title="体验排课" :visible="childVisible_" :direction="direction" @close="handleClose" :size="screenWidth > 1300 ? '35%' : '80vw'">
      <el-row class="paikeTwo">
        <el-col class="cc" style="margin: 1.8vw"></el-col>
        <el-col class="paike">
          学员姓名：
          <span>{{ studentNaC.realName }}</span>
        </el-col>
        <el-col class="paike">
          学员编号：
          <span>{{ studentNaC.studentCode }}</span>
        </el-col>
        <el-col class="cc"></el-col>
      </el-row>

      <el-row class="paikeTwo">
        <!-- 选择课程 -->
        <el-col class="paike" style="margin-top: 2vw">
          <el-form-item label="选择课程:">
            <!-- <el-select v-model="courseType" placeholder="请选择" clearable>
              <el-option label="鼎英语" :value="1"></el-option>
            </el-select> -->
            <el-input v-model="courseType" disabled style="width: 200px" placeholder=""></el-input>
          </el-form-item>
        </el-col>
        <!-- 日期 -->
        <el-col class="paike">
          <el-form-item label="日期:">
            <el-date-picker type="date" value-format="yyyy-MM-dd" v-model="dateListl" placeholder="请选择" :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
        </el-col>
        <!-- 学习时间 -->
        <el-col class="paike" style="padding-bottom: 1vw">
          <el-form-item label="时间:">
            <el-time-picker
              v-model="coniditon.timeList[0].startTime"
              format="HH:mm"
              value-format="HH:mm"
              @change="updateEndTime"
              :style="{ width: screenWidth > 1300 ? '7vw' : '50%' }"
              style="margin-right: 0.5vw"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00'
              }"
              placeholder="任意时间点"
            ></el-time-picker>
            <el-input-number v-model="hourNum" @change="updateEndTime" :min="1" :max="24" label="小时"></el-input-number>
            <el-time-picker
              format="HH:mm"
              :style="{ width: screenWidth > 1300 ? '7vw' : '50%' }"
              style="margin-left: 0.5vw"
              value-format="HH:mm"
              v-model="coniditon.timeList[0].endTime"
              :picker-options="{
                selectableRange: '00:00:00 - 23:59:00'
              }"
              placeholder="任意时间点"
            ></el-time-picker>
          </el-form-item>
        </el-col>
        <!-- 复习时间 -->
        <el-col class="cc"></el-col>
      </el-row>

      <el-row>
        <el-col class="paike">
          <el-row>
            <el-col style="padding-top: 2vw">
              <el-form-item label="授课方式:">
                <el-select v-model="coniditon.teachingType" placeholder="请选择" clearable>
                  <el-option label="远程" :value="1"></el-option>
                  <el-option label="线下" :value="2"></el-option>
                  <el-option label="远程和线下" :value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col style="padding-top: 2vw">
              <el-form-item label="选择老师:">
                <el-select @visible-change="getTeacherList" v-model="planList.teacherId" placeholder="请选择" filterable>
                  <el-option v-for="item in teacherData" :key="item.teacherId" :label="item.teachName" :value="item.teacherId"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col style="padding-top: 2vw" v-if="oneToMore.oneToManyType == 1 && oneToMore.needDeliver == 0">
              <el-form-item label="班级:">
                <el-input-number v-model="oneToMore.deliverClass" :min="1" :max="999"></el-input-number>
                班
              </el-form-item>
            </el-col>
          </el-row>
          <div class="xubtn" type="flex" justify="center">
            <el-button class="del" @click="quxiaoBtn" style="margin-right: 1vw; margin-left: 2.5vw">取消</el-button>
            <span style="width: 2vw"></span>
            <el-button type="primary" @click="quedingBtn">确定</el-button>
          </div>
        </el-col>
      </el-row>
    </el-drawer>
  </el-form>
</template>

<script>
  import { screenTeacherList } from '@/api/paikeManage/LearnManager';
  import { getExperienceInfo, planCourse } from '@/api/studentClass/changeList';

  export default {
    name: 'experienceDialog',
    props: {
      // 控制弹窗显示
      TrialClassStyle: {
        type: Boolean,
        default: false //这里默认为false
      },
      // 学员列表的信息viewTime
      rowlist: {
        default: false //这里默认为false
      },
      details: {
        default: false
      }
    },
    data() {
      return {
        screenWidth: window.screen.width,
        courseType: '鼎英语',
        drawer: false,
        direction: 'rtl',
        studentNaC: {
          realName: '',
          studentCode: ''
        },
        coniditon: {
          //调老师列表用的
          dateList: [],
          teachingType: '',
          timeList: [
            {
              endTime: '',
              startTime: ''
            }
          ]
        },
        linshiTime: ['', ''],
        dateListl: '',
        planList: {
          //体验课程排课用的
          time: '',
          teacherId: '',
          id: '',
          curriculumId: ''
        },
        hourNum: 1,
        TeacherList: [],
        teacherData: [], //列表老师用的
        timer: '',
        val: '',
        pickerOptions: {
          disabledDate: this.disabledDate
        },
        oneToMore: {
          deliverClass: '',
          oneToManyType: '0',
          needDeliver: ''
        }
      };
    },
    mounted() {
      if (this.role == 3) {
        this.chartY = '9.5%';
      } else {
        this.chartY = '18%';
      }
    },
    computed: {
      childVisible_: {
        get() {
          return this.TrialClassStyle;
        },
        //值变化的时候会被调用
        set(v) {
          this.$emit('TrialClassLister', false);
        }
      }
    },
    methods: {
      handleInput(e) {
        // console.log(e);
        const reg = /^[1-9]\d*$/;
        if (!reg.test(e)) {
          this.deliverClass = '';
        }
      },
      updateEndTime() {
        if (this.coniditon.timeList[0].startTime) {
          // 解析所选的开始时间
          const startTimeParts = this.coniditon.timeList[0].startTime.split(':');
          const startHours = parseInt(startTimeParts[0]);
          const startMinutes = parseInt(startTimeParts[1]);

          // 计算一小时后的时间
          const endHours = startHours + this.hourNum;

          // 确保 endHours 在有效范围内（0-23）
          const adjustedEndHours = endHours % 24;

          // 格式化结束时间为 HH:mm
          const endMinutes = startMinutes < 10 ? '0' + startMinutes : startMinutes;
          const endFormatted = `${adjustedEndHours}:${endMinutes}`;

          // 使用计算后的时间更新 endTime
          this.coniditon.timeList[0].endTime = endFormatted;
        }
      },
      // 回显
      async expericeRlook() {
        let { data } = await getExperienceInfo(this.planList.id);
        this.oneToMore = {
          deliverClass: data.deliverClass,
          oneToManyType: data.oneToManyType,
          needDeliver: data.needDeliver
        };
        this.dateListl = data.date;
        this.teacherData.push({ teacherId: data.teacherId, teachName: data.teacherName });
        this.planList.teacherId = data.teacherId;
        this.coniditon.teachingType = data.teachingType;
        let timeone = data.time.split(',');
        if (this.coniditon.teachingType == 0) {
          this.coniditon.teachingType = '';
        }
        // this.coniditon.timeList[0].startTime = timeone[0];
        // this.coniditon.timeList[0].endTime = timeone[1];
        // this.dateListl = '2024-04-01';
        // this.coniditon.timeList[0].startTime = "15:43";
        // this.coniditon.timeList[0].endTime = "16:43";
      },
      disabledDate(time) {
        // time 是new Date
        return time.getTime() < Date.now() - 3600 * 1000 * 24;
      },
      // 获取老师列表
      async getTeacherList() {
        this.coniditon.dateList = [];
        this.coniditon.dateList.push(this.dateListl);
        this.coniditon.isExp = true;
        this.coniditon.studentCode = this.studentNaC.studentCode;
        console.log(this.coniditon);
        console.log(this.dateListl);
        let { data } = await screenTeacherList(this.coniditon);
        this.teacherData = data;
      },
      // 关闭弹窗
      handleClose() {
        this.childVisible_ = false;
        this.coniditon.dateList = [];
        this.coniditon.time = ['', ''];
        this.deliverClass = '';
        this.teacherData = [];
      },
      quxiaoBtn() {
        this.childVisible_ = true;
      },
      async quedingBtn() {
        this.planList.date = this.dateListl;
        this.planList.teachingType = this.coniditon.teachingType;
        this.planList.deliverClass = this.oneToMore.deliverClass;
        // 时间临时处理
        this.linshiTime[0] = this.coniditon.timeList[0].startTime;
        this.linshiTime[1] = this.coniditon.timeList[0].endTime;
        this.planList.time = this.linshiTime.toString();
        let res = await planCourse(this.planList);
        this.$message.success('操作成功');
        this.childVisible_ = false;
        //重新获取列表
        this.$emit('updateList');
      }
    }
  };
</script>

<style lang="scss">
  // .paike {
  //   margin-left: 2vw;
  //   &:last-child {
  //     margin-bottom: 30px;
  //   }
  //   &:first-child {
  //     margin-top: 30px;
  //   }
  // }
  // .paikeTwo {
  //   width: 90%;
  // }
  .xubtn {
    margin-left: 7vw;
  }

  .cc {
    height: 0px;
    margin: 0 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }
</style>

<style lang="scss" scoped>
  .xubtn {
    margin-left: 7vw;
  }

  .paike {
    margin-left: 2vw;
    margin-bottom: 2vw;

    &:last-child {
      margin-bottom: 30px;
    }

    &:first-child {
      margin-top: 30px;
    }
  }

  .paikeTwo {
    width: 90%;
  }

  .cc {
    height: 0;
    margin: 0 1.5vw 0 1.5vw;
    border-bottom: 1px solid #000;
  }

  div ::v-deep .el-drawer__container {
    position: relative;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    width: 100%;
  }

  ::v-deep .el-drawer__header {
    color: #000;
    font-size: 22px;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0;
  }

  ::v-deep :focus {
    outline: 0;
  }

  ::v-deep .el-drawer__body {
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
