import authApi from '@/api/auth';
import courseApi from '@/api/training/course';
import { getToken, setToken, removeToken } from '@/utils/auth';
import avatorImg from '../../../static/image/avator.gif';
import ls from '@/api/sessionStorage';
const user = {
  state: {
    // payUrl: "http://192.168.40.38:8000/",
    payUrl: 'https://pay.dxznjy.com/',
    // 在线表单查询页面缓存
    onlineFormCache: {},
    documentClientHeight: 100,
    documentClienWidth: undefined,
    globalId: '',
    JlbInfo: {},
    user: '',
    status: '',
    code: '',
    token: getToken(),
    name: '',
    nick: '',
    avatar: avatorImg,
    introduction: '',
    visitor: false,
    role: localStorage.getItem('role') || '',
    roles: [],
    perms: [],
    roleName: '',
    setting: {
      articlePlatform: []
    },
    examStatus: false
  },

  mutations: {
    setJlbInfo: (state, JlbInfo) => {
      state.JlbInfo = JlbInfo;
    },
    addOnlineFormCache: (state, data) => {
      state.onlineFormCache[data.key] = data.value;
    },
    removeOnlineFormCache: (state, key) => {
      delete state.onlineFormCache[key];
    },
    clearOnlineFormCache: (state) => {
      state.onlineFormCache = {};
    },
    setClientHeight: (state, height) => {
      state.documentClientHeight = height;
    },
    setClientWidth: (state, width) => {
      state.documentClienWidth = width;
    },
    SET_CODE: (state, code) => {
      state.code = code;
    },
    SET_globalId: (state, globalId) => {
      state.globalId = globalId;
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction;
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting;
    },
    SET_STATUS: (state, status) => {
      state.status = status;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_NICKNAME: (state, nickname) => {
      state.nickname = nickname;
    },
    SET_NICK: (state, nick) => {
      state.nick = nick;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLE: (state, role) => {
      state.role = role;
      localStorage.setItem('role', role);
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
      let str = roles.filter((i) => i.val == state.role)
      console.log(str, 'strstrstr')
    },
    SET_ROLENAME: (state, roleName) => {
      state.roleName = roleName;
    },
    SET_PERMS: (state, perms) => {
      state.perms = perms;
    },
    SET_VISITOR: (state, visitor) => {
      state.visitor = visitor;
    },
    SET_EXAMSTATUS: (state, examStatus) => {
      state.examStatus = examStatus;
    }
  },

  actions: {
    // 不需要验证码登录
    LoginByNoCode({ commit }, userInfo) {
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        authApi
          .loginByNoCode(username, userInfo.password, 8, '', user.state.globalId, userInfo.role)
          .then((response) => {
            commit('SET_ROLE', userInfo.role);
            const data = response.data;
            commit('SET_TOKEN', data.token);
            setToken(response.data.token);
            resolve(data.token);
          })
          .catch((error) => {
            console.log(error);
            reject(error);
          });
      });
    },
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        authApi
          .loginByUsername(username, userInfo.password, 8, userInfo.verificationCode, user.state.globalId, userInfo.role)
          .then((response) => {
            commit('SET_ROLE', userInfo.role);
            const data = response.data;
            commit('SET_TOKEN', data.token);
            setToken(response.data.token);
            resolve(data.token);
            localStorage.setItem('firstLoginTag', data.firstLoginTag);
          })
          .catch((error) => {
            console.log(error);
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetUserInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        authApi
          .getUserInfo(state.token)
          .then((res) => {
            ls.setItem('rolesVal', res.data.roles[0].val);
            ls.setItem('rolesVala', res.data.roles);
            if (!res) reject('res is null');
            if (!res.data) reject('res.data is null');
            if (!res.data.perms || res.data.perms.length == 0 || !res.data.perms || res.data.perms.length == 0) {
              commit('SET_VISITOR', true);
            } else {
              commit('SET_VISITOR', false);
            }
            // if(res.data.nickname!="13349184887"){
            //   res.data.perms = res.data.perms.filter(item=>item.val!="m:ruku:teacherGrade");
            // }
            if (res.data.nickname == '18756030765') {
              res.data.perms = res.data.perms.filter((item) => item.val != 'm:Finance:trialclassDividend');
            }
            // console.log(res.data.nick);
            window.localStorage.setItem('sysUserRoles', JSON.stringify(res.data.sysUserRoles));
            window.localStorage.setItem('sysUserInfo', JSON.stringify(res.data));
            window.localStorage.setItem('roleTag', res.data.roles[0].val);
            // window.localStorage.setItem("userMerchantCode", res.data.merchantCode);
            // window.localStorage.setItem("userId", res.data.id);
            if (state.JlbInfo && state.JlbInfo.logoEnable) {
            } else {
              commit('setJlbInfo', res.data.currentAdminVo);
            }

            commit('SET_CODE', res.data.merchantCode);
            let arr = res.data.roles.filter((i) => i.val == state.role)
            let roleName = ''
            if (arr.length > 0) {
              roleName = arr[0].name
            } else {
              roleName = ''
            }
            commit('SET_ROLENAME', roleName);
            commit('SET_ROLES', res.data.roles);
            commit('SET_PERMS', res.data.perms);
            commit('SET_NICK', res.data.nickname);
            commit('SET_NAME', res.data.username);
            // commit('SET_NICKNAME', res.data.nickname)
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        authApi
          .logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        removeToken();
        resolve();
      });
    },

    // 动态修改权限
    ChangeRoles({ commit }, role) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', role);
        setToken(role);
        authApi.getUserInfo(role).then((response) => {
          const data = response.data;
          commit('SET_ROLES', data.roles);
          commit('SET_NAME', data.name);
          commit('SET_AVATAR', data.avatar);
          commit('SET_INTRODUCTION', data.introduction);
          resolve();
        });
      });
    },

    // 判断账号对应考试是否全部通过
    checkExamStatus({ commit, state }) {
      let sysUserRoles = JSON.parse(window.localStorage.getItem('sysUserRoles'));
      let roleTag = state.roles[0].val;
      let userParams = {
        userId: sysUserRoles[0].userId,
        roleTag
      };
      return new Promise((resolve, reject) => {
        courseApi
          .checkCourseCompleted(userParams)
          .then((res) => {
            commit('SET_EXAMSTATUS', res.data);
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    }
  }
};

export default user;
