<!-- 交付中心-一对多学员管理-待完善上课信息表-正课 -->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px" ref="searchNum" :model="searchNum">
        <el-row>
          <el-col :span="6">
            <el-form-item label="姓名:" prop="studentName" style="width: 80%">
              <el-input v-model="searchNum.studentName" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学员编号:" prop="studentCode" style="width: 80%">
              <el-input v-model="searchNum.studentCode" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="">
              <el-button type="primary" size="small" @click="search">查询</el-button>
              <el-button style="margin-left: 10px" size="small" @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="tableList" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center"></el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { getPendingCompletionClassInfoFormal } from '@/api/oneToManyClass/pendingCompletionClassInfo';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  export default {
    name: 'PendingCompletionClassInfoFormal',
    components: { HeaderSettingsDialog },
    data() {
      return {
        //搜索参数
        searchNum: {
          studentName: '',
          studentCode: '',
          pageNum: 1,
          pageSize: 10
        },

        // 列表属性弹框
        HeaderSettingsStyle: false,
        headerSettings: [
          { name: '订单编号', value: 'id' },
          { name: '学员姓名', value: 'studentName' },
          { name: '学员编号', value: 'studentCode' },
          { name: '学员手机号', value: 'phone' },
          { name: '充值学时', value: 'rechargeHour' },
          { name: '购买时间', value: 'createTime' },
          { name: '门店账号', value: 'merchantCode' },
          { name: '门店名称', value: 'merchantName' },
          { name: '门店手机号', value: 'merchantPhone' }
        ],
        tableHeaderList: [],

        tableLoading: false,
        tableList: [],

        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        }
      };
    },
    mounted() {
      this.getHeaderlist();
      this.getPendingCompletionClassInfoFormalList();
    },
    methods: {
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'PendingCompletionClassInfoFormal',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'PendingCompletionClassInfoFormal'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getPendingCompletionClassInfoFormalList();
      },
      // 重置
      reset() {
        this.searchNum = {};
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getPendingCompletionClassInfoFormalList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getPendingCompletionClassInfoFormalList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getPendingCompletionClassInfoFormalList();
      },
      getPendingCompletionClassInfoFormalList() {
        this.tableLoading = true;
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getPendingCompletionClassInfoFormal(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data || [];
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => {
            this.tableList = [];
            this.tableLoading = false;
          });
      }
    }
  };
</script>

<style></style>
