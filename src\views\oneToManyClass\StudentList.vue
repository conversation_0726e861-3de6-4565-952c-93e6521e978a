<!-- 交付中心-一对多学员管理-学员列表 -->
<template>
  <div>
    <!-- 课程类型 -->
    <el-row style="margin: 20px 0 20px 20px">
      <el-radio-group v-model="courseType" size="medium" @change="handleCourseTypeTabsClick">
        <el-radio-button label="1">试课</el-radio-button>
        <el-radio-button label="2">正式课</el-radio-button>
      </el-radio-group>
    </el-row>

    <!-- 试课学员列表 -->
    <StudentListTrial v-show="courseType === '1'" />

    <!-- 正课学员列表 -->
    <StudentListFormal v-show="courseType === '2'" />
  </div>
</template>

<script>
  import StudentListTrial from './StudentListTrial.vue';
  import StudentListFormal from './StudentListFormal.vue';
  export default {
    name: 'StudentList',
    components: { StudentListTrial, StudentListFormal },
    data() {
      return {
        courseType: '1',
        searchNum: {},
        teacherList: []
      };
    },
    methods: {
      handleCourseTypeTabsClick(value) {
        this.courseType = value;
      }
    }
  };
</script>

<style></style>
