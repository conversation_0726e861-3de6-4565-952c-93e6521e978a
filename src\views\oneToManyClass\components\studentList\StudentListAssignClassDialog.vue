<!-- 一对多-学生列表-指派班级弹窗 -->
<template>
  <el-dialog title="更换班级" :visible.sync="assignClassDialogVisible" width="30%">
    <el-form :model="assignClassFormData" :rules="assignClassRules" label-width="110px" ref="assignClassFormData" size="small">
      <el-form-item v-if="isAdmin" label="当前交付中心:" prop="deliverName">
        <el-input v-model="assignClassFormData.deliverName" disabled></el-input>
      </el-form-item>
      <el-form-item v-else label="课程类型:" prop="courseType">
        <el-input v-model="assignClassFormData.courseType" disabled></el-input>
      </el-form-item>
      <el-form-item label="年级" prop="gradeName">
        <el-input v-model="assignClassFormData.gradeName" disabled></el-input>
      </el-form-item>
      <el-form-item label="当前班级" prop="className">
        <el-input v-model="assignClassFormData.className" disabled></el-input>
      </el-form-item>
      <el-form-item v-if="isAdmin" label="交付中心:" prop="deliverMerchant">
        <BaseElSelectLoadmore
          v-if="assignClassDialogVisible"
          v-model="assignClassFormData.deliverMerchant"
          valueProp="merchantCode"
          labelProp="merchantName"
          :searchFunc="getDeliverList"
          @change="handleAssignClassDeliverChange"
          style-data="width: 100%"
        ></BaseElSelectLoadmore>
      </el-form-item>
      <el-form-item :label="isAdmin ? '班级名称' : '更换班级'" prop="classId">
        <el-select v-model="assignClassFormData.classId" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in assignClassList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="text-align: right; margin-top: 40px">
        <el-button @click="handleAssignClassCancelClick">取消</el-button>
        <el-button type="primary" @click="handleAssignClassSubmitClick">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
  import BaseElSelectLoadmore from './BaseElSelectLoadmore';
  import { getAllDeliver } from '@/api/oneToManyClass/classList';
  import { getClassByCurriculumIdAndDeliverMerchant } from '@/api/oneToManyClass/studentList';
  import ls from '@/api/sessionStorage';
  export default {
    name: 'StudentListAssignClassDialog',
    components: { BaseElSelectLoadmore },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      valueData: {
        type: Object,
        default: () => {
          return {};
        }
      },
      courseType: {
        type: Number,
        default: 2
      }
    },
    data() {
      return {
        isAdmin: false,
        isDeliveryCenter: false,

        // 指派/更换班级弹框数据
        assignClassFormData: {},
        assignClassRules: {
          deliverMerchant: [{ required: true, message: '请选择交付中心', trigger: 'change' }],
          classId: [{ required: true, message: '请选择班级', trigger: 'change' }]
        },
        assignClassList: []
      };
    },
    computed: {
      // 指派班级弹窗显示控制
      assignClassDialogVisible: {
        get() {
          return this.visible;
        },
        set(val) {
          this.$emit('update:visible', val);
        }
      }
    },
    watch: {
      visible(val) {
        if (val) {
          this.assignClassFormData = { ...this.valueData };
          this.handleAssignClassDeliverChange(this.assignClassFormData.deliverMerchant);
        }
      }
    },
    created() {
      // 是否是管理员(dxAdmin+187)
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';

      // 是否是交付中心
      this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';
    },
    methods: {
      // 获取交付中心分页下拉
      getDeliverList(selectQuery) {
        return new Promise((resolve, reject) => {
          getAllDeliver(selectQuery)
            .then((res) => {
              resolve(
                res.data.data.map((item) => {
                  return { value: item.merchantCode, label: item.merchantName };
                })
              );
            })
            .catch((err) => reject(err));
        });
      },
      // 处理指派班级弹窗交付中心下拉框选择事件
      handleAssignClassDeliverChange(deliverMerchant) {
        this.assignClassList = [];
        this.assignClassFormData.classId = '';
        if (!deliverMerchant) return;
        let { curriculumId, grade, oldClassId } = this.assignClassFormData;
        getClassByCurriculumIdAndDeliverMerchant(deliverMerchant, curriculumId, this.courseType, grade).then((res) => {
          this.assignClassList = res.data
            .map((item) => {
              return { value: item.id, label: item.className };
            })
            .filter((item) => item.value !== oldClassId);
        });
      },
      // 处理指派班级弹窗确定点击事件
      handleAssignClassSubmitClick() {
        this.$refs.assignClassFormData.validate((valid) => {
          if (valid) {
            this.$emit('submit', { ...this.assignClassFormData });
          }
        });
      },
      // 处理指派班级弹窗取消点击事件
      handleAssignClassCancelClick() {
        this.assignClassDialogVisible = false;
        this.assignClassFormData = { classId: '', deliverMerchant: '' };
        this.$nextTick(() => {
          this.$refs.assignClassFormData.clearValidate();
        });
      }
    }
  };
</script>

<style></style>
