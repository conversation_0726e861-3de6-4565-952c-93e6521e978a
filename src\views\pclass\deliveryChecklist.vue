<!--正式学员管理 正式学员交付清单-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px" ref="searchNum" :model="searchNum">
        <el-row>
          <el-col :span="8">
            <el-form-item label="上学时间:" prop="timeAll">
              <el-date-picker v-model="timeAll" style="width: 26vw" size="small" format="yyyy-MM-dd HH:mm"
                @change="changeTime" value-format="yyyy-MM-dd HH:mm" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="">
              <el-button type="primary" size="mini" @click="changeStore">选择门店</el-button>
              <el-button type="warning" style="margin-left: 10px" icon="el-icon-download" size="mini" @click="exportExcel"
                :loading="exportLoading">
                导出
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:20px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :cell-style="{ 'text-align': 'center' }" :header-cell-style="getRowClass">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`"
        :prop="item.value" :label="item.name" header-align="center" :width="item.value=='memberPhone'?220:''">
      </el-table-column>
    </el-table>

    <!-- 表格 -->
    <!-- <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :cell-style="{ 'text-align': 'center' }" :header-cell-style="getRowClass">
      <el-table-column prop="merchantName" min-width="130" label="门店名称" header-align="center" />
      <el-table-column prop="merchantCode" min-width="110" label="门店编号" header-align="center" />
      <el-table-column prop="merchantRealName" min-width="110" label="负责人" header-align="center" />
      <el-table-column prop="firstStudyTime" label="首次交付时间" width="180" header-align="center"></el-table-column>
      <el-table-column prop="studentName" label="学员姓名" width="100" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150" header-align="center"></el-table-column>
      <el-table-column prop="memberPhone" label="登录手机号" header-align="center" width="220"></el-table-column>
      <el-table-column prop="grade" label="年级" header-align="center" width="80"></el-table-column>
      <el-table-column prop="totalDeliverHours" label="鼎英语已购学时" min-width="100" header-align="center"></el-table-column>
      <el-table-column prop="planOrStudyDeliverHours" label="累计交付学时" min-width="100"
        header-align="center"></el-table-column>
      <el-table-column prop="nowDeliverHours" label="本月交付学时" min-width="100" header-align="center"></el-table-column>
      <el-table-column prop="haveDeliverHours" label="剩余可排交付学时" min-width="100" header-align="center"></el-table-column>
    </el-table> -->
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <el-dialog :visible.sync="dialogVisible" width="18%" center>
      <div style="display: flex;align-items: center;justify-content: center;margin-bottom: 10px;">
        <i class="el-icon-warning" style="font-size: 56px;color: #3ab1fd;"></i>
        <div style="margin-left: 10px;">
          <div style="font-size: 16px;color: #000;margin-bottom: 10px;">提示</div>
          <div>请先选择上学时间</div>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="选择门店" :visible.sync="centerDialogVisible" width="25%" center>
      <el-form ref="form" label-width="100px">
        <el-form-item label="门店名称:">
          <el-select v-model="storeCode" filterable placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="selectStore">搜 索</el-button>
      </span>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems"/>

  </div>
</template>

<script>
import {
  selectAllSchool,
  selSchoolStudentStudyStatistics,
  schoolStudentStudyStatisticsExport,
  getTableTitleSet, setTableList
} from "@/api/paikeManage/classCard";
import dayjs from "dayjs"
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: "deliveryChecklist",
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      searchNum: {
        merchantCode: "",
        endTime: "",
        startTime: "",
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      tableLoading: false,
      timeAll: [],
      total: null,
      reviewLister: [],

      exportLoading: false,

      dialogVisible: false, // 先选择上学时间提示
      centerDialogVisible: false, // 门店
      storeCode: '',
      options: [],

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '门店名称',
          value: 'merchantName'
        },
        {
          name: '门店编号',
          value: 'merchantCode'
        },
        {
          name: '负责人',
          value: 'merchantRealName'
        },
        {
          name: '首次交付时间',
          value: 'firstStudyTime'
        },
        {
          name: '学员姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '登录手机号',
          value: 'memberPhone'
        },
        {
          name: '年级',
          value: 'grade'
        },
        {
          name: '鼎英语已购学时',
          value: 'totalDeliverHours'
        },
        {
          name: '累计交付学时',
          value: 'planOrStudyDeliverHours'
        },
        {
          name: '本月交付学时',
          value: 'nowDeliverHours'
        },
        {
          name: '剩余可排交付学时',
          value: 'haveDeliverHours'
        }],

      tableHeaderList: [], // 获取表头数据

    };
  },
  created() {
    this.initCode();
    this.initData();
    this.getHeaderlist();

  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 列表数据
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll || this.timeAll == '') {
        this.timeAll = this.getNormalMouth();
      }
      this.tableLoading = true;
      this.searchNum.startTime = this.timeAll[0];
      this.searchNum.endTime = this.timeAll[1];
      let { data } = await selSchoolStudentStudyStatistics(this.searchNum)
      this.tableLoading = false;
      this.total = Number(data.totalItems);
      this.reviewLister = data.data;
    },

    async initCode() {
      let allCodeData = await selectAllSchool()
      this.options = allCodeData.data;
    },

    getNormalMouth() {
      let currentDate = new Date();
      let firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      let formattedFirstDay = `${firstDayOfMonth.getFullYear()}-${(firstDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}-${firstDayOfMonth.getDate().toString().padStart(2, '0')} 00:00`;
      let lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      let formattedLastDay = `${lastDayOfMonth.getFullYear()}-${(lastDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}-${lastDayOfMonth.getDate().toString().padStart(2, '0')} 23:59`;

      let arr = [formattedFirstDay, formattedLastDay];
      return arr;
    },

    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    //定义导出Excel表格事件
    exportExcel() {
      this.exportLoading = true;
      let requestData = {
        startTime: this.timeAll[0],
        endTime: this.timeAll[1],
        merchantCode: this.storeCode
      }
      schoolStudentStudyStatisticsExport(requestData).then(res => {
        console.log(window.URL.createObjectURL(res));
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;//获取服务器端的文件名
        link.setAttribute("download", "正式学员交付清单导出.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      })
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.initData();
    },

    // 时间选择
    changeTime(e) {
      if (e) {
        let diff = dayjs(e[1]).unix() - dayjs(e[0]).unix();
        // 判断时差是否大于一个月
        if (diff > 30 * 24 * 60 * 60) {
          this.$message.error('筛选时间不可大于一个月');
          this.timeAll = '';
        } else {
          console.log('时差小于等于一个月');
        }
      }
    },

    // 选择门店
    changeStore() {
      if (this.timeAll == '' || !this.timeAll || this.timeAll.length == 0) {
        this.dialogVisible = true;
      } else {
        this.centerDialogVisible = true;
      }
    },

    selectStore() {
      if (this.storeCode == '') {
        this.$message.error('请选择门店');
      } else {
        // 请求接口
        this.centerDialogVisible = false;
        this.searchNum.merchantCode = this.storeCode;
        this.initData();
      }
    },

    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "deliveryChecklist",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'deliveryChecklist'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
};
</script>

<style scoped>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}
</style>
