/**
 * “教练工资明细”相关接口
 */
import request from '@/utils/request';
export default {
  queryList(pageNum, pageSize, queryParam) {
    return request({
      url: '/deliver/teacherWagesCount/getTableList',
      method: 'get',
      params: {
        pageNum,
        pageSize,
        month:queryParam.month,
        teacherMobile:queryParam.teacherMobile,
        teacherName:queryParam.teacherName,
        merchantCode:queryParam.merchantCode,
        merchantName:queryParam.merchantName,
      }
    });
  },
  //查询详细列表
  queryDetailList(pageNum, pageSize, queryParam) {
    return request({
      url: '/deliver/teacherWagesCount/getTableDetailList',
      method: 'get',
      params: {
        pageNum,
        pageSize,
        month:queryParam.detailMonth,
        wagesType:queryParam.wagesType,
        teacherName:queryParam.detailTeacherName,
        merchantCode:queryParam.detailMerchantCode,
        curriculumName:queryParam.curriculumName,
        studentCode:queryParam.studentCode,
        isExp:queryParam.isExp,
      }
    });
  },
};
