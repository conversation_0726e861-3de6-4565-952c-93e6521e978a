<template>
  <el-menu class="navbar" mode="horizontal">
    <hamburger class="hamburger-container" :toggleClick="toggleSideBar" :isActive="sidebar.opened"></hamburger>

    <breadcrumb class="breadcrumb-container"></breadcrumb>

    <div class="right-menu" :style="this.screenUWidth < 460 ? 'fontSize:12px;' : 'fontSize:16px;'">
      <!--
            <error-log v-if="this.arrears==true" class="errLog-container right-menu-item">您的账户余额不足</error-log>

            <div v-if="this.roleTag!=='School' &&this.roleTag!=='Agent'" style="display: inline-block;float: left;">
              <span>账户余额：</span>
              <el-tag style="margin-right: 10px;">￥{{ count }}</el-tag>
            </div>

            <div v-if="this.roleTag==='School'" style="display: inline-block;float: left;">
              <span>剩余学时：</span>
              <el-tag style="margin-right: 0px;">{{ count }}节</el-tag>
            </div>
      -->

      <el-tooltip content="Global Size" class="sizeSelect" v-if="this.screenUWidth > 362" effect="dark" placement="bottom">
        <size-select id="size-select" class="right-menu-item hover-effect" />
      </el-tooltip>

      <div style="display: inline-block; float: left">
        <el-dropdown>
          <div>
            <span>帐号：</span>
            <el-tag style="margin-right: 10px">{{ name }}</el-tag>
          </div>
          <!-- <el-dropdown-menu slot="dropdown">
            <el-dropdown-item >
              <span>帐号：</span>
              {{ name }}
            </el-dropdown-item>
          <el-dropdown-item >
            <el-button type="primary" size="mini" @click="personMy">个人信息</el-button><el-button type="primary" size="mini" @click="realName">实名认证</el-button>
          </el-dropdown-item>
          <el-dropdown-item > <div style="height:.3vw"></div> </el-dropdown-item>
          </el-dropdown-menu> -->
          <el-dropdown-menu class="test"></el-dropdown-menu>
        </el-dropdown>
        <span>角色：</span>
        <!--        <el-tag style="margin-right: 5px;" type="danger" v-if="roles.length==0" >游客（未配置任何角色）</el-tag>-->
        <!--        <el-tag style="margin-right: 5px;" type="success" v-else v-for="r in roles" :key="r.val">{{r.name}}</el-tag>-->
        <el-tag style="margin-right: 5px" type="success">{{ roleName }}</el-tag>
        <el-button size="mini" type="primary" v-if="roles[0].val === 'DeliveryCenter'" style="margin: 0 7px" @click="merchantBinding()" round>绑定门店</el-button>
      </div>
      <el-dropdown class="avatar-container right-menu-item" trigger="click">
        <div class="avatar-wrapper" style="margin-top: 0">
          <img class="user-avatar" :src="(JlbInfo && JlbInfo.logoEnable && JlbInfo.avatar) || avatar" :style="this.screenUWidth < 360 ? 'height:30px;margin-top:8px' : 'height:40px'" />
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- 第一 -->
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <!-- 第二 -->
          <router-link to="/layout/personMe/personMy">
            <el-dropdown-item>个人信息</el-dropdown-item>
          </router-link>
          <!-- 第三 -->
          <el-dropdown-item>
            <span @click="handleUpdatePwd" style="display: block">修改密码</span>
          </el-dropdown-item>
          <!-- 第四 -->
          <el-dropdown-item divided>
            <span @click="logout" style="display: block">退出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!--弹出窗口：修改密码-->
    <el-dialog title="修改密码" :visible.sync="dialogVisible" width="70%">
      <el-form :rules="rules" ref="pwdFom" :model="temp" label-position="left" label-width="120px" class="updatePwd">
        <el-form-item label="手机号" prop="mobile" style="width: 60%">
          <el-input v-model="temp.mobile" placeholder="请输入手机号" disabled></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="newPwd" style="width: 60%">
          <el-input v-model="temp.newPwd" placeholder="请输入密码" :type="passwordNewType">
            <template #suffix>
              <span class="svg-container-end" @click="showNewPwd">
                <svg-icon :icon-class="passwordNewType=='password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPwd" style="width: 60%">
          <el-input :type="passwordComType" v-model="temp.confirmPwd" placeholder="请再次输入密码">
            <template #suffix>
              <span class="svg-container-end" @click="showComPwd">
                <svg-icon :icon-class="passwordComType=='password' ? 'eye' : 'eye-open'" />
              </span>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="验证码" prop="smsCode" style="width: 60%">
          <div style="display: flex; justify-content: space-between">
            <el-input placeholder="请输入验证码" v-model="temp.smsCode" style="display: inline-block"></el-input>
            <el-button @click.stop="getSmsClick()" style="display: inline-block;" type="primary" :disabled="disabledSmsClick">{{ countdown }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div class="dialog-tip">温馨提示:</div>
      <div class="dialog-tip">1:新密码长度不少于8位,需包含字母、数字;</div>
      <div class="dialog-tip">2:如果您的登录号码收不到验证码，请联系渠道经理修改登录手机号;</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updatePwd">确定</el-button>
      </div>
    </el-dialog>
    <!-- <el-dialog title="修改密码" :visible.sync="dialogVisible" width="70%">
      <el-form :rules="rules" ref="dataForm" :model="temp" label-position="left" label-width="120px">
        <el-form-item label="密码" prop="pwd">
          <el-input type="password" v-model="temp.pwd"></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="pwd2">
          <el-input type="password" v-model="temp.pwd2"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updatePwd">确定</el-button>
      </div>
    </el-dialog> -->

    <!--弹出窗口：绑定门店-->
    <el-dialog :before-close="bindingClose" title="绑定门店" class="merchantBindingBox" center :visible.sync="merchantVisible" width="550px">
      <el-form ref="dataForm" label-width="80px">
        <el-form-item label="门店账号:" prop="name">
          <el-row>
            <el-col :span="19">
              <el-input :disabled="affirm" v-model="binding.name" placeholder="请输入门店账号或者门店编号"></el-input>
            </el-col>
            <el-col :span="4">
              <el-button :disabled="affirm" icon="el-icon-search" style="margin-left: 5px" @click="search()"></el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <div v-if="binding.merchantCode">
          <el-form-item label="门店编号:" prop="merchantCode">
            <span>{{ binding.merchantCode }}</span>
          </el-form-item>
          <el-form-item label="门店名称:" prop="merchantName">
            <span>{{ binding.merchantName }}</span>
          </el-form-item>
          <el-form-item label="门店地址:" prop="address">
            <span>{{ binding.address }}</span>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="bindingClose">取消</el-button>
        <el-button v-if="!this.affirm" :loading="bindingLoading" type="primary" @click="affirmBinding()">确定</el-button>
      </div>
    </el-dialog>
    <!-- 修改密码短信验证前图形校验 -->
    <Verify ref="verify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" />
  </el-menu>
</template>

<script>
import Verify from '@/components/verifition/Verify';
import { mapGetters } from 'vuex';
import Breadcrumb from '@/components/Breadcrumb';
import Hamburger from '@/components/Hamburger';
import userApi from '@/api/user';
import forgotApi from '@/api/forgot';
import SizeSelect from '@/components/SizeSelect';
import { getMerchantBindingInfo, merchantBinding, searchMerchantInfo } from '@/api/merchant/merchantBinding';

export default {
  data() {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (value.length < 8) {
        callback(new Error('密码不能少于8个字符'));
      } else if (value.length > 50) {
        callback(new Error('密码不能多于50个字符'));
      } else {
        const regexForbidden = /[\u4e00-\u9fa5\s\W_]/; // 匹配汉字和空格
        if (regexForbidden.test(value)) {
          callback(new Error('密码不能包含汉字空格和符号'));
          return;
        }
        const regex = /^(?=.*[A-Za-z])(?=.*\d).+$/;
        if (!regex.test(value)) {
          callback(new Error('密码需同时包含字母和数字'));
        }
        if (this.temp.confirmPwd !== '') {
          this.$refs.pwdFom.validateField('confirmPwd');
        }
        callback();
      }
    };

    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value != this.temp.newPwd) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    return {
      //屏幕宽度
      screenUWidth: 0,
      count: 0,
      roleTag: '',
      dialogVisible: false,
      arrears: false,
      temp: {
        // pwd: null,
        // pwd2: null
        mobile: '',
        newPwd: '',
        confirmPwd: '',
        smsCode: '',
        source: 'admin',
        sysType: 0,
      },
      rules: {
        newPwd: [{ validator: validatePass, trigger: 'blur' }],
        confirmPwd: [{ validator: validatePass2, trigger: 'change' }]
      },
      merchantVisible: false,
      binding: {},
      affirm: false,
      bindingLoading: false,
      countdown: '获取验证码',
      disabledSmsClick: false,
      passwordNewType: 'password',
      passwordComType: 'password'
    };
  },
  //ErrorLog,
  components: {
    Breadcrumb,
    Hamburger,
    SizeSelect,
    Verify
  },
  computed: {
    ...mapGetters(['sidebar', 'name', 'avatar', 'roles', 'roleName', 'role', 'JlbInfo'])
  },
  mounted() {
    const that = this;
    that.searchFormWidth();
    window.onresize = () => {
      if (!that.timer) {
        // 使用节流机制，降低函数被触发的频率
        that.timer = true;
        setTimeout(function () {
          that.$forceUpdate();
          that.searchFormWidth();
          that.timer = false;
        }, 400);
      }
    };
    console.log('this.$store.state:', that.$store.state);
    console.log('this.roles:', that.roles);
    console.log('this.role:', that.role);

    /*    authenticationApi.checkAccountBalance().then(res => {
      this.count = res.data.data.money;
      this.roleTag = res.data.data.roleTag;
      localStorage.setItem("roleTag", this.roleTag);
      if (this.roleTag == "ExpertsFill") {

        if (Number(this.count) > 0) {
          this.arrears = false;
        } else {
          this.arrears = true;
        }

      }
    })*/
  },

  methods: {
    // 拼图成功的事件
    onSuccess(left) {
      // left为滑块移动距离，**调用后端接口把移动距离传给后端，后端进行校验是否验证成功**
    },
    // 拼图失败的事件
    onFail() { },
    // 刷新拼图的事件
    onRefresh() { },
    getSmsClick() {
      if (this.temp.newPwd == '' || this.temp.confirmPwd == '') {
        this.$message.error('请输入密码');
        return;
      }
      this.sendSmg();
    },
    //发送验证码
    sendSmg() {
      forgotApi.sendSmg(this.temp.mobile).then((res) => {
        this.$message.success('短信验证码发送成功，请注意查收');
        var num = 60;
        this.disabledSmsClick = true;
        if (!this.timer) {
          this.timer = setInterval(() => {
            if (num > 0) {
              num--;
              this.countdown = num + 's';
            } else {
              clearInterval(this.timer);
              this.timer = null;
              this.countdown = '重新获取验证码';
              this.disabledSmsClick = false;
            }
          }, 1000);
        }
      });
    },
    //获取关联门店数据
    merchantBinding() {
      getMerchantBindingInfo().then((res) => {
        if (res.data) {
          this.binding = res.data;
          this.affirm = true;
        }
        this.merchantVisible = true;
      });
    },
    //搜索门店
    search() {
      if (!this.binding.name) {
        this.$message.error('请输入门店账号或门店编号！');
        return;
      }
      searchMerchantInfo(this.binding.name).then((res) => {
        if (res.data) {
          this.binding = res.data;
        } else {
          this.$message.error('门店不存在！');
        }
      });
    },
    //确认绑定
    affirmBinding() {
      if (!this.binding.name) {
        this.$message.error('请输入门店账号或门店编号！');
        return;
      }
      if (!this.binding.merchantCode) {
        this.$message.warning('请确认门店信息是否正确！');
        this.search();
        return;
      }
      this.bindingLoading = true;
      merchantBinding(this.binding.merchantCode)
        .then((res) => {
          this.affirm = true;
          this.$message.success('绑定成功！');
          this.bindingLoading = false;
        })
        .catch((err) => {
          this.bindingLoading = false;
        });
    },
    bindingClose() {
      this.$refs.dataForm.resetFields();
      this.binding = {};
      this.merchantVisible = false;
    },

    // // 个人信息
    // personMy(){
    //   this.$router.push("/layout/personMe/personMy");
    // },
    // // 实名认证
    // realName(){
    //   window.localStorage.setItem("telName", this.name);
    //   this.$router.push({
    //     path: "/layout/personMe/realName",
    //     query: {
    //       name: name
    //     },
    //   });
    // },
    searchFormWidth() {
      this.screenUWidth = window.innerWidth;
    },
    toggleSideBar() {
      this.$store.dispatch('toggleSideBar');
    },
    logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.reload(); // In order to re-instantiate the vue-router object to avoid bugs
      });
    },
    handleUpdatePwd() {
      // this.dialogVisible = true;
      this.$nextTick(() => this.$refs['pwdFom'].clearValidate());
      this.getPhoneNum();
      this.dialogVisible = true;
      this.temp.newPwd = '';
      this.temp.confirmPwd = '';
      this.temp.smsCode = '';
      this.countdown = '获取验证码';
    },
    showNewPwd() {
      if (this.temp.newPwd == '') return;
      if (this.passwordNewType === 'password') {
        this.passwordNewType = '';
      } else {
        this.passwordNewType = 'password';
      }
    },
    showComPwd() {
      if (this.temp.confirmPwd == '') return;
      if (this.passwordComType === 'password') {
        this.passwordComType = '';
      } else {
        this.passwordComType = 'password';
      }
    },

    // 获取当前登录用户手机号
    getPhoneNum() {
      userApi.getPhone().then((res) => {
        console.log(res);
        if (res.data) {
          this.temp.mobile = res.data.phone;
        }
      });
    },
    updatePwd() {

      this.$refs.pwdFom.validate((valid) => {
        if (!valid) return;
        const tempData = Object.assign({}, this.temp); //copy obj
        console.log(tempData, '111111111111111');
        // userApi.updatePwd(tempData).then((res) => {
        //   this.dialogVisible = false;
        //   this.$message.success('更新密码成功');
        // });
        forgotApi.pswReset(tempData).then((res) => {
          this.dialogVisible = false;
          this.$message.success('更新密码成功');
        });
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.updatePwd {
  .el-input__inner {
    height: 100% !important;
  }
  .el-input {
    display: inline-block;
    height: 5vh;
    // width: 85%;
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #454545;
    margin-bottom: 20px;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
// .navbar .right-menu .avatar-container[data-v-797e31be] {
//   //margin-right: 10px; //影响页面出现横向滚动条 移除
// }

.merchantBindingBox {
}

.test {
  display: none !important;
}

.sizeSelect .svg-icon.size-icon {
  margin-bottom: 10px !important;
}

.navbar {
  height: 50px;
  line-height: 50px;
  border-radius: 0px !important;

  .hamburger-container {
    line-height: 58px;
    height: 50px;
    float: left;
    padding: 0 10px;
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      margin: 0 8px;
    }

    .screenfull {
      height: 20px;
    }

    .international {
      vertical-align: top;
    }

    .theme-switch {
      vertical-align: 15px;
    }

    .avatar-container {
      height: 50px;
      margin-right: 30px;

      .avatar-wrapper {
        cursor: pointer;
        margin-top: 5px;
        position: relative;

        .user-avatar {
          // width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
  .svg-container-end {
    position: absolute;
    color: #d7dee3;
    vertical-align: middle;
    display: inline-block;
    margin: 0 -30px;
    margin-top: 5px;
    font-size: 20px;
  }
  .dialog-tip {
    text-align: left;
    color: red;
    margin-left: 50px;
    font-size: 12px;
    height: 30px;
  }
  input[type='password']::-ms-reveal,
  input[type='password']::-ms-clear {
    display: none;
  }
}
</style>
