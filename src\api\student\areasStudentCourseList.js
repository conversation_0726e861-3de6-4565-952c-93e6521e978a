/**
 * 学员管理相关接口
 */
import request from '@/utils/request'

export default {
  //购买课程包
  buyCoursePackage(data) {
    return request({
      url: '/znyy/course/product/buy',
      method: 'POST',
      data
    })
  },

  //获取课程信息
  getProductCateGory(id) {
    return request({
      url: '/znyy/course/product/category?id=' + id,
      method: 'GET',
    })
  },
  //根据目录获取课程产品
  getProductVo(categoryId) {
    return request({
      url: '/znyy/course/product/by/categoryId?categoryId=' + categoryId,
      method: 'GET',
    })
  },
  //根据课程获取明细
  getCourseProductSpecVo(productId) {
    return request({
      url: '/znyy/course/product/sku?productId=' + productId,
      method: 'GET',
    })
  },

  // 分页查询
  studentList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/deliver/student/courseList/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },

  courseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/deliver/student/coursePageList/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  courseRecordList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/deliver/student/courseProgress/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  updateStatus(id, isEnable, data) {
    return request({
      url: '/znyy/deliver/student/updateSchedule/' + id + '/' + isEnable,
      method: 'PUT',
      params: data
    })

  },
  regainArchive(id, status, data) {
    return request({
      url: '/znyy/archive/regain/student/word/' + id + '/' + status,
      method: 'PUT',
      params: data
    })

  },
  getSchoolAccount() {
    return request({
      url: '/znyy/areas/student/get/account',
      method: 'GET',
    })
  },

  submitRecharge(data) {
    return request({
      url: '/znyy/areas/student/charge/course/save',
      method: 'POST',
      data
    })
  },
  //根据课程分类查子类
  checkNeedLineCollect(studentCode) {
    return request({
      url: '/znyy/areas/student/line/collect?studentCode=' + studentCode,
      method: 'GET'
    })
  },
  getBackRecharge(studentCode) {
    return request({
      url: '/znyy/areas/student/tui/course/' + studentCode,
      method: 'GET',
    })
  },

  submitBackRecharge(data) {
    return request({
      url: '/znyy/areas/student/tui/course/save',
      method: 'POST',
      data
    })
  },
  //根据课程分类查子类
  checkClassification(categoryCode) {
    return request({
      url: '/znyy/course/big/class/type/' + categoryCode,
      method: 'GET'
    })
  },
  //  //

  //    submitOpenCourse(data){
  //      return request({
  //        url: '/znyy/areas/course/batch/add/student/course',
  //        method: 'POST',
  //        data
  //      })
  //    },

  // 分页查询
  salesRecord(pageNum, pageSize, data) {
    return request({
      url: '/znyy/deliver/student/salesRecord/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
  // 开通
  submitOpenCourse(data) {
    return request({
      url: '/znyy/deliver/student/batchAddCourse',
      method: 'post',
      data
    })
  },
  exportAreasStudentRecord(data) {
    return request({
      url: '/znyy/areas/student/studentCourseProgressList/to/excel',
      method: 'GET',
      params: data,
      responseType: 'blob',
    })

  },
  findStudent(phoneNum) {
    return request({
      url: 'znyy/grammmar/consume/hours/select/student?loginName=' + phoneNum,
      method: 'GET',
    })
  },
  // 开通超级阅读
  openSuperRead(data) {
    return request({
      url: '/znyy/super-read/jf/open',
      method: 'POST',
      data
    })
  },
  // 获取超级阅读课程列表
  getSuperReadCourseList(data) {
    return request({
      url: '/znyy/super-read/student-open-course',
      method: 'GET',
      params: data
    })
  }
}

