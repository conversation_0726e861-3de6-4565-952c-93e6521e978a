<!--交付中心-机器人配置-->
<template>
  <div>
    <!-- 机器人配置 -->
    <div style="padding: 0 0 20px 0">
      <!-- 申请按钮 -->
      <div class="btn-add">
        <el-button size="small" type="primary" @click="clickAdd">添加机器人</el-button>
      </div>
      <!-- 暂无数据 -->
      <div class="nomore" v-if="tableData.length == 0">
        <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
        <div style="color: #999; margin-top: 20px">无数据</div>
      </div>
      <div class="" style="padding: 0 10px" v-if="tableData.length > 0">
        <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
          <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center">
            <template v-slot="{ row }">
              <div v-if="item.value == 'operate'">
                <el-button type="primary" size="mini" @click="getDetail(row.id)">编辑</el-button>
              </div>
              <div v-else-if="item.value == 'type'">
                <span>{{ getType(row[item.value]) }}</span>
              </div>
              <span v-else>{{ row[item.value] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-row type="flex" justify="center" align="middle" style="height: 60px">
          <!-- 3个变量：每页数量、页码数、总数  -->
          <!-- 2个事件：页码切换事件、每页数量切换事件-->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="querydata.pageNum"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="querydata.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </el-row>
      </div>

      <el-dialog :title="dialogForm.id ? '编辑机器人' : '添加机器人'" :visible.sync="dialogShow" width="30%" :before-close="handleClose">
        <el-form :model="dialogForm" label-width="130px" label-position="left">
          <el-form-item label=" 机器人名称:">
            <el-input v-model="dialogForm.name"></el-input>
          </el-form-item>
          <el-form-item label="应用id:">
            <el-input v-model="dialogForm.agentId"></el-input>
          </el-form-item>
          <el-form-item label="应用secret:">
            <el-input v-model="dialogForm.secret"></el-input>
          </el-form-item>
          <el-form-item label="事件接受token:">
            <el-input v-model="dialogForm.token"></el-input>
          </el-form-item>
          <el-form-item label="事件接受AESkey:">
            <el-input v-model="dialogForm.aeskey"></el-input>
          </el-form-item>
          <el-form-item label="机器人类型:">
            <el-select v-model="dialogForm.type" style="width: 100%" placeholder="">
              <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button plain @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import { getRobotDetail, getRobotList, saveRobot } from '@/api/robotSetting';
  export default {
    name: 'robotSetting',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        isAdmin: false,
        loading: false,
        querydata: {
          pageNum: 1,
          pageSize: 10 //页容量
        },
        // 表格头
        tableHeaderList: [
          {
            name: '机器人名称',
            value: 'name'
          },
          {
            name: '应用id',
            value: 'agentId'
          },
          {
            name: '应用secret',
            value: 'secret'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '事件接受token',
            value: 'token'
          },
          {
            name: '事件接受AESkey',
            value: 'aeskey'
          },
          {
            name: '本月已发消息条数',
            value: 'times'
          },
          {
            name: '机器人类型',
            value: 'type'
          }
        ],
        total: 0,
        //表格分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        tableData: [], //表格数据
        dialogShow: false,
        dialogForm: {
          name: '',
          agentId: '',
          secret: '',
          token: '',
          aeskey: '',
          type: ''
        },

        options: [
          {
            value: 1,
            label: '重要消息'
          },
          {
            value: 2,
            label: '普通消息'
          },
          {
            value: 3,
            label: '课程助手'
          },
          {
            value: 4,
            label: '教练端'
          },
          {
            value: 5,
            label: '课程评价通知'
          },
          {
            value: 6,
            label: '建群提醒通知'
          },
          {
            value: 7,
            label: '填写试课单提醒'
          },
          {
            value: 8,
            label: '今日统计数据通知'
          },
          {
            value: 9,
            label: '今日未发信息通知（组长收）'
          },
          {
            value: 10,
            label: '上课链接通知'
          },
          {
            value: 11,
            label: '今日未发信息通知（推荐人收）'
          }
        ]
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      this.initData();
    },
    methods: {
      getType(e) {
        let type = '';
        if (e) {
          let val = this.options.filter((i) => i.value == e);
          type = val.length > 0 ? val[0].label : '';
        } else {
          type = '';
        }
        return type;
      },
      //添加操作
      clickAdd() {
        this.dialogShow = true;
      },
      async getDetail(id) {
        const { data } = await getRobotDetail(id);
        // return console.log(data)
        for (const key in this.dialogForm) {
          this.dialogForm[key] = data[key];
        }
        this.dialogForm.id = data.id;
        this.dialogShow = true;
      },
      // 初始化列表
      async initData() {
        let that = this;
        let { data } = await getRobotList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
      },
      // 添加机器人
      onSubmit() {
        saveRobot(this.dialogForm).then((res) => {
          this.loading = true;
          this.$message.success('添加成功');
          this.initData();
          this.handleClose();
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 抽屉关闭
      handleClose() {
        this.dialogForm = {
          name: '',
          agentId: '',
          secret: '',
          token: '',
          aeskey: '',
          type: ''
        };
        this.dialogShow = false;
        this.loading = false;
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      // 分页
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      statusClass(status) {
        switch (status) {
          case 0:
            return '';
          case 1:
            return 'normal';
          case 2:
            return 'error';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn-add {
    // margin-top: 20px;
    padding: 20px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    cursor: unset !important;
  }

  // 自定义表格边框
  ::v-deep #out-table .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:first-child {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(8) {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep .el-dialog__title {
    font-size: 14px;
    font-weight: 600;
  }
  ::v-deep .el-dialog__headerbtn {
    background-color: #ccc;
    border-radius: 50%;
  }
</style>
