<!--交付中心-正式学员管理-学习课程表-->
<template>
  <div>
    <div v-if="row.needDetail">
      <!-- TODO 新增超级阅读类型不同字段 -->
      <div class="test" v-if="type == 1">
        <div class="row" v-if="row.moduleType !== 4">课程类型：{{ row.curriculumName }}</div>
        <div class="row" v-if="row.moduleType !== 4">教练：{{ row.teacher }}</div>
        <div class="row">日期：{{ row.dateTime }}</div>
        <div class="row">姓名：{{ row.studentName }}</div>
        <div class="row">年级：{{ row.gradeName }}</div>
        <div class="row">学员编号：{{ row.studentCode }}</div>
        <div class="row" v-if="row.moduleType !== 4">时间：{{ row.studyTime }}</div>
        <div class="row">
          实际时间：
          <span class="time">{{ row.actualStart }}</span>
          至
          <span class="time">{{ row.actualEnd }}</span>
        </div>
        <div class="row">试学学时：{{ row.studyHour }}</div>
        <div v-if="row.moduleType === 3">
          <div class="row">所学课程类型：{{ row.superReadCourseStatistics.courseName }}</div>
          <div class="row">所学课程名称进度：{{ mapArray(row.superReadCourseStatistics.courseList) }}</div>
          <div class="row">学习进度：{{ row.superReadCourseStatistics.learningProgress ? row.superReadCourseStatistics.learningProgress : '-' }}%</div>
          <div class="row">学习关卡（正确率)：{{ rateMapArray(row.superReadCourseStatistics.checkpointList) }}</div>
        </div>
        <div v-if="row.moduleType === 4">
          <div class="row">课程类型：{{ row.listeningStatisticsDto.courseName }}</div>
          <div class="row">课程名称:{{ listenName(row.listeningStatisticsDto.courseList) }}</div>
          <div class="row">学习进度：{{ listenProgress(row.listeningStatisticsDto.progressList) }}</div>
          <div class="row">
            <div>听力名称（正确率)：{{ listenArray(row.listeningStatisticsDto.listeningList) }}</div>
          </div>
        </div>
        <div v-else>
          <div class="row">词汇测试水平：{{ row.vocabularyLevel }}</div>
          <div class="row">首测词汇量：{{ row.expWords }}</div>
          <div class="row">识记词汇数量：{{ row.todayWords }}</div>
          <div class="row">遗忘数量：{{ row.forgetWords }}</div>
          <div class="row">记忆率：{{ row.wordsRate }} %</div>
          <div class="row">体验词库：{{ row.studyBooks }}</div>
          <div class="row">
            记忆情况：
            <el-input v-model="form.memoryTime" placeholder="请输入" size="small" clearable style="width: 150px"></el-input>
            分钟记住
            <el-input v-model="form.memoryNum" placeholder="请输入" size="small" clearable style="width: 150px"></el-input>
            个单词
          </div>
          <div class="flex">
            <div>复习时间:</div>
            <div style="margin-left: 100px">
              <div class="row" v-for="(item, index) in reviewTimeArr" :key="index">
                <el-date-picker
                  v-model="reviewTimeArr[index].time"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  placeholder="选择日期时间"
                  :picker-options="pickerOptions"
                  @change="(e) => change(index, e)"
                ></el-date-picker>
              </div>
            </div>
          </div>
        </div>
        <div class="row" style="margin: 10px 0 20px">
          体验后学习意愿：
          <el-select style="width: 150px" v-model="form.studyIntention" size="small" placeholder clearable>
            <el-option value="愿意" />
            <el-option value="不愿意" />
          </el-select>
        </div>
        <div class="row" style="margin: 10px 0">学员学习反馈情况:</div>
        <el-input type="textarea" :rows="4" v-model="form.feedback" placeholder="请输入" size="normal" clearable></el-input>
      </div>
      <div class="formal" v-if="type == 2">
        <div class="row" v-if="row.moduleType !== 4">课程类型：{{ row.curriculumName }}</div>
        <div class="row" v-if="row.moduleType !== 4">教练：{{ row.teacher }}</div>
        <div class="row">日期：{{ row.dateTime }}</div>
        <div class="row">姓名：{{ row.studentName }}</div>
        <div class="row">年级：{{ row.gradeName }}</div>
        <div class="row">学员编号：{{ row.studentCode }}</div>
        <div class="row" v-if="row.moduleType !== 4">时间：{{ row.studyTime }}</div>
        <div class="row">
          实际时间：
          <span class="time">{{ row.actualStart }}</span>
          至
          <span class="time">{{ row.actualEnd }}</span>
        </div>
        <div class="row" v-if="row.moduleType === 4">学习学时：{{ row.studyHour }}个小时</div>
        <div v-if="row.moduleType === 3">
          <div class="row">所学课程类型：{{ row.superReadCourseStatistics.courseName }}</div>
          <div class="row">所学课程名称：{{ mapArray(row.superReadCourseStatistics.courseList) }}</div>
          <div class="row">学习进度：{{ row.superReadCourseStatistics.learningProgress ? row.superReadCourseStatistics.learningProgress : '-' }}%</div>
          <div class="row">
            <!-- 复习知识点(正确率)：-- -->
            <div>学习关卡（正确率)：{{ rateMapArray(row.superReadCourseStatistics.checkpointList) }}</div>
          </div>
        </div>
        <div v-if="row.moduleType === 4">
          <div class="row">课程类型：{{ row.listeningStatisticsDto.courseName }}</div>
          <div class="row">课程名称:{{ listenName(row.listeningStatisticsDto.courseList) }}</div>
          <div class="row">学习进度：{{ listenProgress(row.listeningStatisticsDto.progressList) }}</div>
          <div class="row">
            <div>听力名称（正确率)：{{ listenArray(row.listeningStatisticsDto.listeningList) }}</div>
          </div>
        </div>
        <div v-else>
          <div class="row">已购鼎英语学时：{{ row.totalCourseHours }}</div>
          <div class="row">剩余鼎英语学时：{{ row.leaveCourseHours }}</div>
          <div class="row">所学词库：{{ row.studyBooks.replaceAll('、', '\n') }}</div>
          <div class="row">复习词汇：{{ row.reviewWords }}</div>
          <div class="row">复习遗忘词汇：{{ row.forgetWords }}</div>
          <div class="row">复习遗忘率：{{ row.forgetRate }}%</div>
          <div class="row">新学词汇：{{ row.newWords }}</div>
          <div class="row">新学遗忘词汇：{{ row.newForget }}</div>
          <div class="row">新学遗忘词汇率：{{ row.newForgetRate }}%</div>
          <div class="row">学习进度：{{ row.learnSchedule.replaceAll(',', '\n') }}</div>
          <div class="row">今日共识记词汇（复习遗忘词汇+学新词汇）：{{ row.todayWords }}个</div>
          <div class="row">
            学习效率：
            <el-input v-model="form.studyRate" style="width: 100px" placeholder="" size="small" clearable></el-input>
          </div>
        </div>
        <div class="row" style="margin: 10px 0 20px">教练评语:</div>
        <el-input v-model="form.feedback" type="textarea" :rows="4" placeholder="请输入" size="normal" clearable></el-input>
      </div>
    </div>
    <div v-else>
      <!-- 只有鼎英语needDetail为true，其他均为false -->
      <div v-if="!CurriculumCodeArr.includes(row.curriculumCode)">
        <div class="formal" v-if="row.curriculumName == '拼音法' || row.curriculumName == '拼音法（高年级）'">
          <div class="row">日期：{{ row.dateTime }}</div>
          <div class="row" v-if="row.curriculumName !== '拼音法' && row.curriculumName !== '拼音法（高年级）'">课程类型：{{ row.curriculumName }}</div>
          <div class="row">姓名：{{ row.studentName }}</div>
          <div class="row">年级：{{ row.gradeName }}</div>
          <div class="row">学员编号：{{ row.studentCode }}</div>
          <div class="row">时间：{{ row.studyTime }}</div>
          <div class="row">
            实际时间：
            <span class="time">{{ row.actualStart }}</span>
            至
            <span class="time">{{ row.actualEnd }}</span>
          </div>
          <div class="row">学习学时：{{ row.studyHour }}个小时</div>
          <div class="row" v-if="row.extendProperty.totalCourseHours">已购拼音法课时：{{ row.extendProperty.totalCourseHours }}个小时</div>
          <div class="row" v-if="row.extendProperty.totalCourseHours">剩余拼音法课时：{{ row.extendProperty.haveCourseHours }}个小时</div>
          <div class="row">所学课程类型：{{ row.curriculumName }}{{ row.extendProperty.courseTypeName }}</div>
          <div class="row">所学课程名称：{{ row.extendProperty.courseNames }}</div>
          <div class="row">学习元辅音个数：{{ row.extendProperty.consonantCounts }}</div>
          <div class="row">学习音节个数：{{ row.extendProperty.syllableCounts }}</div>
          <div class="row">学习单词个数：{{ row.extendProperty.wordCounts }}</div>

          <div class="row" style="margin: 10px 0 20px">教练评语:</div>
          <el-input v-model="form.feedback" type="textarea" :rows="4" placeholder="请输入" size="normal" clearable></el-input>
        </div>
        <div v-else>
          <div class="test" v-if="type == 1">
            <div class="row">课程类型：{{ row.curriculumName }}</div>

            <div class="row" style="margin: 10px 0">学员学习反馈情况:</div>
            <el-input type="textarea" :rows="4" v-model="form.feedback" placeholder="请输入" size="normal" clearable></el-input>
          </div>
          <div class="formal" v-if="type == 2">
            <div class="row">课程类型：{{ row.curriculumName }}</div>

            <div class="row" style="margin: 10px 0 20px">教练评语:</div>
            <el-input v-model="form.feedback" type="textarea" :rows="4" placeholder="请输入" size="normal" clearable></el-input>
          </div>
        </div>
      </div>
      <!-- 学考通学习反馈 -->
      <div v-else>
        <div class="row">日期：{{ row.dateTime }}</div>
        <div class="row">姓名：{{ row.studentName }}</div>
        <div class="row">年级：{{ row.gradeName }}</div>
        <div class="row">课程类型：{{ row.curriculumName }}</div>
        <div class="row">学员编号：{{ row.studentCode }}</div>
        <div class="row">上课用时：{{ row.studyHour }}</div>
        <div class="row" v-if="row.totalCourseHours">已购{{ row.curriculumName }}学时：{{ row.totalCourseHours }}</div>
        <div class="row" v-if="row.totalCourseHours">剩余{{ row.curriculumName }}学时：{{ row.leaveCourseHours }}</div>

        <el-form v-if="XKTCurriculumCodeArr.includes(row.curriculumCode)" :model="form" ref="formRef" label-width="120px" style="font-size: 19px !important">
          <div style="display: flex; justify-content: flex-start; line-height: 40px">
            <div class="row">
              <i style="color: rgba(245, 114, 114)">*</i>
              授课视频：
            </div>
            <el-cascader
              size="large"
              v-model="cascaderValue"
              :options="cascaderOptions"
              placeholder="请选择学段/课程名称/视频名称"
              clearable
              :props="{
                expandTrigger: 'hover',
                multiple: true,
                emitPath: false,
                checkStrictly: false, // 不允许父子节点关联
                value: 'value',
                label: 'label',
                children: 'children'
              }"
              style="width: 260px"
              @change="handleCascaderChange"
            ></el-cascader>
          </div>
        </el-form>

        <div class="row">教练评语:</div>
        <el-input type="textarea" :rows="4" v-model="form.feedback" placeholder="请输入" size="normal" clearable></el-input>
      </div>
    </div>
  </div>
</template>

<script>
  import dayjs from 'dayjs';
  import { addFeedback, addExperienceFeedback, addNewFeedback, addNewExperienceFeedback } from '@/api/studentClass/changeList';
  import { CurriculumCodeArr, XKTCurriculumCodeArr } from '@/utils/constants.js';
  export default {
    name: 'writeFeedbook',

    data() {
      return {
        form: {
          memoryNum: '',
          memoryTime: '',
          studyIntention: null,
          feedback: '',
          studyRate: '',
          selectedPath: [] // 存储级联选择的值'
        },
        cascaderOptions: [],
        lastVideoId: null,
        reviewTimeArr: [{ time: '' }, { time: '' }, { time: '' }],
        pickerOptions: {
          disabledDate(time) {
            // 禁止选择今天之前的时间
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
        selectedParent: null, // 用于记录当前选中的二级节点
        selectedRoot: null, // 用于记录当前选中的一级节点
        maxSelection: 10, // 最大选择项数
        cascaderValue: [],
        previousSelectedPath: [], // 用于存储上一次选择的路径
        CurriculumCodeArr: CurriculumCodeArr,
        XKTCurriculumCodeArr: XKTCurriculumCodeArr
      };
    },
    props: {
      row: {
        type: Object,
        default: function () {
          return {};
        }
      },
      rowVideo: {
        type: Array,
        default: function () {
          return [];
        }
      },
      type: {
        type: String,
        default: function () {
          return '';
        }
      },
      id: {
        type: String,
        default: function () {
          return '';
        }
      }
    },
    watch: {
      type: {
        handler(newName, oldName) {
          if (newName == 1) {
            this.form = {
              feedback: '',
              memoryNum: '',
              memoryTime: '',
              studyIntention: null
            };
          } else {
            this.form = {
              feedback: '',
              studyRate: ''
            };
          }
        },
        deep: true
      }
    },

    created() {
      this.processCascaderData(this.rowVideo);
    },
    mounted() {},
    methods: {
      processCascaderData(data) {
        console.log(data, 'data999');
        const cascaderOptions = data.map((grade) => ({
          value: grade.grade,
          label: grade.gradeName,
          children: grade.courseList.map((course) => ({
            value: course.courseId,
            label: course.courseName,
            children: course.videoList.map((video) => ({
              value: video.videoId,
              label: video.videoName
            }))
          }))
        }));
        this.cascaderOptions = cascaderOptions;
        console.log(JSON.stringify(this.cascaderOptions), 'this.cascaderOptions');
      },
      rateMapArray(arr) {
        if (arr && arr.length > 0) {
          let newArr = [];
          newArr = arr.map((i) => {
            let str = '';
            let accuracyRate = i.accuracyRate ? i.accuracyRate + '%' : '-';
            let checkpointName = i.checkpointName;
            str = `${checkpointName}(${accuracyRate})`;
            //arr.push(str)
            return str;
          });
          return newArr.join(',');
          //return arr.map((i) => i.checkpointName + '（' + i.accuracyRate + '%' + '）').join(',');
        } else {
          return '-';
        }
      },
      mapArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.courseName + '（' + i.checkpointNum + '）').join(',');
        } else {
          return '-';
        }
      },
      change(i, e) {
        if (!e) {
          return;
        }
        let now = new Date().getTime();
        let temp = new Date(e).getTime();
        if (now > temp) {
          this.$message.error('请选择目前之后的时间');
          this.$nextTick(() => {
            this.reviewTimeArr[i].time = '';
          });
          return;
        }
        let a = this.getSameDate(i);
        if (a) {
          this.$message.error('请勿选择同一天');
          this.$nextTick(() => {
            this.reviewTimeArr[i].time = '';
          });
          return;
        }
      },
      getSameDate(index) {
        for (let i = 0; i < this.reviewTimeArr.length; i++) {
          if (i == index) {
            continue;
          }
          if (dayjs(this.reviewTimeArr[i].time).format('YYYY-MM-DD') == dayjs(this.reviewTimeArr[index].time).format('YYYY-MM-DD')) {
            return true;
          }
        }
        return false;
      },

      //听力数据处理
      listenProgress(progressList) {
        return progressList.map((item) => `${parseFloat(item.learningProgress)}%`).join(' , ');
      },
      listenName(progressList) {
        return progressList.map((item) => item.courseName).join(' , ');
      },
      listenArray(arr) {
        if (arr && arr.length > 0) {
          return arr.map((i) => i.listeningName + '（' + i.accuracyRate + '%）').join(' , ');
        } else {
          return '-';
        }
      },
      // 正式课
      async submit() {
        console.log(this.row.moduleType);
        if (this.row.moduleType != 3 && this.row.moduleType != 4) {
          if (!this.form.studyRate && this.row.needDetail) {
            return this.$message.error('请输入学习效率');
          }
        }
        console.log("11111111111111111111111🚀 ~ submit ~ this.row.curriculumName == 'XKT':", this.row.curriculumCode);
        if (this.XKTCurriculumCodeArr.includes(this.row.curriculumCode)) {
          console.log("🚀 ~ submit ~ this.row.curriculumName == 'XKT':", this.row.curriculumCode == 'XKT');
          console.log("🚀 ~ submit ~ this.row.curriculumName == 'XKT':", this.form.selectedPath);
          // this.lastVideoId = this.form.selectedPath[this.form.selectedPath?.length - 1];
          let videoIDTemp = this.form.selectedPath.join(',');
          console.log('🚀 ~ submit ~ videoIDTemp:', videoIDTemp);
          this.lastVideoId = videoIDTemp;
          if (!this.lastVideoId) return this.$message.error('请选择视频');
        }

        if (!this.form.feedback) {
          return this.$message.error('请输入教练评语');
        }

        let obj = {};
        if (this.row.needDetail) {
          obj = {
            id: this.row.id,
            actualStart: this.row.actualStart,
            actualEnd: this.row.actualEnd,
            studyRate: this.form.studyRate,
            type: 1,
            feedBack: this.form.feedback,
            videoId: this.lastVideoId
          };
          console.log(obj, 'obj11111111111111111111111111');
          await addFeedback(obj);
        } else {
          obj = {
            id: this.row.id,
            actualStart: this.row.actualStart,
            actualEnd: this.row.actualEnd,
            type: 1,
            feedBack: this.form.feedback,
            videoId: this.lastVideoId
          };
          console.log(obj, 'obj11111111111111111111111111');
          await addNewFeedback(obj);
        }
        // this.$emit("feedbackSubmitted", { ...this.row, studyStatus: 2 }); // 假设 studyStatus 更新为 2
        this.$emit('close');
      },
      // 试课
      async submit1() {
        let that = this;
        if (this.row.moduleType != 3 && this.row.moduleType != 4) {
          if ((this.form.memoryTime == '' || this.form.memoryNum == '') && this.row.needDetail) {
            return this.$message.error('记忆特点字段未填写');
          }
        }
        if (!this.form.studyIntention && this.row.needDetail) {
          return this.$message.error('体验后学习意愿未填写');
        }
        if (this.XKTCurriculumCodeArr.includes(this.row.curriculumCode)) {
          // this.lastVideoId = this.form.selectedPath[this.form.selectedPath?.length - 1];
          // console.log('最后一个视频 ID:', this.lastVideoId);
          let videoIDTemp = this.form.selectedPath.join(',');
          console.log('🚀 ~ submit ~ videoIDTemp:', videoIDTemp);
          this.lastVideoId = videoIDTemp;
          if (!this.lastVideoId) return this.$message.error('请选择视频');
        }

        if (!this.form.feedback) {
          return this.$message.error('请输入学员学习反馈情况');
        }
        // 增加授课视频的提交逻辑
        let arr = this.reviewTimeArr.filter((e) => e.time).map((item) => item.time);
        let obj = {};
        if (that.row.needDetail) {
          let memoryTime = '';
          let memoryNum = '';
          if (that.row.moduleType == 3 || that.row.moduleType == 2 || that.row.moduleType == 4) {
            memoryTime = 0;
            memoryNum = 0;
          } else {
            memoryTime = that.form.memoryNum;
            memoryNum = that.form.memoryTime;
          }
          obj = {
            reviewDateList: arr,
            feedback: encodeURI(encodeURI(this.form.feedback)),
            // memoryNum: this.form.memoryNum,
            // memoryTime: this.form.memoryTime,
            memoryTime,
            memoryNum,
            studyId: this.row.id,
            studyIntention: this.form.studyIntention,
            videoId: this.lastVideoId
          };
          await addExperienceFeedback(obj);
        } else {
          obj = {
            feedback: encodeURI(encodeURI(this.form.feedback)),
            studyId: this.row.id,
            videoId: this.lastVideoId
          };
          await addNewExperienceFeedback(obj);
        }
        // this.$emit("feedbackSubmitted", { ...this.row, studyStatus: 2 }); // 假设 studyStatus 更新为 2
        this.$emit('close');
      },
      handleCascaderChange(selected) {
        if (selected.length === 0) {
          this.selectedParent = null; // 清空选择
          this.selectedRoot = null;
          return;
        }
        if (this.row.experience) {
          this.maxSelection = 1;
        }
        console.log('selectedPath1 Path:', this.form.selectedPath);
        // 限制最多选择 maxSelection 项
        if (selected.length > this.maxSelection) {
          this.$message.error(`最多只能选择 ${this.maxSelection} 项`);
          // 阻止用户选择超过限制的项
          // this.form.selectedPath = this.form.selectedPath.slice(0, this.maxSelection);
          this.cascaderValue = [...this.previousSelectedPath];
          return;
        }
        this.previousSelectedPath = [...selected]; // 记录上一次选择的路径
        this.form.selectedPath = [...selected];
        console.log('selectedPath Path:', this.form.selectedPath);

        // 获取当前选中路径的完整信息
        const selectedNode = this.findPath(this.cascaderOptions, selected[selected.length - 1]);
        console.log(selected, 'Selected Node1111:', selectedNode);
        const selectedNodeFirst = this.findPath(this.cascaderOptions, selected[0]);
        console.log(selected, 'Selected Node:', selectedNodeFirst);
        this.selectedParent = selectedNodeFirst.parent.value; // 二级节点
        this.selectedRoot = selectedNodeFirst.parent.parent.value; // 一级节点
        if (selectedNode && selectedNode.parent && selectedNode.parent.parent) {
          const currentParent = selectedNode.parent.value; // 二级节点
          const currentRoot = selectedNode.parent.parent.value; // 一级节点

          console.log('Current Parent (二级):', currentParent);
          console.log('Current Root (一级):', currentRoot);

          // 如果已经选择了一个一级和二级节点，限制只能选择同一个一级和二级节点下的三级
          if ((this.selectedParent && this.selectedParent !== currentParent) || (this.selectedRoot && this.selectedRoot !== currentRoot)) {
            this.$message.error('只能选择同一个一级节点下的同一个二级节点的选项');
            console.log('移除不符合条件的选项:', this.form.selectedPath);
            // 移除不符合条件的选项
            this.form.selectedPath = this.form.selectedPath.filter((item) => {
              const node = this.findPath(item, this.cascaderOptions);
              return node && node.parent && node.parent.value === this.selectedParent && node.parent.parent && node.parent.parent.value === this.selectedRoot;
            });
          } else {
            this.selectedParent = currentParent; // 设置当前二级节点
            this.selectedRoot = currentRoot; // 设置当前一级节点
          }
        } else {
          console.warn('无法获取一级或二级节点，请检查数据结构');
        }
        // 限制最多选择 maxSelection 项
        console.log('this.maxSelection', this.row);
      },
      findPath(data, targetValue) {
        let result = null;

        // 递归函数，带 parent、grandparent 参数
        function traverse(node, parent = null, grandparent = null) {
          if (node.value === targetValue) {
            result = {
              value: node.value,
              label: node.label,
              parent: parent
                ? {
                    value: parent.value,
                    label: parent.label,
                    parent: grandparent
                      ? {
                          value: grandparent.value,
                          label: grandparent.label
                        }
                      : null
                  }
                : null
            };
            return true;
          }

          if (node.children && node.children.length) {
            for (let i = 0; i < node.children.length; i++) {
              if (traverse(node.children[i], node, parent)) {
                return true;
              }
            }
          }

          return false;
        }

        for (let i = 0; i < data.length; i++) {
          if (traverse(data[i])) break;
        }

        return result;
      },
      getLastValues(arr) {
        return arr.map((path) => path.join(','));
      }
    }
  };
</script>

<style lang="scss" scoped>
  .row {
    //height: 40px;
    line-height: 40px;
    font-size: 14px;
  }

  .flex {
    display: flex;
  }

  .time {
    padding: 10px;
    background-color: #f5f5f6;
    border: 1px solid #ededee;
    margin: 0 10px;
    border-radius: 5px;
  }
  .time {
    padding: 10px;
    background-color: #f5f5f6;
    border: 1px solid #ededee;
    margin: 0 10px;
    border-radius: 5px;
  }
</style>
