<!--交付中心-入库管理-人员入库-学管师的页面-->
<template>
  <el-card v-if="xueisTo">
    <el-form label-width="80px" class="frame" ref="xuelist" :model="xuelist">
      <!-- 1 -->
      <el-row type="flex" justify="space-around">
        <el-col :span="6">
          <el-form-item label="姓名:" prop="realName">
            <el-input v-model="xuelist.realName" placeholder="请选择" clearable size="mini"
              style="width: 250px"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="xuelist.sex" clearable size="mini" placeholder="请选择">
              <el-option label="男" value="1"></el-option>
              <el-option label="女" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="联系方式:" prop="mobile">
            <el-input v-model="xuelist.mobile" clearable placeholder="请选择" size="mini" style="width: 250px"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end">
        <el-col style="margin-left:71vw">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="chaBtn">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script>
import { xueguanListApi } from '@/api/rukuManage/xueTeacher'
export default {
  name: 'xueguanshiTop',
  props: {
    xueisTo: {
      // 定义接收的类型 还可以定义多种类型 [String, Undefined, Number]
      // 如果required为true,尽量type允许undefined类型，因为传递过来的参数是异步的。或者设置默认值。
      type: Boolean,
      // 定义是否必须传
      required: true
    }
  },
  data() {
    return {
      xuelist: {
        realName: "",
        sex: "",
        mobile: "",
        pageNum: 1,
        pageSize: 10
      },
      xuechaList: {
        totalItems: '',
        data: ''
      },
    };
  },
  methods: {
    async chaBtn() {
      // let {data} = await xueguanListApi(this.xuelist)
      // this.xuechaList.data = data.data
      // this.xuechaList.totalItems = Number(data.totalItems
      this.$emit('Xuechalist', this.xuelist)
      // this.$emit('Xuechalist',this.xuechaList)
    },
    //重置
    rest() {
      this.$refs.xuelist.resetFields();
      this.chaBtn();
    },
  }
};
</script>

<style>
</style>
