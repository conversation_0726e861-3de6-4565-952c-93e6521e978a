import request from '@/utils/request';

// 课程表列表
export const getTimetable = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTimetable',
    method: 'GET',
    params: data
  });
};
// 课程表学管师/教练 规定时间段内获取总学时
export const getTotalHours = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTotalHours',
    method: 'GET',
    params: data
  });
};

// 课程表导出
export const studentStudyExport = (data) => {
  return request({
    url: '/deliver/web/learnManager/studentStudyExportAsync',
    method: 'GET',
    params: data
  });
};

// 课程表导出
export const studentReviewExport = (data) => {
  return request({
    url: '/deliver/web/learnManager/studentReviewExport',
    method: 'GET',
    responseType: 'blob',
    params: data
  });
};

// 获取学员课程表内容
export const getAdjustInfo = (id) => {
  return request({
    url: '/deliver/web/learnManager/getAdjustInfo',
    method: 'GET',
    params: { id: id }
  });
};
// 调课接口
export const adjustPlanStudy = (data) => {
  return request({
    url: '/deliver/web/learnManager/adjustPlanStudy',
    method: 'post',
    data: data
  });
};
// 新增学员课程表
export const addStudentRegister = (data) => {
  return request({
    url: '/deliver/web/common/addStudentRegister',
    method: 'POST',
    data: data
  });
};

// 回显新增试课记录表
export const getTableInfo = (data) => {
  return request({
    url: '/deliver/web/common/getTableInfo',
    method: 'POST',
    params: data
  });
};

// 新增试课记录表
export const addTrialClassRecord = (data) => {
  return request({
    url: '/deliver/web/common/addTrialClassRecord',
    method: 'POST',
    data: data
  });
};
//正式学员交付清单列表导出
export const schoolStudentStudyStatisticsExport = (data) => {
  return request({
    url: '/deliver/web/learnManager/schoolStudentStudyStatisticsExport',
    method: 'GET',
    responseType: 'blob',
    params: data
  });
};
//查询门店列表
export const selectAllSchool = () => {
  return request({
    url: '/deliver/web/learnManager/selectAllSchool',
    method: 'GET'
  });
};
//正式学员交付清单列表
export const selSchoolStudentStudyStatistics = (data) => {
  return request({
    url: '/deliver/web/learnManager/selSchoolStudentStudyStatistics',
    method: 'GET',
    params: data
  });
};

// 待上课信息对接表
export const selStudentContactInfoPage = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/selStudentContactInfoPage',
    method: 'GET',
    params: data
  });
};
export const selStudentReviewTimeInfoList = (data) => {
  return request({
    url: '/deliver/web/student/reviewTime/info/selDeliverStudentReviewTimeInfoPage',
    method: 'GET',
    params: data
  });
};

// 课程表列表
export const getTimetableReview = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTimetableReview',
    method: 'GET',
    params: data
  });
};

// 课程表-获取请假申请详情
export const getVacationInfo = (data) => {
  return request({
    url: '/deliver/web/learnManager/getVacationInfo',
    method: 'GET',
    params: data
  });
};

// 数据查看-根据年月获取课程日期列表
export const getStudyDateList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getStudyDateList',
    method: 'GET',
    params: data
  });
};

// 获取学管师列表
export const getLearnTubeList = (data) => {
  return request({
    url: '/deliver/web/sysUser/getLearnTubeList',
    method: 'GET',
    params: data
  });
};

// 预警相关-学员分配学管师
export const allotLearnTube = (data) => {
  return request({
    url: '/deliver/web/warning/allotLearnTube',
    method: 'post',
    params: data
  });
};

// 获取指派中心
export const belongDeliverAndAllDeliver = (data) => {
  return request({
    url: '/deliver/web/learnManager/belongDeliverAndAllDeliver?id=' + data,
    method: 'get'
  });
};

// 修改学员列表状态
export const updateStudentIsEnable = (id, isEnable) => {
  return request({
    url: '/deliver/web/learnManager/updateStudentIsEnable?id=' + id + '&isEnable=' + isEnable,
    method: 'put'
  });
};

// 重新指派
export const submitAssign = (code, id) => {
  return request({
    url: '/deliver/web/learnManager/assign?deliverMerchantCode=' + code + '&id=' + id,
    method: 'put'
  });
};

export const agentDeliver = (data) => {
  return request({
    url: '/deliver/web/finance/agent/deliver',
    method: 'put',
    params: data
  });
};

export const getDeductWage = (data) => {
  return request({
    url: '/deliver/web/learnManager/deductWage',
    method: 'put',
    params: data
  });
};

export const getCourseModulelist = (data) => {
  return request({
    url: '/deliver/web/learnManager/selPlanCourseModuleList',
    method: 'get',
    params: data
  });
};

// 设置课程模块
export const getCourseModule = (data) => {
  return request({
    url: '/deliver/web/learnManager/setCourseModule',
    // url: '/deliver/web/learnManager/selplanCourseModulelist',
    method: 'put',
    params: data
  });
};

// 获取表头设置
export const getTableTitleSet = (data) => {
  return request({
    url: '/deliver/web/merchant/selDeliverTableTitleSet',
    method: 'get',
    params: data
  });
};

// 设置表头
export const setTableList = (data) => {
  return request({
    url: '/deliver/web/merchant/deliverTableTitleSet',
    method: 'post',
    data: data
  });
};
// 查询交付中心课程类型
export const bvstatusList = (data) => {
  return request({
    url: '/znyy/curriculum/all',
    method: 'GET',
    params: data
  });
};
// 查询交付中心1v1课程类型
export const bvstatusListOne = (data) => {
  return request({
    url: '/znyy/curriculum/allNew',
    method: 'GET',
    params: {
      ...data,
      curriculumType: 0
    }
  });
};
