<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="dialogVisible"
      width="35%"
      :modal-append-to-body="false"
      :show-close="false"
      title="解锁验证"
      align="center"
      style="font-weight: 700"
    >
      <div class="dialog-Show-title">为保障数据安全，解锁查看需要进行验证，请确认是否向您的登录账号发送手机验证码？</div>
      <el-form :rules="rules" ref="dataForm" :model="temp">
        <el-form-item prop="smsCode" style="width: 50%; text-align: center">
          <div style="display: flex; justify-content: center">
            <el-input placeholder="请输入验证码" v-model="temp.smsCode" style="width: 85%"></el-input>
            <el-button @click.stop="getSmsClick()" :disabled="disabledSmsClick" type="primary">{{ count }}</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="phoneUnclock" size="medium">解锁</el-button>
        <el-button @click="closeDialog" size="medium" class="closeButton">取消</el-button>
      </div>
    </el-dialog>
    <Verify ref="changeVerify" :captcha-type="'blockPuzzle'" :img-size="{ width: '375px', height: '200px' }" @success="sendSmg" style="z-index: 10000 !important" />
  </div>
</template>
<script>
  import auth from '@/api/auth';
  import Verify from '@/components/verifition/Verify';
  import forgotApi from '@/api/forgot';
  import userApi from '@/api/user';
  export default {
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
      phoneShowStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      Verify
    },

    data() {
      return {
        temp: {
          mobile: '',
          smsCode: '',
          source: 'admin'
        },
        rules: {
          smsCode: [
            { required: true, message: '请输入验证码', trigger: 'blur' },
            { message: '验证码不得小于6位', trigger: 'blur' }
          ]
        },
        count: '获取验证码',
        timer: null,
        disabledSmsClick: false
      };
    },
    created() {
      this.getPhoneNum();
    },
    methods: {
      // 获取当前登录用户手机号
      getPhoneNum() {
        userApi.getPhone().then((res) => {
          // this.temp.mobile = res.data.phone;
          this.temp.mobile = '15256228190';
        });
      },

      //发送验证码
      getSmsClick() {
        this.$refs.changeVerify.show();
      },
      sendSmg(params) {
        forgotApi.sendSmg(this.temp.mobile, params.captchaVerification).then((res) => {
          console.log(res);
          this.$message.success('短信验证码发送成功，请注意查收');
          var num = 60;
          this.disabledSmsClick = true;
          if (!this.timer) {
            this.timer = setInterval(() => {
              if (num > 0) {
                num--;
                this.countdown = num + 's';
              } else {
                clearInterval(this.timer);
                this.timer = null;
                this.countdown = '重新获取验证码';
                this.disabledSmsClick = false;
              }
            }, 1000);
          }
        });
      },
      phoneUnclock() {
        this.$emit('update:dialogVisible', false);
        this.$emit('update:phoneShowStatus', false);
        this.temp = {
          smsCode: '',
          mobile: ''
        };
        this.$refs.dataForm.resetFields();
        this.timer = null;
        this.disabledSmsClick = false;
        this.countdown = '获取验证码';
      },
      closeDialog() {
        console.log('关闭');
        this.$emit('update:dialogVisible', false);
        this.temp = {
          smsCode: '',
          mobile: ''
        };
        this.$refs.dataForm.resetFields();
      }
    }
  };
</script>
<style lang="scss">
  .dialog-Show-title {
    color: #000;
    margin-bottom: 20px;
  }
  .dialog-footer {
    text-align: center;
    .closeButton {
      margin-left: 40px;
    }
  }
</style>
