/**
 * 交付中心关联门店相关接口
 */
import request from '@/utils/request'

// 获取交付中心关联门店信息
export const getMerchantBindingInfo = () => {
  return request({
    url: '/deliver/web/merchant/getMerchantBindingInfo',
    method: 'GET'
  })
}

// 搜索门店信息
export const searchMerchantInfo = (name) => {
  return request({
    url: '/deliver/web/merchant/searchMerchantInfo',
    method: 'GET',
    params: { name: name }
  })
}

// 交付中心关联门店
export const merchantBinding = (merchantCode) => {
  return request({
    url: '/deliver/web/merchant/merchantBinding',
    method: 'POST',
    params: { merchantCode: merchantCode }
  })
}
