import request from '@/utils/request';

// 教练工资课程列表
export const getCourseclassList = () => {
  return request({
    url: '/znyy/deliver/wage/courseClassificationWage',
    method: 'GET'
  });
};

export const getCoursList = () => {
  return request({
    url: '/znyy/deliver/wage/getCourseModuleList',
    method: 'GET'
  });
};

export const updateCourseClass = (params) => {
  return request({
    url: '/znyy/deliver/wage/updateCourseClassificationWage',
    method: 'GET',
    params
  });
};

export const addCourseClass = (data) => {
  return request({
    url: '/znyy/deliver/wage/addCourseClassificationWage',
    method: 'POST',
    data
  });
};

export const getDeliverCodes = () => {
  return request({
    url: '/deliver/app/teacher/getIfExistOpenDeliverCodes',
    method: 'GET'
  });
};
// /znyy/deliver/wage/getCurriculumExpCourse
// 教练试课工资配置列表
export const getCurriculumExpCourse = (data) => {
  return request({
    url: '/znyy/deliver/wage/getCurriculumExpCourse',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 0
    }
  });
};

export const updateWageReward = (data) => {
  return request({
    url: '/znyy/deliver/wage/updateExpWageReward',
    method: 'POST',
    data
  });
};
// /znyy/deliver/wage/addOrUpdateCurriculumExpCourseConfig
export const addOrUpdateCurriculumExpCourseConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateCurriculumExpCourseConfig',
    method: 'POST',
    data
  });
};
//新增编辑试课奖励
export const saveOrUpdateExpWageReward = (data) => {
  return request({
    url: '/znyy/deliver/wage/saveOrUpdateExpWageReward',
    method: 'POST',
    params: {
      oneToManyType: 0
    },
    data: {
      ...data
    }
  });
};

// /deliver/wage/findExpWageRewardPage

// 获取奖励列表
export const findExpWageRewardPage = (data) => {
  return request({
    url: 'znyy/deliver/wage/findExpWageRewardPage',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 0
    }
  });
};

//删除奖励列表
export const deleteExpWageReward = (data) => {
  return request({
    url: '/znyy/deliver/wage/deleteExpWageReward?id=' + data,
    method: 'POST'
  });
};
// 教练正式课工资列表 - 鼎英语
export const getTeacherWageList = (data) => {
  return request({
    url: '/znyy/deliver/wage/findTeacherWageConfigPage',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 0
    }
  });
};
// 教练等级
export const getTeacherLevel = () => {
  return request({
    url: '/znyy/bSysConfig/getTeacherLevelConfig ',
    method: 'GET'
  });
};

// 教练正式课工资新增
export const getAddOrUpdateTeacher = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateTeacherWageConfig',
    method: 'POST',
    data
  });
};
//教练正式课工资新增(除鼎英语)
export const addOrUpdateCurriculumTeacherWageConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateCurriculumTeacherWageConfig',
    method: 'POST',
    data
  });
};
//教练正式课工资列表查询(除鼎英语)
export const getCurriculumTeacherWageConfig = (data) => {
  return request({
    url: '/znyy/deliver/wage/getCurriculumTeacherWageConfig',
    method: 'GET',
    params: {
      ...data,
      oneToManyType: 0
    }
  });
};
// /znyy/deliver/wage/deleteCurriculumExpCourse
// 教练正式课工资除鼎英语删除
export const getDeleteNoTeacher = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteCurriculumExpCourse?id=' + id,
    method: 'POST'
  });
};
// 教练正式课工资删除
export const getDeleteTeacher = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteTeacherWageConfig',
    method: 'DELETE',
    params: id
  });
};
//教练正式课工资删除(除鼎英语) /znyy/deliver/wage/deleteCurriculumTeacherWageConfig
export const deleteCurriculumTeacherWageConfig = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteCurriculumTeacherWageConfig?id=' + id,
    method: 'POST'
  });
};
// 教练复习工资列表
export const getTeacherReviewWageList = () => {
  return request({
    url: '/znyy/deliver/wage/getTeacherReviewWageConfig',
    method: 'GET'
  });
};

// 教练复习工资新增修改
export const addOrUpdateTeacherReviewWage = (data) => {
  return request({
    url: '/znyy/deliver/wage/addOrUpdateTeacherReviewWageConfig',
    method: 'POST',
    data
  });
};

// 教练复习工资删除
export const deleteTeacherReviewWage = (id) => {
  return request({
    url: '/znyy/deliver/wage/deleteTeacherReviewWageConfig',
    method: 'DELETE',
    params: id
  });
};

// 获取语法模块
export const getCourseGrammar = () => {
  return request({
    url: 'znyy/bvstatus/courseGrammar',
    method: 'GET'
  });
};
