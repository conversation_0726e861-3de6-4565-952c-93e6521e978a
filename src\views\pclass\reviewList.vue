<!--交付中心-正式学员管理-复习课程表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px" ref="searchNum" :model="searchNum">
        <!-- 1 -->
        <el-row style="display: flex; flex-wrap: wrap">
          <el-col :span="6">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请输入" size="small"
                style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" size="small"
                style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="课程内容:" prop="courseType">
              <el-select v-model="searchNum.courseType" clearable placeholder="请选择" style="width: 10vw">
                <el-option label="鼎英语" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="复习状态:" prop="reviewStatus">
              <el-select v-model="searchNum.reviewStatus" clearable placeholder="请选择" style="width: 10vw">
                <el-option label="未复习" value="0"></el-option>
                <el-option label="已复习" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="教练老师:" prop="teacherId">
              <el-select v-el-select-loadmore="handleLoadmore" :loading="loadingShip" :filter-method="filterValue"
                clearable v-model="searchNum.teacherId" filterable remote reserve-keyword placeholder="请选择"
                @input="changeMessage" @blur="clearSearchRecord" @change="changeTeacher">
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              <!-- <el-input v-model="searchNum.teacherName" clearable placeholder="请输入" size="small"
                style="width: 10vw"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col v-if="isAdmin" :span="6">
            <el-form-item label="交付编号:" prop="deliverMerchant">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small"
                style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="isAdmin" :span="6">
            <el-form-item label="交付名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"
                style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="时间筛选:" prop="timeAll">
              <el-date-picker v-model="timeAll" style="width: 16vw" size="small" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 3 -->
        <el-row style="display: flex; flex-direction: row-reverse">
          <el-button type="warning" style="margin-left: 10px" icon="el-icon-download" size="mini" @click="exportExcel"
            :loading="exportLoading">
            导出
          </el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="rest()">重置</el-button>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:10px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column v-for="(item, index) in tableHeaderList" :width="item.value=='operate'?200:''"
        :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="danger" v-if="row.status == 2" size="mini" @click="leaveViewFn(row)">请假查看</el-button>
            <el-button type="success" size="mini" @click="SumLookerFn(row)">数据查看</el-button>
            <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteReview(row)">取消</el-button>
          </div>

          <div v-else-if="item.value == 'courseType'">
            <span>{{ row.courseType == 1 ? '鼎英语' : '-' }}</span>
          </div>

          <div v-else-if="item.value == 'teachingType'">
            <span v-if="row.teachingType == 1 || row.teachingType == 2 || row.teachingType == 3">{{ row.teachingType
              != 1 ? (row.teachingType == 2 ? '线下' : '远程和线下') : '远程' }}</span>
            <span v-else>暂无</span>
          </div>

          <div v-else-if="item.value == 'reviewTime'">
            <div>{{ row.date }}</div>
            <div>{{ row.format }} {{ row.time }}<span v-if="row.endTime">-</span>{{ row.endTime }}
            </div>
          </div>

          <div v-else-if="item.value == 'studyStatus'">
            <span v-if="row.studyStatus == 0">未复习</span>
            <span v-if="row.studyStatus == 2">已复习</span>
          </div>

          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ status(row) }}</span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表格 -->
    <!-- <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column prop="deliverMerchant" min-width="110" label="交付中心编号" header-align="center" />
      <el-table-column prop="deliverName" min-width="130" label="交付中心名称" header-align="center" />
      <el-table-column prop="address" label="操作" header-align="center" width="300">
        <template v-slot="{ row }">
          <el-button type="danger" v-if="row.status == 2" size="mini" @click="leaveViewFn(row)">请假查看</el-button>
          <el-button type="success" size="mini" @click="SumLookerFn(row)">数据查看</el-button>
          <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteReview(row)">取消</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="姓名" width="80" header-align="center"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150" header-align="center"></el-table-column>
      <el-table-column prop="courseType" :formatter="courseType" label="课程分类" width="120"
        header-align="center"></el-table-column>
      <el-table-column prop="merchantName" label="学员来源" header-align="center" width="220"></el-table-column>
      <el-table-column prop="teachingType" :formatter="teachingType" label="授课方式" header-align="center"></el-table-column>
      <el-table-column prop="date,format,time" label="复习时间" width="180" header-align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.date }}</div>
          <div>{{ scope.row.format }} {{ scope.row.time }}<span v-if="scope.row.endTime">-</span>{{ scope.row.endTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="teacher" label="教练老师" header-align="center"></el-table-column>
      <el-table-column prop="studyStatus" label="复习状态" :formatter="studyStatus" header-align="center"></el-table-column>
      <el-table-column prop="status" width="120" label="状态" :formatter="status" header-align="center">
        <template slot-scope="{ row }">
          <span :class="statusClass(row.status)">{{ status(row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="vacationCount" width="80" label="请假次数" header-align="center" />
    </el-table> -->
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <LeaveLookDialog @LeaveDialog="LeaveDialog" :LeaveViewStyle="LeaveViewStyle" :direction="direction"
      @fMethod="initData" />
    <reviewListLookDialog ref="reviewLi" @reviewDialog="reviewDialog" :reviewStyle="reviewStyle"
      :direction="direction" />

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import { selAllTeacher } from "@/api/studentClass/changeList"
import { deletePlanReview, getFeedbackInfo } from "@/api/paikeManage/LearnManager";
import LeaveLookDialog from "../pclass/components/LeaveLookDialog.vue";
import reviewListLookDialog from "../pclass/components/reviewListLookDialog.vue";
import { getTimetableReview, getVacationInfo, studentReviewExport, getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";
import ls from "@/api/sessionStorage";
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'


export default {
  name: "reviewList",
  components: {
    LeaveLookDialog,
    reviewListLookDialog,
    HeaderSettingsDialog
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date();
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const temp = new Date();
              temp.setTime(temp.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "最近七天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      reviewStyle: false,
      LeaveViewStyle: false,
      direction: "rtl", //超哪边打开
      LeaveId: "",
      searchNum: {
        courseType: "",
        name: "",
        endTime: "",
        startTime: "",
        teacherId: "",
        studentCode: "",
        studyStatus: "",
        teacherId: '',
        lastStudyTime: "",
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      tableLoading: false,
      teacher: "",
      timeAll: [],
      contentType: "",
      total: null,
      reviewLister: [],
      getVacationList: {
        id: ""
      },
      leaveApplication: {
        id: "",
        type: 2
      },
      isAdmin: false,
      exportLoading: false,

      option: [],
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        name: ""
      },

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '交付中心编号',
          value: 'deliverMerchant'
        },
        {
          name: '交付中心名称',
          value: 'deliverName'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '姓名',
          value: 'name'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '课程分类',
          value: 'courseType'
        },
        {
          name: '学员来源',
          value: 'merchantName'
        },
        {
          name: '授课方式',
          value: 'teachingType'
        },
        {
          name: '复习时间',
          value: 'reviewTime'
        },
        {
          name: '教练老师',
          value: 'teacher'
        },
        {
          name: '复习状态',
          value: 'studyStatus'
        },
        {
          name: '状态',
          value: 'status'
        }],
      tableHeaderList: [], // 获取表头数据

    };
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
    this.initData();
    this.getTeacherList();
    this.getHeaderlist();

  },
  // watch:{
  //   searchNum:{
  //     handler(n,o){
  //       this.searchNum.pageNum=1
  //     },
  //     immediate:true,
  //     deep:true
  //   }
  // },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    // 获取教练
    async getTeacherList() {
      let allData = await selAllTeacher(this.selectObj);
      this.option = this.option.concat(allData.data.data);
    },

    filterValue(value) {
      console.log(value)
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = value;
      this.getTeacherList();
    },

    changeMessage() {
      this.$forceUpdate();
    },

    clearSearchRecord() {
      setTimeout(() => {
        if (this.searchNum.teacherId == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      }, 500)
      this.$forceUpdate();
    },
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
      }
    },
    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData();
    },
    // 列表数据
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = [];
      }
      this.tableLoading = true;
      // this.searchNum.pageNum = this.tablePage.currentPage
      // this.searchNum.pageSize = this.tablePage.size
      this.searchNum.startTime = this.timeAll[0];
      this.searchNum.endTime = this.timeAll[1];
      let { data } = await getTimetableReview(this.searchNum)
      this.tableLoading = false;
      this.total = Number(data.totalItems);
      this.reviewLister = data.data;
    },
    statusClass(status) {
      switch (status) {
        case 1:
          return "";
        case 2:
          return "error";
      }
    },
    // 请假查看
    async leaveViewFn(row) {
      this.LeaveViewStyle = true;
      this.getVacationList.id = row.id;
      let res = await getVacationInfo(this.getVacationList);
      this.$refs.reviewLi.leaveDialogList = res.data;
    },
    // 数据查看
    async SumLookerFn(row) {
      this.leaveApplication.id = row.id;
      let res = await getFeedbackInfo(this.leaveApplication);
      this.reviewStyle = true;
      this.$refs.reviewLi.fuxiList = res.data;
      this.$refs.reviewLi.teacher = row.teacher;
      this.$refs.reviewLi.reviewTotal.planId = row.planId
      this.$refs.reviewLi.reviewTotal.id = row.id
      this.$refs.reviewLi.leaveApplication.id = row.id
    },
    LeaveDialog(v) {
      this.LeaveViewStyle = v;
    },
    reviewDialog(v) {
      this.reviewStyle = v;
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return "远程";
      } else if (val.teachingType == 2) {
        return "线下";
      } else if (val.teachingType == 3) {
        return "远程和线下";
      } else {
        return "暂无";
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return "未复习";
      } else if (val.studyStatus == 2) {
        return "已复习";
      }
    },
    status(val) {
      if (val.status == 1) {
        return "正常";
      } else if (val.status == 2) {
        return "请假";
      } else if (val.status == 3) {
        return "请假已处理";
      }
    },
    reviewStatus(val) {
      if (val.reviewStatus == 0) {
        return "未复习";
      } else if (val.reviewStatus == 2) {
        return "已复习";
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return "鼎英语";
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    //定义导出Excel表格事件
    exportExcel() {
      this.exportLoading = true;
      studentReviewExport(this.searchNum).then(res => {
        console.log(window.URL.createObjectURL(res));
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;//获取服务器端的文件名
        link.setAttribute("download", "复习课程表.xls");
        document.body.appendChild(link);
        link.click();
        this.exportLoading = false;
      })
    },
    //删除复习课程
    async deleteReview(row) {
      await this.$confirm("您确定要取消复习课程吗，取消后自动延后一天！");
      await deletePlanReview(row.id);
      this.$message.success("删除成功");
      await this.initData()
    },
    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = "";
      this.getTeacherList();
      this.initData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "reviewList",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'reviewList'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
          this.$forceUpdate();
        } else {
          this.tableHeaderList = this.headerSettings;
          this.$forceUpdate();
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
};
</script>

<style scoped>
body {
  background-color: #f5f7fa;
}

.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}
</style>
