<template>
  <div>
    <el-row>
      <el-col :span="4" :xs="24">
        <div slot="header" class="clearfix">
          <span>卡片名称</span>
          <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>
        </div>
        <div class="text item">
          {{ '列表内容 ' + o }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        data:[
            {
                title:"总单量(单)",
                value:14596
            },
            {
                title:"总成交单量(单)",
                value:8515
            },
            {
                title:"转化率",
                value:8515
            },
            {
                title:"成交课时(节)",
                value:14596
            },
        ]
      };
    },
  };
</script>

<style lang="scss" scoped></style>
