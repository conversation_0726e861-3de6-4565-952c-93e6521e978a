<template>
  <el-form label-width="140px" style="margin-top: 2vh" :model="addlist" ref="editForm" :rules="loginRules">
    <el-row>
      <el-col :span="8">
        <el-form-item label="姓名:" prop="name">
          <el-input style="width: 70%" placeholder="请输入" v-model="addlist.name"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="所学专业:" prop="major">
          <el-input style="width: 70%" placeholder="请输入" v-model="addlist.major"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="性别:" prop="sex">
          <el-select style="width: 70%" v-model="addlist.sex" placeholder="请选择">
            <el-option label="男" :value="1"></el-option>
            <el-option label="女" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="联系方式:" prop="mobile">
          <el-input :disabled="!!id" style="width: 70%" placeholder="请输入" v-model="addlist.mobile"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="性格特点:" prop="personality">
          <el-input style="width: 70%" placeholder="请输入" v-model="addlist.personality"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="授课方式:" prop="teachingType">
          <el-select style="width: 70%" v-model="addlist.teachingType" placeholder="请选择">
            <el-option label="远程" :value="1"></el-option>
            <el-option label="线下" :value="2"></el-option>
            <el-option label="远程和线下" :value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="毕业院校:" prop="graduate">
          <el-input style="width: 70%" placeholder="请输入" v-model="addlist.graduate"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="岗位类型:" prop="postType">
          <el-select style="width: 70%" v-model="addlist.postType" placeholder="请选择">
            <el-option label="全职" :value="1"></el-option>
            <el-option label="兼职" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="试课能力:">
          <template>
            <el-radio v-model="addlist.experience" :label="1">是</el-radio>
            <el-radio v-model="addlist.experience" :label="0">否</el-radio>
          </template>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item label="所属交付小组:" prop="teamId">
          <!-- <el-input v-model.trim="zhuSou.teamName" placeholder="请输入" size="mini" style="width: 250px"></el-input> -->
          <el-select v-model="addlist.teamId" clearable size="small" filterable placeholder="请选择" style="width: 10vw">
            <el-option v-for="(item, index) in leaderTeamList" :key="index" :label="item.teamName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 按钮最后一行 -->
    <el-row>
      <el-col v-for="(item, index) in usableDateList" :key="index">
        <el-form-item label="时间:">
          <el-select v-model="item.usableWeek" style="width: 9vw; margin-right: 1vw" placeholder="请选择">
            <el-option v-for="item in optionsz" :key="item.index" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-time-picker
            v-model="item.startTime"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 9vw; margin-right: 1vw"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:00'
            }"
            placeholder="任意时间点"
          ></el-time-picker>
          <el-time-picker
            style="width: 9vw; margin-right: 1vw"
            format="HH:mm"
            value-format="HH:mm"
            v-model="item.endTime"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:00'
            }"
            placeholder="任意时间点"
          ></el-time-picker>
          <el-button type="primary" round size="mini" @click="addItem(item)">增添</el-button>
          <el-button v-if="index !== 0" type="danger" size="mini" @click="deleteItem(item, index)">-</el-button>
        </el-form-item>
      </el-col>
      <el-col v-if="!editShow">
        <el-form-item>
          <div style="color: #666">
            <i class="el-icon-warning" style="color: #fbc90c; margin-right: 10px"></i>
            若不选择时间，则默认为所有时间均可排课
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="courseType">
      <el-form-item label="课程类型">
        <el-select style="width: 20%" v-model="projectType" multiple @change="getCurriculun" placeholder="请选择">
          <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="`${item.id},${item.enName}`"></el-option>
        </el-select>
      </el-form-item>
    </div>
    <div class="courseType" v-for="(item, index) in teacherCurriculumList" :key="index">
      <div style="padding-left: 62px">
        <h2>{{ item.curriculumName }}</h2>
      </div>

      <el-form-item label="英语水平:" prop="englishLevel" v-if="item.curriculumName == '鼎英语'">
        <el-input style="width: 20%" placeholder="请输入" v-model="addlist.englishLevel"></el-input>
      </el-form-item>

      <el-form-item label="词汇水平:" prop="vocabulary" v-if="item.curriculumName == '鼎英语'">
        <el-input style="width: 20%" placeholder="请输入" v-model="addlist.vocabulary"></el-input>
      </el-form-item>
      <el-form-item label="上传证书:" v-if="item.curriculumName == '鼎英语'">
        <el-upload
          ref="clearupload"
          v-loading="uploadLoading"
          list-type="picture-card"
          :file-list="fileList"
          action
          element-loading-text="图片上传中"
          :limit="10"
          :on-exceed="justPictureNum"
          :http-request="uploadDetailHttp"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemoveDetailContract"
        >
          <i class="el-icon-plus" />
        </el-upload>
      </el-form-item>
      <el-form-item label="所教学段:" v-show="!item.curriculumName.includes('AI课程') && !item.curriculumName.includes('1V') && !item.curriculumName.includes('1对')">
        <el-select style="width: 20%" v-model="item.gradeStage" multiple placeholder="请选择">
          <el-option v-for="item in optionsOne" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所教模块:" prop="teachModule" v-show="item.curriculumName == '鼎英语' || item.curriculumName == '鼎学能'">
        <el-select style="width: 20%" v-model="item.teachModule" multiple placeholder="请选择" v-if="item.curriculumName == '鼎英语'">
          <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select style="width: 20%" v-model="item.teachModule" multiple placeholder="请选择" v-if="item.curriculumName == '鼎学能'">
          <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </div>

    <div class="addBtn">
      <el-button @click="quxiao">取 消</el-button>
      <el-button @click="btnOk" :loading="isSubmit" type="primary">确 定</el-button>
    </div>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </el-form>
</template>

<script>
  import { addZhugetTeacher, getTeacher, update } from '@/api/rukuManage/zhuTeacher';
  // import studentApi from "@/api/student/areasStudentCourseList";
  import { bvstatusList } from '@/api/paikeManage/classCard';
  import { ossPrClient } from '@/api/alibaba';
  import { findTeamList } from '@/api/studentClass/changeList';
  export default {
    name: 'QualityDialog',
    components: {},
    data() {
      return {
        loginRules: {
          name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
          major: [{ required: true, message: '请输入所学专业', trigger: 'blur' }],
          sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
          teachingType: [{ required: true, message: '请选择授课方式', trigger: 'change' }],
          postType: [{ required: true, message: '请选择岗位类型', trigger: 'change' }],
          // projectType: [
          //   { required: true, message: '请选择课程类型', trigger: 'change' },
          // ],
          // gradeStage: [
          //   { required: true, message: '请选择所教学段', trigger: 'change' },
          // ],
          mobile: [
            { required: true, message: '请输入手机号码', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
          ],
          personality: [{ required: true, message: '请输入性格特点', trigger: 'blur' }]
        },
        uploadLoading: false, // 上传图片加载按钮
        fileDetailList: [], // 上传图片已有图片列表
        fileDetailList1: [], // 上传图片已有图片列表
        visible: false,
        dialogImageUrl: '',
        dialogVisible: false,
        disabled: false,
        imageUrl: '',
        usableDateList: [
          {
            usableWeek: '',
            startTime: '',
            endTime: ''
          }
        ],
        linshi: '',
        optionsz: [
          {
            value: 1,
            label: '星期一'
          },
          {
            value: 2,
            label: '星期二'
          },
          {
            value: 3,
            label: '星期三'
          },
          {
            value: 4,
            label: '星期四'
          },
          {
            value: 5,
            label: '星期五'
          },
          {
            value: 6,
            label: '星期六'
          },
          {
            value: 7,
            label: '星期天'
          }
        ],
        options: [],
        optionsOne: [
          {
            value: '1',
            label: '小学'
          },
          {
            value: '2',
            label: '初中'
          },
          {
            value: '3',
            label: '高中'
          },
          {
            value: '4',
            label: '大学'
          }
        ],
        optionsb: [],
        options1: [
          {
            value: '1',
            label: '单词'
          },
          {
            value: '2',
            label: '语法'
          },
          {
            value: '3',
            label: '阅读'
          }
        ],
        options2: [
          {
            value: '4',
            label: '注意力'
          },
          {
            value: '5',
            label: '思维力'
          },
          {
            value: '6',
            label: '记忆力'
          }
        ],
        gradeStage: [], //所教学段
        teachModule: [], //所教模块
        teachingType: [], //授课方式
        projectType: [], //课程类型
        teacherCurriculumList: [],
        curriculumName: '鼎英语',
        postType: [], //岗位类型
        addlist: {
          name: '',
          sex: '',
          englishLevel: '',
          mobile: '',
          personality: '',
          graduate: '',
          major: '',
          vocabulary: '',
          gradeStage: '',
          teachModule: '',
          teachingType: '',
          postType: '',
          certificate: '', //图片证书
          experience: 0,
          teamId: ''
        },
        id: undefined,
        fileList: [],
        // 1鼎英语，2鼎学能，3只迷，4珠心算
        courseList: [
          // { name: '鼎英语', value: 1 },
          // { name: '鼎学能', value: 2 },
          // { name: '只迷', value: 3 },
          // { name: '珠心算', value: 4 }
        ],
        editShow: false,
        leaderTeamList: [],
        isSubmit: false
      };
    },
    created() {
      this.id = this.$route.query.id;
      // this.check()
      ossPrClient();
      if (this.id) {
        this.initDate();
      }
    },
    mounted() {
      this.getbvstatusList();
      this.getTeamList();
    },
    methods: {
      async getTeamList() {
        let { data } = await findTeamList();
        this.leaderTeamList = data.data;
      },
      delInput() {},
      getbvstatusList() {
        bvstatusList({}).then((res) => {
          this.courseList = res.data;
        });
      },
      async initDate() {
        let res = await getTeacher(this.id);
        this.addlist = res.data;
        this.projectType = res.data.teacherCurriculumList.length > 0 ? res.data.teacherCurriculumList.map((i) => (i = i.curriculumId + ',' + i.curriculumName)) : [];
        if (res.data.usableDateList != '') {
          this.usableDateList = res.data.usableDateList;
        }
        this.addlist.certificate = res.data.certificate;
        this.teacherCurriculumList = res.data.teacherCurriculumList ? res.data.teacherCurriculumList : [];
        console.log('🚀 ~ initDate ~ res.data.teacherCurriculumList:', res.data.teacherCurriculumList);
        this.teacherCurriculumList.forEach((i) => {
          i.teachModule = i.teachModule.split(',');
          i.gradeStage = i.gradeStage.split(',');
        });
        let split = res.data.certificate.split(',');
        for (let i = 0; i < split.length; i++) {
          if (split[i].includes('.com/')) {
            let urlPart = split[i].split('.com/')[1];
            this.fileList.push({ url: this.aliUrl + urlPart });
            this.fileDetailList.push({ name: urlPart });
          } else {
            // 处理不包含 .com/ 的情况
            this.fileList.push({ url: this.aliUrl + split[i] });
            this.fileDetailList.push({ name: split[i] });
          }
        }
        this.editShow = true;
      },
      // 上传照片请求
      uploadDetailHttp({ file }) {
        this.uploadLoading = true;
        const fileName = 'manage/' + Date.parse(new Date());
        this.$nextTick(function () {
          ossPrClient()
            .put(fileName, file)
            .then(({ res, url, name }) => {
              if (res && res.status === 200) {
                // console.log(`阿里云OSS上传图片成功回调1`, res, url, name);
                this.fileDetailList.push({
                  uid: file.uid,
                  name: name
                });

                this.$nextTick(() => {
                  this.uploadLoading = false;
                });
              }
            })
            .catch((err) => {
              this.$message.error('上传图片失败请检查网络或者刷新页面');
              console.log(`阿里云OSS上传图片失败回调`, err);
            });
        });
      },
      // 上传图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      // 上传图片数量超限
      justPictureNum(file, fileList) {
        this.$message.warning(`当前限制选择10个文件`);
      },
      //根据课程分类获取课程类型的下拉列表
      // check() {
      //   studentApi.classification('CURRICULUM').then(res => {
      //     this.courseList = res.data;
      //   })
      // },
      //根据课程类型更新所教学段和所教模块数据
      findDifferentIds(arr1, arr2) {
        return arr1.filter((item1) => {
          return !arr2.some((item2) => {
            return item2.curriculumId === item1.curriculumId;
          });
        });
      },
      getCurriculun(val) {
        if (val) {
          console.log('🚀 ~ getCurriculun ~ val:', val);
          let arr = [];
          for (let index = 0; index < val.length; index++) {
            let obj = {};
            obj.curriculumName = val[index].split(',')[1];
            obj.curriculumId = val[index].split(',')[0];
            arr.push(obj);
          }
          let arr2 = [];
          arr.forEach((e) => {
            let b = this.teacherCurriculumList.find((o) => o.curriculumId == e.curriculumId);
            if (b) {
              arr2.push(b);
            } else {
              arr2.push(e);
            }
          });
          this.teacherCurriculumList = arr2;
          console.log('🚀 ~ getCurriculun ~ this.teacherCurriculumList:', this.teacherCurriculumList);
          // let newArr = this.findDifferentIds(arr, this.teacherCurriculumList);

          // this.teacherCurriculumList = this.teacherCurriculumList.concat(newArr);
        } else {
          this.teacherCurriculumList = [];
        }
      },
      // 删除证书照片
      handleRemoveDetailContract(file, fileList) {
        for (let i = 0; i < this.fileDetailList.length; i++) {
          if (this.fileDetailList[i].uid === file.uid || this.aliUrl + this.fileDetailList[i].name === file.url) {
            this.fileDetailList.splice(i, 1);
          }
        }
      },
      quxiao() {
        this.$router.go(-1);
      },
      // 提交
      async btnOk() {
        let that = this;
        that.isSubmit = true;
        // 日期时间的
        if (that.usableDateList[0].usableWeek && that.usableDateList[0].startTime && that.usableDateList[0].endTime) {
          that.addlist.usableDateList = that.usableDateList;
        }
        // 图片上传的
        let img = undefined;
        if (that.fileDetailList.length > 0) {
          img = that.fileDetailList[0].name;
          if (that.fileDetailList.length > 1) {
            for (let i = 1; i < that.fileDetailList.length; i++) {
              img = img + ',' + that.fileDetailList[i].name;
            }
          }
        }
        that.addlist.certificate = img;
        let arr = JSON.parse(JSON.stringify(that.teacherCurriculumList));
        arr.forEach((i) => {
          i.gradeStage = i.gradeStage ? i.gradeStage.join(',') : '';
          i.teachModule = i.teachModule ? i.teachModule.join(',') : '';
        });

        that.addlist.teacherCurriculumList = arr;
        that.$refs.editForm.validate(async (vaild) => {
          if (vaild) {
            if (that.projectType.length < 1) {
              that.$message.error('请选择课程类型');
              return false;
            }
            try {
              let res = that.id ? await update(that.addlist) : await addZhugetTeacher(that.addlist);
              if (res.code === 20000) {
                let msg = that.id ? '修改成功' : '添加教练成功!';
                that.$message.success(msg);
                that.$router.go(-1);
              }
            } catch (error) {
              that.isSubmit = false;
              return that.$message.error(error);
            }
          } else {
            that.isSubmit = false;
            that.$message.error({ showClose: true, message: '请按规范填写数据', type: 'error' });
            return;
          }
        });
      },
      //新增方法页面绘制用的！！请求用
      addItem(item) {
        if (this.usableDateList.length > 20) {
          this.$message({
            type: 'warning',
            message: '最多可新增20条!'
          });
        } else {
          let week = item.usableWeek;
          if (week) {
            week = parseInt(item.usableWeek) + 1;
            if (week > 7) {
              week = 7;
            }
          }
          this.usableDateList.push({
            usableWeek: week,
            startTime: item.startTime,
            endTime: item.endTime
          });
        }
      },
      //删除方法方法页面绘制用的！！请求用
      deleteItem(item, index) {
        this.usableDateList.splice(index, 1);
      }
    }
  };
</script>

<style lang="scss">
  // 输入框样式
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;

    &::before {
      line-height: 80px;
    }
  }

  .avatar {
    width: 80px;
    height: 80px;
    display: block;
  }

  .addBtn {
    display: flex;
    justify-content: center;
  }
  .courseType {
    padding: 20px 0;
    border-top: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    margin-bottom: 20px;
  }
</style>
