<template>
  <!-- 教练工资明细 -->
  <div class="app-container">
    <!-- 查询（1.教练列表/2.单个教练详情） -->
    <el-form v-show="!isItDetailed" :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery" :rules="rules">
      <el-row>
        <el-col :span="5" :xs="24">
          <el-form-item label="月份：" prop="month" label-width="60px">
            <el-date-picker
              v-model="dataQuery.month"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择月"
              @change="
                () => {
                  (this.tablePage.currentPage = 1), this.fetchData();
                }
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="教练名称：" prop="teacherName">
            <el-input id="teacherName" v-model="dataQuery.teacherName" name="id" placeholder="请输入教练名称" />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="联系方式：" prop="teacherMobile">
            <el-input id="teacherMobile" v-model="dataQuery.teacherMobile" name="id" placeholder="请输入联系方式" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="交付中心名称：" prop="merchantName" label-width="110px" v-if="checkPermission(['admin'])">
            <el-input id="merchantName" v-model="dataQuery.merchantName" name="id" placeholder="请输入交付中心名称" maxlength="10" />
          </el-form-item>
        </el-col>
        <el-col :span="5" :xs="24">
          <el-form-item label="交付中心编号：" prop="merchantCode" label-width="110px" v-if="checkPermission(['admin'])">
            <el-input id="merchantCode" v-model="dataQuery.merchantCode" name="id" placeholder="请输入交付中心编号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="10" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  (this.tablePage.currentPage = 1), fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form v-show="isItDetailed" :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery">
      <el-row>
        <el-col :span="6">
          <el-form-item label="月份：" prop="detailMonth" label-width="60px">
            <el-date-picker
              v-model="dataQuery.detailMonth"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择月"
              @change="
                () => {
                  (this.tablePage2.currentPage = 1), this.fetchData();
                }
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="课程类型：" prop="curriculumName">
            <el-select v-model="dataQuery.curriculumName" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: '鼎英语', label: '鼎英语' },
                  { value: '拼音法', label: '拼音法' },
                  { value: '拼音法（高年级）', label: '拼音法（高年级）' },
                  { value: '珠心算', label: '珠心算' },
                  { value: '鼎学能', label: '鼎学能' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工资类型：" prop="wagesType">
            <el-select v-model="dataQuery.wagesType" filterable value-key="value" placeholder="请选择" clearable>
              <el-option v-for="(item, index) in textMap[isTrial]" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button icon="el-icon-arrow-left" @click="back()">返回</el-button>
            <el-button
              v-if="!isItDetailed"
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  (this.tablePage.currentPage = 1), fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button
              v-else
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  (this.tablePage2.currentPage = 1), fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 表格（1.教练列表/2.单个教练详情） -->
    <div class="subsidy-center-style" v-if="!checkPermission(['admin'])">
      <el-button type="primary" @click="rechargeSubsidy()">充值试课补贴</el-button>
      <span class="subsidy-style-text">{{ monther }}月试课补贴金额：{{ monthAllowanceInfo.monthAllowance }}</span>
      <div class="subsidy-bottom-style">
        <i class="el-icon-warning"></i>
        <span>申请使用试课工资的交付中心，每月1日0:00会统计教练上月的试课补贴总额，需在每月1日0:00-每月9日23:59日充值上月试课补贴，否则只可手动结算</span>
      </div>
    </div>

    <el-table
      v-show="!isItDetailed"
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      :header-cell-style="{ color: '#666', height: '60px', textAlign: 'center', background: '#F5F7FA' }"
      :row-style="{ height: '50px' }"
      :cell-style="cellStyle"
      border
    >
      <el-table-column prop="teacherName" label="教练名称" width="150px"></el-table-column>
      <el-table-column prop="teacherMobile" label="联系方式" width="150px"></el-table-column>
      <el-table-column prop="deliverMerchantName" label="交付中心名称" v-if="checkPermission(['admin'])" min-width="130px">
        <template v-slot="{ row }">
          <el-tag>{{ row.deliverMerchantName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="deliverMerchantCode" label="交付中心编号" v-if="checkPermission(['admin'])"></el-table-column>
      <el-table-column prop="courseFormal" label="已上正式课数" min-width="130px">
        <template v-slot="{ row }">
          <el-tag>{{ row.courseFormal }}</el-tag>
          &nbsp;
          <el-link type="primary" @click="handleDetailClick(row, 1)" :disabled="!dataQuery.month">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="courseTry" label="已上试课数" min-width="130px">
        <template v-slot="{ row }">
          <el-tag>{{ row.courseTry }}</el-tag>
          &nbsp;
          <el-link type="primary" @click="handleDetailClick(row, 0)" :disabled="!dataQuery.month">详情</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="commissionAmount" label="绩效工资">
        <template v-slot="{ row }">
          <span>{{ (row.commissionAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="basicAmount" label="基本工资">
        <template v-slot="{ row }">
          <span>{{ (row.basicAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="expAllowance" label="试课补贴" :render-header="checkPermission(['admin']) ? renderHeader : ''">
        <template v-slot="{ row }">
          <span>{{ (row.expAllowance * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="testAmount" label="试课奖励">
        <template v-slot="{ row }">
          <span>{{ (row.testAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="reviewAmount" label="复习工资">
        <template v-slot="{ row }">
          <span>{{ (row.reviewAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="reviewBonus" label="复习奖金">
        <template v-slot="{ row }">
          <span>{{ (row.reviewBonus * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalAmount" label="总工资">
        <template v-slot="{ row }">
          <span>{{ (row.totalAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-show="isItDetailed"
      class="common-table"
      v-loading="tableLoading"
      :data="detailData"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      :header-cell-style="{ color: '#666', height: '60px', textAlign: 'center', background: '#F5F7FA' }"
      :row-style="{ height: '50px' }"
      border
    >
      <el-table-column prop="teacherName" label="教练名称" width="150px"></el-table-column>
      <el-table-column prop="teacherMobile" label="联系方式" width="150px"></el-table-column>
      <el-table-column prop="deliverMerchantName" label="交付中心名称" v-if="checkPermission(['admin'])">
        <template v-slot="{ row }">
          <el-tag>{{ row.deliverMerchantName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="deliverMerchantCode" label="交付中心编号" v-if="checkPermission(['admin'])"></el-table-column>
      <el-table-column prop="studentName" label="学员名称">
        <template v-slot="{ row }">
          <span v-if="row.studentName == ''">-</span>
          <el-tag v-else>{{ row.studentName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="studentCode" label="学员编号">
        <template v-slot="{ row }">
          <span>{{ row.studentCode != '0' ? row.studentCode : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上课时间" min-width="150px">
        <template v-slot="{ row }">
          <el-tag v-if="row.wagesType == '7' || row.wagesType == '9'">{{ row.classBeginTime.split(' ')[0] + ' - ' + row.classEndTime.split(' ')[0] }}</el-tag>
          <el-tag v-else>{{ row.classBeginTime + ' - ' + row.classEndTime.split(' ')[1] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="课程类型">
        <template v-slot="{ row }">
          <!-- <div type="success">{{ row.curriculumName }}</div> -->
          <el-tag type="danger" v-if="row.curriculumName == '鼎英语'">鼎英语</el-tag>
          <el-tag type="success" v-else-if="row.curriculumName == '鼎学能'">鼎学能</el-tag>
          <el-tag type="danger" v-else-if="row.curriculumName == '珠心算'">珠心算</el-tag>
          <el-tag v-else-if="row.curriculumName == '拼音法'">拼音法</el-tag>
          <el-tag type="success" v-else-if="row.curriculumName == '拼音法（高年级）'">拼音法（高年级）</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工资类型">
        <template v-slot="{ row }">
          <span v-if="row.wagesType == '1'">试课奖励</span>
          <span v-else-if="row.wagesType == '2'">基本工资</span>
          <span v-else-if="row.wagesType == '3'">绩效工资</span>
          <span v-else-if="row.wagesType == '4'">基本工资</span>
          <span v-else-if="row.wagesType == '5'">绩效工资</span>
          <span v-else-if="row.wagesType == '6'">复习工资</span>
          <span v-else-if="row.wagesType == '7'">复习奖金</span>
          <span v-else-if="row.wagesType == '9'">试课补贴</span>
        </template>
      </el-table-column>
      <el-table-column label="金额">
        <template v-slot="{ row }">
          <span>{{ (row.wagesAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="150px">
        <template v-slot="{ row }">
          <span>{{ row.createTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页（1.教练列表/2.单个教练详情） -->
    <el-col :span="24" style="overflow-x: auto" :xs="24" v-show="!isItDetailed">
      <el-pagination
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <el-col :span="24" style="overflow-x: auto" :xs="24" v-show="isItDetailed">
      <el-pagination
        :current-page.sync="tablePage2.currentPage"
        :page-size.sync="tablePage2.pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage2.totalItems"
        @size-change="handleDetailSizeChange"
        @current-change="handleDetailCurrentChange"
      />
    </el-col>
    <el-dialog :title="rechargeTitle" :visible.sync="rechargeDialogVisible" width="550px" :before-close="dialogBeforeClose">
      <el-form :model="monthAllowanceInfo" label-position="right" style="padding-right: 50px" label-width="130px">
        <el-form-item label="充值试课补贴：">
          <el-input v-model="monthAllowanceInfo.monthAllowance" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="手续费：">
          <el-input v-model="monthAllowanceInfo.fee" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="总计：">
          <el-input v-model="monthAllowanceInfo.amount" :disabled="true"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="rechargeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="rechargeSubsidyReview()">充 值</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import checkPermission from '@/utils/permission';
  import orderPage from '@/api/FinanceApi/coachSalaryDetails';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import { getCheckRecharge, getMonthAllowance, allowanceSave } from '@/api/FinanceApi/coachTurnover';
  import dayjs from 'dayjs';
  import { getToken } from '@/utils/auth';
  import { log } from 'bpmn-js-token-simulation';
  import { detail } from '@/api/peizhi/peizhi';
  import ls from '@/api/sessionStorage';

  export default {
    name: 'coachSalaryDetails',
    data() {
      return {
        isAdmin: false,
        rechargeDialogVisible: false,
        rechargeForm: {},
        rechargeTitle: '',
        monthAllowanceInfo: {}, //月度补贴
        dataQuery: {
          month: '',
          teacherName: '',
          teacherMobile: '',
          merchantCode: '',
          merchantName: '',
          //详细数据查询条件
          detailMonth: '',
          detailTeacherName: '', //不显示
          wagesType: '',
          curriculumName: '',
          studentCode: ''
        },
        dateShow: true,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        tableLoading: false,
        //是否单个教练详情
        isItDetailed: false,
        // 详细数据是否试课
        isTrial: false,
        // 详细数据分页
        tablePage2: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        monther: '',
        detailData: [],
        textMap: [
          [
            { value: 3, label: '绩效工资' },
            { value: 2, label: '基本工资' },
            { value: 1, label: '试课奖励' }
            // { value: 9, label: '试课补贴' }
          ],
          [
            { value: 5, label: '绩效工资' },
            { value: 4, label: '基本工资' },
            { value: 6, label: '复习工资' },
            { value: 7, label: '复习奖金' }
          ]
        ],
        rules: {
          studentName: [{ min: 0, max: 10, message: '姓名最多输入10个字符', trigger: 'blur' }],
          studentName: [{ min: 0, max: 10, message: '姓名最多输入10个字符', trigger: 'blur' }],
          teacherName: [{ min: 0, max: 10, message: '教练名称最多输入10个字符', trigger: 'blur' }],
          studentCode: [
            { min: 0, max: 10, message: '编号最多输入10个数字', trigger: 'blur' },
            { pattern: /^\d*$/, message: '请输入数字编号', trigger: 'blur' }
          ],
          teacherMobile: [
            { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ]
        }
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin';
      //拿到目前的年月
      this.month = dayjs().format('YYYY-MM');
      this.monther = dayjs().format('M') - 1;
      console.log(dayjs().format('D'));
      //判断是否是充值时间
      if (1 <= dayjs().format('D') && dayjs().format('D') <= 9) {
        this.dateShow = true;
      } else {
        this.dateShow = false;
      }
      this.dataQuery.month = this.month;
      this.dataQuery.detailMonth = this.month;
    },
    mounted() {
      this.fetchData();
      if (!this.checkPermission(['admin'])) {
        this.getMonthAllowance();
      }
    },
    methods: {
      getMonthAllowance() {
        getMonthAllowance({}).then((res) => {
          this.monthAllowanceInfo = res.data;
        });
      },
      renderHeader(h, { column, $index }) {
        return h('div', [
          h('span', column.label + ' ', { align: 'center' }),
          h(
            'el-popover',
            {
              props: {
                placement: 'top-start',
                trigger: 'hover'
              }
            },
            [
              h('div', { style: { margin: '10px' } }, ['每月1日00:00会统计教练上月的试课补贴总额']),
              h('i', {
                class: 'el-icon-warning warning-color',
                slot: 'reference'
              })
            ]
          )
        ]);
      },
      checkPermission,
      // 分页
      handleSizeChange(val) {
        this.tablePage.currentPage = 1;
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      // 单个教练详情分页
      handleDetailSizeChange(val) {
        this.tablePage2.currentPage = 1;
        this.tablePage2.size = val;
        this.fetchData();
      },
      handleDetailCurrentChange(val) {
        this.tablePage2.currentPage = val;
        this.fetchData();
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        if (!this.dataQuery.month && !this.isItDetailed) {
          that.$message({
            message: '请选择月份',
            type: 'warning'
          });
          return;
        }
        this.dataQuery = {
          month: this.isItDetailed ? this.dataQuery.month : this.month,
          teacherName: this.isItDetailed ? this.dataQuery.teacherName : '',
          teacherMobile: this.isItDetailed ? this.dataQuery.teacherMobile : '',
          merchantCode: this.isItDetailed ? this.dataQuery.merchantCode : '',
          merchantName: this.isItDetailed ? this.dataQuery.merchantName : '',
          //详细数据查询条件
          detailMonth: this.month,
          detailTeacherName: this.dataQuery.detailTeacherName, //这个在详细列表重置的时候不能清空
          detailMerchantCode: this.dataQuery.detailMerchantCode, //这个在详细列表重置的时候不能清空
          wagesType: '',
          curriculumName: '',
          studentCode: ''
        };

        this.fetchData();
      },
      //返回
      back() {
        // 重置‘教练详细数据’查询条件
        this.dataQuery = {
          ...this.dataQuery,
          wagesType: '',
          curriculumName: '',
          studentCode: ''
        };
        this.isItDetailed = false;
        this.dataQuery.isExp = false;
        // 重置详细列表分页
        this.tablePage2 = {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        };
        this.fetchData();
      },
      // 查询列表
      fetchData() {
        const that = this;
        if (!this.dataQuery.month && !this.isItDetailed) {
          that.$message({
            message: '请选择月份',
            type: 'warning'
          });
          return;
        } else if (!this.dataQuery.detailMonth && this.isItDetailed) {
          that.$message({
            message: '请选择月份',
            type: 'warning'
          });
          return;
        }
        this.$refs['dataQuery'].validate((valid) => {
          if (valid) {
            that.tableLoading = true;
            /**
             * 根据'单个教练详情'查询不同的接口
             * 1.单个教练详情列表 2.所有教练工资列表（默认）
             * 2.单个教练详情列表又分为'试课'和'正式课',以isExp字段区分
             */
            if (that.isItDetailed) {
              this.dataQuery.isExp = this.isTrial;
              this.dataQuery.teacherName = this.teacherName;
              orderPage
                .queryDetailList(that.tablePage2.currentPage, that.tablePage2.size, that.dataQuery)
                .then((res) => {
                  that.detailData = res.data.data;
                  that.tableLoading = false;
                  // 设置后台返回的分页参数
                  pageParamNames.forEach((name) => {
                    that.$set(that.tablePage2, name, parseInt(res.data[name]));
                  });
                })
                .catch((err) => {
                  that.tableLoading = false;
                });
            } else {
              orderPage.queryList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then((res) => {
                that.tableData = res.data.data;
                that.tableLoading = false;
                // 设置后台返回的分页参数
                pageParamNames.forEach((name) => {
                  that.$set(that.tablePage, name, parseInt(res.data[name]));
                });
              });
            }
          }
        });
      },
      dialogBeforeClose() {
        this.rechargeDialogVisible = false;
      },
      //试课补贴按钮点击
      rechargeSubsidy() {
        getCheckRecharge({}).then((res) => {
          if (res.data) {
            this.$message.error({
              message: '你已充值过' + this.monther + '月的试课补贴'
            });
          } else {
            if (this.dateShow) {
              this.rechargeTitle = '充值' + this.monther + '月试课补贴';
              this.rechargeDialogVisible = true;
            } else {
              this.$message.error({
                message: '暂时不在充值时间内'
              });
            }
          }
        });
      },
      rechargeSubsidyReview() {
        allowanceSave({ amountYuan: this.monthAllowanceInfo.monthAllowance }).then((res) => {
          if (res.success) {
            res.data.dxSource = res.data.registerCallIInfo.appSource + '##BROWSER##WEB';
            let params = JSON.stringify(res.data);
            let req = 'token=' + getToken() + '&params=' + params + '&back=' + window.location.href;
            //需要编码两遍，避免出现+号等
            var encode = Base64.encode(Base64.encode(req));
            // window.open("https://pay.dxznjy.com/product?" + encode, "_blank");
            if (window.location.host.split('.')[0] == 'deliver') {
              window.open('https://pay.dxznjy.com/product?' + encode, '_blank');
            } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host)) {
              window.open('http://*************:8000/product?' + encode, '_blank');
            } else {
              let a = window.location.host.split('.')[0].slice(0, -1);
              let b = `https://${a}i.dxznjy.com/`;
              window.open(b + 'product?' + encode, '_blank');
            }
          }
          this.rechargeDialogVisible = false;
        });
      },
      // 查看单个教练详情
      handleDetailClick(row, isTrial) {
        const that = this;
        if (!this.dataQuery.month) {
          that.$message({
            message: '请选择月份',
            type: 'warning'
          });
          return;
        }
        // // 重置分页
        // this.tablePage = {
        //   currentPage: 1,
        //   size: 10,
        //   totalPage: null,
        //   totalItems: null
        // };
        //‘教练姓名,交付中心编号及月份’查询条件带过去
        this.dataQuery.detailMonth = this.dataQuery.month;
        this.dataQuery.detailTeacherName = row.teacherName;
        // this.dataQuery.detailMerchantCode = this.isAdmin ? '' : row.deliverMerchantCode;
        this.dataQuery.detailMerchantCode = row.deliverMerchantCode;
        // console.log(this.dataQuery.detailMerchantCode,row, 'this.dataQuery');

        this.isTrial = isTrial; //试课/正式课
        this.isItDetailed = true; //显示单个教练详情列表
        this.fetchData();
      },
      // 加重最后一行样式
      cellStyle({ row, column, rowIndex, columnIndex }) {
        if (column.label === '总工资') {
          //指定列号
          return 'color: #1890ff;';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .subsidy-center-style {
    padding-bottom: 15px;
    .subsidy-style-text {
      display: inline-block;
      margin-left: 15px;
      line-height: 40px;
    }
    .subsidy-bottom-style {
      font-size: 13px;
      margin-top: 10px;
      color: #666;
      i {
        color: #f89728;
      }
    }
  }
  .el-col-5 {
    max-width: 20%;
    width: 20%;
    flex: 0 0 20%;
  }
</style>
