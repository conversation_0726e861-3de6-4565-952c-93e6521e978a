<template>
  <div>
    <el-dialog :before-close="close" title="转移学员" center class="studentTransferBox" :visible.sync="transferVisible"
      :width="screenWidth > 1300 ? '45%' : '90%'">
      <el-form ref="dataForm" :label-width="screenWidth > 1300 ? '200px' : '170px'">
        <!-- <el-form-item label="交付中心账号:" prop="name">
          <el-row>
            <el-col :span="19">
              <el-input v-model="from.name"
                        placeholder="请输入交付中心账号或者交付中心编号"></el-input>
            </el-col>
            <el-col :span="4">
              <el-button icon="el-icon-search" style="margin-left: 5px" @click="search()">
              </el-button>
            </el-col>
          </el-row>
        </el-form-item> -->
        <el-form-item label="重新指派交付中心名称：" prop="deliverMerchantCode">
          <el-select v-model="deliverMerchantCode" placeholder="请选择" filterable>
            <el-option v-for="item in deliverCenterLists" :key="item.merchantCode" :label="item.merchantName"
              :value="item.merchantCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="首次上课时间：" prop="deliverMerchantCode">
          <div style="display:flex;">
            <el-date-picker v-model="value1" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              :picker-options="pickerOptions1" placeholder="选择日期">
            </el-date-picker>
            <el-time-picker v-model="value2" :picker-options="pickerOptions2" value-format="HH:mm" format="HH:mm"
              placeholder="任意时间点">
            </el-time-picker>
          </div>
        </el-form-item>
        <el-form-item label="复习时间" prop :style="{ width: screenWidth > 1300 ? '80%' : '100%' }"
          style="margin-top: 10px;" v-if="transferFrom.needTime">
          <el-row>
            <el-checkbox-group v-model="reviewTimes" size="small">
              <el-checkbox-button v-for="(item, index) in normalWeekData" :label="index"
                :key="index">{{item}}</el-checkbox-button>
            </el-checkbox-group>
          </el-row>
        </el-form-item>
        <el-form-item label=" " v-if="transferFrom.needTime">
          <el-time-picker v-model="time3" :picker-options="pickerOptions3" value-format="HH:mm" format="HH:mm"
            placeholder="任意时间点">
          </el-time-picker>
        </el-form-item>
        <!-- <div v-if="from.merchantCode">
          <el-form-item label="交付中心编号:" prop="merchantCode">
            <span>{{ from.merchantCode }}</span>
          </el-form-item>
          <el-form-item label="交付中心名称:" prop="merchantName">
            <span> {{ from.merchantName }}</span>
          </el-form-item>
          <el-form-item label="交付中心地址:" prop="address">
            <span> {{ from.address }}</span>
          </el-form-item>
        </div> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button :loading="loading" type="primary" @click="affirmTransfer()">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchDeliverInfo, studentTransfer, belongDeliverAndAllDeliver } from "@/api/merchant/studentTransfer";
export default {
  name: "studentTransfer",
  //传值
  props: {
    //默认关闭
    transferVisible: {
      type: Boolean,
      default: false
    },
    //表单数据
    transferFrom: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      screenWidth: window.screen.width,
      from: {},
      loading: false,
      deliverCenterLists: {}, // 可指派交付中心列表
      deliverMerchantCode: '',
      value1: "",
      value2: '',
      time3: '',
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      pickerOptions2: {
        selectableRange: '00:00:00 - 23:59:59'
      },
      pickerOptions3: {
        selectableRange: '00:00:00 - 23:59:59'
      },
      reviewTimes: [],
      normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),
      needTime: false
    }
  },
  watch: {
    value1: function (val) {
      console.log(val)
      let now = new Date();
      let time = new Date(val).getTime()
      let today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      let timestampDate = new Date(time);
      if (today.getTime() === timestampDate.setHours(0, 0, 0, 0)) {
        // console.log('isToday');
        let nextTime = this.getThreeHour()
        console.log(nextTime, '==============')
        if (nextTime == '23:59:59' || nextTime == '00:00:00') {
          this.pickerOptions2 = {
            selectableRange: '00:00:00 - 23:59:59'
          }
        } else {
          this.pickerOptions2 = {
            selectableRange: `${nextTime} '- 23:59:59'`
          }
          this.value2 = this.getThreeHourNoSecond()
        }
      } else {
        console.log('false')
        this.pickerOptions2 = {
          selectableRange: '00:00:00 - 23:59:59'
        }
      }
    }
  },
  methods: {
    getThreeHour() {
      let nowDate = new Date();
      nowDate.setHours(nowDate.getHours() + 3);
      let hours = nowDate.getHours().toString().padStart(2, '0');
      let minutes = nowDate.getMinutes().toString().padStart(2, '0');
      let seconds = nowDate.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`
    },
    getThreeHourNoSecond() {
      let nowDate = new Date();
      nowDate.setHours(nowDate.getHours() + 3);
      let hours = nowDate.getHours().toString().padStart(2, '0');
      let minutes = nowDate.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`
    },
    //搜索交付中心
    search() {
      if (!this.from.name) {
        this.$message.error("请输入交付中心账号或交付中心编号！")
        return;
      }
      searchDeliverInfo(this.from.name).then(res => {
        if (res.data) {
          this.from = res.data
        } else {
          this.$message.error("交付中心不存在！")
        }
      })
    },
    //确认转移
    affirmTransfer() {
      // if (!this.from.name) {
      //   this.$message.error("请输入门店账号或门店编号！")
      //   return;
      // }
      // if (!this.from.merchantCode) {
      //   this.$message.warning("请确认交付中心信息是否正确！")
      //   this.search();
      //   return;
      // }
      if (!this.deliverMerchantCode) {
        this.$message.error("请选择交付中心！")
        this.search();
        return;
      }
      if (!this.value1) {
        return this.$message.error("请选择日期！")
      }
      if (!this.value2) {
        return this.$message.error("请选择时间！")
      }
      if (this.transferFrom.needTime && this.reviewTimes.length < 1) {
        return this.$message.error("请选择复习日期！")

      }
      if (this.transferFrom.needTime && !this.time3) {
        return this.$message.error("请选择复习时间！")

      }
      this.loading = true;
      let firstTime = `${this.value1} ${this.value2}`
      // let reviewWeek = JSON.stringify(this.reviewTimes.sort())
      let reviewWeek = this.reviewTimes.join(',')
      let reviewTime = this.time3
      // return console.log(firstTime);
      studentTransfer(this.transferFrom.studentCode, this.transferFrom.merchantCode, this.transferFrom.deliverMerchant, this.deliverMerchantCode, firstTime, reviewTime, reviewWeek,this.transferFrom.curriculumId).then((res) => {
        this.$message.success("转移成功！");
        console.log('1111111111111')
        console.log('转移成功')
        console.log('1111111111111')
        // this.$nextTick(()=>{
        //   this.$emit('initData')
        // });
        this.$parent.initData();
        this.loading = false;
        this.deliverMerchantCode = '';
        this.close();
      }).catch(err => {
        this.loading = false;
      })
    },
    close() {
      this.$refs.dataForm.resetFields()
      this.deliverMerchantCode = '';
      this.value1 = ''
      this.value2 = ''
      this.$emit("transferClose", false);
    },

    // 获取可指派交付中心列表
    async editDelivery() {
      await belongDeliverAndAllDeliver().then(res => {
        this.deliverCenterLists = res.data;
      })
    },
  }


}
</script>

<style lang="scss" scoped>
.studentTransferBox {
  ::v-deep .el-dialog__body {
    height: auto;
  }
}
</style>
