<template>
  <div class="dialog">
    <el-drawer title="数据查看" :visible.sync="lookstyle_" :direction="direction" @close="handleClose"
      style="margin-top:6vw">
      <el-row style="margin-top:1vw">
        <div style="margin-left:20vw" v-if="studyType==1">
          <span class="Second" v-if="studyStatus">
            <button type="button" style="forecolor: blue" @click="studyFn">
              <span style="color: skyblue">日</span>/
              <span>总</span>
            </button>
          </span>
          <span class="Second" v-else>
            <button type="button" style="forecolor: blue" @click="studyBtn">
              <span style="color: skyblue">总</span>/
              <span>日</span>
            </button>
          </span>
        </div>
        <div style="margin-left:20vw" v-if="studyType==2">
          <span class="Second" v-if="fuxiStatus">
            <button type="button" style="forecolor: blue" @click="fuxiBtn">
              <span style="color: skyblue">日</span>/
              <span>总</span>
            </button>
          </span>
          <span class="Second" v-else>
            <button type="button" style="forecolor: blue" @click="fuxiBtan">
              <span style="color: skyblue">总</span>/
              <span>日</span>
            </button>
          </span>
        </div>
        <!-- 学习 -->
        <el-row v-if="studyType==1">
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            时间：
            <span>{{ studyList1.studyTime }}</span>
          </el-col>
          <el-col class="paike">
            已购鼎英语学时：
            <span>{{ studyList1.totalCourseHours }}小时</span>
          </el-col>
          <el-col class="paike">
            剩余鼎英语学时：
            <span>{{ studyList1.leaveCourseHours }}小时</span>
          </el-col>
          <el-col class="paike">
            所学内容：
            <span>{{ studyList1.studyBooks }}</span>
          </el-col>
          <el-col class="paike">
            学习进度：
            <span>{{ studyList1.learnSchedule }}</span>
          </el-col>
          <el-col class="paike">
            复习词汇：
            <span>{{ studyList1.reviewWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘词汇：
            <span>{{ studyList1.forgetWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘率：
            <span>{{ studyList1.forgetRate }}%</span>
          </el-col>
          <el-col class="paike">
            学新词汇：
            <span>{{ studyList1.newWords }}个</span>
          </el-col>
          <el-col class="paike">
            学新遗忘词汇：
            <span>{{ studyList1.newForget }}个</span>
          </el-col>
          <el-col class="paike">
            学新遗忘词汇率：
            <span>{{ studyList1.newForgetRate }}%</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            今日共识记词汇（复习遗忘词汇+学新词汇）：
            <span>{{ studyList1.todayWords }}个</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px" v-if="studyStatus">
            教练评语：
            <span>{{ studyList1.feedback }}</span>
            <div style="height: 20vh"></div>
          </el-col>
        </el-row>
        <!-- 复习 -->
        <el-row v-if="studyType==2">
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            复习内容：
            <span>{{ studyList1.studyBooks }}</span>
          </el-col>
          <el-col class="paike">
            教练：
            <span>{{ teacher }}</span>
          </el-col>
          <el-col class="paike">
            复习词汇：
            <span>{{ studyList1.reviewWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘词汇：
            <span>{{ studyList1.forgetWords }}个</span>
          </el-col>
          <el-col class="paike">
            遗忘率：
            <span>{{ studyList1.forgetRate }}%</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px" v-if="fuxiStatus">
            教练评语：
            <span>{{ studyList1.feedback }}</span>
          </el-col>
        </el-row>
      </el-row>
    </el-drawer>
  </div>
</template>

<script>
import { getTotalStatistics } from "@/api/paikeManage/LearnManager";
export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    reviewStyle: {
      type: Boolean,
      default: false
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl"
    }
  },
  name: "lookDialog",
  data() {
    return {
      studyStatus: true,//学习反馈的，日和总按钮
      fuxiStatus: true,
      studyfuxiList1: "",
      studyList1: "",
      drawer: false,
      activeName: "second",
      day: true,
      always: false,
      firstStudy: true,
      aganStudy: false,
      BtnAdialog: true,
      BtnBdialog: false,
      studyList: {
        type: 2, //复习学习的反馈
        date: "",
        studentCode: ""
      },
      fuxiList: "",
      teacher: "",
      reviewTotal: {
        id: "",
        planId: "",
        type: 1
      },
      studyType: ""
    };
  },
  //计算属性
  computed: {
    lookstyle_: {
      get() {
        return this.reviewStyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("reviewDialog", v);
      }
    }
  },
  methods: {
    // 学习反馈的日切总
    async studyFn() {
      this.studyStatus = false;
      let res = await getTotalStatistics(this.reviewTotal);
      this.studyfuxiList1 = res.data;
    },
    // 学习反馈的总切日
    studyBtn() {
      this.studyStatus = true;
    },
    // 复习反馈的日切总
    async fuxiBtn() {
      this.fuxiStatus = false
      this.reviewTotal.type = 2
      let res = await getTotalStatistics(this.reviewTotal);
      this.studyfuxiList1 = res.data;
    },
    // 复习反馈的总切日
    fuxiBtan() {
      this.fuxiStatus = true;
    },
    // 关闭抽屉
    handleClose() {
      this.$emit("reviewDialog", false);
      (this.studyStatus = true), (this.fuxiStatus = true), (this.studyfuxiList1 = "");
      this.studyList1 = "";
    },
  }
};
</script>

<style lang="scss" scoped>
.borders {
  margin: 1vw 1vw;
  // width: 28vw;
  height: 28vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}

.paike {
  margin-bottom: 20px;
  margin-left: 2vw;

  &:first-child {
    margin-top: 0.5vw;
  }
}

div ::v-deep .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
}
::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}
::v-deep :focus {
  outline: 0;
}
::v-deep .el-drawer__body {
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
