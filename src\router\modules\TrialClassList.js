
import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const TrialClassListRouter = {
  path: '/pclass/TrialClassList',
  component: Layout,
  name: 'experiencestudentclass',
  meta: {
    perm: 'm:pclass:experiencestudentclass',
    title: '试课学员管理',
    icon: 'divisionList',
    noCache: true
  },
  children: [
    {
      path: '/pclass/TrialClassList',
      component: _import('pclass/TrialClassList'),
      name: 'TrialClassList',
      meta: {
        perm: 'm:pclass:TrialClassList',
        title: '试课列表',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: '/pclass/TrialClassListNopay',
      component: _import('pclass/TrialClassListNopay'),
      name: 'TrialClassListNopay',
      meta: {
        perm: 'm:pclass:TrialClassListNopay',
        title: '未支付试课列表',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: '/pclass/TrialClassOrderList',
      component: _import('pclass/TrialClassOrderList'),
      name: 'TrialClassOrderList',
      meta: {
        perm: 'm:pclass:TrialClassOrderList',
        title: '待完善试课信息表',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: '/pclass/StudentFeedback',
      component: _import('pclass/StudentFeedback'),
      name: 'StudentFeedback',
      meta: {
        perm: 'm:pclass:StudentFeedback',
        title: '试课满意度列表',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: '/pclass/shareProfitList',
      component: _import('pclass/shareProfitList'),
      name: 'shareProfitList',
      meta: {
        perm: 'm:pclass:shareProfitList',
        title: '试课奖励分润名单',
        icon: 'divisionList',
        noCache: true
      }
    },
    {
      path: '/pclass/trialClassConfigure',
      component: _import('pclass/trialClassConfigure'),
      name: 'TrialClassConfigure',
      meta: {
        perm: 'm:pclass:TrialClassConfigure',
        title: '试课预约人数配置',
        icon: 'divisionList',
        noCache: true
      }
    }
  ]
}

export default TrialClassListRouter
