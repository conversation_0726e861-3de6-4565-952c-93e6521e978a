<!-- 交付中心-一对多学员管理-学员列表-正课学员列表 -->
<template>
  <div>
    <!-- 查询栏 -->
    <el-card class="frame" shadow="never">
      <el-form label-width="122px" ref="searchNum" :model="searchNum" :inline="true" style="display: flex; flex-wrap: wrap">
        <el-form-item label="姓名:" prop="studentName" style="display: flex">
          <el-input v-model.trim="searchNum.studentName" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="学员编号:" prop="studentCode" style="display: flex">
          <el-input v-model.trim="searchNum.studentCode" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="班级:" prop="classId">
          <BaseElSelectLoadmore v-model="searchNum.classId" valueProp="id" labelProp="className" :searchFunc="getAllClassList"></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="门店:" prop="merchantCodeOrMerchantPhone">
          <el-input v-model.trim="searchNum.merchantCodeOrMerchantPhone" clearable placeholder="请输入门店账号或手机号"></el-input>
        </el-form-item>
        <template v-if="isAdmin">
          <el-form-item label="交付中心编号:" prop="deliverMerchant">
            <el-input v-model.trim="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号"></el-input>
          </el-form-item>
          <el-form-item label="交付中心名称:" prop="deliverName">
            <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="上课教练:" prop="teacherId" style="display: flex">
          <BaseElSelectLoadmore v-model="searchNum.teacherId" valueProp="id" labelProp="name" :searchFunc="getTeacherList"></BaseElSelectLoadmore>
        </el-form-item>
        <el-form-item label="课程类型:" prop="curriculumId" style="display: flex">
          <el-select v-model="searchNum.curriculumId" filterable clearable placeholder="请选择">
            <el-option v-for="item in curriculumList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间:" prop="searchTimeRange">
          <el-date-picker
            v-model="searchTimeRange"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 360px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
          <el-button icon="el-icon-refresh-left" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      ref="configurationTable"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :width="item.value == 'operate' ? 550 : 160"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }" v-if="needTableSlotProp.includes(item.value)">
          <div v-if="item.value == 'referrerRemark'">
            <span>{{ row.referrerRemark || '无' }}</span>
            <i class="el-icon-edit" style="color: #66b1ff; margin-left: 10px" @click="handleReferrerRemarkClick(row)"></i>
          </div>
          <div v-else-if="item.value == 'totalDeliverHours'">
            <span>{{ row.totalDeliverHours }}</span>
            <span @click="handleTotalDeliverHoursClick(row)" style="font-size: 12px; color: #46a6ff; margin-left: 10px; cursor: pointer">详情</span>
          </div>
          <div v-else-if="['className', 'deliverName', 'deliverMerchant'].includes(item.value)">{{ row[item.value] || '无' }}</div>
          <div v-else-if="item.value == 'teachingType'">{{ commonFormat(teachingTypeList, row[item.value]) }}</div>
          <div v-else-if="item.value == 'grade'">{{ commonFormat(gradeList, row[item.value]) }}</div>
          <div v-else-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="handleAssignClassClick(row)">更换班级</el-button>
            <el-button type="primary" size="mini" @click="handleCourseScheduleClick(row)">查看课程表</el-button>
            <el-button type="primary" size="mini" @click="handleLessonTestReportClick(row)">试课报告</el-button>
            <el-button type="primary" size="mini" @click="handleClassConnectionClick(row.studentContactInfoId)">上课信息对接表</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 推荐人备注编辑弹窗 -->
    <el-dialog
      title="修改备注"
      :visible.sync="referrerRemarkDialogVisible"
      :width="screenWidth > 1300 ? '30%' : '90%'"
      center
      :class="screenWidth > 1300 ? 'notes' : 'notes-phone'"
    >
      <div style="display: flex; align-items: center; margin-top: 30px">
        <span>推荐人备注：</span>
        <el-input v-model="referrerRemarkData.referrerRemark" placeholder="请输入备注" style="width: 50%"></el-input>
      </div>
      <span slot="footer">
        <el-button @click="referrerRemarkDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleReferrerRemarkEditClick">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 已购交付学时详情   -->
    <el-dialog
      :title="totalDeliverHoursData.length > 0 ? totalDeliverHoursData[0].name + '的学时详情' : '学时详情'"
      :visible.sync="handleTotalDeliverHoursClickVisible"
      :width="screenWidth > 1300 ? '60%' : '90%'"
    >
      <el-table
        v-loading="totalDeliverHoursLoading"
        :data="totalDeliverHoursData"
        style="width: 100%; margin-top: 20px"
        :header-cell-style="getRowClass"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column prop="totalDeliverHours" label="已购交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="planDeliverHours" min-width="160" header-align="center">
          <template slot="header">
            <span>已排交付学时</span>
            <el-popover placement="top" width="400" align="center">
              <p>已排交付学时=已排已上交付学时+已排未上交付学时</p>
              <i class="el-icon-question" slot="reference"></i>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="planAndStudyDeliverHours" label="已排已上交付学时" width="150" header-align="center"></el-table-column>
        <el-table-column prop="planAndNoStudyDeliverHours" label="已排未上交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="lessonsDeliverWithoutFeedback" label="已上未反馈学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="haveDeliverHours" label="剩余可排交付学时" min-width="120" header-align="center"></el-table-column>
        <el-table-column prop="refundHours" label="已退学时" min-width="120" header-align="center"></el-table-column>
      </el-table>
    </el-dialog>

    <!-- 更换班级弹窗 -->
    <StudentListAssignClassDialog :visible.sync="assignClassDialogVisible" :value-data="assignClassFormData" :course-type="2" @submit="handleAssignClassSubmitClick" />

    <!-- 试课报告弹窗 -->
    <StudyScheduleDataLook :visible.sync="testReportDialogVisible" :loading="testReportLoading" :data-list="testReportData" ref="StudyScheduleDataLook" />
    <StudyScheduleMathDataLook :visible.sync="testReportDialogMathVisible" :loading="testReportMathLoading" :data-list="testReportMathData" ref="StudyScheduleMathDataLook" />

    <!-- 上课信息对接表弹窗 -->
    <StudentListClassConnectionDialog :visible.sync="classConnectionDialogVideible" :value-data="classConnectionData" />

    <!-- 表头设置弹窗 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import BaseElSelectLoadmore from './components/studentList/BaseElSelectLoadmore.vue';
  import StudentListAssignClassDialog from './components/studentList/StudentListAssignClassDialog.vue';
  import StudyScheduleDataLook from './components/studySchedule/StudyScheduleDataLook.vue';
  import StudyScheduleMathDataLook from './components/studySchedule/StudyScheduleMathDataLook.vue';
  import StudentListClassConnectionDialog from './components/studentList/StudentListClassConnectionDialog.vue';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { selAllTeacher, GradeType } from '@/api/studentClass/changeList';
  import { getFeedbackInfo, getMathFeedbackInfo } from '@/api/oneToManyClass/studySchedule';
  import { getAllOneToManyCurriculumType } from '@/api/oneToManyClass/classList';
  import { getStudentDeliverHoursVo, getStudentContactInfoDetail, modifyRemarks } from '@/api/paikeManage/LearnManager';
  import { getAllClass, getStudentFormalData, setAssignClassData } from '@/api/oneToManyClass/studentList';
  import ls from '@/api/sessionStorage';
  export default {
    name: 'StudentListFormal',
    components: {
      HeaderSettingsDialog,
      BaseElSelectLoadmore,
      StudentListAssignClassDialog,
      StudyScheduleDataLook,
      StudyScheduleMathDataLook,
      StudentListClassConnectionDialog
    },
    data() {
      return {
        isAdmin: false,
        isDeliveryCenter: false,

        // 搜索栏
        searchNum: {},
        searchTimeRange: [],
        curriculumList: [],

        // 列表属性弹框
        HeaderSettingsStyle: false,
        tableHeaderList: [],
        headerSettings: [
          { name: '姓名', value: 'name' },
          { name: '学员编号', value: 'studentCode' },
          { name: '期望上课时间', value: 'expectsTime' },
          { name: '所属交付小组', value: 'teamName' },
          { name: '创建时间', value: 'createTime' },
          { name: '门店手机号', value: 'merchantPhone' },
          { name: '门店负责人', value: 'merchantRealName' },
          { name: '所属班级', value: 'className' },
          { name: '上课教练', value: 'studyTeacherName' },
          { name: '代课教练', value: 'replaceTeacherName' },
          { name: '门店账号', value: 'merchantCode' },
          { name: '课程类型', value: 'courseType' },
          { name: '授课方式', value: 'teachingType' },
          { name: '年级', value: 'grade' },
          { name: '操作', value: 'operate' },
          { name: '推荐人手机号', value: 'referrerPhone' },
          { name: '推荐人备注', value: 'referrerRemark' },
          { name: '剩余学时', value: 'haveCourseHours' },
          { name: '联系方式', value: 'phone' },
          { name: '已购学时', value: 'totalCourseHours' },
          { name: '剩余未交付课时', value: 'remainderCourseHours' },
          { name: '已购交付学时', value: 'totalDeliverHours' }
        ],

        // 表格数据
        tableLoading: false,
        needTableSlotProp: ['className', 'deliverName', 'deliverMerchant', 'teachingType', 'grade', 'referrerRemark', 'totalDeliverHours', 'operate'],
        tableList: [],
        teachingTypeList: [
          { label: '远程', value: '1' },
          { label: '线下', value: '2' },
          { label: '远程和线下', value: '3' }
        ],
        gradeList: [],

        // 分页器数据
        pagination: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },

        screenWidth: window.screen.width,

        // 修改推荐人备注弹窗数据
        referrerRemarkDialogVisible: false,
        referrerRemarkData: {},

        // 已购交付学时弹窗数据
        handleTotalDeliverHoursClickVisible: false,
        totalDeliverHoursLoading: false,
        totalDeliverHoursData: [],

        // 更换班级弹窗数据
        assignClassDialogVisible: false,
        assignClassFormData: {},

        // 试课报告弹窗数据
        testReportDialogVisible: false,
        testReportLoading: false,
        testReportData: {},
        testReportDialogMathVisible: false,
        testReportMathLoading: false,
        testReportMathData: {},

        // 上课信息对接表数据
        classConnectionDialogVideible: false,
        classConnectionData: {}
      };
    },
    created() {
      // 是否是管理员(dxAdmin+187)
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';

      // 是否是交付中心
      this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';

      if (this.isAdmin) {
        this.headerSettings.splice(4, 0, { name: '交付中心名称', value: 'deliverName' }, { name: '交付中心编号', value: 'deliverMerchant' });
      }

      // 获取表头设置
      this.getHeaderlist();
    },
    mounted() {
      this.getGradeList();
      this.getCurriculumList();
      this.getStudentFormalList();
    },
    methods: {
      // 获取教练老师下拉列表
      getTeacherList(selectQuery) {
        return new Promise((resolve, reject) => {
          selAllTeacher(selectQuery)
            .then((res) => {
              resolve(
                res.data.data.map((item) => {
                  return { value: item.value, label: item.label };
                })
              );
            })
            .catch((err) => {
              reject(err);
            });
        });
      },
      // 获取课程类型下拉列表
      getCurriculumList() {
        getAllOneToManyCurriculumType().then((res) => {
          this.curriculumList = res.data.map((item) => {
            return { label: item.enName, value: item.id };
          });
        });
      },
      // 获取年级列表
      getGradeList() {
        GradeType().then((res) => {
          this.gradeList = res.data.map((item) => {
            return {
              value: item.value,
              label: item.label
            };
          });
        });
      },
      // 获取班级下拉列表
      getAllClassList(selectQuery) {
        return new Promise((resolve, reject) => {
          getAllClass({ ...selectQuery, type: 2 })
            .then((res) => {
              const allClass = res.data.data || [];
              resolve(
                allClass.map((item) => {
                  return {
                    label: item.className,
                    value: item.id
                  };
                })
              );
            })
            .catch((err) => {
              reject(err);
            });
        });
      },
      // 打开列表属性弹窗
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      // 同步列表属性弹窗开启状态
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'StudentListFormal',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'StudentListFormal'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = [];
            JSON.parse(res.data.value).forEach((item) => {
              if (item) {
                this.tableHeaderList.push(item);
              }
            });
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then(() => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 表格动态class
      getRowClass({ rowIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 查询
      search() {
        this.pagination.pageNum = 1;
        this.getStudentFormalList();
      },
      // 重置
      reset() {
        this.searchTimeRange = [];
        this.searchNum = {};
        this.pagination.pageNum = 1;
        this.pagination.pageSize = 10;
        this.getStudentFormalList();
      },
      // 更改每页条数
      handleSizeChange(val) {
        this.pagination.pageSize = val;
        this.getStudentFormalList();
      },
      //更改当前页
      handleCurrentChange(val) {
        this.pagination.pageNum = val;
        this.getStudentFormalList();
      },
      // 分页查询一对多正课学员列表
      getStudentFormalList() {
        this.tableLoading = true;
        if (this.searchTimeRange.length == 2) {
          this.searchNum.startTime = this.searchTimeRange[0];
          this.searchNum.endTime = this.searchTimeRange[1];
        }
        this.searchNum.pageNum = this.pagination.pageNum;
        this.searchNum.pageSize = this.pagination.pageSize;
        getStudentFormalData(this.searchNum)
          .then((res) => {
            this.tableList = res.data.data;
            this.pagination.total = Number(res.data.totalItems);
            this.tableLoading = false;
          })
          .catch(() => (this.tableLoading = false));
      },
      // 处理推荐人备注点击事件
      handleReferrerRemarkClick(row) {
        this.referrerRemarkData = {
          referrerRemark: row.referrerRemark,
          studentCode: row.studentCode,
          deliverCode: row.deliverMerchant
        };
        this.referrerRemarkDialogVisible = true;
      },
      // 修改推荐人备注
      handleReferrerRemarkEditClick() {
        if (this.referrerRemarkData.referrerRemark) {
          modifyRemarks(this.referrerRemarkData).then(() => {
            this.$message.success('操作成功');
            this.referrerRemarkDialogVisible = false;
            this.getStudentFormalList();
          });
        } else {
          this.$message.error('请填写推荐人备注');
        }
      },
      // 处理已购交付学时点击事件
      handleTotalDeliverHoursClick(row) {
        let paramdata = {
          deliverMerchant: row.deliverMerchant,
          merchantCode: row.merchantCode,
          studentCode: row.studentCode,
          curriculumId: row.curriculumId
        };
        this.handleTotalDeliverHoursClickVisible = true;
        this.totalDeliverHoursLoading = true;
        getStudentDeliverHoursVo(paramdata)
          .then((res) => {
            this.totalDeliverHoursData = [];
            res.data.name = row.name;
            this.totalDeliverHoursData.push(res.data);
            this.totalDeliverHoursLoading = false;
          })
          .catch(() => {
            this.totalDeliverHoursLoading = false;
          });
      },
      // 处理更换班级点击事件
      handleAssignClassClick(row) {
        this.assignClassFormData = row;
        this.assignClassFormData.gradeName = this.commonFormat(this.gradeList, row.grade);
        this.assignClassFormData.oldClassId = row.classId;
        this.assignClassDialogVisible = true;
      },
      // 处理更换班级弹窗确定点击事件
      handleAssignClassSubmitClick(assignClassFormData) {
        let submitData = {
          flag: true,
          oldClassId: assignClassFormData.oldClassId,
          studentId: assignClassFormData.id,
          studentCode: assignClassFormData.studentCode,
          studentName: assignClassFormData.name,
          classId: assignClassFormData.classId,
          merchantCode: assignClassFormData.merchantCode,
          merchantName: assignClassFormData.merchantName,
          curriculumId: assignClassFormData.curriculumId
        };
        if (submitData.oldClassId == submitData.classId) {
          this.$message.error('更换的班级不可与当前班级一致');
          return;
        }
        console.log('更换班级弹窗提交数据：', submitData);
        setAssignClassData(submitData).then(() => {
          this.$message.success('操作成功');
          this.assignClassDialogVisible = false;
          this.getStudentFormalList();
        });
      },
      // 处理查看课程表点击事件
      handleCourseScheduleClick(row) {
        if (row.classCode) {
          this.$router.push({
            path: '/oneToManyClass/studySchedule',
            query: {
              classCode: row.classCode
            }
          });
        } else {
          this.$message.warning('该学生没有所属班级');
        }
      },
      // 处理试课报告点击事件
      handleLessonTestReportClick(row) {
        if (row.curriculumCode !== 'MATH') {
          const { id, grade } = row;
          this.testReportDialogVisible = true;
          this.testReportLoading = true;
          getFeedbackInfo({ id })
            .then((res) => {
              if (!res.data) {
                this.testReportDialogVisible = false;
                return;
              }
              this.testReportData = res.data;

              // 年级名称取分页的数据
              this.testReportData.gradeName = this.commonFormat(this.gradeList, grade);
              let studentNames = '';
              this.testReportData.deliverClassStudentList.forEach((each) => {
                studentNames += each.studentName + '（' + each.studentCode + '）、';
              });
              this.testReportData.studentNames = studentNames.substring(0, studentNames.length - 1);
              this.testReportLoading = false;
            })
            .catch(() => {
              this.testReportDialogVisible = false;
            });
        } else {
          this.testReportDialogMathVisible = true;
          this.testReportMathLoading = true;
          getMathFeedbackInfo({ id: row.id, isShow: 1, courseId: '', courseType: '' })
            .then((res) => {
              console.log('数学反馈', res.data);
              if (!res.data) {
                this.testReportDialogMathVisible = false;
                this.$message.error('没有获取到排课学习时间信息');
                return;
              }
              this.testReportMathData = res.data;
              this.dataLookMathLoading = false;
            })
            .catch((err) => {
              console.error('获取数学反馈信息失败', err);
              this.testReportDialogMathVisible = false;
            });
        }
      },
      // 处理上课信息对接表点击事件
      handleClassConnectionClick(id) {
        getStudentContactInfoDetail({ id }).then((res) => {
          this.classConnectionData = res.data;
          this.classConnectionDialogVideible = true;
        });
      },
      commonFormat(array, value) {
        let item = array.find((item) => item.value == value);
        if (item) {
          return item.label;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep.notes-phone {
    .el-dialog__body {
      padding: 0 2vw;
      height: 15vw !important;
    }
  }

  ::v-deep.notes {
    .el-dialog__body {
      padding: 0 2vw;
      height: 4vw !important;
    }
  }
</style>
