<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" placeholder="请输入学员编号" clearable disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" placeholder="请输入登录账号" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" placeholder="请输入姓名" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="16" :xs="24">
          <el-form-item>
            <el-form-item label="销课时间：">
              <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd hh:mm:ss" v-model="value1" clearable
                type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item>
        <el-button type="primary" icon="el-icon-search" v-loading="exportLoading " @click="exportFlow()" v>导出</el-button>
      </el-form-item> -->
    </el-form>
    <el-button type="primary" @click="headerList()" style="margin:20px 0">列表显示属性</el-button>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;" row-key="id" border
              default-expand-all :tree-props="{list: 'children', hasChildren: 'true'}">
      <el-table-column prop="studentCode" label="学员编号"   sortable></el-table-column>
      <el-table-column prop="loginName" label="登录账号"  sortable></el-table-column>
      <el-table-column prop="realName" label="姓名"  sortable></el-table-column>
      <el-table-column prop="startTime" label="开始时间"   sortable></el-table-column>
      <el-table-column prop="endTime" label="结束时间"  sortable></el-table-column>
      <el-table-column prop="beforeHours" label="账户学时（节）"  sortable></el-table-column>
      <el-table-column prop="userHours" label="本次销课（节）"   sortable></el-table-column>
      <el-table-column prop="remark" label="销课说明" width="300" show-overflow-tooltip></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems"/>
  </div>
</template>

<script>
import studentMarketingApi from "@/api/student/areasStudentCourseList";
import { pageParamNames } from "@/utils/constants";
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";

import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name:'areasStudentCourseFlow',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        merchantCode: '',
        startDate: '',
        endDate: '',
        loginName: '',
        realName: ''
      },
      value1: '',
      exportLoading: false,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '登录账号',
          value: 'loginName'
        },
        {
          name: '姓名',
          value: 'realName'
        },
        {
          name: '开始时间',
          value: 'startTime'
        },
        {
          name: '结束时间',
          value: 'endTime'
        },
        {
          name: '账户学时（节）',
          value: 'beforeHours'
        },
        {
          name: '本次销课（节）',
          value: 'userHours'
        },
        {
          name: '剩余学时（节）',
          value: 'afterHours'
        },
        {
          name: '销课说明',
          value: 'remark'
        }],

      tableHeaderList: [], // 获取表头数据

    };
  },
  created() {
    this.dataQuery.studentCode = window.localStorage.getItem("studentCode");
    this.dataQuery.merchantCode = window.localStorage.getItem("merchantCode");
    this.fetchData01();
    this.getHeaderlist();
  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.startDate = e[0]
      this.dataQuery.endDate = e[1]
    },

    //重置
    rest() {
      this.dataQuery.studentCode = '';
      this.dataQuery.loginName = '';
      this.dataQuery.realName = '';
      this.value1 = '';
      this.fetchData01()
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
        if (that.value1.length > 0) {
          that.dataQuery.startDate = that.value1[0]
          that.dataQuery.endDate = that.value1[1]
        } else {
          that.dataQuery.startDate = ''
          that.dataQuery.endDate = ''
        }
      } else {
        that.dataQuery.startDate = ''
        that.dataQuery.endDate = ''
      }
      that.tableLoading = true
      studentMarketingApi.salesRecord(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //导出
    exportFlow() {
      const that = this;
      that.exportLoading = true;
      studentMarketingApi.exportAreasStudentRecord(that.dataQuery).then(res => {
        const url = window.URL.createObjectURL(res);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url; // 获取服务器端的文件名
        link.setAttribute("download", "学员销毁学时表.xls");
        document.body.appendChild(link);
        link.click();
        that.exportLoading = false;
      })

    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "areasStudentCourseFlow",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'areasStudentCourseFlow'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}</style>
