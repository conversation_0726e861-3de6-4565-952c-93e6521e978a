// 一对多学员管理 路由配置
import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const oneToManyClassRouter = {
  path: '/oneToManyClass',
  component: Layout,
  name: 'oneToManyClass',
  meta: {
    perm: 'm:oneToManyClass',
    title: '一对多学员管理',
    icon: 'studentList',
    noCache: false
  },
  children: [
    {
      path: 'classTimeManagement',
      component: _import('oneToManyClass/classTimeManagement'),
      name: 'classTimeManagement',
      meta: {
        perm: 'm:oneToManyClass:classTimeManagement',
        title: '班级上课时间管理',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: 'newClassList',
      component: _import('oneToManyClass/newClassList'),
      name: 'newClassList',
      meta: {
        perm: 'm:oneToManyClass:newClassList',
        title: '新班级列表',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: 'classList',
      component: _import('oneToManyClass/ClassList'),
      name: 'ClassList',
      meta: {
        perm: 'm:oneToManyClass:classList',
        title: '班级列表',
        icon: 'divisionList',
        noCache: false
      }
    },
    {
      path: 'studentList',
      component: _import('oneToManyClass/StudentList'),
      name: 'StudentList',
      meta: {
        perm: 'm:oneToManyClass:studentList',
        title: '学员列表',
        icon: 'studentList',
        noCache: false
      }
    },
    {
      path: 'studySchedule',
      component: _import('oneToManyClass/StudySchedule'),
      name: 'StudySchedule',
      meta: {
        perm: 'm:oneToManyClass:studySchedule',
        title: '学习课程表',
        icon: 'student_course_progress',
        noCache: false
      }
    },
    {
      path: 'pendingCompletionClassInfo',
      component: _import('oneToManyClass/PendingCompletionClassInfo'),
      name: 'PendingCompletionClassInfo',
      meta: {
        perm: 'm:oneToManyClass:pendingCompletionClassInfo',
        title: '试课单与待完善上课信息对接表',
        icon: 'student_course_progress',
        noCache: false
      }
    }
  ]
};

export default oneToManyClassRouter;
