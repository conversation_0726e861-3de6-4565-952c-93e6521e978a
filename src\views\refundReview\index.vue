<!--甄选退款审核-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="80px">
        <!-- 1 -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="学员编号:">
              <el-input v-model="searchNum.studentCode" :disabled="!!stuudentCode" clearable placeholder="请输入" size="small" style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="isAdmin" :span="8">
            <el-form-item label="门店编号:">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请输入门店编号" size="small" style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核状态:">
              <el-select v-model="searchNum.checkStatus" clearable placeholder="请选择" style="width: 13vw">
                <el-option label="待审核" value="0"></el-option>
                <el-option label="已审核或该审核" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 2 -->
        <el-row>
          <el-col :span="isAdmin ? 8 : 16">
            <el-form-item label="时间筛选:">
              <el-date-picker
                v-model="timeAll"
                style="width: 18vw"
                size="small"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                align="right"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
            <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">导出</el-button>
          </el-col>
        </el-row>
        <!-- 3 -->
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :width="item.value == 'operate' ? 200 : ''"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="danger" v-if="row.status == 11" size="mini" @click="LeaveBtn(row)">审核</el-button>
          </div>
          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ status(row) }}</span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableIshow"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <!-- 弹窗 -->
    <el-dialog title="审批" :visible.sync="open" width="70%" @close="cancel">
      <el-form label-width="120px" ref="checkForm" :model="checkInfo" :rules="rules">
        <el-form-item label="审批原因：">
          <el-input v-model.trim="checkInfo.auditReason" placeholder="请输入审批原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkWorkOrder">通过</el-button>
        <el-button @click="onDisagree">拒绝</el-button>
      </div>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { deletePlanStudy, getFeedbackInfo } from '@/api/paikeManage/LearnManager';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';

  import { getAdjustInfo, getRefundReviewtable, checkRefundReview, studentStudyExport, getRefundReview, getZXRefundReview } from '@/api/paikeManage/checkOrder';
  import ls from '@/api/sessionStorage';

  export default {
    name: 'checkOrder',
    components: {
      HeaderSettingsDialog
    },
    data() {
      return {
        checkInfo: {
          auditReason: '',
          id: '',
          studentCode: '',
          merchantCode: ''
        },
        rules: {
          auditReason: [{ required: true, message: '审批原因不能为空', trigger: 'blur' }],
          id: [{ required: true, message: '请先选择', trigger: 'blur' }]
        },
        open: false,
        stuudentCode: null,
        dataLookerStyle: false,
        // 日期组件
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                const temp = new Date();
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                const temp = new Date();
                temp.setTime(temp.getTime() - 3600 * 1000 * 24);
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '最近七天',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        },
        qjstartTime: '',
        qjendTime: '',
        tableIshow: true,
        redLeave: [],
        lookstyle: false,
        LeaveStyle: false,
        classCardstyle: false, //抽屉状态
        LeaveId: '',
        searchNum: {
          checkStatus: '',
          endTime: '',
          startTime: '',
          studentCode: '',
          pageNum: 1,
          pageSize: 10,
          merchantCode: ''
        }, //搜索参数
        tableLoading: false,
        direction: 'rtl', //超哪边打开
        teacher: '',
        timeAll: [],
        contentType: '',
        total: null,
        luyouclassCard: [],
        leaveApplication: {
          id: '',
          type: 1
        },
        getFeedback: {
          //详情的参数
          id: '',
          type: '1'
        },
        isAdmin: false,
        auditReason: '',
        exportLoading: false,

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '门店编号',
            value: 'merchantCode'
          },
          {
            name: '所属门店',
            value: 'merchantName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '学员姓名',
            value: 'studentName'
          },

          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '说明',
            value: 'remark'
          },
          {
            name: '退款/拒绝原因',
            value: 'auditReason'
          },
          {
            name: '审核状态',
            value: 'status'
          }
        ],

        tableHeaderList: [] // 获取表头数据
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      this.initData();
      this.stuudentCode = this.$route.query.classCard;
      this.searchNum.planId = this.$route.query.planId;
      this.searchNum.studentCode = this.stuudentCode ? this.stuudentCode : '';
      this.getHeaderlist();
    },
    methods: {
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      cancel() {
        this.open = false;
        this.reset();
      },
      reset() {
        this.checkInfo = {
          auditReason: '',
          id: '',
          studentCode: '',
          merchantCode: ''
        };
      },
      /*
       * 同意审核
       * */
      checkWorkOrder() {
        if (this.checkInfo.auditReason == '') {
          this.$message.error('请输入退款原因');
          return;
        }
        getZXRefundReview(this.checkInfo.id, this.checkInfo.studentCode, this.checkInfo.merchantCode).then((res) => {
          if (!res.data) {
            checkRefundReview(this.checkInfo.id, true, this.checkInfo.auditReason).then((res) => {
              this.$message({
                type: 'success',
                message: '已通过审核'
              });
              this.open = false;
              this.initData();
            });
          }
        });
      },
      /*
       * 不同意批量审批
       * */
      onDisagree() {
        checkRefundReview(this.checkInfo.id, false, this.checkInfo.auditReason).then((res) => {
          this.$message({
            type: 'error',
            message: '已拒绝退课'
          });
          this.open = false;
          this.initData();
        });
      },

      rest() {
        this.searchNum.studentCode = '';
        this.searchNum.checkStatus = '';
        this.searchNum.merchantCode = '';
        this.timeAll = '';
        this.initData01();
      },

      // 搜索
      initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      dataLooker(v) {
        this.dataLookerStyle = v;
      },
      statusClass(status) {
        switch (status) {
          case 11:
            return 'error';
          case 8:
            return '';
          case 9:
            return '';
          default:
            return 'normal';
        }
      },
      // 学习课程表请假处理
      async LeaveBtn(row) {
        this.reset();
        this.checkInfo.id = row.id;
        this.checkInfo.studentCode = row.studentCode;
        this.checkInfo.merchantCode = row.merchantCode;
        this.open = true;
      },
      async initData() {
        // 判断为null的时候赋空
        if (!this.timeAll) {
          this.timeAll = [];
        }
        this.tableLoading = true;
        this.searchNum.startTime = this.timeAll[0];
        this.searchNum.endTime = this.timeAll[1];
        this.tableLoading = true;
        let { data } = await getRefundReviewtable(this.searchNum);
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.luyouclassCard = data.data;
      },
      status(val) {
        switch (val.status) {
          case 8:
            return '已取消';
          case 9:
            return '已拒绝';
          case 11:
            return '待审核';
          default:
            return '已审核或未该审核';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      //定义导出Excel表格事件
      exportExcel() {
        this.exportLoading = true;
        studentStudyExport(this.searchNum).then((res) => {
          console.log(window.URL.createObjectURL(res));
          const url = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url; //获取服务器端的文件名
          link.setAttribute('download', '学习课程表.xls');
          document.body.appendChild(link);
          link.click();
          this.exportLoading = false;
        });
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'checkOrder',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'checkOrder'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style scoped>
  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  body {
    background-color: #f5f7fa;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
</style>
