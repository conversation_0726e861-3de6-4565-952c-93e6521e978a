// 一对多-承单量配置相关接口
import request from '@/utils/request';
/**
 * 获取一对多-承单量配置详情
 * @param {*} id
 * @returns
 */
export const getOrderSettingDetailData = (id) => {
  // return request({
  //     url: '/deliver/web/orderSetting/getOrderSettingDetail',
  //     method: 'GET',
  //     params: { id }
  // });
  return Promise.resolve({
    code: 20000,
    success: true,
    message: '操作成功！',
    data: {
      id: 1,
      name: 'test',
      type: 1,
      typeName: 'test',
      status: 1,
      statusName: 'test',
      createTime: '2021-09-01 00:00:00',
      updateTime: '2021-09-01 00:00:00'
    }
  });
};
