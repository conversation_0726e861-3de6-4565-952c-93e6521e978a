<template>
  <div class="app-container">
    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;">
      <el-table-column prop="scheduleName" label="课程名称" width="120"></el-table-column>
      <el-table-column prop="word" label="单词">
        <template slot-scope="scope">
          <el-input type="textarea" resize="none" :rows="4" v-model="scope.row.word"></el-input>
        </template>
        <!--        <el-input type="textarea" resize="none" :rows="4" v-model="tableData.word"></el-input>-->
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import studentWordViewApi from "@/api/student/studentWordViewApi";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'

export default {
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        wordReviewId: '',
        studentCode: '',
        loginName: '',
        merchantCode: '',
        realName: ''
      },
    }
  },
  created() {
    // this.dataQuery.wordReviewId=this.$route.query.id;
    // 获取上个页面的学员编号
    this.dataQuery.wordReviewId = ls.getItem('id')
    this.fetchData();


  },
  methods: {
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 查询列表
    fetchData() {
      const that = this;
      that.tableLoading = true
      studentWordViewApi.studentRecord(that.dataQuery.wordReviewId).then(res => {
        that.tableData = res.data.studentWords
          ;
        console.log(that.tableData)
        console.log(that.tableData.word)
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //查看进度
    enterChildrenList(studentCode) {
      ls.setItem('courseStudentCode', studentCode);
      const that = this;
      that.$router.push({
        path: "/student/studentCourseProgress",
        query: {
          studentCode: studentCode
        }
      });
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}</style>
