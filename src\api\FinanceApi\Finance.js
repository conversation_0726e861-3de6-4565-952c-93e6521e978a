import request from '@/utils/request'

// 个人流水
export const getProfitOrderPage = (data) => {
  return request({
    url: '/deliver/web/finance/getProfitOrderPage',
    method: 'GET',
    params: data
  })
}

// 渠道交付中心订单列表
export const getDeliverOrderPage = (data) => {
  return request({
    url: '/deliver/web/finance/getDeliverOrderPage',
    method: 'GET',
    params: data
  })
}

// 渠道交付中心订单列表统计
export const getDeliverOrderStat = (data) => {
  return request({
    url: '/deliver/web/finance/getDeliverOrderStat',
    method: 'GET',
    params: data
  })
}

// 渠道交付中心订单列表统计
export const studentDeliverCourseInfo = (studentCode,merchantCode,deliverMerchantCode) => {
  return request({
    url: '/deliver/web/finance/student/course/info',
    method: 'GET',
    params: {'studentCode':studentCode,'merchantCode':merchantCode,'deliverMerchantCode':deliverMerchantCode}
  })
}


// 集中交付清单列表
export const pageDeliverHoursList = (data) => {
  return request({
    url: '/deliver/web/finance/pageDeliverHoursList',
    method: 'GET',
    params: data
  })
}
export const pageStudentDeliverFlowChanges = (data) => {
  return request({
    url: '/znyy/deliver/student/school/deliverFlowChang',
    method: 'GET',
    params: data
  })
}

export const backDeliver = (id,remark) => {
  return request({
    url: '/znyy/deliver/student/back/deliver',
    method: 'PUT',
    params: {'flowId':id,'remark':remark}
  })
}
export const checkbackDeliver = (id,pass) => {
  return request({
    url: '/znyy/deliver/student/check/back/deliver',
    method: 'PUT',
    params: {'flowId':id,'pass':pass}
  })
}

export const agentDeliverFinance = (data) => {
  return request({
    url: '/deliver/web/finance/agent/deliver',
    method: 'put',
    params: data
  })
}
