import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const trainingCenterRouter = {
  path: '/personalCenter',
  component: Layout,
  redirect: '/personalCenter',
  meta: {
    perm: 'm:trainingCenter:learnCenter',
    title: '学习中心',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: 'personalCenter',
      component: _import('trainingCenter/learnCenter/learnCenter'),
      name: 'learnCenter',
      meta: {
        perm: 'm:trainingCenter:learnCenter',
        title: '学习中心',
        icon: 'studentList',
        noCache: false
      }
    }
  ]
};

export default trainingCenterRouter;
