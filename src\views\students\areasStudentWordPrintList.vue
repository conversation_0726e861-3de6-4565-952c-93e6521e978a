<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程类型:">
            <el-select v-model="dataQuery.courseContentType" filterable value-key="value" placeholder="请选择">
              <el-option
                v-for="(item, index) in [{ value: 'Word', label: '单词' }, { value: 'Reading', label: '阅读理解' }, { value: 'PrintClose', label: '结业报告' }]"
                :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="打印状态：">
            <el-select v-model="dataQuery.isEnable" placeholder="全部" style="width: 185px;">
              <el-option v-for="(item, index) in [{ value: 1, label: '已打印' }, { value: 0, label: '未打印' }]" :key="index"
                :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="14" :xs="24">
          <el-form-item>
            <el-form-item label="结束时间：">
              <el-date-picker style="width: 100%;" value-format="yyyy-MM-dd hh:mm:ss" clearable v-model="value1"
                type="daterange" align="right" unlink-panels range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-form-item>
        </el-col>
        <el-col :span="10" :xs="24" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button type="primary" icon="el-icon-refresh" @click="fetchData03()">刷新</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-button type="primary" @click="headerList()" style="margin:20px 0 20px">列表显示属性</el-button>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center" :width="item.value == 'operate' ? 180 : ''" sortable>
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" icon="el-icon-edit-outline"
            @click="jumpOpenCourse(row.wordPrintCode, row.realName, row.courseContentType)">打印</el-button>
          </div>

          <div v-else-if="item.value == 'isEnable'">
            <span class="blue" v-if="row.isEnable === 1">已打印</span>
            <span class="red" v-else>未打印</span>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import wordPrintApi from "@/api/printApi/areasStudentWordPrintList";
import { pageParamNames } from "@/utils/constants";
import ls from '@/api/sessionStorage'
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";

import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: 'areasStudentWordPrintList',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        merchantCode: '',
        startDate: '',
        endDate: '',
        loginName: '',
        realName: ''
      },
      studyRank: [],
      value1: '',
      exportLoading: false,
      linshi: null,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '打印编号',
          value: 'wordPrintCode'
        },
        {
          name: '姓名',
          value: 'realName'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '标题',
          value: 'title'
        },
        {
          name: '课程类型',
          value: 'courseContentTypeStr'
        },
        {
          name: '结束时间',
          value: 'addTime'
        },
        {
          name: '状态',
          value: 'isEnable'
        }],
        tableHeaderList: [], // 获取表头数据
    };
  },
  created() {
    this.dataQuery.studentCode = window.localStorage.getItem("studentCode");
    this.dataQuery.merchantCode = window.localStorage.getItem("merchantCode");
    this.fetchData();
    this.linshi = this.$route.query.realName;
    this.getHeaderlist();
    // this.dataQuery.realName = this.linshi ? this.$route.query.realName : ''
  },
  //  watch: {
  //   '$route' () {
  //     this.fetchData();//我的初始化方法
  //     this.linshi = this.$route.query.realName
  //     this.dataQuery.realName = this.linshi ? this.$route.query.realName : ''
  //   }
  // },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 获取起始时间
    dateVal(e) {
      // console.log(e[0]);
      this.dataQuery.addStartTime = e[0]
      this.dataQuery.addEndTime = e[1]
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    fetchData03() {
      this.tablePage = {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      }
      this.dataQuery = {
        studentCode: window.localStorage.getItem("studentCode"),
        merchantCode: window.localStorage.getItem("merchantCode"),
        startDate: '',
        endDate: '',
        loginName: '',
        realName: ''
      }
      this.fetchData();
    },
    // 查询提现列表
    fetchData() {
      const that = this;
      if (that.value1 != '' && that.value1 != null && that.value1 != undefined) {
        if (that.value1.length > 0) {
          that.dataQuery.addStartTime = that.value1[0]
          that.dataQuery.addEndTime = that.value1[1]
        } else {
          that.dataQuery.addStartTime = ''
          that.dataQuery.addEndTime = ''
        }
      } else {
        that.dataQuery.addStartTime = ''
        that.dataQuery.addEndTime = ''
      }
      that.tableLoading = true
      wordPrintApi.wordPrintList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 跳转到打印页面
    jumpOpenCourse(wordPrintCode, name, courseContentType) {
      const that = this
      var url = ''
      // 判断课程类型
      if (courseContentType == 'Word') {
        url = '/students/components/studentTestPrint'
      } else if (courseContentType == 'Reading') {
        url = '/students/components/studentTestPrintReading'
      } else {
        url = '/students/components/studentTestPrintReport'
      }
      ls.setItem('wordPrintCode', wordPrintCode);
      ls.setItem('name', name);
      ls.setItem('courseContentType', courseContentType);
      ls.setItem('studentCode', this.dataQuery.studentCode);
      ls.setItem('merchantCode', this.dataQuery.merchantCode);
      that.$router.push({
        path: url,
        query: {
          wordPrintCode: wordPrintCode,
          name: name,
          courseContentType: courseContentType
        }
      })
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "areasStudentWordPrintList",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'areasStudentWordPrintList'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },

  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

.blue {
  color: blue;
}</style>
