<!--交付中心-试课学员管理-试课满意度列表-->
<template>
  <div>
    <el-form :inline="true" class="container-card" label-width="110px" ref="searchNum" :model="searchNum">
      <!-- 1 -->
      <!--      <el-form-item label="姓名:">
              <el-input
                v-model="searchNum.name"
                clearable
                placeholder="请选择"
                size="small"
                style="width: 13vw"
              ></el-input>
            </el-form-item>-->
      <el-form-item label="学员编号:" prop="studentCode1">
        <el-input v-model="searchNum.studentCode1" clearable placeholder="请输入" size="small"
          style="width: 13vw"></el-input>
      </el-form-item>
      <!-- 3 -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="searBtn">查询</el-button>
        <el-button icon="el-icon-refresh-left" @click="rest()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="headerList()" style="margin:10px 0 20px 20px">列表显示属性</el-button>

    <el-table :data="studentFeedbackList.data" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }">
      <el-table-column prop="date" width="80" type="selection" align="center"></el-table-column>
      <el-table-column v-for="(item, index) in tableHeaderList" :width="item.value != 'operate' ? ((item.value == 'feedbackTime' || item.value == 'parentFeedback' || item.value == 'createTime' ||
        item.value == 'referrerScore' || item.value == 'deliverMerchantName') ? 180 : '') : 190"
        :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center"
        :show-overflow-tooltip="item.value == 'parentFeedback' ? true : false">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <!-- <el-button type="primary" size="mini" @click="delCard(row)">删除</el-button> -->
            <el-button type="success" size="mini" @click="lookBtn(row)">查看详情</el-button>
            <el-button v-if="!row.deliverIsReply" type="primary" size="mini" @click="replyBtn(row)">回复</el-button>
          </div>

          <div v-else-if="item.value == 'referrerScore'">
            <el-rate v-model="row.parentScore" disabled></el-rate>
          </div>
          <div v-else-if="item.value == 'feedbackTime'">
            <span>{{ row.createTime }}</span>
          </div>

          <div v-else-if="item.value == 'parentScore'">
            {{ !!row.referrerFeedback ? '已反馈' : '未反馈' }}
          </div>

          <div v-else-if="item.value == 'parentIsAnonymity'">
            <el-switch disabled v-model="row.parentIsAnonymity"></el-switch>
          </div>

          <div v-else-if="item.value == 'deliverIsReply'">
            <el-button v-if="row.deliverIsReply" type="primary" size="mini"
              style="background:#EEF8E8;border-color: #EEF8E8;color: #7AC756;">已回复
            </el-button>
            <el-button v-else type="primary" size="mini"
              style="background:#F26161;border-color: #F26161;">未回复</el-button>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="querydate.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="querydate.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>

    <el-dialog title="反馈回复" :visible.sync="studentFeedBackReplystyle" @close="studentFeedBackReplystyle = false">
      <el-form ref="form" :model="form" label-width="130px">
        <el-form-item label="家长评分" style="width: 50%;">
          <el-rate style="position: relative;top: 10px;" disabled v-model="form.parentScore"></el-rate>
        </el-form-item>

        <el-form-item label="家长反馈">
          <textarea disabled v-model="form.parentFeedback" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

        <el-form-item v-if="form.referrerScore > 0" label="推荐人评分" style="width: 50%;">
          <el-rate style="position: relative;top: 10px;" disabled v-model="form.referrerScore"></el-rate>
        </el-form-item>

        <el-form-item v-if="form.referrerFeedback" label="推荐人反馈">
          <textarea disabled v-model="form.referrerFeedback" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

        <el-form-item label="教练反馈">
          <textarea disabled v-model="form.feedback" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

        <el-form-item label="体验后的学习意愿">
          <textarea disabled v-model="form.studyIntention" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

        <el-form-item label="学员信息状况反馈">
          <textarea disabled v-model="form.feedback" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

        <el-form-item label="交付中心回复">
          <textarea v-model="form.deliverReply" name="" id="" cols="50" rows="8"
            style="border-color:rgba(187, 187, 187, 1);"></textarea>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="modifyBtn">保 存</el-button>
        <el-button @click="studentFeedBackReplystyle = false">取 消</el-button>
      </div>
    </el-dialog>
    <studentFeedBackDetail @changeDrawer="changeDrawer" :studentFeedBackDetailstyle="studentFeedBackDetailstyle"
      :direction="direction" @updateList="initData" ref="rowshuju" />
    <studentFeedBackReply @lookDrawer="lookDrawer" :studentFeedBackReplystyle="studentFeedBackReplystyle"
      :direction="directions" @updateList="initData" ref="reply" />

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import {
  getStudentFeedbackDetail,
  getStudentFeedbackList,
  replyStudentFeedback
} from "@/api/paikeManage/studentFeedback";
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard'

import studentFeedBackDetail from './components/studentFeedBackDetail.vue'
import studentFeedBackReply from './components/studentFeedBackReply.vue'
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

import FileSaver from "file-saver";
import XLSX from "xlsx";

export default {
  name: "studentFeedback",
  components: {
    studentFeedBackDetail,
    studentFeedBackReply,
    HeaderSettingsDialog
  },
  data() {
    return {
      value1: 3,
      form: {},
      studentFeedBackReplystyle: false,
      studentFeedBackDetailstyle: false, //抽屉状态
      direction: 'rtl',//超哪边打开
      directions: '',
      querydate: {
        studentCode: '',
        pageNum: 1,
        pageSize: 10 //页容量
      },
      searchNum: {
        name: "",
        studentCode1: "",
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      teacher: "",
      contentType: "",
      id: '',
      classCard: '',
      studentFeedbackList: '',
      changesClass: {},
      total: null,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '学员姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '试课时间',
          value: 'createTime'
        },
        {
          name: '试课交付时间',
          value: 'deliverMerchantName'
        },
        {
          name: '评分',
          value: 'referrerScore'
        },
        {
          name: '反馈内容',
          value: 'parentFeedback'
        },
        {
          name: '反馈时间',
          value: 'feedbackTime'
        },
        {
          name: '家长推荐人',
          value: 'parentScore'
        },
        {
          name: '家长匿名提交',
          value: 'parentIsAnonymity'
        },
        {
          name: '是否回复',
          value: 'deliverIsReply'
        }],

      tableHeaderList: [], // 获取表头数据

    };
  },
  created() {
    this.searchNum.studentCode1 = this.$route.query.studentCode
    this.initData()
    this.getHeaderlist();
  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    async modifyBtn() {//修改课程表列表
      this.changesClass.id = this.form.id
      this.changesClass.deliverCode = this.form.deliverMerchant
      this.changesClass.content = this.form.deliverReply
      await replyStudentFeedback(this.changesClass)
      this.$message.success("修改成功")
      this.studentFeedBackReplystyle = false
      this.$emit("updateList");
      await this.initData()
    },
    async initData() {
      this.querydate.studentCode = this.searchNum.studentCode1
      let { data } = await getStudentFeedbackList(this.querydate)
      this.studentFeedbackList = data
      this.total = Number(data.totalItems);
    },
    changeDrawer(v) {
      this.studentFeedBackDetailstyle = v
    },
    lookDrawer(v) {
      this.studentFeedBackReplystyle = v
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return "远程";
      } else if (val.teachingType == 2) {
        return "线下";
      } else if (val.teachingType == 3) {
        return "远程和线下";
      } else {
        return "暂无";
      }
    },
    parentIsScore(val) {
      if (val.parentIsScore != null) {
        return "是";
      } else {
        return "否";
      }
    },
    // 搜索框的位置
    async searBtn() {
      console.log(this.studentCode1)
      this.querydate.studentCode = this.searchNum.studentCode1
      console.log(this.querydate.studentCode)
      let { data } = await getStudentFeedbackList(this.querydate)
      this.studentFeedbackList = data
      this.total = Number(data.totalItems);
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 回复按钮
    async replyBtn(row) {
      this.studentFeedBackReplystyle = true
      let data = await getStudentFeedbackDetail(row.id)
      this.form = data.data
    },
    // 删除按钮
    delCard(row) {
      console.log(1);
    },
    // 数据查看按钮
    async lookBtn(row) {
      let reslist = await getStudentFeedbackDetail(row.id)
      this.$refs.rowshuju.detail = reslist.data
      this.studentFeedBackDetailstyle = true
    },
    // 分页
    handleSizeChange(val) {
      this.querydate.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.querydate.pageNum = val;
      this.initData();
    },
    //定义导出Excel表格事件
    exportExcel() {
      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector("#out-table"));
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: "xlsx",
        bookSST: true,
        type: "array"
      });
      try {
        FileSaver.saveAs(
          //Blob 对象表示一个不可变、原始数据的类文件对象。
          //Blob 表示的不一定是JavaScript原生格式的数据。
          //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
          //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
          new Blob([wbout], { type: "application/octet-stream" }),
          //设置导出文件名称
          "sheetjs.xlsx"
        );
      } catch (e) {
        if (typeof console !== "undefined") console.log(e, wbout);
      }
      return wbout;
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.initData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "studentFeedback",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'studentFeedback'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
};
</script>

<style>
body {
  background-color: #f5f7fa;
}
</style>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}

/*.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #107667;
}*/

::v-deep .el-form-item {
  display: flex;
}

::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

.el-form-item--medium .el-form-item__label {
  line-height: normal;
}
</style>
