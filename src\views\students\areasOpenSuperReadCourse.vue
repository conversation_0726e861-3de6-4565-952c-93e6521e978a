<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :inline="true" ref="form" class="SearchForm">
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程编号:">
            <el-input v-model="dataQuery.courseCode" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程名称:">
            <el-input v-model="dataQuery.courseName" @keyup.enter.native="fetchData()" style="width: 200px" placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="题目类型:">
            <el-select v-model="dataQuery.courseTypeDetail" filterable value-key="value" placeholder="请选择" size="small">
              <el-option v-for="(item, index) in courseTypes" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="课程阶段:">
            <el-select v-model="dataQuery.courseStage" filterable value-key="value" placeholder="请选择" size="small">
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.idx" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button size="small" type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button size="small" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="SearchForm">
      <!-- 添加 -->
      <div class="btn-add">
        <el-button size="small" type="warning" icon="el-icon-plus" @click="openCourse()">开通</el-button>
        <el-button size="small" type="primary" @click="headerList()" style="margin: 0 0 20px 20px">列表显示属性</el-button>
      </div>

      <el-table
        class="course-table"
        ref="multipleTable"
        v-loading="tableLoading"
        :key="itemKey"
        tooltip-effect="dark"
        :row-key="getRowKey"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
        :cell-style="{ 'text-align': 'center' }"
        :default-sort="{ prop: 'date', order: 'descending' }"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="item.value == 'courseCode' || item.value == 'courseName' || item.value == 'coverUrl' ? 180 : ''"
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'coverUrl'" class="demo-image__preview">
              <el-image class="table_list_pic" :src="row.coverUrl" @click="openImg(row)" />
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="dataQuery.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>

    <el-dialog title="授权码录入" :visible.sync="showAuthCode" width="30%" :close-on-click-modal="false">
      <el-form :inline="true" style="margin-bottom: 20px" model="form">
        <h3 style="text-align: center">超出异地登录开课限制，如需继续操作，请输入授权码</h3>
        <br />
        <el-form-item label="风控授权码:" prop="authCode" style="width: 100%; text-align: center">
          <el-input placeholder="请输入授权码" v-model="authCode" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="info" @click="closeAuthCode()">取消</el-button>
        <el-button size="mini" type="primary" :disabled="disabledA" @click="BindingCode()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 图片显示 -->
    <el-dialog title="" :visible.sync="dialogOpenimg" width="30%" :before-close="handleClose">
      <div class="coverimg">
        <el-image class="" :src="coverImg"></el-image>
      </div>
    </el-dialog>

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import courseApi from '@/api/student/areasStudentCourseList';
  import enTypes from '@/api/student/bstatus';
  import courseApitwo from '@/api/student/courseChildren';
  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import riskAuthCodeApi from '@/api/student/riskAuthCode';
  export default {
    name: 'areasOpenSuperReadCourse',
    components: {
      HeaderSettingsDialog
    },
    data() {
      return {
        disabledA: '',
        tableLoading: false,
        submitOpenCourse: {
          studentCode: '',
          courseIdList: []
        },
        selectKey: [],
        tableLoading: false,
        dataQuery: {
          pageNum: 1,
          pageSize: 10,
          studentCode: '',
          courseCode: '',
          courseName: '',
          courseTypeDetail: '',
          courseStage: ''
        },
        total: 0,
        activeType: [], // 活动类型

        tableData: [], //表格数据
        //授权码录入
        showAuthCode: false,
        authCode: undefined,
        dialogVisible: false, // 修改弹窗是否展示
        addOrUpdate: true, // 是新增还是修改
        addCourseData: {}, // 新增课程
        updateCourseData: {}, // 修改数据
        fileListPending: [], // 待处理已上传图片信息
        fileList: [], // 上传图片已有图片列表

        fileDetailListPending: [], // 待处理已上传图片信息
        fileDetailList: [], // 上传图片已有图片列表

        uploadLoading: false, // 上传图片加载按钮
        fullscreenLoading: false, // 保存啥的加载

        content: '',
        isUploadSuccess: true, // 是否上传成功
        isShowRelevance: true, // 新增或修改是否展示关联产品
        itemKey: '',
        radio: '0', //单选框状态 值必须是字符串
        value1: [],
        categoryType: [], //课程分类
        bigClassType: [], //题目类型
        courseStageType: [], //课程学段类型
        courseEditionType: [], //教材版本
        courseTypes: [
          {
            label: '阅读理解',
            value: 0
          },
          {
            label: '完型填空',
            value: 1
          }
        ], //题目类型
        courseLevelType: [], //课程等级
        multipleSelection: [], //多选数据
        fromCourse: false, // 来自页面

        coverImg: '',
        dialogOpenimg: false,

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '课程编号',
            value: 'courseCode'
          },
          {
            name: '课程名称',
            value: 'courseName'
          },
          {
            name: '课程阶段',
            value: 'courseStageName'
          },
          {
            name: '题目类型',
            value: 'courseTypeDetailRemark'
          },
          {
            name: '课程版本',
            value: 'textbookVersion'
          },
          {
            name: '课程封面',
            value: 'coverUrl'
          }
        ],
        tableHeaderList: [] // 获取表头数据
      };
    },
    created() {
      this.dataQuery.studentCode = window.localStorage.getItem('studentCode');
      this.dataQuery.merchantCode = window.localStorage.getItem('merchantCode');
      this.getHeaderlist();
      this.fetchData();
      this.fromCourse = this.$route.query.fromCourse ?? '';
      this.dataQuery.courseStage = this.$route.query.level ?? '';
      this.getCategoryType();
      //获取学段下拉框
      this.getStady();
      //获取课程等级下拉框
      this.getCourseLevel();
      //获取教材版本下拉学段
      this.getCourseEditionType();
    },
    methods: {
      handleClose() {
        this.coverImg = '';
        this.dialogOpenimg = false;
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      getRowKey(row) {
        return row.id;
      },
      //重置
      rest() {
        this.dataQuery.courseCode = '';
        this.dataQuery.courseName = '';
        this.dataQuery.courseTypeDetail = '';
        this.dataQuery.courseStage = '';
        this.fetchData01();
      },
      fetchData01() {
        this.dataQuery.pageNum = 1;
        this.itemKey = Math.random();
        this.fetchData();
      },
      // 查询+搜索课程列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        courseApi.getSuperReadCourseList(that.dataQuery).then((res) => {
          that.tableData = res.data.data;
          that.total = Number(res.data.totalItems);
          that.tableLoading = false;
        });
      },
      // 这里
      openCourse() {
        this.submitOpenCourse = {
          studentCode: '',
          courseIdList: []
        };
        if (this.multipleSelection.length <= 0) {
          this.$message('请选择课程');
          return false;
        }
        const studentCode = window.localStorage.getItem('studentCode');
        const merchantCode = window.localStorage.getItem('merchantCode');
        this.submitOpenCourse.studentCode = studentCode;
        this.submitOpenCourse.merchantCode = merchantCode;
        for (let i = 0; i < this.multipleSelection.length; i++) {
          this.submitOpenCourse.courseIdList.push(this.multipleSelection[i].id);
        }
        this.$confirm('确定操作吗?', '开课', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          courseApi.openSuperRead(this.submitOpenCourse).then((res) => {
            if (res.success) {
              this.submitOpenCourse = {
                studentCode: '',
                courseIdList: []
              };
              this.$refs.multipleTable.clearSelection();
              this.multipleSelection = [];
              this.disabledA = false;
              this.rest();
              this.$message.success('开课成功');
            } else {
              this.disabledA = false;
              return this.$message.warning(res.message);
            }
          });
        });
        // if (this.fromCourse) {
        //   if (this.multipleSelection.length <= 0) {
        //     this.$message('请选择课程');
        //     return false;
        //   }
        //   let multipleSelection = this.multipleSelection.map((item) => {
        //     return {
        //       courseIdList: item.id,
        //       courseName: item.courseName
        //     };
        //   });
        //   window.localStorage.setItem('multipleSelection', JSON.stringify(multipleSelection));
        //   this.$router.push({
        //     path: '/course/coursePackageList',
        //     name: 'coursePackageList',
        //     params: {
        //       choose: this.fromCourse
        //     }
        //   });
        // } else {

        // }
      },

      BindingCode() {
        if (!this.authCode) {
          this.$message.warning('授权码不能为空');
          return;
        }
        riskAuthCodeApi
          .bindingCode(this.authCode)
          .then((res) => {
            if (res.success) {
              this.$message.success('授权码录入成功');
              this.showAuthCode = false;
              this.authCode = '';
              this.submitBackRecharge();
            }
          })
          .catch((err) => {});
      },
      closeAuthCode() {
        this.showAuthCode = false;
        this.authCode = '';
      },
      // 多选的值
      handleSelectionChange(val) {
        // console.log(val);
        this.multipleSelection = val;
      },
      // 获取分类返回类型
      getCategoryType() {
        courseApitwo.categoryType().then((res) => {
          this.categoryType = res.data;
        });
      },
      //获取学段下拉框
      getStady() {
        var enType = 'CJYDCourseStage';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取课程等级下拉框
      getCourseLevel() {
        var enType = 'CourseLevel';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseLevelType = res.data;
        });
      },
      //获取教材版本下拉学段
      getCourseEditionType() {
        // var enType = "CourseEdition";
        enTypes.getEnumerationcheckList().then((res) => {
          this.courseEditionType = res.data;
        });
      },
      //进入子类
      enterChildrenList(courseCode, courseContentType, courseName) {
        console.log(courseContentType + 'wyy');
        const that = this;
        if (courseContentType === 'Word') {
          that.$router.push({
            path: '/course/courseMake',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Reading' || courseContentType === 'ClozeTest') {
          that.$router.push({
            path: '/course/courseReading',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'Listening') {
          that.$router.push({
            path: '/course/courseListening',
            query: {
              courseCode: courseCode,
              courseContentType: courseContentType,
              courseName: courseName
            }
          });
        } else if (courseContentType === 'WenZong') {
        }
      },
      // 分页
      handleSizeChange(val) {
        this.dataQuery.pageSize = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.dataQuery.pageNum = val;
        this.fetchData();
      },
      // 关闭弹窗
      close() {
        this.dialogVisible = false;
      },

      openImg(row) {
        this.coverImg = row.coverUrl;
        this.dialogOpenimg = true;
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'areasOpenSuperReadCourse',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },

      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'areasOpenSuperReadCourse'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            let Json = this.removeNullValues(res.data.value);
            this.tableHeaderList = JSON.parse(Json);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>

<style>
  .lh36 {
    line-height: 36px;
    font-size: 14px;
  }

  .SearchForm {
    width: 100%;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    padding: 15px 0 0 15px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  }

  .btn-add {
    padding: 5px;
  }

  .red {
    color: red;
  }

  .green {
    color: green;
  }

  .course-table {
    text-align: center;
  }

  .course-table td,
  .course-table th {
    padding: 5px 0;
    text-align: center;
  }

  .course-table button {
    padding: 2px;
  }

  .mt22 {
    margin-top: 22px;
  }

  .coverimg {
    text-align: center !important;
    padding: 50px;

    /* .el-dialog__body{
    text-align:center !important;
  } */
  }
  .table_list_pic {
    width: 100px;
    height: 100px;
  }
</style>
