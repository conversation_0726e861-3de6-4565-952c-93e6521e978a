const getters = {
  setpayUrl: (state) => state.user.payUrl,
  sidebar: (state) => state.app.sidebar,
  language: (state) => state.app.language,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  token: (state) => state.user.token,
  code: (state) => state.user.code,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  JlbInfo: (state) => state.user.JlbInfo,
  nick: (state) => state.user.nick,
  introduction: (state) => state.user.introduction,
  status: (state) => state.user.status,
  role: (state) => state.user.role,
  roles: (state) => state.user.roles,
  roleName: (state) => state.user.roleName,
  perms: (state) => state.user.perms,
  visitor: (state) => state.user.visitor,
  examStatus: (state) => state.user.examStatus,
  setting: (state) => state.user.setting,
  permission_routers: (state) => state.permission.routers,
  addRouters: (state) => state.permission.addRouters,
  errorLogs: (state) => state.errorLog.logs,
  //引用全局标识
  globalId: (state) => state.user.globalId,
  // 阿里云OSS配置

  stsToken: (state) => state.settings.stsToken,
  stsBucketName: (state) => state.settings.stsBucketName,
  stsAccessKeyId: (state) => state.settings.stsAccessKeyId,
  stsAccessKeySecret: (state) => state.settings.stsAccessKeySecret,
  stsExpiration: (state) => state.settings.stsExpiration,
  prStsToken: (state) => state.settings.prStsToken,
  prStsBucketName: (state) => state.settings.prStsBucketName,
  prStsAccessKeyId: (state) => state.settings.prStsAccessKeyId,
  prStsAccessKeySecret: (state) => state.settings.prStsAccessKeySecret,
  prStsExpiration: (state) => state.settings.prStsExpiration,

  getClientWidth: (state) => state.user.documentClienWidth,
  getClientHeight: (state) => state.user.documentClientHeight,
  getOnlineFormCache: (state) => state.user.onlineFormCache
};
export default getters;
