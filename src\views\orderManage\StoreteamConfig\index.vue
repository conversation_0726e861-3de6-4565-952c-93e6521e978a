<!--交付中心-接单管理-正式课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame" v-if="isAdmin">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="门店编号:" label-width="120px" prop="merchantCode">
              <el-input v-model.trim="querydata.merchantCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="门店名称:" prop="merchantName">
              <el-input v-model.trim="querydata.merchantName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付中心编号:" label-width="120px" prop="deliverMerchant">
              <el-input v-model.trim="querydata.deliverMerchant" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付中心名称:" label-width="120px" prop="deliverMerchantName">
              <el-input v-model.trim="querydata.deliverMerchantName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5" :xs="24">
            <el-form-item label="交付小组:" label-width="120px" prop="teamName">
              <el-input v-model.trim="querydata.teamName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 交付中心头部 -->
    <div class="frame" v-else>
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4" :xs="24">
            <el-form-item label="门店编号:" prop="merchantCode">
              <el-input v-model.trim="querydata.merchantCode" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :xs="24">
            <el-form-item label="门店名称:" prop="merchantName">
              <el-input v-model.trim="querydata.merchantName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="交付小组:" prop="teamName">
              <el-input v-model.trim="querydata.teamName" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 列表显示属性 -->

    <!-- 表格 -->

    <el-table :data="tableData" style="width: 100%" id="out-table" v-loading="tableLoading"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" size="mini" fit>
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center" min-width="150" :width="
          item.value == 'operate' || item.value == 'createTime' ? '300' : ''
        ">
        <template v-if="item.value == 'operate'" v-slot="{ row }">
          <div>
            <el-button v-if="row.schoolBingDevlierCode||row.spareSchoolBindDevlierCode" type="primary" size="mini"
              @click="openEdit(row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems"></el-pagination>
    </el-row>
    <el-dialog title="编辑" :visible.sync="isShow" width="40%" @close="close">
      <span>
        <el-form :model="formData" ref="form" label-position="left" label-width="200px" :inline="false" size="normal">
          <el-form-item label="门店名称">
            <el-input disabled v-model="formData.merchantName" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item label="(主)交付中心名称" v-if="isAdmin&&formData.schoolBingDevlierCode">
            <el-input disabled v-model="formData.schoolBingDevlierName " style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item label="(备用)交付中心名称" v-if="isAdmin&& formData.spareSchoolBindDevlierCode">
            <el-input disabled v-model="formData.spareSchoolBindDevlierName" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item :label="`绑定的${isAdmin?'(主)':''}交付小组`" v-if="formData.schoolBingDevlierCode">
            <el-input disabled v-model="formData.schoolBindDevlierGroupName" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item :label="`绑定的${isAdmin?'(备用)':''}交付小组`" v-if="formData.spareSchoolBindDevlierCode">
            <el-input disabled v-model="formData.spareSchoolBindDevlierGroupName" style="width: 300px"></el-input>
          </el-form-item>
          <el-form-item :label="`更换绑定的${isAdmin?'(主)':''}交付小组`" v-if="formData.schoolBingDevlierCode">
            <el-select v-model="formData.schoolBindDevlierGroupId" placeholder="请选择" clearable filterable>
              <el-option v-for="item in groupList" :key="item.id" :label="item.teamName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="`更换绑定的${isAdmin?'(备用)':''}交付小组`" v-if="formData.spareSchoolBindDevlierCode">
            <el-select v-model="formData.spareSchoolBindDevlierGroupId" value-key="" placeholder="请选择" clearable
              filterable>
              <el-option v-for="item in groupList1" :key="item.id" :label="item.teamName" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>

        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">取消</el-button>
        <el-button type="primary" @click="btnOK">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import ls from "@/api/sessionStorage";
import { pageParamNames } from "@/utils/constants";
import { selectTeam, selectMerchant, changeBindTeam } from "@/api/orderManage";

export default {
  name: "StoreteamConfig",
  data() {
    return {
      isShow: false,
      screenWidth: window.screen.width, //屏幕宽度
      // 搜索表单
      querydata: {
        merchantName: "",
        merchantCode: "",
        deliverMerchant: "",
        deliverMerchant: "",
        deliverMerchantName: "",
        teamName: "",
        pageNum: 1,
        pageSize: 10, //页容量
      },
      total: null,
      tableData: [],
      tableLoading: false,
      isAdmin: false,
      formData: {},
      abutmentList: {},
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null,
      },

      selectObj: {
        pageNum: 1,
        pageSize: 20,
        deliverName: "",
      },
      headerSettings: [],
      headerSettings1: [
        {
          name: "门店名称",
          value: "merchantName",
        },
        {
          name: "门店编号",
          value: "merchantCode",
        },
        {
          name: "绑定的交付小组",
          value: "schoolBindDevlierGroupName1",
        },
        // {
        //   name: "绑定的交付小组",
        //   value: "spareSchoolBindDevlierGroupName",
        // },
        {
          name: "操作",
          value: "operate",
        },
      ],
      headerSettings2: [
        {
          name: "门店名称",
          value: "merchantName",
        },
        {
          name: "门店编号",
          value: "merchantCode",
        },
        {
          name: "（主）交付中心编号:",
          value: "schoolBingDevlierCode",
        },
        {
          name: "（主）交付中心名称:",
          value: "schoolBingDevlierName",
        },
        {
          name: "绑定的（主）交付小组",
          value: "schoolBindDevlierGroupName",
        },
        {
          name: "（备用）交付中心编号:",
          value: "spareSchoolBindDevlierCode",
        },
        {
          name: "（备用）交付中心名称:",
          value: "spareSchoolBindDevlierName",
        },
        {
          name: "绑定的（备用）交付小组",
          value: "spareSchoolBindDevlierGroupName",
        },
        {
          name: "操作",
          value: "operate",
        },
      ],
      tableHeaderList: [], // 获取表头数据
      dialogHistory: false,
      historys: [],
      isSubmit: false,
      groupForm: {},
      groupList: [],
      groupList1: [],
      dialogAddgroup: false,
    };
  },
  created() {
    this.isAdmin =
      ls.getItem("rolesVal") === "admin" ||
      ls.getItem("rolesVal") == "JiaofuManager";
    this.headerSettings = this.isAdmin
      ? this.headerSettings2
      : this.headerSettings1;
    if (!this.isAdmin) {
      this.getGroupList();
    }
    this.initData();
    this.tableHeaderList = this.headerSettings;
  },
  watch: {
    isAdmin: function (val) {
      this.headerSettings = val ? this.headerSettings2 : this.headerSettings1;
    },
  },
  computed: {
    ...mapGetters(["code"]),
  },
  methods: {
    searchData() {
      this.querydata.pageNum = 1;
      this.querydata.pageSize = 10;
      this.initData();
    },
    async getGroupList() {
      let res = await selectTeam(this.code);
      this.groupList = res.data;
      this.groupList1 = res.data
    },
    async openEdit(row) {

      if (this.isAdmin) {
        let res = await selectTeam(row.schoolBingDevlierCode);
        this.groupList = res.data;
        if (row.spareSchoolBindDevlierCode) {
          let res1 = await selectTeam(row.spareSchoolBindDevlierCode);
          this.groupList1 = res1.data;
        }

      }
      this.formData = JSON.parse(JSON.stringify(row));
      this.isShow = true;
    },
    async btnOK() {
      let obj = {
        merchantCode: this.formData.merchantCode,
        schoolBindDevlierGroupId: this.formData.schoolBindDevlierGroupId,
        spareSchoolBindDevlierGroupId: this.formData.spareSchoolBindDevlierGroupId,
      }
      await changeBindTeam(obj)
      this.$message.success('操作成功')
      this.initData();
      this.isShow = false;
    },
    close() { },
    // 时间戳转换 HH:MM:SS
    getTimeFromTimestamp(timestamp) {
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");
      return `${hours}:${minutes}:${seconds}`;
    },

    async initData() {
      let that = this;
      that.tableLoading = true;
      let { data } = await selectMerchant(this.querydata);
      this.tableData = data.data;
      this.tableData.forEach(e => {
        if (e.schoolBindDevlierGroupName) {
          e.schoolBindDevlierGroupName1 = e.schoolBindDevlierGroupName
        }
        if (e.spareSchoolBindDevlierGroupName) {
          e.schoolBindDevlierGroupName1 = e.spareSchoolBindDevlierGroupName
        }
      })
      this.total = Number(data.totalItems);
      pageParamNames.forEach((name) =>
        that.$set(that.tablePage, name, parseInt(data[name]))
      );
      that.tableLoading = false;

      this.$forceUpdate();
    },

    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.querydata.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.querydata.pageNum = val;
      this.initData();
    },
    //重置
    rest() {
      this.$refs.querydata.resetFields();
      this.querydata = {
        merchantName: "",
        merchantCode: "",
        deliverMerchant: "",
        deliverMerchant: "",
        deliverMerchantName: "",
        teamName: "",
        pageNum: 1,
        pageSize: 10, //页容量
      };
      this.initData();
    },
  },
};
</script>

<style lang="scss" scoped>
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

body {
  background-color: #f5f7fa;
}

.frame {
  // margin:  0 30px;
  background-color: rgba(255, 255, 255);
  padding: 20px;
}

.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #6ed7c4;
}

.transferred {
  color: #ea2424;
}

.no_transferred {
  color: #1cb31c;
}

.timeClass {
  border: 1px solid #dfe4ed;
  border-radius: 5px;
  background-color: #fff;
  box-sizing: border-box;
  margin-left: 20px;
}

.week {
  border: 1px solid #dfe4ed;
  border-radius: 5px;
  box-sizing: border-box;
  padding: 7px 20px;
}
.vocabulary {
  position: absolute;
  top: 6px;
  right: -1px;
  height: 24px;
  color: #fff;
  font-size: 12px;
  line-height: 24px;
  border-radius: 3px;
  padding: 0 4px;
  background-color: #46a6ff;
}

.thesaurus {
  position: relative;
}
</style>
