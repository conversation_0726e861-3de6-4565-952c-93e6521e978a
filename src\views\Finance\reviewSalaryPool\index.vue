<template>
    <div>
        <el-card class="frame" shadow="never">
            <div class="back-top-css" @click="goBack()">
                <i class="el-icon-arrow-left"></i> 
                <span>返回上一页</span>
            </div>
            <el-form label-width="100px" ref="querydata" :model="querydata">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="订单号：" >
                            <el-input v-model.trim="querydata.sourceOrderId " style="width: 10vw" size="small" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="来源类型:" >
                            <el-select v-model="querydata.sourceType " size="small" style="width: 10vw" placeholder="请选择" clearable>
                                <el-option v-for="(item,index) in wagesStates" :key="index" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="primary" size="mini" icon="el-icon-search" @click="searchData">查询</el-button>
                        <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>
        <div style="margin-top:25px;"> 
            <el-table  v-loading="tableLoading"  :data="reviewSalaryList" style="width: 100%" :header-cell-style="getRowClass">
                <el-table-column prop="sourceOrderId" label="订单编号" width="300" align="center"></el-table-column>
                <el-table-column prop="amountSumYuan" label="金额" width="200" align="center"></el-table-column>
                <el-table-column prop="sourceTypeDesc" label="来源类型" width="200" align="center"></el-table-column>
                <el-table-column prop="remark" label="备注" align="center">
                    <template slot-scope="scope">
                        <el-popover placement="top-start" width="500" trigger="hover" :content="scope.row.remark">
                            <div slot="reference" class="referenc_css">{{ scope.row.remark }}</div>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination class="pagination-css" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="querydata.pageNum" :page-sizes="[10, 20, 30, 40, 50]"  layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
        </div>
    </div>
</template>
<script>
import { getReviewpage } from '@/api/FinanceApi/coachTurnover'
export default {
    data(){
        return{
            reviewSalaryList:[],
            total:2,
            wagesStates:[
                {label:'分润',value:1},
                {label:'充值',value:2}
            ],
            tableLoading:false,
            querydata:{
                pageNum: 1,
                pageSize: 10
            }
        }
    },
    created(){
        this.initData()
    },
    methods:{
        // 动态class
        getRowClass({ rowIndex, columnIndex }) {
            if (rowIndex == 0) {
                return "background:#f5f7fa";
            }
        },
        searchData(){
            this.querydata.pageNum = 1
            this.initData()
        },
        rest(){
            this.querydata = {
                pageNum: 1,
                pageSize: 10
            }
            this.initData()
        },
        async initData(){
            this.tableLoading = true
            let { data } = await getReviewpage(this.querydata)
            this.reviewSalaryList = data.data
            this.tableLoading=false
            this.total = Number(data.totalCount)
        },
        // 分页
        handleSizeChange(val) {
            this.querydata.pageSize = val;
            this.searchData();
        },
        handleCurrentChange(val) {
        // this.searchData();
            this.querydata.pageNum=val
            this.initData()
        },
        goBack(){
            this.$emit('goBack','coach')
        },
    }
}
</script>
<style scoped lang="scss">
.pagination-css {
  text-align: center;
  margin-top: 18px;
}
.back-top-css{
    padding-left: 8px;
    margin-bottom: 19px;
    color:#409EFF;
    font-size: 14px;
    width: 200px;
    cursor: pointer;
}
.referenc_css{
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>