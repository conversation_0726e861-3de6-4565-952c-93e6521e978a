//@import "./flow-element-variables.scss";
@import "~bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css";
@import "~bpmn-js-token-simulation/assets/css/font-awesome.min.css";
@import "~bpmn-js-token-simulation/assets/css/normalize.css";
@import "~bpmn-js/dist/assets/diagram-js.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn.css";
@import "~bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css";
@import "./process-designer.scss";
@import "./process-panel.scss";

$success-color: #4eb819;
$current-color: #409EFF;

.process-viewer {
  position: relative;
  border: 1px solid #EFEFEF;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+') repeat!important;

  .success-arrow {
    fill: $success-color;
    stroke: $success-color;
  }

  .success-conditional {
    fill: white;
    stroke: $success-color;
  }

  .success.djs-connection {
    .djs-visual path {
      stroke: $success-color!important;
      marker-end: url(#sequenceflow-end-white-success)!important;
    }
  }

  .success.djs-connection.condition-expression {
    .djs-visual path {
      marker-start: url(#conditional-flow-marker-white-success)!important;
    }
  }

  .success.djs-shape {
    .djs-visual rect {
      stroke: $success-color!important;
      fill: $success-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: $success-color!important;
    }

    .djs-visual path:nth-child(2) {
      stroke: $success-color!important;
      fill: $success-color!important;
    }

    .djs-visual circle {
      stroke: $success-color!important;
      fill: $success-color!important;
      fill-opacity: 0.15!important;
    }
  }

  .current.djs-shape {
    .djs-visual rect {
      stroke: $current-color!important;
      fill: $current-color!important;
      fill-opacity: 0.15!important;
    }

    .djs-visual polygon {
      stroke: $current-color!important;
    }

    .djs-visual circle {
      stroke: $current-color!important;
      fill: $current-color!important;
      fill-opacity: 0.15!important;
    }
  }
}

.process-viewer .djs-tooltip-container, .process-viewer .djs-overlay-container, .process-viewer .djs-palette {
  display: none;
}
