import variables from '@/styles/element-variables.scss'
import defaultSettings from '@/settings'
import { getSts, getPrSts } from '@/api/alibaba'
import { Message } from 'element-ui'

// const { showSettings, tagsView, fixedHeader, sidebarLogo } = defaultSettings

const state = {
  // theme: variables.theme,
  // showSettings: showSettings,
  // tagsView: tagsView,
  // fixedHeader: fixedHeader,
  // sidebarLogo: sidebarLogo,
  // 阿里云OSS配置
  stsToken: '',
  //全局身份标识

  stsBucketName: '',
  stsAccessKeyId: '',
  stsAccessKeySecret: '',
  stsExpiration: '',
  // 阿里云PUBLIC READ OSS配置
  prStsToken: '',
  prStsBucketName: '',
  prStsAccessKeyId: '',
  prStsAccessKeySecret: '',
  prStsExpiration: ''
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  SET_stsToken: (state, stsToken) => {
    state.stsToken = stsToken
  },

  SET_stsBucketName: (state, stsBucketName) => {
    state.stsBucketName = stsBucketName
  },
  SET_stsAccessKeyId: (state, stsAccessKeyId) => {
    state.stsAccessKeyId = stsAccessKeyId
  },
  SET_stsAccessKeySecret: (state, stsAccessKeySecret) => {
    state.stsAccessKeySecret = stsAccessKeySecret
  },
  SET_stsExpiration: (state, stsExpiration) => {
    state.stsExpiration = stsExpiration
  },
  SET_prStsToken: (state, prStsToken) => {
    state.prStsToken = prStsToken
  },
  SET_prStsBucketName: (state, prStsBucketName) => {
    state.prStsBucketName = prStsBucketName
  },
  SET_prStsAccessKeyId: (state, prStsAccessKeyId) => {
    state.prStsAccessKeyId = prStsAccessKeyId
  },
  SET_prStsAccessKeySecret: (state, prStsAccessKeySecret) => {
    state.prStsAccessKeySecret = prStsAccessKeySecret
  },
  SET_prStsExpiration: (state, prStsExpiration) => {
    state.prStsExpiration = prStsExpiration
  }

}

const actions = {
  changeSetting({ commit }, data) {
    commit('CHANGE_SETTING', data)
  },
  // user login
  getSts({ commit }) {
    return new Promise((resolve, reject) => {
      getSts().then(response => {
        console.log(response)
        // eslint-disable-next-line eqeqeq
        if (!response.success) {
          Message({
            message: response.message || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        } else {
          //
          const { data } = response
          commit('SET_stsToken', data.credentials.securityToken)
          commit('SET_stsBucketName', data.bucketName)
          commit('SET_stsAccessKeyId', data.credentials.accessKeyId)
          commit('SET_stsAccessKeySecret', data.credentials.accessKeySecret)
          commit('SET_stsExpiration', data.credentials.expiration)
          resolve()
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  getPrSts({ commit }) {
    return new Promise((resolve, reject) => {
      getPrSts().then(response => {
        console.log(response)
        // eslint-disable-next-line eqeqeq
        if (!response.success) {
          Message({
            message: response.message || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
        } else {
          //
          const { data } = response
          commit('SET_prStsToken', data.credentials.securityToken)
          commit('SET_prStsBucketName', data.bucketName)
          commit('SET_prStsAccessKeyId', data.credentials.accessKeyId)
          commit('SET_prStsAccessKeySecret', data.credentials.accessKeySecret)
          commit('SET_prStsExpiration', data.credentials.expiration)
          resolve()
        }
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
