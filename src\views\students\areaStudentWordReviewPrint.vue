<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员编号：">
            <el-input v-model="dataQuery.studentCode" clearable disabled placeholder="请输入学员编号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="登录账号：">
            <el-input v-model="dataQuery.loginName" clearable placeholder="请输入登录账号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="学员手机号：">
            <el-input v-model="dataQuery.memberPhone" clearable placeholder="请输入学员手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8" :xs="24">
          <el-form-item label="添加时间：">
            <el-date-picker style="width: 90%;" value-format="yyyy-MM-dd hh:mm:ss" v-model="regTime" type="daterange"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              <!-- @change="dateVal" -->
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item label="姓名：">
            <el-input v-model="dataQuery.realName" clearable placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData01()">搜索</el-button>
            <el-button icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-button type="primary" @click="headerList()" style="margin:20px 0 20px">列表显示属性</el-button>

    <!-- <el-col :span="24" style="margin-bottom: 30px;">
      <el-button type="warning" icon="el-icon-document-copy" @click="openBigRecharge()" size="mini">学员充值学时</el-button>
    </el-col> -->

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :width="item.value == 'operate' ? 220 : ''"
        :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center" :show-overflow-tooltip="item.value=='school'?true:false">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <!-- <el-button type="success" size="mini" icon="el-icon-edit-outline" @click="jumpOpenCourse(scope.row.studentCode)">开通课程</el-button>
            <el-button type="primary" size="mini" icon="el-icon-sell" @click="openRecharge(scope.row.studentCode)">充值学时</el-button>
            <el-button type="danger" size="mini" icon="el-icon-sold-out" @click="openBackRecharge(scope.row.studentCode)">退课</el-button> -->
            <el-button type="warning" size="mini" icon="el-icon-view"
              @click="enterChildrenList(row.studentCode, row.realName)">打印21天抗遗忘</el-button>
            <el-button type="warning" size="mini" icon="el-icon-view"
              @click="enterChildrenList1(row.studentCode, row.realName)">21天抗遗忘记录</el-button>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="realName" label="姓名" ></el-table-column>
      <el-table-column prop="memberPhone" label="学员手机号" ></el-table-column>
      <el-table-column prop="gradeName" label="年级" ></el-table-column>
      <el-table-column prop="school" label="学校"  show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="totalCourseHours" label="已购学时（节）" width="150"></el-table-column>
      <el-table-column prop="haveCourseHours" label="剩余学时（节）" width="150"></el-table-column> -->
      <el-table-column prop="addTime" label="添加时间" ></el-table-column>

    </el-table>

    <el-dialog class="recharge-dialog" title="充值" :visible.sync="showRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="recharge" label-position="left" label-width="150px" style="width: 100%;">
        <el-form-item label="门店剩余学时(节)：" prop="course">
          <el-input v-model="recharge.course" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值账户：" prop="studentCode">
          <el-input v-model="recharge.studentCode" />
        </el-form-item>
        <el-form-item label="充值学时：" prop="courseLength">
          <el-input v-model="recharge.courseLength" type="number" maxlength="20" isNumber2="true" min="1" @blur="blurCourseLength(recharge.courseLength)" />
        </el-form-item>
        <el-form-item label="学时单价：" prop="coursePrice">
          <el-input v-model="recharge.coursePrice" @blur="blurCoursePrice(recharge.coursePrice)" type="number"
            maxlength="20" isNumber2="true" min="1" />
        </el-form-item>
        <el-form-item label="合计金额 ：" prop="sumCoursePrice">
          <el-input v-model="recharge.sumCoursePrice" disabled />
        </el-form-item>
        <el-form-item label="到账学时 ：" prop="rechargeCourse">
          <el-input v-model="recharge.toAccountCourse" readonly='readonly' disabled />
        </el-form-item>
        <el-form-item label="充值说明 ：" prop="remark">
          <el-input v-model="recharge.remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="openDialogVisible()">确定</el-button>
      </div>
    </el-dialog>


    <el-dialog title="退课" :visible.sync="showBackRecharge" width="70%" :close-on-click-modal="false">
      <el-form v-model="backRecharge" label-position="left" label-width="120px" style="width: 100%;">
        <el-form-item label="学员编号：" prop="course">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.studentCode" readonly='readonly' disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="剩余学时(节)：" prop="haveCourseHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.haveCourseHours" readonly="readonly" disabled />
          </el-col>
        </el-form-item>
        <el-form-item label="退学时(节)：" prop="tuiHours">
          <el-col :xs="24" :span="18">
            <el-input v-model="backRecharge.tuiHours" type="number" maxlength="20" isNumber2="true" min="1" />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledA" @click="submitBackRecharge()">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog class="remark-dialog" title="交易密码确认" :visible.sync="dialogVisible" width="20%"
      :close-on-click-modal="false">
      <el-input v-model="secondPassWord" show-password></el-input>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" :disabled="disabledF" @click="submitRecharge(secondPassWord)">确认</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto;" :xs="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import studentApi from "@/api/student/areasStudentCourseList";
import {
  pageParamNames
} from "@/utils/constants";
import merchantAccountFlowApi from '@/api/student/merchantAccountFlow'
import ls from '@/api/sessionStorage'
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";

import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: 'areaStudentWordReviewPrint',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      showBackRecharge: false,
      showRecharge: false,
      recharge: {
        course: 0,
        toAccountCourse: 0,
        sumCoursePrice: 0,
      },
      backRecharge: {},
      tableLoading: false,
      disabledF: false,
      disabledA: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        studentCode: '',
        startDate: '',
        endDate: '',
        isEnable: '',
        isFormal: '',
        loginName: '',
        realName: '',
        memberCode: '',
        memberPhone: '',
        restrictedUse: ''
      },
      dialogVisible: false,
      updateMarketDate: {},
      addMarketDate: {},
      showLoginAccount: false,
      secondPassWord: '',
      regTime: '',

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '登录账号',
          value: 'loginName'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '姓名',
          value: 'realName'
        },
        {
          name: '学员手机号',
          value: 'memberPhone'
        },
        {
          name: '年级',
          value: 'gradeName'
        },
        {
          name: '学校',
          value: 'school'
        },
        {
          name: '添加时间',
          value: 'addTime'
        }],

      tableHeaderList: [], // 获取表头数据

    };
  },
  created() {
    this.dataQuery.studentCode = window.localStorage.getItem("studentCode");
    this.dataQuery.merchantCode = window.localStorage.getItem("merchantCode");
    // this.getAccount();
    this.fetchData();
    this.getHeaderlist();
  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 搜索
    initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData()
    },

      openDialogVisible() {
        if (!this.recharge.course) {
          this.$message.error('门店剩余学时不能为空')
          return false;
        }
        if (this.recharge.course <= 0) {
          this.$message.error('门店剩余学时不能为0')
          return false;
        }
        if (!this.recharge.courseLength) {
          this.$message.error('充值学时不能为空')
          return false;
        }
        if (this.recharge.courseLength <= 0) {
          this.$message.error('充值学时不能0')
          return false;
        }
        if (!this.recharge.coursePrice) {
          this.$message.error('学时单价不能为空')
          return false;
        }
        if (this.recharge.coursePrice <= 0) {
          this.$message.error('学时单价不能为0')
          return false;
        }
        this.dialogVisible = true;
        this.secondPassWord = "";
      },



      submitBackRecharge() {
        const that = this;
        that.disabledA=true;
        if (!that.backRecharge.studentCode) {
          that.$message.error('学员编号不能为空')
          that.disabledA=false;
          return false
        }
        if (!that.backRecharge.haveCourseHours) {
          that.$message.error('剩余学时不能为空')
          that.disabledA=false;
          return false
        }

        if (that.backRecharge.haveCourseHours <= 0) {
          that.$message.error('剩余学时不能为0')
          that.disabledA=false;
          return false
        }

        if (!that.backRecharge.tuiHours) {
          that.$message.error('退学时不能为空')
          that.disabledA=false;
          return false
        }

        // this.$confirm('确定操作吗?', '退学时', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   type: 'warning'
        // }).then(() => {
          studentApi.submitBackRecharge(this.backRecharge).then(res => {
            that.showBackRecharge = false;
            that.disabledA=false;
            that.$nextTick(() => that.fetchData())
            that.$message.success('退学时成功')
          })
        //})
      },


      submitRecharge(secondPassWord) {
        this.disabledF=true;
        if (!secondPassWord) {
          this.$message.error('二级密码不能为空')
          return false;
        }
        if (!this.recharge.course) {
          this.$message.error('门店剩余学时不能为空')
          return false;
        }
        if (!this.recharge.studentCode) {
          this.$message.error('学员账户不能空')
          return false;
        }
        if (this.recharge.course <= 0) {
          this.$message.error('门店剩余学时不能为0')
          return false;
        }
        if (!this.recharge.courseLength) {
          this.$message.error('充值学时不能为空')
          return false;
        }
        if (this.recharge.courseLength <= 0) {
          this.$message.error('充值学时不能0')
          return false;
        }
        if (!this.recharge.coursePrice) {
          this.$message.error('学时单价不能为空')
          return false;
        }
        if (this.recharge.coursePrice <= 0) {
          this.$message.error('学时单价不能为0')
          return false;
        }

      merchantAccountFlowApi.checkSecondPwd(secondPassWord).then(res => {
        if (!res.success) {
          this.$message.error('二级密码验证失败')
          this.disabledF = false;
          return false;
        }

          // this.$confirm('确定操作吗?', '充值学时', {
          //   confirmButtonText: '确定',
          //   cancelButtonText: '取消',
          //   type: 'warning'
          // }).then(() => {
            studentApi.submitRecharge(this.recharge).then(res => {
              if (!res.success) {
                this.$message.error(res.message);
                return false;
              }
              this.disabledF=false;
              this.showRecharge = false;
              this.dialogVisible = false;
              this.fetchData01();
              this.$message.success('充值成功!')

        })
      })
      // })
    },

    blurCourseLength(courseLength) {
      this.recharge.toAccountCourse = courseLength;
    },
    blurCoursePrice(coursePrice) {
      if (coursePrice !== "" && coursePrice !== 0 && coursePrice !== '') {
        this.recharge.sumCoursePrice = coursePrice * this.recharge.toAccountCourse;
      }
    },
    getAccount() {
      studentApi.getSchoolAccount().then(res => {
        if (res.data != null) {
          this.recharge.course = res.data.course
        }
      })
    },
    openBigRecharge() {
      this.showRecharge = true;
      this.recharge = {
        studentCode: '',
        course: this.recharge.course,
        coursePrice: 0,
        courseLength: 0,
        sumCoursePrice: 0,
        toAccountCourse: 0,
        remark: ""
      }
    },
    openBackRecharge(studentCode) {

      this.backRecharge.studentCode = studentCode;
      //查询退课
      studentApi.getBackRecharge(studentCode).then(res => {
        this.backRecharge.haveCourseHours = res.data.haveCourseHours
        this.showBackRecharge = true;
      })
      this.backRecharge = {
        studentCode: this.backRecharge.studentCode,
        haveCourseHours: this.backRecharge.haveCourseHours,
        tuiHours: 0
      }
    },
    openRecharge(studentCode) {
      this.recharge.studentCode = studentCode
      this.showRecharge = true;
      this.recharge = {
        studentCode: studentCode,
        course: this.recharge.course,
        courseLength: 0,
        coursePrice: 0,
        sumCoursePrice: 0,
        toAccountCourse: 0,
        remark: ""
      }
    },
    //重置
    rest() {
      this.dataQuery.studentCode = '';
      this.dataQuery.loginName = '';
      this.dataQuery.memberPhone = '';

      this.regTime = '';
      this.dataQuery.realName = '';
      this.fetchData01()
    },
    fetchData01() {
      this.tablePage = {
        currentPage: 1,
        size: this.tablePage.size,
        totalPage: null,
        totalItems: null
      }
      this.fetchData();
    },
    // 获取起始时间
    // dateVal(e) {
    //   // console.log(e[0]);
    //   this.dataQuery.startTime = e[0]
    //   this.dataQuery.endTime = e[1]
    // },
    // 查询提现列表
    fetchData() {
      const that = this;
      var a = that.regTime;
      if (a != null) {
        that.dataQuery.startRegTime = a[0];
        that.dataQuery.endRegTime = a[1];
      }
      if (!that.regTime) {
        that.regTime = []
        that.dataQuery.startRegTime = ''
        that.dataQuery.endRegTime = ''
      }
      that.tableLoading = true
      studentApi.studentList(that.tablePage.currentPage, that.tablePage.size, that.dataQuery).then(res => {
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },

    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    //进入21天抗遗忘打印
    enterChildrenList(studentCode, realName) {
      const that = this;
      ls.setItem('printReviewStudentCode', studentCode);
      ls.setItem('printReviewRealName', realName);
      that.$router.push({
        path: "/students/components/studentWordReviewPrint",
        query: {
          studentCode: studentCode
        }
      });
    },
    //进入查看
    enterChildrenList1(studentCode, realName) {
      const that = this;
      ls.setItem('printReviewStudentCode', studentCode);
      ls.setItem('printReviewRealName', realName);
      that.$router.push({
        path: "/students/components/studentWordReviewList",
        query: {
          studentCode: studentCode
        }
      });
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "areaStudentWordReviewPrint",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'areaStudentWordReviewPrint'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
          this.$forceUpdate();
        } else {
          this.tableHeaderList = this.headerSettings;
          this.$forceUpdate();
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },

  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}

.red {
  color: red;
}

.green {
  color: green;
}

@media screen and (max-width:767px) {
  .recharge-dialog .el-dialog {
    width: 80% !important;
  }

  .el-message-box {
    width: 80% !important;
  }
}
</style>
