<!--交付中心-接单管理-试课新学员列表-->
<template>
  <div>
    <!-- 管理员头部 -->
    <div class="frame" v-if="isAdmin">
      <el-form label-width="110px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名：" prop="studentName">
              <el-input v-model.trim="querydata.studentName" style="width:70%" size="small"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" style="width:70%"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="门店:" prop="merchantCodeOrMerchantPhone">
              <el-input v-model.trim="querydata.merchantCodeOrMerchantPhone" style="width:70%" size="small"
                placeholder="请输入门店账号或门店手机号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交付中心编号：" prop="deliverName">
              <el-input v-model.trim="querydata.deliverMerchantCode" @change="changeInput" style="width:70%"
                size="small" placeholder=""></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="交付中心名称：" label-width="120px" prop="deliverName">
              <el-select v-el-select-loadmore="handleLoadmore" :loading="loadingShip" remote clearable
                v-model="querydata.deliverMerchantName" filterable reserve-keyword placeholder="请选择"
                @input="changeMessage" @blur="clearSearchRecord" @change="changeTeacher">
                <el-option v-for="(item,index) in option" :key="index" :label="item.deliverMerchantName"
                  :value="item.deliverMerchantName">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="审核状态" prop="status">
              <el-select v-model="querydata.status" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value=""></el-option>
                <el-option label="已通过" value="1"></el-option>
                <el-option label="已拒绝" value="2"></el-option>
                <el-option label="审核中" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="申请时间:" prop="source">
              <el-date-picker size="small" v-model="times" type="datetimerange" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>

          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="feachData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 管理员头部 -->
    <div class="frame" v-if="!isAdmin">
      <el-form label-width="90px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名：" label-width="120px" prop="studentName">
              <el-input v-model.trim="querydata.studentName" size="small" style="width: 70%"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" size="small" style="width: 70%"
                placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核状态" prop="status">
              <el-select v-model="querydata.status" clearable size="small" placeholder="请选择" style="width: 70%">
                <el-option label="全部" value=""></el-option>
                <el-option label="已通过" value="1"></el-option>
                <el-option label="已拒绝" value="2"></el-option>
                <el-option label="审核中" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label-width="120px" label="申请时间:" prop="source">
              <el-date-picker size="small" v-model="times" type="datetimerange" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="7" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="feachData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>

    <!-- 列表显示属性 -->
    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <!-- 表格 -->
    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }" size="mini" fit>
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center" min-width="150"
        :width="item.value=='operate'||item.value=='createTime'?'200':''">
        <template v-slot="{ row }">
          <div v-if="item.value == 'changeStatus'">
            <span :class="statusClass(row.changeStatus)">{{transformStatus(row[item.value])}}</span>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="querydata.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import ls from '@/api/sessionStorage'
// import { pageParamNames } from "@/utils/constants";
import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard'
import HeaderSettingsDialog from './components/HeaderSettingsDialog.vue'
import { deliverlist } from "@/api/peizhi/peizhi"
import { changeTeamList, } from '@/api/orderManage'
// import { CodeToText, regionData, TextToCode } from "element-china-area-data";
export default {
  name: 'changeTeamAud',
  components: {
    HeaderSettingsDialog,
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) {
            binding.value()
          }
        })
      }
    }
  },
  data() {
    return {
      isAdmin: false,
      screenWidth: window.screen.width, //屏幕宽度
      // 搜索表单
      querydata: {
        pageNum: 1,
        pageSize: 10,//页容量
        studentName: '',
        studentCode: '',
        merchantCodeOrMerchantPhone: '',
        deliverMerchantCode: "",
        deliverMerchantName: "",
        status: '',
        startTime: '',
        endTime: '',
      },
      // 表格数据总数量
      total: null,
      // 表格数据
      tableData: [],
      times: [],
      option: [],
      optionTotal: 0,
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        deliverName: ''
      },
      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [],
      tableHeaderList: [],
      headerSettings1: [
        {
          name: '学员姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '当前交付小组',
          value: 'teamName'
        },
        {
          name: '修改交付小组',
          value: 'changeTeamName'
        },
        {
          name: '门店编号',
          value: 'merchantCode'
        },

        {
          name: '所属门店',
          value: 'merchantName'
        },
        {
          name: '交付中心编号',
          value: 'deliverMerchantCode'
        },
        {
          name: '交付中心名称',
          value: 'deliverMerchantName'
        },
        {
          name: '审核状态',
          value: 'changeStatus'
        },
        {
          name: '申请时间',
          value: 'createTime'
        },
        {
          name: '审批原因',
          value: 'changeReason'
        }
      ],
      headerSettings2: [
        {
          name: '学员姓名',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '门店编号',
          value: 'merchantCode'
        },
        {
          name: '当前交付小组',
          value: 'teamName'
        },
        {
          name: '修改交付小组',
          value: 'changeTeamName'
        },
        {
          name: '审核状态',
          value: 'changeStatus'
        },
        {
          name: '申请时间',
          value: 'createTime'
        },
        {
          name: '审批原因',
          value: 'changeReason'
        }
      ],
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager'
    this.headerSettings = this.isAdmin ? this.headerSettings1 : this.headerSettings2
    if (this.isAdmin) {
      this.getTeacherList()
    }
    this.getHeaderlist()

    this.initData()
  },
  watch: {
    isAdmin: function (val) {
      this.headerSettings = val ? this.headerSettings1 : this.headerSettings2
    },
    times: {
      immediate: true,
      deep: true,
      handler(val) {
        console.log(val != null && val.length > 0);
        if (val != null && val.length > 0) {
          this.querydata.startTime = val[0]
          this.querydata.endTime = val[1]
        }
      }
    }
  },
  methods: {
    changeTime(e) {
      console.log(e);
    },
    // 改变交付中心编号事件
    changeInput(e) {
      if (!!e) {
        let arr = this.option.filter(i => i.deliverMerchantCode == e)
        this.deliverName = arr.length > 0 ? arr[0].deliverMerchantName : this.deliverName
      }
    },
    feachData() {
      this.querydata.pageNum = 1
      this.initData()
    },
    onChild(e) {
      console.log("获取子组件传过来的值：", e);
      this.dateslot = e;
      if (this.dateslot != "" && this.timeslot != "") {
        this.editForm.expectTime = this.dateslot + " " + this.timeslot + ":00";
        console.log(this.editForm.expectTime);
      }
    },
    // 初始化表格
    async initData() {
      let that = this
      let { data } = await changeTeamList(this.querydata)
      // console.log(data, '=======================')
      this.tableData = data.data
      this.total = Number(data.totalItems)
      // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))

    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return 'background:#f5f7fa'
      }
    },
    //重置
    rest() {
      // this.$refs.querydata.resetFields()
      this.times = []
      this.querydata = {
        pageNum: 1,
        pageSize: 10,//页容量
        studentName: '',
        studentCode: '',
        merchantCodeOrMerchantPhone: '',
        deliverMerchantCode: "",
        deliverMerchantName: "",
        status: '',
        startTime: '',
        endTime: '',
      }
      this.initData()
    },
    // 获取交付中心
    async getTeacherList() {
      let allData = await deliverlist(this.selectObj);
      this.option = this.option.concat(allData.data.data);
      this.optionTotal = Number(allData.data.totalPage)
    },
    // 改变交付中心编号事件
    changeInput(e) {
      if (!!e) {
        let arr = this.option.filter(i => i.deliverMerchantCode == e)
        this.querydata.deliverMerchantName = arr.length > 0 ? arr[0].deliverMerchantName : this.querydata.deliverMerchantName
      }
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        if (this.selectObj.pageNum == this.optionTotal) return//节流防抖
        this.selectObj.pageNum++
        this.getTeacherList()
      }
    },
    // 清除交付中心名称事件
    clearSearchRecord() {
      setTimeout(() => {
        if (this.querydata.deliverName == '') {
          this.option = []
          this.selectObj.pageNum = 1
          this.selectObj.deliverName = ''
          this.getTeacherList()
        }
      }, 500)
      this.$forceUpdate()
    },
    // 改变交付中心名称事件
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        // this.querydata.deliverMerchantCode = ''
        this.selectObj.pageNum = 1;
        this.getTeacherList();
      } else {
        let arr = this.option.filter(i => i.deliverMerchantName == e)
        this.querydata.deliverMerchantCode = arr[0].deliverMerchantCode
      }
    },
    changeMessage() {
      this.$forceUpdate();
    },
    // 表头设置
    headerList() {
      if (this.tableHeaderList.length > 0) {
        // console.log(this.tableHeaderList)
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value) // 回显
      }
      this.HeaderSettingsStyle = true
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e
    },
    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {}
      data = this.isAdmin ? {
        type: 'changeTeamAud-admin',
        value: JSON.stringify(arr)
      } : {
        type: 'changeTeamAud',
        value: JSON.stringify(arr)
      }
      this.setHeaderSettings(data)
    },
    // 获取表头设置
    async getHeaderlist() {
      let data = {}
      data = this.isAdmin ? { type: 'changeTeamAud-admin' } : { type: 'changeTeamAud' }
      await getTableTitleSet(data).then((res) => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value)
        } else {
          this.tableHeaderList = this.headerSettings
        }
      })
    },
    // 设置表头
    async setHeaderSettings(data) {
      console.log(data)
      await setTableList(data).then((res) => {
        this.$message.success('操作成功')
        this.HeaderSettingsStyle = false
        this.getHeaderlist()
      })
    },
    // 分页
    handleSizeChange(val) {
      this.querydata.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.querydata.pageNum = val
      this.initData()
    },
    // 动态class
    transformStatus(status) {
      switch (status) {
        case 1:
          return "已通过";
        case 2:
          return "已拒绝";
        case 3:
          return "审核中";
      }
    },
    // 动态class
    statusClass(status) {
      switch (status) {
        case 3:
          return "primary";
        case 1:
          return "normal";
        case 2:
          return "error";
      }
    },
  }
}
</script>

<style lang="scss" scoped>
body {
  background-color: #f5f7fa;
}
.primary {
  color: #46a6ff;
}
.normal {
  color: rgb(28, 179, 28);
}

.error {
  color: rgba(234, 36, 36, 1);
}

.frame {
  // margin:  0 30px;
  background-color: rgba(255, 255, 255);
  padding: 20px;
}

.el-button--success {
  color: #ffffff;
  background-color: #6ed7c4;
  border-color: #6ed7c4;
}

.transferred {
  color: #ea2424;
}

.no_transferred {
  color: #1cb31c;
}
</style>
