import request from '@/utils/request';

// 试课列表
export const getExperienceList = (data) => {
  return request({
    url: '/deliver/web/experience/getExperienceList',
    method: 'get',
    params: data
  });
};
// 试课列表
export const getNoPayExperienceList = (data) => {
  return request({
    url: '/deliver/web/experience/getNoPayExperienceList',
    method: 'get',
    params: data
  });
};

// 未填写试课单列表(阿拉鼎)
export const getExperienceOrderList = (data) => {
  return request({
    url: '/alading/exp/pageExperienceOrder',
    method: 'get',
    params: data
  });
};

// 未填写试课单列表(甄选)
export const getExperienceOrderZXList = (data) => {
  return request({
    url: '/zx/exp/pageExperienceOrder',
    method: 'get',
    params: data
  });
};
// 查询试课反馈列表
export const geteExperienceInfo = (data) => {
  return request({
    url: '/deliver/app/teacher/getExperienceInfo/' + data.id,
    method: 'post'
  });
};
// 查询正式课反馈列表
export const getFeedbackInfo1 = (data) => {
  return request({
    url: '/deliver/app/teacher/getFeedbackInfo',
    method: 'get',
    params: data
  });
};
//新增正课 学习反馈
export const addFeedback = (params) => {
  return request({
    url: '/deliver/app/teacher/addFeedback',
    method: 'post',
    params
  });
};
//新增试课 学习反馈

export const addExperienceFeedback = (data) => {
  return request({
    url: '/deliver/app/teacher/addExperienceFeedback',
    method: 'post',
    data
  });
};

//新增正课 学习反馈--其他课程类型
export const addNewFeedback = (params) => {
  return request({
    url: '/deliver/app/teacher/addSimpleFeedback',
    method: 'post',
    params
  });
};
//新增试课 学习反馈-其他课程类型

export const addNewExperienceFeedback = (data) => {
  return request({
    url: '/deliver/app/teacher/addSimpleExperienceFeedback',
    method: 'post',
    data
  });
};
// 体验课程-数据查看
export const getCourseInfo = (id) => {
  return request({
    url: '/deliver/web/experience/getCourseInfo/' + id,
    method: 'get'
  });
};
export const updateExperienceReservationTime = (data) => {
  return request({
    url: '/znyy/bSysConfig/updateExperienceReservationTimeConfig',
    method: 'GET',
    params: data
  });
};
//校验手机号
export const check = (id) => {
  return request({
    url: '/alading/exp/check?mobile=' + id,
    method: 'get'
  });
};

// 创建试课单
export const createCourse = (data) => {
  return request({
    url: '/deliver/web/experience/createCourse',
    method: 'post',
    data: data
  });
};

// 填写试课单(阿拉鼎)
export const fillInCourse = (data) => {
  return request({
    url: '/alading/exp/saveDeliver',
    method: 'post',
    data: data
  });
};

// 填写试课单(甄选)
export const fillInCourseZX = (data) => {
  return request({
    url: '/zx/exp/saveDeliver',
    method: 'post',
    data: data
  });
};

// 编辑试课单
export const update = (data) => {
  return request({
    url: '/deliver/web/experience/update',
    method: 'put',
    data: data
  });
};

// 获取交付中心
export const allDeliver = () => {
  return request({
    url: '/deliver/web/experience/allDeliver',
    method: 'get'
  });
};
export const xueguanListApi = (data) => {
  return request({
    url: '/deliver/web/sysUser/getLearnManagerList',
    method: 'get',
    params: data
  });
};

// 获取教练列表
export const selAllTeacher = (data) => {
  return request({
    url: '/deliver/web/learnManager/selAllTeacher',
    method: 'get',
    params: data
  });
};
//修改学员所属教练
export const updateStudentBinding = (data) => {
  return request({
    url: '/deliver/web/learnManager/updateStudentBinding',
    method: 'post',
    data
  });
};

// 编辑信息详情
export const updateDetail = (id) => {
  return request({
    url: '/deliver/web/experience/updateDetail?id=' + id,
    method: 'get'
  });
};

// 获取 数学-版本
export const selectVersionInfo = (params) => {
  return request({
    url: '/dyf/math/web/basisConfig/selectVersionInfo',
    method: 'get',
    params
  });
};

// 获取 数学-学科列表/课程分类
export const selectTreeVersion = (params) => {
  return request({
    url: '/dyf/math/web/coursePeriodConfig/selectTreeVersion',
    method: 'get',
    params
  });
};

// 获取数学试课单详情
export const queryStudentExperienceDetail = (id) => {
  return request({
    url: '/dyf/math/wab/extend/queryStudentExperienceDetail?orderId=' + id,
    method: 'get'
  });
};

// 数学课程-试课单提交信息接口
export const saveStudentExperience = (data) => {
  return request({
    url: '/dyf/math/wab/extend/saveStudentExperience',
    method: 'post',
    data
  });
};
// 获取数学-上课信息对接表-详情
export const queryStudentContactInfoDetail = (id) => {
  return request({
    url: '/dyf/math/wab/extend/queryStudentContactInfoDetail?id=' + id,
    method: 'get'
  });
};

// 数学课程-上课信息对接表-提交信息接口
export const saveStudentContactInfo = (data) => {
  return request({
    url: '/dyf/math/wab/extend/saveStudentContactInfo',
    method: 'post',
    data: data
  });
};

// 年级选择
export const GradeType = () => {
  return request({
    url: '/znyy/bvstatus/GradeType',
    method: 'get'
  });
};

// 体验课程排课
export const planCourse = (data) => {
  return request({
    url: '/deliver/web/experience/planCourse',
    method: 'POST',
    params: data
  });
};

// 体验课程排课
export const getExperienceInfo = (id) => {
  return request({
    url: '/deliver/web/experience/getExperienceInfo',
    method: 'get',
    params: { id: id }
  });
};

// 预警相关-学习预警列表
export const getWarningList = (data) => {
  return request({
    url: '/deliver/web/warning/getWarningList',
    method: 'get',
    params: data
  });
};

// 预警相关-学习预警详情
export const getWarningInfo = (warningId) => {
  return request({
    url: '/deliver/web/warning/getWarningInfo',
    method: 'get',
    params: { warningId: warningId }
  });
};

// 预警相关-学员分配学管师
export const allotLearnTube = (data) => {
  return request({
    url: '/deliver/web/warning/allotLearnTube',
    method: 'POST',
    params: data
  });
};

// 预警相关-新增学习预警反馈
export const addWarningFeedback = (data) => {
  return request({
    url: '/deliver/web/warning/addWarningFeedback',
    method: 'POST',
    params: data
  });
};
// 预警相关-新增学习预警反馈
export const belongDeliverAndAllDeliver = (data) => {
  return request({
    url: '/deliver/web/experience/belongDeliverAndAllDeliver?id=' + data,
    method: 'get'
  });
};

// 重新指派
export const submitAssign = (paramUrl) => {
  return request({
    url: '/deliver/web/experience/assign' + paramUrl,
    method: 'put'
  });
};

// 批量支付试课单
export const batchPayExperience = (data) => {
  return request({
    url: '/deliver/web/experience/batchPayExperience',
    method: 'POST',
    data: data
  });
};

// 获取所有可预约时间段人数
export const selAllExperienceUsableTime = (data) => {
  return request({
    url: '/deliver/web/experience/selAllExperienceUsableTime',
    method: 'GET',
    params: data
  });
};

// 设置可预约时间段人数
export const setExperienceUsableTimeNum = (data) => {
  return request({
    url: '/deliver/web/experience/setExperienceUsableTimeNum',
    method: 'POST',
    data
  });
};

// 扣绩效
export const deductingPerformance = (data) => {
  return request({
    url: 'deliver/web/experience/deductWage',
    method: 'PUT',
    params: data
  });
};

// 新增试课奖励分润名单
export const getshareProfit = (data) => {
  return request({
    url: 'deliver/web/expReward/profitRoster/getPageList',
    method: 'GET',
    params: data
  });
};

export const getScheduleTime = (orderId) => {
  return request({
    url: 'zx/exp/getInfo',
    method: 'get',
    params: { orderId: orderId }
  });
};
export const initQrCode = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/getSubscribeQrCode',
    method: 'GET',
    params: data
  });
};

export const getParentByMobile = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/getParentByMobile',
    method: 'GET',
    params: data
  });
};

export const useableList = (data) => {
  return request({
    url: '/deliver/web/teacher/plan/teacher/useable',
    method: 'GET',
    params: data
  });
};

export const changeTeacher = (data) => {
  return request({
    url: '/deliver/web/teacher/plan/teacher/change',
    method: 'PUT',
    params: data
  });
};

export const findTeacherById = (data) => {
  return request({
    url: '/deliver/web/teacher/findTeacherById',
    method: 'GET',
    params: data
  });
};
export const changestudyTeacher = (data) => {
  return request({
    url: '/deliver/web/teacher/changeStudyTeacher',
    method: 'POST',
    data
  });
};
export const getCurrentHour = (data) => {
  return request({
    url: '/znyy/bSysConfig/getExperienceReservationTimeConfig',
    method: 'GET',
    params: data
  });
};
export const findTeamList = (data) => {
  return request({
    url: '/deliver/web/team/list/findSimpleTeam',
    method: 'GET',
    params: data
  });
};
// export const findTeamList = (data) => {
//   return request({
//     url: '/deliver/web/team/list/all',
//     method: 'GET',
//     params: data
//   })
// }
export const addExportData = (data) => {
  return request({
    url: '/deliver/web/expReward/profitRoster/exportExcelPageList',
    method: 'GET',
    params: data
  });
};

export const finishCourse = (data) => {
  return request({
    url: '/deliver/app/teacher/updateStudyFinish',
    method: 'POST',
    params: data
  });
};

// 获取授课视频信息
export const getVideoInfo = (data) => {
  return request({
    url: '/dyf/web/xktCourse/all/video',
    method: 'GET',
    params: data
  });
};
