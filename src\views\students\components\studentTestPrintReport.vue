<template>
  <div class="app-container">
    <div ref="print">
      <div id="print" class="clearfix">
        <img src="../../../assets/bg.png"
          style="width:1124px !important;height:714px !important;display: block !important;margin: 0 auto !important;">
        <div
          style="width: 58% !important;position: absolute !important;left: 50% !important;margin-left: -29% !important;top: 260px !important;font-size:20px;font-family:arial !important;color:#474747 !important">
          <div><a
              style="color:black !important;font-size: 25px !important; font-weight: 600 !important; margin-right: 5px !important;">{{ studentName }}</a>同学
          </div>
          <div style="margin-top: 20px !important;line-height: 30px !important;text-indent:2em">
            经过一段时间的努力学习，您已记会《{{ courseName }}》Round<a style="color:#474747">{{ nowRound }}</a>共<a
              style="color:red !important;">{{ wordCount }}</a>词，识记率达<a
              style="color:#ff0000 !important;">100%</a>，顺利完成识记任务。建议后期按照21天抗遗忘计划表继续复习，
            同时进行相关阅读课程训练。
          </div>
          <div
            style="text-align: center !important; float: right !important;margin-top: 90px !important;margin-right: 30px !important;font-size:16px !important">
            <span>{{ addTime }}
            </span><br><span> 鼎校英语单词速记</span></div>
        </div>
      </div>
    </div>
    <div class="buttom clearfix">
      <a style="background:#3dab93" @click="print()">打印</a>
      <a style="background:#f0ad4e; margin-left:20px;" @click="goBack()">返回</a>
    </div>
  </div>
</template>

<script>
import printCloseApi from "@/api/printApi/studentPrintClose";
import ls from '@/api/sessionStorage'
export default {
  data() {
    return {
      studentName: '',
      courseName: '',
      nowRound: '',
      wordCount: '',
      addTime: ''
    }
  },
  created() {
    this.getPrintClose()
  },
  methods: {
    getPrintClose() {
      printCloseApi.getPrintClose(ls.getItem('wordPrintCode'),
        ls.getItem('studentCode'), ls.getItem('merchantCode')).then(res => {
          this.studentName = res.data.studentName
          this.courseName = res.data.courseName
          this.nowRound = res.data.nowRound
          this.wordCount = res.data.wordCount
          this.addTime = res.data.addTime
        })
    },
    print() {
      printCloseApi.editEnable(ls.getItem('wordPrintCode')).then(res => {


      })
      this.$print(this.$refs.print)
    },
    //返回按钮
    goBack() {
      this.$store.dispatch('delVisitedViews', this.$route);
      this.$router.go(-1);
      this.$router.push({
        path: "/student/studentCourseRecord"
      });

    }
  }
}
</script>

<style scoped>
.app-container {
  background-color: #f3f3f4;
}

.clearfix {
  zoom: 1;
}

.clearfix:before,
.clearfix:after {
  content: "";
  line-height: 0;
  display: table;
}

.clearfix:after {
  clear: both;
}

#print {
  width: 1124px;
  height: 714px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.buttom {
  margin: 10px 50px 10px 0;
  color: #fff;
  cursor: pointer;
  text-align: right;
}

.buttom a {
  display: inline-block;
  color: #fff;
  width: 90px;
  height: 35px;
  border-radius: 5px;
  text-align: center;
  line-height: 35px;
}</style>
