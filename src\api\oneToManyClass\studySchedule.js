// 一对多-学习课程表相关接口
import request from '@/utils/request';
/**
 * 获取学习课程表分页数据
 * @param {*} data
 * @returns
 */
export const getStudyScheduleData = (data) => {
  return request({
    url: '/deliver/web/oneMore/getOneMorePlanCourseList',
    method: 'GET',
    params: data
  });
};

/**
 * 获取反馈详情
 * @param {*} data
 * @returns
 */
export const getFeedbackInfo = (data) => {
  return request({
    url: '/deliver/web/oneMore/getCourseStudyInfo',
    method: 'GET',
    params: data
  });
};
/**
 * 获取数学反馈详情
 * @param {*} data
 * @returns
 */
export const getMathFeedbackInfo = (data) => {
  return request({
    url: '/dyf/math/web/feedback/detail',
    method: 'GET',
    params: data
  });
};
/**
 * 学习课程表删除
 * @param {*} data
 * @returns
 */
export const setStudyScheduleDelete = (classStudyId, courseType) => {
  return request({
    url: '/deliver/web/oneMore/deletePlanStudy',
    method: 'POST',
    data: { classStudyId, courseType }
  });
};
