import Vue from 'vue';
import Router from 'vue-router';
/* Layout */
import Layout from '../views/layout/Layout';

const _import = require('./_import_' + process.env.NODE_ENV);
// 按模块抽离
import systemRouter from './modules/system';
import peizhiRouter from './modules/peizhi';
import rukuRouter from './modules/ruku';
import orderManageRouter from './modules/orderManage';
import trainingCenterRouter from './modules/trainingCenter';
import pclassRouter from './modules/pclass';
import oneToManyClassRouter from './modules/oneToManyClass';
import TrialClassListRouter from './modules/TrialClassList';
import FinanceRouter from './modules/Finance';
import messageRouter from './modules/message';
import robotSettingRouter from './modules/robotSetting';
import downloadListRouter from './modules/downloadList';
import transferOrderRouter from './modules/transferOrder';
import deductionRconciliationRouter from './modules/deductionRconciliation';
Vue.use(Router);
export const constantRouterMap = [
  { path: '/', redirect: '/pclass/index' },
  {
    path: '/login',
    component: _import('login/index'),
    hidden: true
  },
  {
    path: '/ui_test_auto_login',
    component: _import('login/auto_login'),
    hidden: true
  },
  {
    path: '/forgot',
    component: _import('forgot/index'),
    hidden: true
  },
  {
    path: '/404',
    component: _import('error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: _import('error-page/401'),
    hidden: true
  }
];

// 动态路由
export const asyncRouterMap = [
  systemRouter, // 权限管理
  peizhiRouter, // 配置管理
  rukuRouter, // 入库管理
  orderManageRouter, // 接单管理
  trainingCenterRouter, // 学习中心
  pclassRouter, // 正式学员管理
  TrialClassListRouter, // 试课学员管理
  oneToManyClassRouter, // 一对多学员管理
  FinanceRouter, // 财务管理
  messageRouter, // 消息管理
  robotSettingRouter, // 机器人管理
  downloadListRouter, // 下载列表
  transferOrderRouter, // 资转订单
  deductionRconciliationRouter, // 合同扣款对账
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
];

export default new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
});
