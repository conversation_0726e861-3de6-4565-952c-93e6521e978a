import request from '@/utils/request'

// 获取教练等级数据
export function getTeacherLevel(query) {
  return request({
    url: '/znyy/bSysConfig/getTeacherLevelConfig',
    method: 'get',
    params: query
  })
}
//更新教练等级
export function updateTeacherLevel(data) {
  return request({
    url: '/deliver/web/teacher/updateTeacherLevel',
    method: 'put',
    params: data
  })
}
//批量删除学习课程
export function DeletePlanStudy(data) {
  return request({
    url: '/deliver/web/learnManager/batchDeletePlanStudy',
    method: 'delete',
    data: data
  })
}
//批量删除学习课程列表
export function DeletePlanStudyList(data) {
  return request({
    url: `/deliver/web/learnManager/batchDeletePlanStudyList?pageSize=1000&pageNum=1`,
    method: 'post',
    data: data
  })
}

// 获取续费提醒配置信息
export function getRenewalReminderConfig() {
  return request({
    url: '/znyy/bSysConfig/getRenewalReminderConfig',
    method: 'get',

  })
}
//修改续费配置信息
export function UpdateReConfig(data) {
  return request({
    url: `/znyy/bSysConfig/addOrUpdateRenewalReminderConfig`,
    method: 'post',
    data: data
  })
}

// 获取续费提醒列表
export function stuentRenewalreminderList(query) {
  return request({
    url: '/deliver/web/learnManager/stuentRenewalreminderList',
    method: 'get',
    params: query
  })
}
