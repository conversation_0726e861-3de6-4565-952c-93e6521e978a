<template>
  <div></div>
</template>
<script>
  import courseApi from '@/api/training/course';
  export default {
    data() {
      return {};
    },
    mounted() {
      console.log('mounted');
      courseApi
        .noSecretDirect({ loginUserName: localStorage.getItem('Name'), roleTag: localStorage.getItem('roleTag') })
        .then((res) => {
          console.log(res, '99999999');
          if (res.success) {
            window.open(
              'https://study.dxznjy.com/#/trainingCenter/courseCenter?isNoSecretLogin=true&appSource=DELIVER&loginUserName=' +
                localStorage.getItem('Name') +
                '&roleTag=' +
                localStorage.getItem('roleTag')
            );
            this.$router.push({ path: '/' });
          } else {
            this.$message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('error', err);
        });
    }
  };
</script>
