import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const orderManageRouter = {
  path: '/orderManage',
  component: Layout,
  meta: {
    perm: 'm:orderManage',
    title: '接单管理',
    icon: 'divisionList'
  },
  children: [
    {
      path: 'testStudent',
      name: 'testStudent',
      component: _import('orderManage/testStudent/index'),
      meta: {
        perm: 'm:orderManage:testStudent',
        title: '试课新学员列表',
        noCache: false,
        icon: 'studentList'
      }
    },
    {
      path: 'formalStudent',
      name: 'formalStudent',
      component: _import('orderManage/formalStudent/index'),
      meta: {
        perm: 'm:orderManage:formalStudent',
        noCache: false,
        title: '正式课新学员列表',
        icon: 'studentList'
      }
    },
    {
      path: 'deliveryTeam',
      name: 'deliveryTeam',
      component: _import('orderManage/deliveryTeam/index'),
      meta: {
        perm: 'm:orderManage:deliveryTeam',
        title: '交付小组配置',
        noCache: false,
        icon: 'role_perm'
      }
    },
    {
      path: 'orderSetting',
      name: 'orderSetting',
      component: _import('orderManage/orderSetting/index'),
      meta: {
        perm: 'm:orderManage:orderSetting',
        title: '承单量配置',
        noCache: false,
        icon: 'role_perm'
      }
    },
    {
      path: 'orderAuditing',
      name: 'orderAuditing',
      component: _import('orderManage/orderAuditing/index'),
      meta: {
        perm: 'm:orderManage:orderAuditing',
        title: '承单量审核',
        noCache: false,
        icon: 'role_perm'
      }
    },
    {
      path: 'turnSort',
      name: 'turnSort',
      component: _import('orderManage/turnSort/index'),
      meta: {
        perm: 'm:orderManage:turnSort',
        title: '轮排配置',
        noCache: false,
        icon: 'role_perm'
      }
    },
    {
      path: 'lossTable',
      name: 'lossTable',
      component: _import('orderManage/lossTable/index'),
      meta: {
        perm: 'm:orderManage:lossTable',
        title: '流失单列表',
        noCache: false,
        icon: 'role_perm'
      }
    },
    {
      path: 'StoreteamConfig',
      name: 'StoreteamConfig',
      component: _import('orderManage/StoreteamConfig/index'),
      meta: {
        perm: 'm:orderManage:StoreteamConfig',
        title: '门店小组配置',
        noCache: false,
        icon: 'group'
      }
    }
  ]
};

export default orderManageRouter;
