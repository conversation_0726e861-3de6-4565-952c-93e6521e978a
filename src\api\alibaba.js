import request from '@/utils/request'
import OSS from 'ali-oss'
import store from '@/store'

export function getSts() {
  return request({
    url: '/znyy/alibaba/oss/sts',
    method: 'get'
  })
}

export function sendSmsCode(mobile, type) {
  return request({
    url: '/znyy/alibaba/sms/send',
    method: 'PUT',
    params: {'mobile': mobile, 'type': type}
  })
}

export function getPrSts() {
  return request({
    url: '/znyy/alibaba/oss/publicRead/sts',
    method: 'get'
  })
}

export function ossClient() {
  var {stsToken, stsBucketName, stsAccessKeyId, stsAccessKeySecret, stsExpiration} = store.getters
  var region = 'oss-cn-shanghai'
  var tokenAvalible = false
  if (stsToken !== undefined && stsToken != null && stsToken.length > 5) {
    tokenAvalible = true
  }
  if (tokenAvalible) {
    const date = new Date(Date.parse(stsExpiration.replace(/-/g, '/')))
    const nowDate = new Date()
    if (nowDate > date) {
      tokenAvalible = false
    }
  }
  if (!tokenAvalible) {
    store.dispatch('settings/getPrSts').then(() => {
      var uploadConf = {
        region: '',
        accessKeyId: '',
        accessKeySecret: '',
        bucket: '',
        stsToken: '',
        timeout: 60000
      }
      uploadConf.timeout = 6000000
      uploadConf.region = region
      uploadConf.accessKeyId = stsAccessKeyId
      uploadConf.accessKeySecret = stsAccessKeySecret
      uploadConf.bucket = stsBucketName
      uploadConf.stsToken = stsToken
      console.log(uploadConf)
      return new OSS(uploadConf)
    })
      .catch(() => {
        console.log('fail')
      })
  } else {
    var uploadConf = {
      region: '',
      accessKeyId: '',
      accessKeySecret: '',
      bucket: '',
      stsToken: '',
      timeout: 60000,
    }
    uploadConf.timeout = 6000000
    uploadConf.region = region
    uploadConf.accessKeyId = stsAccessKeyId
    uploadConf.accessKeySecret = stsAccessKeySecret
    uploadConf.bucket = stsBucketName
    uploadConf.stsToken = stsToken
    return new OSS(uploadConf)
  }
}

export function ossPrClient() {
  var {prStsToken, prStsBucketName, prStsAccessKeyId, prStsAccessKeySecret, prStsExpiration} = store.getters
  var region = 'oss-cn-shanghai'
  var tokenAvalible = false

  if (prStsToken !== undefined && prStsToken != null && prStsToken.length > 5) {
    tokenAvalible = true
  }
  if (tokenAvalible) {
    const date = new Date(Date.parse(prStsExpiration.replace(/-/g, '/')))
    const nowDate = new Date()
    if (nowDate > date) {
      tokenAvalible = false
    }
  }
  if (!tokenAvalible) {
    store.dispatch('settings/getPrSts').then(() => {
      var uploadConf = {
        region: '',
        accessKeyId: '',
        accessKeySecret: '',
        bucket: '',
        stsToken: '',
        timeout: 60000,
      }
      uploadConf.timeout = 6000000
      uploadConf.region = region
      uploadConf.accessKeyId = prStsAccessKeyId
      uploadConf.accessKeySecret = prStsAccessKeySecret
      uploadConf.bucket = prStsBucketName
      uploadConf.stsToken = prStsToken
      var oss = new OSS(uploadConf);
      return oss;
    })
      .catch(() => {
        console.log('fail')
      })
  } else {
    var uploadConf = {
      region: '',
      accessKeyId: '',
      accessKeySecret: '',
      bucket: '',
      stsToken: '',
      timeout: 60000,
    }
    uploadConf.timeout = 6000000
    uploadConf.region = region
    uploadConf.accessKeyId = prStsAccessKeyId
    uploadConf.accessKeySecret = prStsAccessKeySecret
    uploadConf.bucket = prStsBucketName
    uploadConf.stsToken = prStsToken
    var oss = new OSS(uploadConf);
    return oss;
  }
}
