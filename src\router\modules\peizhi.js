
import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const peizhiRouter = {
  path: '/peizhi/index',
  component: Layout,
  name: 'peizhi',
  meta: {
    perm: 'm:peizhi:index',
    title: '配置管理',
    icon: 'divisionList',
    noCache: true
  },
  children: [
    {
      path: 'peizhi_index',
      component: _import('peizhi/index'),
      name: 'index',
      meta: {
        perm: 'm:peizhi:index',
        title: '交付中心配置',
        icon: 'divisionList',
        noCache: true
      }
    }
  ]
}

export default peizhiRouter
