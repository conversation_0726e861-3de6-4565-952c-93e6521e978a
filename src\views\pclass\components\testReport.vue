<template>
  <el-form class="dialog" ref="ruleForm">
    <el-drawer :title="`${reportList.curriculumName}体验反馈结果`" :visible="reportDialog" :direction="direction"
      :size="screenWidth > 1300 ? '530px' : '70vw'" :before-close="handleClose">
      <div class="">
        <el-row>
          <el-col class="paike">
            日期：
            <span>{{ reportList.dateTime }}</span>
          </el-col>
          <el-col class="paike">
            姓名：
            <span>{{ reportList.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ reportList.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            学员编号：
            <span>{{ reportList.studentCode }}</span>
          </el-col>
          <el-col class="paike">
            上学时间：
            <span>{{ reportList.studyTime }}个</span>
          </el-col>
          <el-col class="paike">
            教练：
            <span>{{ reportList.teacherName }}个</span>
          </el-col>
          <el-col class="paike">
            试学学时：
            <span>{{ reportList.studyHour }}/时</span>
          </el-col>
          <el-col class="paike">
            词汇测试水平：
            <span>{{ !!reportList.vocabularyLevel ? reportList.vocabularyLevel : '未测试' }}</span>
          </el-col>
          <el-col class="paike">
            首测词汇量：
            <span>{{ !!reportList.expWords ? reportList.expWords : '未测试' }}</span>
          </el-col>
          <el-col class="paike">
            识记词汇数量：
            <span>{{ reportList.todayWords }}</span>
          </el-col>
          <el-col class="paike">
            遗忘数量：
            <span>{{ reportList.forgetWords }}</span>
          </el-col>
          <el-col class="paike">
            记忆率：
            <span>{{ !!reportList.wordsRate ? reportList.wordsRate + '%' : '' }}</span>
          </el-col>
          <el-col class="paike">
            体验词库：
            <span>{{ reportList.studyBooks }}</span>
          </el-col>
          <el-col class="paike">
            记忆情况：
            <span>{{ reportList.memoryTime }}分钟记住{{reportList.memoryNum}}个单词</span>
          </el-col>
          <el-col class="paike">
            体验后学习意愿：
            <span>{{ reportList.studyIntention }}</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px">
            学员学习状况反馈：
            <el-input placeholder="请输入内容" type="textarea" autosize v-model="reportList.feedback"
              :disabled="true"></el-input>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </el-form>
</template>

<script>
import { addPlanCourse, screenTeacherListAndHours } from "@/api/paikeManage/LearnManager";
import { getCourseclassList } from "@/api/FinanceApi/assistantWages";

import dayjs from 'dayjs'
import { mapGetters } from 'vuex'

export default {
  name: "testReport",
  props: {
    // 控制弹窗显示
    reportVisible: {
      type: Boolean,
      default: false, //这里默认为false
    },
    // 学员列表的信息viewTime
    reportList: {
      default: false, //这里默认为false
    }
  },
  data() {
    return {
      screenWidth: window.screen.width,
      direction: "rtl",
    };
  },
  created() {
    // this.getCourseList();
    // this.coniditon.reviewTime[1] ='18:29'
  },
  mounted() { },
  computed: {
    reportDialog: {
      get() {
        return this.reportVisible;
      },
      //值变化的时候会被调用
      set(v) {
        this.$emit("changeReport", false);
      },
    },

    ...mapGetters([
      'roles',
    ])
  },
  /*  watch: {
      coniditon: {
        handler() {
          if (this.coniditon.dateList.length === 0) return;
          let hashEmpty = this.coniditon.timeList.some((item) => {
            return !item.startTime || !item.endTime;
          });
          if (hashEmpty) return;
          if (!this.coniditon.teachingType) return;
          // 获取老师列表
          if (this.timer != null) clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            this.getTeacherList();
          }, 500);
        },
        deep: true,
      },
    },*/
  methods: {
    // 关闭弹窗
    handleClose() {
      console.log('关闭弹窗')
      this.reportDialog = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.xubtn {
  margin-left: 7vw;
}

.paike {
  margin-left: 1vw;
  margin-top: 15px;
}

.paikeTwo {
  width: 97%;
}

.cc {
  height: 0;
  margin: 0 1.5vw 0 1.5vw;
  border-bottom: 1px solid #000;
}

div ::v-deep .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
