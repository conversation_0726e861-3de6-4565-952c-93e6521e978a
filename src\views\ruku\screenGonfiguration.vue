<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="110px" label-position="left">
      <el-row>
        <el-col :span="8">
          <el-form-item label="按键查询：">
            <el-input v-model="dataQuery.name" placeholder="请输入按键查询"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="按值查询：">
            <el-input v-model="dataQuery.value" placeholder="请输入按值查询"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="fetchData()">搜索</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-form :inline="true" style="margin-bottom: 20px;">
      <el-button type="primary" icon="el-icon-plus" @click="clickAdd">添加</el-button>
    </el-form>

    <el-table class="common-table" v-loading="tableLoading" :data="tableData" style="width: 100%;margin-bottom: 20px;"
      row-key="id" border default-expand-all :tree-props="{ list: 'children', hasChildren: 'true' }">
  
      <el-table-column prop="enName" label="键"></el-table-column>
      <el-table-column prop="enCode" label="值" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" >
        <template slot-scope="scope">
          <el-button type="primary" size="mini" icon="el-icon-edit-outline"
            @click="handleUpdate(scope.row.id)">编辑</el-button>
          <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="isEnable" label="状态">
          <template slot-scope="scope">
            <span class="green" v-if="scope.row.isEnable === 1">开通</span>
            <span class="red" v-else>暂停</span>
          </template>
        </el-table-column> -->
    </el-table>
    <!-- 添加弹窗 -->
    
    <el-dialog :title="showTitle ? '添加录屏配置' : '编辑录屏配置'" :visible.sync="dialogVisible" width="70%"
      :close-on-click-modal="false" @close="close">
      <el-form :ref="addOrUpdate ? 'addCourseData' : 'updateActive'" :rules="rules"
        :model="addOrUpdate ? addCourseData : updateActive" label-position="left" label-width="80px"
        style="width: 100%;">
        <el-form-item label="键:" prop="enName">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.enName" />
            <el-input v-else v-model="updateActive.enName" />
          </el-col>
        </el-form-item>
        <el-form-item label="值:" prop="enCode">
          <el-col :xs="24" :span="18">
            <el-input v-if="addOrUpdate" v-model="addCourseData.enCode" />
            <el-input v-else v-model="updateActive.enCode" />
          </el-col>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="addOrUpdate" size="mini" type="primary" @click="addActiveFun('addCourseData')">新增</el-button>
        <el-button v-if="!addOrUpdate" size="mini" type="primary"
          @click="updateActiveFun('updateActive')">修改</el-button>
        <el-button size="mini" @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 分页 -->
    <el-col :span="24">
      <el-pagination :current-page="tablePage.currentPage" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="tablePage.totalItems" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-col>
  </div>
</template>

<script>
import screenApi from "@/api/ScreenRecording";
import {
  pageParamNames
} from "@/utils/constants";
export default {
  name: "screenGonfiguration",
  data() {
    return {
      tableLoading: false,
      // 分页
      tablePage: {
        currentPage: 1,
        size: 10,
        totalPage: null,
        totalItems: null
      },
      tableData: [],
      dataQuery: {
        name: '',
        value: ''
      },
      rules: { // 表单提交规则
        enName: [{
          required: true,
          message: '请填写键',
          trigger: 'blur'
        }],
        enCode: [{
          required: true,
          message: '请填写值',
          trigger: 'blur'
        }],

      },
      radio: '', //状态
      dialogVisible: false,
      addOrUpdate: true,
      showTitle:true,
      updateActive: {},
      addCourseData: {},
      rowId: ''
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 查询列表
    fetchData() {
      const that = this
      that.tableLoading = true
      const data = {
        pageNum: that.tablePage.currentPage,
        pageSize: that.tablePage.size,
        enName: that.dataQuery.name,
        enCode: that.dataQuery.value
      }
      screenApi.systemList(data).then(res => {
        console.log(res);
        that.tableData = res.data.data
        that.tableLoading = false
        // 设置后台返回的分页参数
        pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(res.data[name])))
      })
    },
    //新增操作
    clickAdd() {
      this.addCourseData = {
        'enName': '',
        'enCode': '',
      }
      this.dialogVisible = true;
      this.addOrUpdate = true;
      this.showTitle = true;
    },
    // 新增提交
    addActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '新增App版本管理',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log('11111111');
          debugger
          screenApi.addsystem(that.addCourseData).then(() => {
            that.dialogVisible = false
            that.fetchData();
            that.radio = '';
            loading.close()
            //that.$nextTick(() => that.fetchData())
            that.$message.success('新增录屏配置成功')
          }).catch(err => {
            loading.close()
          })
        } else {
          console.log('error submit!!')
          //loading.close();
          return false
        }
      })
    },
    // 点击编辑按钮
    handleUpdate(id) {
      const that = this
      that.dialogVisible = true
      that.addOrUpdate =false
      that.showTitle = false;
      console.log(that.addOrUpdate);
      console.log('11111111', id);
      that.rowId = id

      screenApi.seeDetails(id).then(res => {
        console.log(res.data);
        that.updateActive = res.data
        that.radio = that.updateActive.isEnable.toString(); //状态回显
      }).catch(err => {

      })


    },
    // 修改提交
    updateActiveFun(ele) {
      const that = this
      that.$refs[ele].validate(valid => { // 表单验证
        if (valid) {
          that.updateActive.id = that.rowId
          const loading = this.$loading({
            lock: true,
            text: '修改系统配置提交',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          console.log(that.updateActive, '1111111111111111');

          screenApi.eidtDispositio(that.updateActive).then(() => {
            that.dialogVisible = false
            loading.close()
            that.$nextTick(() => that.fetchData())
            that.$message.success('修改系统配置成功')

          }).catch(err => {
            // 关闭提示弹框
            loading.close()
          })
        } else {
          console.log('error submit!!')
          // loading.close();
          return false
        }
      })
    },
    // 单个删除
    singleDelete(id) {
      const that = this;
      this.$confirm('确定操作吗?', '删除系统配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        screenApi.deletesystem(id).then(res => {
          that.$nextTick(() => that.fetchData())
          that.$message.success('删除成功!')
        }).catch(err => {

        })
      }).catch(err => {

      })
    },
    // 关闭
    close() {
      this.dialogVisible = false; 
      
      console.log(this.showTitle);
    },
    // 分页
    handleSizeChange(val) {
      this.tablePage.size = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.tablePage.currentPage = val;
      this.fetchData();
    },
    // 状态改变事件
    change(radio) {
      if (radio == "1") {
        this.addCourseData.isEnable = 1;
      } else {
        this.addCourseData.isEnable = 0;
      }
    },
  }
};
</script>

<style>
.container-card {
  padding: 15px 15px 15px 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.period-table td,
.period-table th {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>