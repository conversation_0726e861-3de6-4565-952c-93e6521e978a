<template>
  <div class="dialog">
    <el-drawer
      title="请假处理"
      :visible.sync="classCardstyle_"
      :direction="direction"
      @close="handleClose"
    >
      <div class="borders">
        <el-row>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >课程名称:鼎英语</el-col
          >
          <el-col style="margin-top: 2vw; margin-left: 1vw">
            课程内容: {{ leaveDialogList.courseName }}
          </el-col>

          <el-col style="margin-top: 2vw; margin-left: 1vw">
            学员名称:{{ leaveDialogList.studentName }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >复习时间:{{ leaveDialogList.studyDateTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >调整时间:{{ leaveDialogList.date }}{{ leaveDialogList.startTime
            }}{{ leaveDialogList.endTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw"
            >请假原因:
            <div style="margin: 1vw">
              <el-input
                style="width: 24vw"
                type="textarea"
                :rows="2"
                disabled
                placeholder="请输入内容"
                v-model="leaveDialogList.cause"
              >
              </el-input>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { screenTeacherList } from "@/api/paikeManage/LearnManager";
import { adjustPlanStudy } from "@/api/paikeManage/classCard";

export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    LeaveViewStyle: {
      type: Boolean,
      default: false,
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl",
    },
  },
  name: "LeaveDialog",
  data() {
    return {
      value1: "",
      textarea: "",
      teacherId: "",
      leaveDialogList: [],
      teacherList: {}, //排课获得的老师列表
    };
  },
  //计算属性
  computed: {
    classCardstyle_: {
      get() {
        return this.LeaveViewStyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("LeaveDialog", v);
      },
    },
  },
  methods: {
    paikeFn() {
      this.$emit("fMethod");
    },
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.$emit("LeaveDialog", false);
    },
    timestudy1() {
      if (this.classCardnum.endStudyTime != "") {
        this.getTeachlist();
      }
    },
    timestudy2() {
      if (this.classCardnum.startStudyTime != "") {
        this.getTeachlist();
      }
    },
  },
};
</script>

<style lang="scss">
.borders {
  margin: 1vw 1vw;
  width: 28vw;
  height: 32vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}
</style>


<style scoped>
::v-deep .el-textarea__inner {
  height: 8vw;
  margin-top: 0.2vw;
}
div /deep/ .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>
