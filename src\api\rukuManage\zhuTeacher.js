import request from '@/utils/request'
import { data } from 'jquery'
// 新增教练信息
export const addZhugetTeacher = (data) => {
  return request({
    url: "/deliver/web/teacher/add",
    method: 'post',
    data
  })
}
// 获取教练信息
export const getTeacher = (id) => {
  return request({
    url: '/deliver/web/teacher/getTeacher',
    method: 'GET',
    params: { id: id }
  })
}
// 教练列表查询
export const searchTeacherApi = (data) => {
  return request({
    url: '/deliver/web/teacher/pageTeacherList',
    method: 'post',
    params: data
  })
}

//获取教练当前周的备课情况
export const searchTeacherPrepareRecordList = (teacherId) => {
  return request({
    url: '/znyy/deliver/teacher/prepare/selTeacherPrepareRecordList',
    method: 'get',
    params: { "teacherId": teacherId }
  })
}

export const searchTeacherStudentList = (teacherId, type) => {
  return request({
    url: '/deliver/web/teacher/selTeacherStudyStudentList',
    method: 'get',
    params: { "teacherId": teacherId, 'type': type }
  })
}

export const updateTeacherIsEnable = (data) => {
  return request({
    url: '/deliver/web/teacher/updateTeacherIsEnable',
    method: 'put',
    params: data
  })
}




// 管理教练列表查询
export const teacherManagerList = (data) => {
  return request({
    url: '/deliver/web/teacher/teacherManagerList',
    method: 'get',
    params: data
  })
}
// 教练注销
export const cancelTeacher = (id) => {
  return request({
    url: '/deliver/web/teacher/cancelTeacher',
    method: 'put',
    params: { id: id }
  })
}
// 教练授课学员列表
export const getTeacherStudentList = (data) => {
  return request({
    url: '/deliver/web/teacher/getTeacherStudentList',
    method: 'get',
    params: data
  })
}

// 教练学员调课详情
export const getStudentAdjust = (data) => {
  return request({
    url: '/deliver/web/teacher/getStudentAdjust',
    method: 'get',
    params: data
  })
}
// 学员更换教练
export const updateStudentByTeacher = (data) => {
  return request({
    url: '/deliver/web/teacher/updateStudentByTeacher',
    method: 'put',
    params: data
  })
}

// 修改教练
export const update = (data) => {
  return request({
    url: '/deliver/web/teacher/update',
    method: 'post',
    data: data
  })
}

// 获取指派中心
export const belongDeliverAndAllDeliver = () => {
  return request({
    url: "/znyy/school/queryDeliverList",
    method: "GET",
  });
}

// 转移教练
export const transferTeacher = (data) => {
  return request({
    url: "/deliver/web/teacher/transferTeacherBelongMerchant",
    method: "PUT",
    params: data
  });
}

// 转移教练
export const teacherClass = (data) => {
  return request({
    url: "/deliver/web/teacher/selTeacherHavePlanStudy",
    method: "GET",
    params: data
  });
}


// 教练等级列表
export const getTeacherLevelList = () => {
  return request({
    url: '/znyy/bSysConfig/getTeacherLevelConfig',
    method: 'GET'
  })
}

// 新增、修改教练等级
export const addOrUpdateTeacherLevel = (data) => {
  return request({
    url: '/znyy/bSysConfig/addOrUpdateTeacherLevelConfig',
    method: 'POST',
    data
  })
}
// 删除教练等级
export const deleteTeacherLevel = (data) => {
  return request({
    url: '/znyy/bSysConfig/deleteTeacherLevelConfig',
    method: 'DELETE',
    params: data
  })
}
// 删除教练等级
export const checkWechat = (data) => {
  return request({
    url: '/deliver/web/teacher/wechat/check',
    method: 'GET',
    params: data
  })
}







