<!--待完善上课信息表-->
<template>
    <div>
        <el-card class="frame" shadow="never">
            <el-form label-width="80px" ref="searchNum" :model="searchNum">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="姓名:" prop="studentName" style="width:80%">
                            <el-input v-model="searchNum.studentName" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="学员编号:" prop="studentCode" style="width:80%">
                            <el-input v-model="searchNum.studentCode" placeholder="请输入"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="">
                            <el-button type="primary" size="small" @click="changeStore">查询</el-button>
                            <el-button style="margin-left: 10px" size="small" :loading="exportLoading" @click="reset">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>
        <!-- 表格 -->
        <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
             :cell-style="{ 'text-align': 'center' }">
            <el-table-column prop="id" min-width="130" label="订单编号" header-align="center" />
            <el-table-column prop="studentName" min-width="110" label="学员姓名" header-align="center" />
            <el-table-column prop="studentCode" min-width="110" label="学员编号" header-align="center" />
            <el-table-column prop="phone" label="学员手机号" header-align="center" width="220"></el-table-column>
            <el-table-column prop="rechargeReviewTime" label="已购复习包时长" min-width="80" header-align="center"></el-table-column>
            <el-table-column prop="createTime" label="购买时间" width="180" header-align="center">
            </el-table-column>
            <el-table-column prop="merchantCode" min-width="130" label="门店账号" header-align="center" />
            <el-table-column prop="merchantName" min-width="110" label="门店名称" header-align="center" />
            <el-table-column prop="merchantPhone" min-width="110" label="门店手机号" header-align="center" />
        </el-table>
        <!-- 分页器 -->
        <el-row type="flex" justify="center" align="middle" style="height: 60px">
            <!-- 3个变量：每页数量、页码数、总数  -->
            <!-- 2个事件：页码切换事件、每页数量切换事件-->
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
                layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
        </el-row>
    </div>
</template>

<script>
import { selStudentReviewTimeInfoList } from "@/api/paikeManage/classCard";

export default {
    name: "",
    data() {
        return {
            searchNum: {
                studentName: "",
                studentCode: "",
                pageNum: 1,
                pageSize: 10
            }, //搜索参数
            tableLoading: false,
            total: null,
            reviewLister: [],
            getVacationList: {
                id: ""
            },
            exportLoading: false,
            studentCode: '',
        };
    },
    created() {
        // this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
        this.initData();
    },
    methods: {
        initData01() {
            this.searchNum.pageNum = 1
            this.searchNum.pageSize = 10
            this.initData();
        },
        // 列表数据
        async initData() {
            this.tableLoading = true;;
            let { data } = await selStudentReviewTimeInfoList(this.searchNum)
            this.tableLoading = false;
            this.total = Number(data.totalItems);
            this.reviewLister = data.data;
        },

        // 分页
        handleSizeChange(val) {
            this.searchNum.pageSize = val;
            this.initData();
        },
        handleCurrentChange(val) {
            this.searchNum.pageNum = val;
            this.initData();
        },
        changeStore(){
          this.initData01();
        },

        //重置
        reset() {
            this.$refs.searchNum.resetFields();
            this.initData();
        },

    }
};
</script>

<style scoped>
body {
    background-color: #f5f7fa;
}

.normal {
    color: rgb(28, 179, 28);
}

.error {
    color: rgba(234, 36, 36, 1);
}
</style>

<style lang="scss" scoped>
.frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
}
</style>
