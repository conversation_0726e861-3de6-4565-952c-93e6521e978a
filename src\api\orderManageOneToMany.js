/**
 * 一对多承单量配置、审核与轮排配置相关接口
 */
import request from '@/utils/request';
/**
 * 一对多承单量配置-查询生效列表
 * @returns
 */
export const getManyCurrentConfigData = () => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getManyCurrent',
    method: 'GET'
  });
};
/**
 * 一对多承单量配置-查询审核中列表
 * @returns
 */
export const getManyOperateConfigData = () => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getManyOperate',
    method: 'GET'
  });
};
/**
 * 一对多承单量配置-根据课程大类ID查看承单量详情
 * @param {*} curriculumId
 * @returns
 */
export const getOrderDetailByCurriculumData = (curriculumId) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/getTimeConfigByCurriculum',
    method: 'GET',
    params: { curriculumId }
  });
};
/**
 * 一对多承单量配置-承单量配置保存接口
 * @param data
 */
export const saveToMany = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/saveToMany',
    method: 'POST',
    data: data
  });
};
/**
 * 一对多承单量配置-查询详情
 * @param data
 */
export const getManyConfigDetailData = (configId) => {
  return request({
    url: `/deliver/web/deliverWithstandConfig/getManyConfigDetail`,
    method: 'GET',
    params: { configId }
  });
};
/**
 * 一对多承单量配置-撤销一对多承单量申请中记录
 * @param {*} id
 */
export const setRevokeOneToManyOrder = (id) => {
  return request({
    url: `/deliver/web/deliverWithstandConfig/revokeMany`,
    method: 'PUT',
    params: { id }
  });
};

/**
 * 一对多承单量审核-管理员审核列表分页
 * @param data
 */
export const auditConfigList = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/adminManyList',
    method: 'GET',
    params: data
  });
};
/**
 * 一对多承单量审核-管理员审核
 * @param data
 */
export const auditConfig = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/auditMany',
    method: 'POST',
    data
  });
};
/**
 * 一对多轮排配置-轮排配置分页查询
 * @param data
 */
export const getList = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/oneToMany/turnConfigList',
    method: 'GET',
    params: data
  });
};
/**
 * 一对多轮排配置-保存轮排配置
 * @param data
 */
export const saveRatio = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/oneToMany/saveRatio',
    method: 'POST',
    data
  });
};
/**
 * 一对多轮排配置-试课/正课的已接承单量历史
 * @param data
 */
export const getHistoryNum = (data) => {
  return request({
    url: '/deliver/web/deliverWithstandConfig/oneToMany/getSixMonthNum',
    method: 'GET',
    params: data
  });
};
