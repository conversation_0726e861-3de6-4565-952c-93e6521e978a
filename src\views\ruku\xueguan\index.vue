<template>
  <el-form label-width="140px" style="margin-top: 2vh">
    <el-form-item label="*姓名:" prop="name">
      <el-input style="width: 30%" placeholder="请输入" v-model="xueguan.realName"></el-input>
    </el-form-item>
    <el-form-item label="*性别:">
      <el-select style="width: 30%" v-model="xueguan.sex" placeholder="请选择">
        <el-option label="男" value="1"></el-option>
        <el-option label="女" value="2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="*联系方式:" prop="name">
      <el-input style="width: 30%" placeholder="请输入" v-model="xueguan.mobile"></el-input>
    </el-form-item>
    <el-form-item label="*初始密码:" prop="password">
      <el-input style="width: 30%" placeholder="请输入" v-model="xueguan.password"></el-input>
    </el-form-item>
    <!-- 按钮最后一行 -->
    <el-row type="flex" justify="center" style="margin-top: 12vh">
      <el-button @click="quxiao" style="margin-right: 6vh">取 消</el-button>
      <el-button @click="btnOk" type="primary">确 定</el-button>
    </el-row>
    <div style="height: 40vh"></div>
  </el-form>
</template>

<script>
import { addxueguanListApi } from "@/api/rukuManage/xueTeacher";
export default {
  name: "QualityDialog",
  components: {},
  data() {
    return {
      // loginRules: {
      //   password: [
      //     { required: true, trigger: "blur", message: "密码太短" },
      //     { pattern: /^\S{6,}$/, message: "密码太短", trigger: "blur" }
      //     ],
      // },
      visible: false,
      dialogImageUrl: "",
      dialogVisible: false,
      disabled: false,
      xueguan: {
        realName: "",
        sex: "",
        mobile: "",
        password: ""
      }
    };
  },
  methods: {
    quxiao() {
      this.$router.go(-1);
    },
    async btnOk() {
      if (this.xueguan.mobile.length != 11) {
        this.$message.error("手机号格式不正确");
        return false;
      }
      if (
        this.xueguan.realName.length > 0 &&
        this.xueguan.sex.length > 0 &&
        this.xueguan.mobile.length > 0 &&
        this.xueguan.password.length > 0
      ) {
        let res = await addxueguanListApi(this.xueguan);
        if (res.code === 20000) {
          this.$router.go(-1);
        }
      } else {
        this.$message.error("参数不能为空！");
      }
    }
  }
};
</script>
