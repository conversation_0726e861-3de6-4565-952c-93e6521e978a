<template>
  <div class="dialog">
    <el-form>
      <el-drawer :visible.sync="drawer" :direction="direction" :before-close="handleClose"
        :size="screenWidth > 1300 ? '30%' : '90vw'">
        <!-- 1 -->
        <div class="diyih">
          <span class="firste">反馈查看</span>
          <div v-if="firstStudy">
            <!-- 学习的日/总按钮 -->
            <span class="Second" v-if="studyStatus">
              <button type="button" style="forecolor: blue" @click="studyFn">
                <span style="color: skyblue">日</span>/
                <span>总</span>
              </button>
            </span>
            <span class="Second" v-else>
              <button type="button" style="forecolor: blue" @click="studyBtn">
                <span style="color: skyblue">总</span>/
                <span>日</span>
              </button>
            </span>
          </div>
          <!-- 复习的日/总按钮 -->
          <div v-else>
            <span class="Second" v-if="BtnAdialog">
              <button type="button" style="forecolor: blue" @click="btnAn">
                <span style="color: skyblue">日</span>/
                <span>总</span>
              </button>
            </span>
            <span class="Second" v-else>
              <button type="button" style="forecolor: blue" @click="btnBn">
                <span style="color: skyblue">总</span>/
                <span>日</span>
              </button>
            </span>
          </div>
        </div>
        <!-- 2 -->
        <div class="dierr">
          <span>
            <button type="button" class="nob" @click="study">
              <span :class="{ active1: firstStudy }">学习反馈</span>
            </button>
          </span>
          <span>
            <button type="button" class="nob" @click="algan">
              <span :class="{ active1: aganStudy }">复习反馈</span>
            </button>
          </span>
        </div>
        <div class="cc" style="margin-bottom: 1vw"></div>

        <!-- 学习反馈 -->
        <el-row v-if="firstStudy">
          <el-row>
            <el-col class="paike" v-if="studyStatus">
              选择查看日期:
              <el-date-picker :picker-options="pickerOptions" v-model="studyList.dateTime" value-format="yyyy-MM-dd"
                type="date" :style="{width: screenWidth > 1300 ? '12vw' : '50%'}" size="mini"
                placeholder="选择日期"></el-date-picker>
            </el-col>
            <el-col class="paike" v-if="studyStatus">
              选择时间范围:
              <el-select v-model="getFeedback.id" clearable @change="xuexi()" placeholder="请选择"
                :style="{width: screenWidth > 1300 ? '12vw' : '50%'}" size="mini">
                <el-option v-for="item in studyList2" :key="item.id" :label="item.time" :value="item.id"></el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-col class="paike">
            姓名：
            <span>{{ studyList1.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ studyList1.gradeName }}</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            时间：
            <span>{{ studyList1.studyTime }}</span>
          </el-col>
          <el-col class="paike">
            已购鼎英语学时：
            <span>{{ studyList1.totalCourseHours }}小时</span>
          </el-col>
          <el-col class="paike">
            剩余鼎英语学时：
            <span>{{ studyList1.leaveCourseHours }}小时</span>
          </el-col>
          <el-col class="paike">
            所学内容：
            <span>{{ studyList1.studyBooks }}</span>
          </el-col>
          <el-col class="paike">
            <el-col :span="4">学习进度：</el-col>
            <el-col :span="20" v-if="studyList1.learnSchedule" style="white-space: pre-line;">
              <div>{{ studyList1.learnSchedule.replaceAll(',', '\n') }}</div>
            </el-col>
          </el-col>
          <el-col class="paike">
            复习词汇：
            <span>{{ studyList1.reviewWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘词汇：
            <span>{{ studyList1.forgetWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘率：
            <span>{{ studyList1.forgetRate }}%</span>
          </el-col>
          <el-col class="paike">
            学新词汇：
            <span>{{ studyList1.newWords }}个</span>
          </el-col>
          <el-col class="paike">
            学新遗忘词汇：
            <span>{{ studyList1.newForget }}个</span>
          </el-col>
          <el-col class="paike">
            学新遗忘词汇率：
            <span>{{ studyList1.newForgetRate }}%</span>
          </el-col>
          <el-col class="paike" v-if="studyStatus">
            今日共识记词汇（复习遗忘词汇+学新词汇）：
            <span>{{ studyList1.todayWords }}个</span>
          </el-col>
          <el-col class="paike">
            学习效率：
            <span>{{ studyList1.studyRate }}</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px" v-if="studyStatus">
            教练评语：
            <span>{{ studyList1.feedback }}</span>
            <div style="height: 20vh"></div>
          </el-col>
        </el-row>
        <!-- 复习反馈 -->
        <el-row v-if="aganStudy">
          <el-row>
            <el-col class="paike" v-if="BtnAdialog">
              选择查看日期:
              <el-date-picker v-model="studyList.dateTime" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
                type="date" :style="{width: screenWidth > 1300 ? '12vw' : '50%'}" size="mini"
                placeholder="选择日期"></el-date-picker>
            </el-col>
            <el-col class="paike" v-if="day">
              选择时间范围:
              <el-select v-model="getFeedbackf.id" clearable @change="fuxi()" placeholder="请选择"
                :style="{width: screenWidth > 1300 ? '12vw' : '50%'}" size="mini">
                <el-option v-for="item in studyList2" :key="item.id" :label="item.time" :value="item.id"></el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-col class="paike">
            姓名：
            <span>{{ fuxiList.studentName }}</span>
          </el-col>
          <el-col class="paike">
            年级：
            <span>{{ fuxiList.gradeName }}</span>
          </el-col>
          <el-col class="paike">
            复习内容：
            <span>{{ fuxiList.studyBooks }}</span>
          </el-col>
          <el-col class="paike">
            教练：
            <span>{{ teacher }}</span>
          </el-col>
          <el-col class="paike">
            复习词汇：
            <span>{{ fuxiList.reviewWords }}个</span>
          </el-col>
          <el-col class="paike">
            复习遗忘词汇：
            <span>{{ fuxiList.forgetWords }}个</span>
          </el-col>
          <el-col class="paike">
            遗忘率：
            <span>{{ fuxiList.forgetRate }}%</span>
          </el-col>
          <el-col class="paike">
            学习效率：
            <span>{{ fuxiList.studyRate }}</span>
          </el-col>
          <el-col class="paike" style="padding-right: 50px" v-if="!BtnBdialog">
            教练评语：
            <span>{{ fuxiList.feedback }}</span>
          </el-col>
        </el-row>
      </el-drawer>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
import { getFeedbackInfo, getPlanTimeList, getStudyDateList, getTotalStatistics } from "@/api/paikeManage/LearnManager";

export default {
  name: "xiangqingbtn",
  props: {
    // 控制弹窗显示
    islook: {
      type: Boolean,
      default: false
    },
    num: {
      type: String,
      default: ""
    },
    idser: {
      type: String
    },
    teacher: {
      type: String
    },
    contentType: {
      type: String
    }
  },
  data() {
    return {
      screenWidth: window.screen.width,
      pickerOptions: {
        cellClassName: time => {
          let timeDate = moment(time.getTime()).format("yyyy-MM-DD");
          if (this.dateArr.includes(timeDate)) {
            // dateArr是我们需要的日期组成的数组
            return "dateArrClass"; // 返回值设置的是我们添加的类名
          }
        },
      },
      dateArr: "",
      rowList: "",
      // loading: true,加载框用的
      value: "",
      drawer: false,
      direction: "rtl",
      activeName: "second",
      day: true,
      always: false,
      firstStudy: true,
      aganStudy: false,
      BtnAdialog: true,
      studyStatus: true, //学习的日总切换状态
      BtnBdialog: false,
      studyList: {
        type: 1, //复习学习的反馈
        dateTime: "",
        deliverId: ""
      },
      studyList1: "",
      getFeedback: {
        //详情的参数
        id: "",
        type: "1"
      },
      getFeedbackf: {
        //详情的参数
        id: "",
        type: "2"
      },
      reviewTotal: {
        id: "",
        planId: "",
        type: 2
      },
      fuxiList: "",
      studyList2: "",
      getStudyDateList: {
        date: "",
        type: 1,
        studentCode: ""
      },
      allStudent: '',
      dayStudent: '',
    };
  },
  watch: {
    islook(val) {
      this.drawer = val;
    },
    // 学习
    studyList: {
      async handler() {
        this.studyList.deliverId = this.idser;
        const { data } = await getPlanTimeList(this.studyList);
        this.studyList2 = data;
      },
      deep: true
    }
    // 复习
    // getFeedback: {
    //   async handler() {
    //     // this.getFeedback.type = this.studyList.type;
    //     const { data } = await getPlanTimeList(this.studyList);
    //     this.fuxiList = data;
    //   },
    //   deep: true
    // }
  },
  methods: {
    // 获取接口日期列表
    async getStudyfun() {
      let str = this.rowList.date;
      if (str) {
        this.getStudyDateList.date = str.substring(0, str.length - 3);
        this.getStudyDateList.studentCode = this.rowList.studentCode;
        let { data } = await getStudyDateList(this.getStudyDateList);
        this.dateArr = data
      }
      // console.log(this.rowList, '========================')

    },
    // 切换总
    async studyFn() {
      this.studyStatus = false;
      this.reviewTotal.type = 1;
      let res = await getTotalStatistics(this.reviewTotal);
      this.allStudent = res.data
      this.studyList1 = this.allStudent
    },
    studyBtn() {
      this.studyStatus = true;
      this.studyList1 = this.dayStudent
    },
    async xuexi() {
      // return console.log(this.studyList.dateTime, '111111111111111111111')
      this.getFeedback.dateTime = this.studyList.dateTime;
      if (this.getFeedback.id) {
        const { data } = await getFeedbackInfo(this.getFeedback);
        this.dayStudent = data;
        this.studyList1 = this.dayStudent
      } else {
        this.studyList1 = ''
      }
      // return console.log(data, '11111111111111111111')

    },
    async fuxi() {
      const { data } = await getFeedbackInfo(this.getFeedbackf);
      this.fuxiList = data;
    },
    // 关闭弹窗
    handleClose(done) {
      done();
      this.$emit("update:islook", false);
      this.studyList.dateTime = "";
      this.getFeedback.id = "";
      this.getFeedbackf.id = "";
      this.fuxiList = "";
      this.BtnAdialog = true;
      this.studyStatus = true;
      this.firstStudy = true;
      this.aganStudy = false;
      this.getStudyDateList.type = 1
      this.dateArr = ''
      this.studyList1 = ''
      this.allStudent = ''
    },
    // 标签页
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    // 学习反馈按钮
    async study() {
      this.studyList.dateTime = "";
      this.getFeedbackf.id = "";
      this.firstStudy = true;
      this.aganStudy = false;
      this.studyList.type = 1;
      this.studyList.deliverId = this.idser;
      this.fuxiList = "";
      this.getStudyDateList.type = 1//查看红点的日期列表
      this.getStudyfun()
      //  const {data} = await getPlanTimeList(this.studyList)
      //  this.studyList1 = data
    },
    // 复习反馈按钮
    async algan() {
      this.studyList.dateTime = "";
      this.getFeedback.id = "";
      this.firstStudy = false;
      this.aganStudy = true;
      this.studyList.type = 2;
      this.getStudyDateList.type = 2//查看红点的日期列表
      this.getStudyfun()
    },
    // 切换到总的界面里
    async btnAn() {
      (this.BtnAdialog = false),
        (this.BtnBdialog = true),
        (this.day = false),
        (this.always = true);
      this.reviewTotal.type = 2
      let res = await getTotalStatistics(this.reviewTotal);
      this.fuxiList = res.data;
    },
    btnBn() {
      (this.BtnBdialog = false),
        (this.BtnAdialog = true),
        (this.always = false),
        (this.day = true);
    }
  }
};
</script>

<style lang="scss" scoped>
.diyih {
  display: flex;
  justify-content: center;
  margin-bottom: 3vh;
}

.firste {
  font-size: 18px;
  font-weight: 900;
  margin-left: 5vw;
}

.Second {
  padding-left: 60px;
}

// 2行
.dierr {
  display: flex;
  justify-content: space-between;
}

.nob {
  border: none;
  background: #fff;

  &:first-child {
    padding-left: 3vw;
  }

  &:last-child {
    padding-right: 3vw;
  }
}

.paike {
  margin-bottom: 20px;
  margin-left: 2vw;

  &:first-child {
    margin-top: 0.5vw;
  }
}

.paikeTwo {
  width: 90%;
}

.xubtn {
  margin-top: 10vh;
}

.cc {
  height: 0px;
  margin: 1vh 1.5vw 0 1.5vw;
  border-bottom: 1px solid #000;
}

.active1 {
  font-weight: 900;
  font-size: 18px;
  border-bottom: 2px solid #000;
}

.dateArrClass > div ::after {
  content: "";
  position: absolute;
  right: 9px;
  top: 21px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #fc3c39;
}

div ::v-deep .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  top: 9vh;
  bottom: 0;
  border-radius: 25px;
  height: 100%;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
  padding-top: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
