<!--交付中心-接单管理-轮排配置-->
<template>
  <div>
    <div class="table">
      <!-- 表格 -->
      <el-table size="mini" :data="tableData" id="out-table-oneToMany" v-loading="tableDataLoading" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" fit>
        <el-table-column header-align="center" align="center" prop="deliverName" label="交付中心名称" min-width="100px" />
        <el-table-column header-align="center" align="center" prop="deliverMerchant" label="交付中心编号" min-width="100px" />
        <!-- 课程类型1 -->
        <template v-if="courseLength > 0">
          <el-table-column header-align="center" label="课程类型" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 0, 'curriculumName') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课承单量" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 0, 'experienceNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课承单量" align="center" min-width="100px">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 0, 'learnNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 0, 'recordRatio') : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 0, 'experienceRatio') : '-' }}</span>
            </template>
          </el-table-column>
        </template>
        <!-- 课程类型1 -->
        <!-- 课程类型2 -->
        <template v-if="courseLength > 1">
          <el-table-column header-align="center" label="课程类型" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 1, 'curriculumName') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课承单量" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 1, 'experienceNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课承单量" align="center" min-width="100px">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 1, 'learnNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 1, 'recordRatio') : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 1, 'experienceRatio') : '-' }}</span>
            </template>
          </el-table-column>
        </template>
        <!-- 课程类型2 -->
        <!-- 课程类型3 -->
        <template v-if="courseLength > 2">
          <el-table-column header-align="center" label="课程类型" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 2, 'curriculumName') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课承单量" align="center">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 2, 'experienceNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课承单量" align="center" min-width="100px">
            <template v-slot="{ row }">
              <span>{{ getCurriculumText(row, 2, 'learnNum') }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="正式课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 2, 'recordRatio') : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column header-align="center" label="试课比例" align="center">
            <template v-slot="{ row }">
              <span>{{ row.isEachSet == 1 ? getCurriculumText(row, 2, 'experienceRatio') : '-' }}</span>
            </template>
          </el-table-column>
        </template>
        <!-- 课程类型3 -->
        <el-table-column header-align="center" align="center" label="正式课总比例" width="130px">
          <template v-slot="{ row }">
            <span>{{ row.isEachSet == 0 ? row.ratio : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="试课总比例" width="130px">
          <template v-slot="{ row }">
            <span>{{ row.isEachSet == 0 ? row.experienceRatio : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="申请承单量时间" width="150px">
          <template v-slot="{ row }">
            <span>{{ row.applyTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="正式课教练数量" width="150px">
          <template v-slot="{ row }">
            <span>{{ row.learnTeacherNum || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="(正式课+试课)教练数量" width="150px">
          <template v-slot="{ row }">
            <span>{{ row.totalTeacherNum || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="本月试课已接单量" width="150px">
          <template v-slot="{ row }">
            <span style="margin-right: 10px">{{ row.experienceThisMonth }}</span>
            <el-button type="text" @click="getHistory(row, 1)">历史详情</el-button>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" label="本月正式课已接单量" width="150px">
          <template v-slot="{ row }">
            <span style="margin-right: 10px">{{ row.deliverThisMonth }}</span>
            <el-button type="text" @click="getHistory(row, 2)">历史详情</el-button>
          </template>
        </el-table-column>
        <el-table-column header-align="center" align="center" width="150px" label="操作">
          <template v-slot="{ row }">
            <div style="display: flex">
              <el-button type="primary" size="mini" @click="eidtFn(row)">编辑</el-button>
              <el-button type="success" size="mini" @click="getDetail(row)">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="querydata.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <el-dialog :title="isEdit ? '编辑' : '详情'" :visible.sync="dialogVisible" :close-on-click-modal="false" width="25%" :before-close="dialogBeforeClose">
      <div style="overflow-x: hidden; height: 400px">
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="6" :offset="0">
            <span>交付中心名称</span>
          </el-col>
          <el-col :span="18" :offset="0">
            <el-input v-model="dialogForm.deliverName" size="small" disabled placeholder=""></el-input>
          </el-col>
        </el-row>
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="6" :offset="0">
            <span>交付中心编号</span>
          </el-col>
          <el-col :span="18" :offset="0">
            <el-input v-model="dialogForm.deliverMerchant" size="small" disabled placeholder=""></el-input>
          </el-col>
        </el-row>
        <el-row :gutter="20" type="flex" align="middle" v-if="dialogForm.isEachSet == 0">
          <el-col :span="6" :offset="0">
            <span>正式课总比例</span>
          </el-col>
          <el-col :span="6" :offset="0" style="display: flex; align-items: center">
            <el-input-number v-model="dialogForm.ratio" :controls="false" size="small" :disabled="!isEdit" style="width: 80px"></el-input-number>
            <span style="margin-left: 6px">%</span>
          </el-col>
          <el-col :span="6" :offset="0">
            <span>试课总比例</span>
          </el-col>
          <el-col :span="6" :offset="0" style="display: flex; align-items: center">
            <el-input-number v-model="dialogForm.experienceRatio" :controls="false" size="small" :disabled="!isEdit" style="width: 80px"></el-input-number>
            <span style="margin-left: 6px">%</span>
          </el-col>
        </el-row>
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="9" :offset="0">
            <span>根据课程类型设置比例</span>
          </el-col>
          <el-col :span="15" :offset="0">
            <el-switch v-model="dialogForm.isEachSet" active-color="#13ce66" :active-value="1" :inactive-value="0" :disabled="!isEdit"></el-switch>
          </el-col>
        </el-row>
        <div class="formList" v-for="(item, index) in dialogForm.curriculumConfigVoList" :key="index">
          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="6" :offset="0">
              <span>课程类型</span>
            </el-col>
            <el-col :span="18" :offset="0">
              <el-input v-model="item.curriculumName" size="small" disabled placeholder=""></el-input>
            </el-col>
          </el-row>

          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="5" :offset="0">
              <span>承单量</span>
            </el-col>
            <el-col :span="4" :offset="0">
              <span>试课</span>
            </el-col>
            <el-col :span="5" :offset="0">
              <el-input-number v-model="item.experienceNum" :min="0" size="small" style="width: 80px" :disabled="!isEdit" :controls="false" />
            </el-col>
            <el-col :span="3" :offset="0" v-if="dialogForm.isEachSet == 1">
              <span>比例</span>
            </el-col>
            <el-col :span="6" :offset="0" v-if="dialogForm.isEachSet == 1" style="display: flex; align-items: center">
              <el-input-number v-model="item.experienceRatio" :controls="false" size="small" :disabled="!isEdit" style="width: 80px"></el-input-number>
              <span style="margin-left: 6px">%</span>
            </el-col>
          </el-row>
          <el-row :gutter="20" type="flex" align="middle">
            <el-col :span="5" :offset="0">
              <span></span>
            </el-col>
            <el-col :span="4" :offset="0">
              <span>正式课</span>
            </el-col>
            <el-col :span="5" :offset="0">
              <el-input-number v-model="item.learnNum" :min="0" size="small" style="width: 80px" :disabled="!isEdit" :controls="false" />
            </el-col>
            <el-col :span="3" :offset="0" v-if="dialogForm.isEachSet == 1">
              <span>比例</span>
            </el-col>
            <el-col :span="6" :offset="0" v-if="dialogForm.isEachSet == 1" style="display: flex; align-items: center">
              <el-input-number v-model="item.recordRatio" :controls="false" size="small" :disabled="!isEdit" style="width: 80px"></el-input-number>
              <span style="margin-left: 6px">%</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="dialogBeforeClose">取 消</el-button>
        <el-button type="primary" @click="onSave()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 流转指派历史弹框 -->
    <el-dialog :title="historyTitle" :close-on-click-modal="false" :visible.sync="dialogHistory" width="30%" center>
      <div style="overflow: auto; margin: 30px 0; height: 400px">
        <el-steps direction="vertical" :active="0" :space="200">
          <el-step v-for="(item, index) in historys" :title="item.month" :key="index" icon="iconfont icon-luyin">
            <template slot="description">
              <div style="white-space: pre-wrap">{{ item.content }}</div>
            </template>
            <template slot="icon">
              <i class="el-icon-info" v-if="index == 0" style="font-size: 24px"></i>
              <i class="el-icon-success" v-else style="font-size: 24px"></i>
            </template>
          </el-step>
        </el-steps>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogHistory = false">取 消</el-button>
        <el-button type="primary" @click="dialogHistory = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { pageParamNames } from '@/utils/constants';
  import { getList, saveRatio, getHistoryNum } from '@/api/orderManageOneToMany';

  export default {
    name: 'OneToManyTurnSort',
    data() {
      return {
        tableDataLoading: false,
        tableData: [],
        courseLength: 0,
        total: 0,
        formData: {},
        //表格分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        querydata: {
          pageNum: 1,
          pageSize: 10 //页容量
        },
        disabled: true,
        dialogVisible: false,
        dialogHistory: false,
        historyTitle: '',
        dialogForm: {},
        historys: [],
        isEdit: false
      };
    },
    created() {
      this.initData();
    },
    methods: {
      async getHistory(row, type) {
        let course = type == 1 ? '试课' : '正式课';
        this.historyTitle = row.deliverName + course + '已接承单量历史';
        let { data } = await getHistoryNum({
          deliverMerchant: row.deliverMerchant,
          type: type
        });
        console.log(data);
        this.historys = data;
        this.dialogHistory = true;
      },
      // 初始化表格
      initData() {
        let that = this;
        this.tableDataLoading = true;
        getList(this.querydata)
          .then((res) => {
            this.tableData = res.data.data || [];
            let courseLength = 0;
            if (this.tableData.length > 0) {
              this.tableData.forEach((item) => {
                if (courseLength < item.curriculumConfigVoList.length) {
                  courseLength = item.curriculumConfigVoList.length;
                }
              });
            }
            this.courseLength = courseLength;
            this.total = Number(res.data.totalItems);
            pageParamNames.forEach((name) => that.$set(that.tablePage, name, parseInt(res.data[name])));
            this.tableDataLoading = false;
          })
          .catch(() => {
            this.tableDataLoading = false;
          });
      },
      onSave() {
        let obj = {};
        obj = {
          id: this.dialogForm.id,
          ratio: this.dialogForm.ratio,
          experienceRatio: this.dialogForm.experienceRatio,
          isEachSet: this.dialogForm.isEachSet,
          curriculumConfigList: this.dialogForm.curriculumConfigVoList
        };
        saveRatio(JSON.stringify(obj)).then(() => {
          this.$message.success('编辑成功');
          this.initData();
          this.dialogBeforeClose();
        });
      },
      eidtFn(row) {
        this.isEdit = true;
        this.dialogForm = JSON.parse(JSON.stringify(row));
        this.dialogVisible = true;
      },
      getDetail(row) {
        this.isEdit = false;
        this.dialogForm = JSON.parse(JSON.stringify(row));
        this.dialogVisible = true;
      },
      dialogBeforeClose() {
        this.dialogForm = {};
        this.dialogVisible = false;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.querydata.pageSize = val;
        this.initData();
      },
      // 分页
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.querydata.pageNum = val;
        this.initData();
      },
      getCurriculumText(row, index, prop) {
        return row.curriculumConfigVoList.length > index ? row.curriculumConfigVoList[index][prop] : '-';
      }
    }
  };
</script>

<style lang="scss" scoped>
  // --table-curriculum-num : 0;
  .formList {
    border-top: 1px solid #f5f7fa;
    padding: 10px;
  }
  ::v-deep .el-input__suffix {
    color: #000;
    top: 8px;
  }
  ::v-deep .el-row {
    margin-bottom: 10px;
  }
  .table {
    width: 100%;
  }
  // 自定义表格边框
  ::v-deep #out-table-oneToMany .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table-oneToMany .el-table__header-wrapper th:first-child {
    border-left: 1px solid #d3dce6 !important;
  }
  // ::v-deep #out-table-oneToMany .el-table__header-wrapper th:nth-child(1) {
  //   border-left: 1px solid #d3dce6 !important;
  // }
  ::v-deep #out-table-oneToMany .el-table__header-wrapper th:last-child {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(2),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(7),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(12),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(17),
  // ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(19),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(20),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(24),
  ::v-deep #out-table-oneToMany .el-table__header-wrapper.el-table__header-wrapper th:nth-child(25) {
    border-right: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(1) {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(2),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(7),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(12),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(17),
  // ::v-deep #out-table-oneToMany .el-table__row td:nth-child(19),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(20),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(24),
  ::v-deep #out-table-oneToMany .el-table__row td:nth-child(25) {
    border-right: 1px solid #d3dce6 !important;
  }
</style>
