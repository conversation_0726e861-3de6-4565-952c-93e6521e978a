<template>
  <div v-if="pageType == 'coach'">
    <el-card class="frame" shadow="never">
      <el-form label-width="120px" ref="querydata" :model="querydata">
        <el-row>
          <el-col :span="4">
            <el-form-item label="学员编号：" prop="studentCode">
              <el-input v-model.trim="querydata.studentCode" style="width: 10vw" size="small" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="教练名称:" prop="teacherName">
              <el-input v-model="querydata.teacherName" clearable placeholder="请输入" size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="联系方式:" prop="teacherMobile">
              <el-input v-model="querydata.teacherMobile" clearable placeholder="请输入" size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-if="roleTag != 'DeliveryCenter'">
            <el-form-item label="交付中心名称：" label-width="120px" prop="deliverName">
              <el-select
                size="small"
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                remote
                clearable
                v-model="querydata.deliverName"
                filterable
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="(item, index) in deliverList" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-if="roleTag != 'DeliveryCenter'">
            <el-form-item label="交付中心编号:" label-width="120px" prop="deliverMerchantCode">
              <el-input v-model.trim="querydata.deliverMerchantCode" @change="changeInput" size="small" placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="roleTag == 'DeliveryCenter' ? 5 : 4">
            <el-form-item label="状态:" prop="wagesState">
              <el-select v-model="querydata.wagesState" size="small" style="width: 10vw" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in wagesStates" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="课程类型:" prop="curriculumName">
              <el-select v-model="querydata.curriculumName" size="small" style="width: 10vw" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.enName" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="roleTag != 'DeliveryCenter' ? 5 : 4">
            <el-form-item label="工资类型:" prop="wagesType">
              <el-select v-model="querydata.wagesType" size="small" style="width: 10vw" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in wagesTypes" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="上课时间:" prop="timeAll">
              <el-date-picker
                v-model="timeAll"
                style="width: 18vw"
                size="small"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                align="right"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                @blur="clearTime"
                @change="changeTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-button type="primary" size="mini" icon="el-icon-search" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div class="table-top-style">
      <div>
        <el-button type="primary" @click="setheaderList()" style="margin: 20px">列表显示属性</el-button>
        <el-button type="warning" v-if="roleTag == 'DeliveryCenter'" @click="dialogVisible = true" style="margin: 20px">充值复习工资池</el-button>
        <el-button type="primary" v-if="roleTag == 'DeliveryCenter'" @click="changeGoType('review')" plain style="margin: 20px">复习工资池流水</el-button>
      </div>
      <div>
        <div>
          <span>
            <span>复习工资池金额：</span>
            <span>{{ reviewInfo.poolAmountYuan }}</span>
          </span>
          <span class="review-right-css">
            <span>需发放复习金额：</span>
            <span>{{ reviewInfo.needAmountYuan }}</span>
          </span>
        </div>
        <!-- v-if="roleTag=='DeliveryCenter'" -->
        <div v-if="roleTag == 'DeliveryCenter'" class="remark-bottom-css">
          <i class="el-icon-warning icon-warning-css"></i>
          <span class="text-danger">复习工资池余额，每月10日不足发放教练复习工资，则不会发放，请及时充值</span>
        </div>
      </div>
    </div>
    <div class="nomore" v-show="tableData.length < 1">
      <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
      <div style="color: #999; margin-top: 20px">无数据</div>
    </div>
    <div v-show="tableData.length > 0" class="table-center-css">
      <!-- 暂无数据 -->
      <el-table
        v-loading="tableLoading"
        :header-cell-style="getRowClass"
        :data="tableData"
        style="width: 100%"
        id="out-table"
        :cell-style="{ 'text-align': 'center' }"
        :row-key="getRowKey"
      >
        <el-table-column
          v-for="(item, index) in tableHeaderList"
          :key="`${index}-${item.id}`"
          :prop="item.value"
          :label="item.name"
          header-align="center"
          :width="item.value == 'deliverMerchantName' || item.value == 'classBeginTime' ? '200' : ''"
        >
          <template v-slot="{ row }">
            <div v-if="item.value == 'courseTime'"></div>
            <div v-else-if="item.value == 'wagesState'">
              {{ filterItem(row.wagesState, wagesStates) }}
            </div>
            <div v-else-if="item.value == 'classBeginTime'">
              <span v-if="row.wagesType == 9 || row.wagesType == 7">
                {{ row.classBeginTime ? row.classBeginTime.slice(0, 10) : '-' }}~{{ row.classEndTime ? row.classEndTime.slice(0, 10) : '-' }}
              </span>
              <span v-else>{{ row.classBeginTime ? row.classBeginTime : '-' }}</span>
            </div>
            <div v-else-if="item.value == 'wagesAmount'">
              {{ parseFloat((row.wagesAmount / 100).toFixed(2)) }}
            </div>
            <div v-else-if="item.value == 'wagesType'">
              {{ filterItem(row.wagesType, wagesTypes) }}
            </div>
            <div v-else-if="item.value == 'studentName'">
              {{ row.studentName ? row.studentName : '-' }}
            </div>
            <div v-else-if="item.value == 'studentCode'">
              {{ row.studentCode != 0 ? row.studentCode : '-' }}
            </div>
            <span v-else>{{ row[item.value] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="pagination-css"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="querydata.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
    <!-- 表头设置  -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
    <el-dialog title="充值复习工资池" :visible.sync="dialogVisible" :before-close="reviewCancel" width="500px" :close-on-click-modal="false">
      <el-form label-width="140px" label-position="left" class="el-form-css" ref="reviewRef" :model="reviewData">
        <!-- :rules="reviewRules" -->
        <el-form-item
          label="充值金额："
          prop="reviewMoney"
          :rules="[
            { required: true, message: '请输入充值金额', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
        >
          <!-- :min="10"  -->
          <el-input-number v-model="reviewData.reviewMoney" placeholder="请输入" controls-position="right" :precision="2"></el-input-number>
        </el-form-item>
        <el-form-item label="复习奖池余额：">
          {{ reviewInfo.poolAmountYuan }}
        </el-form-item>
        <el-form-item label="需发放复习金额：">
          {{ reviewInfo.needAmountYuan }}
        </el-form-item>
      </el-form>
      <span slot="footer" class="footer-center-css">
        <el-button @click="reviewCancel">取消</el-button>
        <el-button type="primary" @click="reviewSure">确定</el-button>
      </span>
    </el-dialog>
  </div>
  <div v-else-if="pageType == 'review'">
    <reviewSalaryPool @goBack="changeGoType"></reviewSalaryPool>
  </div>
</template>
<script>
  import ls from '@/api/sessionStorage';
  import { mapGetters } from 'vuex';
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';
  import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue';
  import { deliverlist } from '@/api/peizhi/peizhi';
  import { coachTurnoverList, getReviewWageCount, reviewSalary } from '@/api/FinanceApi/coachTurnover';
  import reviewSalaryPool from '../reviewSalaryPool/index.vue';
  import { getToken } from '@/utils/auth';
  export default {
    name: 'coachTurnover',
    components: {
      HeaderSettingsDialog,
      reviewSalaryPool
    },
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        querydata: {
          pageNum: 1,
          pageSize: 10
        },
        roleTag: window.localStorage.getItem('roleTag'),
        startTime: '',
        timeAll: [],
        courseList: [],
        tableData: [],
        tableLoading: false,
        total: 0,
        deliverList: [],
        optionTotal: 0,
        loadingShip: false,
        HeaderSettingsStyle: false, // 列表属性弹框
        tableHeaderList: [],
        reviewInfo: {},
        reviewData: {},
        dialogVisible: false,
        pageType: 'coach',
        headerSettings: [
          { name: '教练名称', value: 'teacherName' },
          { name: '联系方式', value: 'teacherMobile' },
          { name: '学员名称', value: 'studentName' },
          { name: '学员编号', value: 'studentCode' },
          { name: '交付中心名称', value: 'deliverMerchantName' },
          { name: '交付中心编号', value: 'deliverMerchantCode' },
          { name: '上课时间', value: 'classBeginTime' },
          { name: '课程类型', value: 'curriculumName' },
          { name: '状态', value: 'wagesState' },
          { name: '金额', value: 'wagesAmount' },
          { name: '工资类型', value: 'wagesType' }
        ],
        headerSettingsCopy: [
          { name: '教练名称', value: 'teacherName' },
          { name: '联系方式', value: 'teacherMobile' },
          { name: '学员名称', value: 'studentName' },
          { name: '学员编号', value: 'studentCode' },
          { name: '上课时间', value: 'classBeginTime' },
          { name: '课程类型', value: 'curriculumName' },
          { name: '状态', value: 'wagesState' },
          { name: '金额', value: 'wagesAmount' },
          { name: '工资类型', value: 'wagesType' }
        ],
        wagesStates: [
          { label: '账期内', value: 1 },
          { label: '可提现', value: 2 }
          // { label: "提现中", value: 3 },
          // { label: "提现成功", value: 4 },
        ],
        wagesTypes: [
          { label: '试课奖励', value: 1 },
          { label: '试课-基本工资', value: 2 },
          { label: '试课-绩效工资', value: 3 },
          { label: '正式课-基本工资', value: 4 },
          { label: '正式课-绩效工资', value: 5 },
          { label: '正式课-复习工资', value: 6 },
          { label: '正式课-复习奖金', value: 7 },
          { label: '试课补贴', value: 9 }
        ],
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          deliverName: ''
        }
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      if (this.isAdmin) {
        this.getDeliverList();
      }
      this.getHeaderlist();
      this.initData();
      this.getReviewWageCount();
      // console.log(this.dateDiff('2024-9-11', '2024-10-9'))
    },
    watch: {
      timeAll: {
        handler(newVal) {
          this.pickerOptions.disabledDate = this.disabledDate;
        },
        deep: true
      }
    },
    computed: {
      pickerOptions() {
        let that = this;
        return {
          // 快捷选项
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近半月',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
                picker.$emit('pick', [start, end]);
              }
            },
            {
              text: '最近七天',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            }
          ],
          // 禁选逻辑 --跨度不超过3个月
          disabledDate: that.disabledDate,
          // 选中项
          onPick({ maxDate, minDate }) {
            that.startTime = minDate;
            that.disabledDate(minDate);
            // currentFirstDate = !maxDate ? minDate : null
          }
        };
      },
      ...mapGetters(['roles'])
    },
    mounted() {
      this.getbvstatusList();
    },
    methods: {
      validator(rule, value, callback) {
        console.log(value);
        console.log('====================================');
        if (value == 0) {
          callback(new Error('数量不能为 0'));
        } else {
          callback();
        }
      },
      disabledDate(time) {
        const start = this.startTime;
        // console.log(this.startTime);
        if (!start) return false;

        const startDate = new Date(start);
        const threeMonthsBefore = new Date(startDate);
        threeMonthsBefore.setMonth(startDate.getMonth() - 3);

        const threeMonthsAfter = new Date(startDate);
        threeMonthsAfter.setMonth(startDate.getMonth() + 3);

        return time.getTime() < threeMonthsBefore.getTime() || time.getTime() > threeMonthsAfter.getTime();
      },
      reviewSure() {
        this.$refs['reviewRef'].validate((valid) => {
          if (valid) {
            let parms = {
              amountYuan: this.reviewData.reviewMoney
              // userCode:window.localStorage.getItem("userId"),
              // deliverMerchant:window.localStorage.getItem("userMerchantCode")
            };
            reviewSalary(parms)
              .then((res) => {
                if (res.success) {
                  res.data.dxSource = res.data.registerCallIInfo.appSource + '##BROWSER##WEB';
                  let params = JSON.stringify(res.data);
                  let req = 'token=' + getToken() + '&params=' + params + '&back=' + window.location.href;
                  //需要编码两遍，避免出现+号等
                  var encode = Base64.encode(Base64.encode(req));
                  // window.open("https://pay.dxznjy.com/product?" + encode, "_blank");
                  if (window.location.host.split('.')[0] == 'deliver') {
                    window.open('https://pay.dxznjy.com/product?' + encode, '_blank');
                  } else if (/^[0-9.]+:[0-9]+$/.test(window.location.host) || window.location.host.concat('ngrok')) {
                    window.open('http://*************:8000/product?' + encode, '_blank');
                  } else {
                    let a = window.location.host.split('.')[0].slice(0, -1);
                    let b = `https://${a}i.dxznjy.com/`;
                    window.open(b + 'product?' + encode, '_blank');
                  }
                }
                this.$refs['reviewRef'].resetFields();
                this.dialogVisible = false;
              })
              .catch((err) => {
                this.$refs['reviewRef'].resetFields();
                this.dialogVisible = false;
              });
          }
        });
      },
      reviewCancel() {
        this.$refs['reviewRef'].resetFields();
        this.dialogVisible = false;
      },
      getbvstatusList() {
        bvstatusList({}).then((res) => {
          this.courseList = res.data;
        });
      },
      clearTime() {
        this.startTime = '';
      },
      changeTime(val) {
        if (val) {
          this.querydata.classBeginTime = val[0] + ':00';
          this.querydata.classEndTime = val[1] + ':00';
        } else {
          this.querydata.classBeginTime = null;
          this.querydata.classEndTime = null;
        }
      },
      filterItem(val, arr) {
        let result = '';
        let fil = arr.filter((item) => item.value == val);
        if (fil.length > 0) {
          result = fil[0].label;
        } else {
          result = '-';
        }
        return result;
      },
      async initData() {
        this.tableLoading = true;
        let { data } = await coachTurnoverList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        this.tableLoading = false;
      },
      async getReviewWageCount() {
        let { data } = await getReviewWageCount();
        this.reviewInfo = data;
      },
      searchData() {
        this.querydata.pageNum = 1;
        this.initData();
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getDeliverList();
        }
      },
      // 获取交付中心
      async getDeliverList() {
        let allData = await deliverlist(this.selectObj);
        this.deliverList = this.deliverList.concat(allData.data.data);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 清除交付中心名称事件
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.deliverName == '') {
            this.deliverList = [];
            this.selectObj.pageNum = 1;
            this.selectObj.deliverName = '';
            this.getDeliverList();
          }
        }, 500);
        this.$forceUpdate();
      },
      // 改变交付中心编号事件
      changeInput(e) {
        console.log(e);
        if (!!e) {
          let arr = this.deliverList.filter((i) => i.deliverMerchantCode == e);
          this.querydata.deliverName = arr.length > 0 ? arr[0].deliverMerchantName : this.querydata.deliverName;
        }
      },
      // 改变交付中心名称事件
      changeTeacher(e) {
        if (e == '') {
          this.deliverList = [];
          // this.querydata.deliverMerchantCode = ''
          this.selectObj.pageNum = 1;
          this.getDeliverList();
        } else {
          let arr = this.deliverList.filter((i) => i.deliverMerchantName == e);
          this.querydata.deliverMerchantCode = arr[0].deliverMerchantCode;
        }
      },
      rest() {
        this.timeAll = [];
        this.querydata = {
          pageNum: 1,
          pageSize: 10
        };
        this.searchData();
      },
      getRowKey(row) {
        return row.id;
      },
      setheaderList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      changeMessage() {
        this.$forceUpdate();
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'coachTurnover'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            this.tableHeaderList = JSON.parse(res.data.value);
          } else {
            this.tableHeaderList = this.roleTag == 'admin' ? this.headerSettings : this.headerSettingsCopy;
          }
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.searchData();
      },
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      changeGoType(type) {
        this.pageType = type;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'coachTurnover',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },
      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      }
    }
  };
</script>
<style scoped lang="scss">
  .table-center-css {
    padding: 20px;
  }
  .el-form-css {
    padding-left: 20px;
  }
  .footer-center-css {
    display: block;
    text-align: center;
  }
  .table-top-style {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .icon-warning-css {
      color: #e6a23c;
    }
    .text-danger {
      color: red;
      font-size: 14px;
    }
  }
  .remark-bottom-css {
    margin-top: 8px;
    i,
    span {
      vertical-align: middle;
    }
    span {
      display: inline-block;
      margin-left: 5px;
    }
  }
  .review-right-css {
    display: inline-block;
    margin-left: 40px;
  }
  .pagination-css {
    text-align: center;
    margin-top: 15px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding: 200px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  //el-input-number样式
  ::v-deep.is-controls-right .el-input-number__increase {
    display: none !important;
  }
  ::v-deep.is-controls-right .el-input__inner {
    text-align: left !important;
    padding-left: 20 !important;
  }
  ::v-deep.is-controls-right .el-input__inner {
    padding-left: 20 !important;
  }
  ::v-deep.is-controls-right .el-input-number__decrease {
    display: none !important;
  }
</style>
