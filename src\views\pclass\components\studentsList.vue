<template>
  <div>
    <div id="print" style="margin-top: 1vw;background: #ffffff" ref="print">
      <el-button type="primary" style="margin-left: 80vw" @click="dayin" class="no-print">打印</el-button>
      <h3>学员情况登记表</h3>
      <table border="1" cellspacing="0" cellpadding="10" align="center">
        <tr class="top">
          <td rowspan="6" style="font-size: 17px;font-weight: bold;">基本资料</td>
          <td colspan="2" class="paddingTopBottom">
            <div style="width:80px;">姓名</div>
          </td>
          <td colspan="1">
            <input type="text" style="width:240px"  v-model="inforMation.name"/>
          </td>
          <td colspan="1">
            <div style="width:80px;">性别</div>
          </td>
          <td colspan="1">
            <input type="text" style="width:100px" v-model="inforMation.sex"/>
          </td>
          <td colspan="1">
            <div style="width:80px;">年级</div>
          </td>
          <td colspan="1">
            <input type="text" style="width:100px" v-model="inforMation.grade"/>
          </td>
        </tr>
        <tr>
          <td class="paddingTopBottom">学校</td>
          <td colspan="2">
            <input type="text" style="width:240px" v-model="inforMation.school"/>
          </td>
          <!-- <td>1</td>
          <td>1</td>-->
          <td>学科强项</td>
          <td>
            <input type="text" style="width:100px" v-model="inforMation.disciplineStrengths"/>
          </td>
          <td style="width: 80px;">学科弱项</td>
          <td>
            <input type="text" style="width:100px" v-model="inforMation.disciplineWeakness"/>
          </td>
        </tr>
        <tr>
          <td colspan="1"  class="paddingTopBottom">学校类别</td>
          <td colspan="8">
            <div class="disanhang">
              <el-checkbox-group v-model="schoolType">
                <el-checkbox label="普通学校"></el-checkbox>
                <el-checkbox label="市/区重点"></el-checkbox>
                <el-checkbox label="省重点"></el-checkbox>
              </el-checkbox-group>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="1"  class="paddingTopBottom">性格特征</td>
          <td colspan="8">
            <div class="disanhang">
              <el-checkbox-group v-model="character">
                <el-checkbox label="偏内向"></el-checkbox>
                <el-checkbox label="偏外向"></el-checkbox>
                <el-checkbox label="寻常性格"></el-checkbox>
              </el-checkbox-group>
            </div>
          </td>
        </tr>
        <tr>
          <td class="paddingTopBottom">英语成绩</td>
          <td colspan="3">
            <input type="text" style="width:330px" v-model="inforMation.englishScore"/>

          </td>
          <td>学习态度</td>
          <td colspan="3">
            <input type="text" style="width:330px" v-model="inforMation.learningAttitude"/>
          </td>
        </tr>

        <tr>
          <td class="paddingTopBottom">所在城市</td>
          <td colspan="3">
            <input type="text" style="width:330px" v-model="inforMation.liveCity"/>
          </td>
          <td>联系电话</td>
          <td colspan="3">
            <input type="text" style="width:330px" v-model="inforMation.telephone"/>
          </td>
        </tr>
        <tr>
          <td class="bottom" colspan="2">课外辅导</td>
          <td colspan="8"  style="padding: 50px 10px">
            <div class="disanhang" style="text-align: left">
              <el-radio-group v-model="inforMation.afterClassEdu" >
                <el-radio label="有" value="有"></el-radio>
                <el-radio label="无" value="无"></el-radio>
              </el-radio-group>
            </div>
            <div style="margin-top: 10px;text-align: left;display: flex;align-items: start">
              课外辅导情况：
              <el-input class="userTextarea" type="textarea" style="width:80%;" v-model="inforMation.afterClassEduDesc" ></el-input>
            </div>
          </td>
        </tr>
        <tr>
          <td class="bottom" colspan="2">英语学习存在的问题</td>
          <td colspan="8">
              <el-input class="userTextarea" type="textarea" style="width:100%" v-model="inforMation.englishExistProblem"></el-input>
          </td>
        </tr>
        <tr>
          <td class="bottom" colspan="2">辅导计划（需求）</td>
          <td colspan="8">
              <el-input class="userTextarea" type="textarea" style="width:100%" v-model="inforMation.mentoringProgram"></el-input>
          </td>
        </tr>
      </table>
    </div>
    <div style="margin-top: 1vw">
      <el-button type="primary" style="margin-left: 10vw" @click="querenFn">确认</el-button>
      <el-button type="primary" style="margin-left: 63vw" @click="delFn">取消</el-button>
    </div>
  </div>
</template>

<script>
import { addStudentRegister, getTableInfo } from "@/api/paikeManage/classCard";
export default {
  name: "studentlist",
  data() {
    return {
      studentCodeS: "",
      inforMation: {
        name: "",
        sex: "",
        grade: "",
        school:"",
        disciplineStrengths:"",
        disciplineWeakness:"",

        englishScore: "",
        learningAttitude:"",
        liveCity:'',
        telephone:"",
        afterClassEdu:"",
        afterClassEduDesc:"",
        englishExistProblem:"",
        mentoringProgram: ""
      },
      schoolType: [],
      character:[],
      settleMatter: [],
      existingProblem: [],
      tableType: 1,
      lookwatch: {
        tableType: 1
      }
    };
  },
  created() {
    this.merchantCodeS = this.$route.query.merchantCode;
    this.studentCodeS = this.$route.query.studentCode;
    this.initData();
  },
  methods: {
    dayin() {
      // window.print()
      this.$print(this.$refs.print);
    },
    // 回显
    async initData() {
      this.lookwatch.merchantCode = this.merchantCodeS;
      this.lookwatch.studentCode = this.studentCodeS;
      let res = await getTableInfo(this.lookwatch);
      if (res.data == null) {
        this.inforMation = {};
      } else {

        this.inforMation = res.data;
        this.schoolType = res.data.schoolType.split(",");
        this.character = res.data.character.split(",");
        this.existingProblem = res.data.existingProblem;
      }
    },
    // 保存/提交
    async querenFn() {
      this.inforMation.merchantCode = this.merchantCodeS;
      this.inforMation.studentCode = this.studentCodeS;
      this.inforMation.schoolType = this.schoolType.toString();
      this.inforMation.character = this.character.toString();
      console.log(this.inforMation)
      let res = await addStudentRegister(this.inforMation);
      console.log(res)
      this.$message.success("保存成功");
      // this.$router.go(-1);
    },
    delFn() {
      this.$router.go(-1);
    }
  }
};
</script>


<style lang="scss" scoped>
  @media print {
    @page  {
      margin: 0;
    }
    body{
      margin: 0;
    }
  }

  body{
    background: red;
  }

h3 {
  font-size: 19px;
  width: 36vw;
  margin: 1vw auto;
  text-align: center;
}
table {
  width: 60%;
  margin-left: 200px;
  text-align: center;
  margin: 0 auto;
}

th {
  font-size: 25px;
}

.top td:first-child {
  width: 30px;
  margin: 0 auto;
  line-height: 50px;
  padding: 35px;
}

.question {
  line-height: 30px;
}
.paddingTopBottom{
  padding-top: 40px;
  padding-bottom: 40px;

}

.question span {
  font-size: 18px;
  font-weight: bold;
}

.jiazhang {
  height: 40px;
}

.bottom {
  height: 80px;
}
.disanhang > * {
  height: 1.5vw;
  text-align: center;
  line-height: 1.5vw;
}
.chengjiyi > * {
  margin-left: 2vw;
}
 .shuru1{
      outline:none;
    }
  ::v-deep .el-textarea__inner {
    resize: none;/* 这个是去掉 textarea 下面拉伸的那个标志，如下图 */
  }
  input{
      width:4.8vw;
      border-style: none;
      outline: none
    }

::v-deep  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate
  ::v-deep  .el-checkbox__inner{
        background-color:#000;
        border-color:#6f7374;
       }
   ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #000;
       }
   ::v-deep .el-checkbox.is-bordered.is-checked{
        border-color: #6f7374;
       }
   ::v-deep .el-checkbox__input.is-focus .el-checkbox__inner{
      border-color:  #000;
     }

  ::v-deep .userTextarea textarea {
    min-height: 140px!important;
  }


</style>
