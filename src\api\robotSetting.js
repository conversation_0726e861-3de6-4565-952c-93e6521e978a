/**
 * “机器人配置”相关接口
 */
import request from '@/utils/request'

/**
 * 试课新学员---列表查询
 * @param data
 */
export const getRobotList = (data) => {
  return request({
    url: '/deliver/qywechatRobot/list',
    method: 'GET',
    params: data
  })
}
/**
 * 试课新学员---保存
 * @param data
 */
export const saveRobot = (data) => {
  return request({
    url: '/deliver/qywechatRobot/save',
    method: 'POST',
    data
  })
}
/**
 * 试课新学员---列表查询详情
 * @param data
 */
export const getRobotDetail = (id) => {
  return request({
    url: `/deliver/qywechatRobot/find?id=${id}`,
    method: 'GET'
  })
}
/**
 * 试课新学员---删除
 * @param data
 */
export const delRobot = (data) => {
  return request({
    url: '/deliver/qywechatRobot/delete',
    method: 'POST',
    data
  })
}