<!-- 一对多-新班级列表-试课等待成班中 -->
<template>
  <div>
    <el-form :model="searchNum" ref="query" label-width="100px" class="container-card my-form" :inline="true" size="small">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="40">
        <el-col :span="6">
          <el-form-item label="姓名:" prop="studentName">
            <el-input v-model="searchNum.studentName" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学员编号:" prop="studentCode">
            <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="searchNum.grade" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="门店:" prop="merchantName">
            <el-input v-model="searchNum.merchantName" clearable placeholder="请输入门店账号或门店手机号" size="small"></el-input>
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="6">
          <el-form-item label="时间筛选:">
            <el-date-picker
              v-model="timeAll"
              size="small"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              :picker-options="pickerOptions"
              align="right"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-row style="margin: 20px 0 20px 20px">
      <el-col style="margin-right: 20px" :span="1.5">
        <el-button type="primary" @click="headerList()">列表显示属性</el-button>
      </el-col>
      <el-col style="margin-right: 20px" :span="1.5">
        <el-button type="warning" @click="handleOpenAddClass">新增班级</el-button>
      </el-col>
    </el-row>
    <!--  -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      :header-cell-style="{ background: '#f5f7fa', 'text-align': 'center' }"
      :cell-style="{ 'text-align': 'center' }"
      @selection-change="handleSelectionChange"
      height="400"
      ref="multipleTable"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :min-width="getWidth(item.value)"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="success" size="mini" @click="openEdit(row)">试课单</el-button>
            <el-button type="warning" size="mini" @click="onAssign(row)">指派班级</el-button>
          </div>
          <!-- 状态 -->
          <!-- <div v-else-if="item.value == 'week'">
            <span>{{ getWeek(row.week) }}</span>
          </div> -->
          <!-- 课程类型 -->
          <div v-else-if="item.value == 'curriculumId'">
            <span>
              {{ getCourse(row.curriculumId) || '-' }}
            </span>
          </div>
          <!-- 年级 -->
          <div v-else-if="item.value == 'grade'">
            <span>
              {{ getGrade(row.grade) || '-' }}
            </span>
          </div>
          <!-- 上课时间 -->
          <div v-else-if="item.value == 'extendTime'">
            <el-row style="white-space: break-spaces">
              <!-- {{ getWeek(row.week) }} -->
              {{ row.extendTime }}
            </el-row>
          </div>
          <!-- 其他 -->
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row v-if="tableList.length > 0" type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 创建班级 -->
    <el-dialog :title="`创建班级`" :visible.sync="dialogVisibleAdd" width="40%" :before-close="handleCloseAdd">
      <el-row>
        <el-form label-width="100px" label-position="left" style="text-align: left" ref="addForm" :inline="true" :model="addForm" :rules="addFormRules" class="my-form">
          <!-- <el-form-item label="上课时间:" prop="week">
            <el-checkbox-group v-model="addForm.week" size="medium" @change="reviewWeekChange" v-if="weeklist.length > 0">
              <el-checkbox-button v-for="item in weeklist" :label="item.value" :key="item.value">
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label=" " prop="classTime">
            <el-row>
              <el-col :span="10">
                <el-time-select
                  placeholder="起始时间"
                  @change="changeTime"
                  v-model="addForm.startTime"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '23:00'
                  }"
                ></el-time-select>
              </el-col>
              <el-col :span="2" style="text-align: center">至</el-col>
              <el-col :span="10">
                <el-time-select
                  disabled
                  placeholder="结束时间"
                  v-model="addForm.endTime"
                  :picker-options="{
                    start: '00:00',
                    step: '00:30',
                    end: '24:00',
                    minTime: addForm.startTime
                  }"
                ></el-time-select>
              </el-col>
            </el-row>
          </el-form-item> -->
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-select v-model="addForm.curriculumId" placeholder="请选择" disabled>
              <el-option v-for="item in courseList" :key="item.enCode" :label="item.enName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="addForm.gradeList" placeholder="请选择" multiple @change="handleGradeChange">
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上课时间:" prop="classTime">
            <BaseClassStudyTimeSelect
              v-model="addForm.classTimeConfigId"
              :isExperience="true"
              :curriculumId="addForm.curriculumId"
              :grade="addForm.grade"
              @change="henbdleClassTimeConfigIdChange"
            ></BaseClassStudyTimeSelect>
          </el-form-item>
          <el-form-item label="班级名称:">
            <el-input placeholder="班级名称系统生成" disabled></el-input>
          </el-form-item>
          <el-form-item label="包含学员:" prop="studentList">
            <el-select v-model="addForm.studentList" placeholder="请选择" multiple>
              <el-option v-for="item in addClassList" :key="item.id" :label="item.name" :value="item.studentCode">
                <span style="float: left">{{ `${item.name} (${item.studentCode})` }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAdd">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 指派班级弹框 -->
    <el-dialog :visible.sync="assignDialog" :width="'30%'" :title="'指派班级'" :before-close="closeAssgin">
      <el-form ref="form" label-width="110px" inline style="text-align: left" class="my-form">
        <el-form-item label="姓名">
          <el-input v-model="assignForm.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="学员编号">
          <el-input v-model="assignForm.studentCode" disabled></el-input>
        </el-form-item>
        <el-form-item label="课程类型">
          <el-input v-model="assignForm.courseName" disabled></el-input>
        </el-form-item>
        <el-form-item label="年级">
          <el-input :value="getGrade(assignForm.grade)" disabled></el-input>
        </el-form-item>
        <el-form-item label="交付中心">
          <el-select
            placeholder="请选择"
            :popper-append-to-body="false"
            :loading="deliverMerchantLoad"
            v-model="assignForm.deliverMerchant"
            remote
            filterable
            :filter-method="deliverMerchantFilterValue"
            v-el-select-loadmore="loadmoreDeliverMerchant"
            reserve-keyword
            clearable
            @change="changeDeliverMerchant"
            @focus="changeDeliverMerchant(deliverOptions.length > 0 ? '非空' : '')"
          >
            <el-option v-for="item in deliverOptions" :key="item.merchantCode" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称">
          <el-select
            :disabled="!assignForm.deliverMerchant"
            placeholder="请选择"
            :loading="classLoad"
            v-model="assignForm.classId"
            filterable
            @change="changeClass"
            @focus="getClass"
          >
            <el-option v-for="item in classList2" :key="item.classCode" :label="item.className" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="end" style="margin-top: 2.5vh">
        <el-button type="primary" style="width: 100px" plain size="small" @click="closeAssgin">取消</el-button>
        <el-button type="primary" style="width: 100px" size="small" :loading="isSubmit" @click="submitAssign">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 查看试课单 -->
    <el-dialog :visible.sync="dialogAbutment" top="2vh" :close-on-click-modal="false" :width="screenWidth > 1300 ? '60%' : '90%'" title="试课单" @close="closeEdit">
      <TrialClass v-if="dialogAbutment" :id="editFormId" @close="closeEdit"></TrialClass>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeEdit">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import { updateDetail } from '@/api/studentClass/changeList';
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';
  import { getStudentList2 } from '@/api/oneToManyClass/WaitingBuildingClass.js';
  import { getOneToMoreClassList, searchDeliverCenter, getNotFullClassList, assignClass, createClass } from '@/api/oneToManyClass/newClassList';
  import { getCurrentHour } from '@/api/studentClass/changeList';
  import TrialClass from '../TrialClass.vue'; // 试课单
  import BaseClassStudyTimeSelect from '../pendingCompletionClassInfo/BaseClassStudyTimeSelect.vue';

  import HeaderSettingsDialog from '../../../pclass/components/HeaderSettingsDialog.vue';
  import { CodeToText, regionData, TextToCode } from 'element-china-area-data';
  import { getWaitList } from '@/api/oneToManyClass/newClassList';
  import trialDate from '@/views/pclass/components/trialDate';
  import ls from '@/api/sessionStorage';

  import dayjs from 'dayjs';
  export default {
    name: 'WaitingBuildingClassTrial',
    components: {
      HeaderSettingsDialog,
      trialDate,
      TrialClass,
      BaseClassStudyTimeSelect
    },
    props: {
      gradeList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      let classTimeValidate = (rule, value, callback) => {
        if (this.addForm.startTime && this.addForm.endTime && this.addForm.dayOfWeek) {
          callback();
        } else {
          callback(new Error('请填写上课时间'));
        }
      };
      let studentListValidate = (rule, value, callback) => {
        if (this.addForm.studentList.length > 0) {
          callback();
        } else {
          callback(new Error('请至少选择一个学员'));
        }
      };
      return {
        screenWidth: window.screen.width, //屏幕宽度
        dialogVisibleAdd: false, // 新增班级上课时间弹窗
        addFormRules: {
          //   curriculumId: {
          //     required: true,
          //     message: '请选择课程类型',
          //     trigger: 'change'
          //   },
          grade: {
            required: true,
            message: '请选择年级',
            trigger: 'change'
          },
          week: {
            required: true,
            message: '请选择星期几',
            trigger: 'change'
          },
          classTime: {
            validator: classTimeValidate,
            trigger: 'change',
            required: true
          },
          studentList: {
            required: true,
            validator: studentListValidate,
            trigger: 'change'
          }
        }, // 新增form验证规则
        addForm: { studentList: [], courseName: '', week: [], grade: '', startTime: '', endTime: '', limitNum: [], gradeList: [] }, // 新增form
        courseList: [], // 课程类型
        total: 11, // 总数
        searchNum: {
          studentName: '', // 姓名
          studentCode: '',
          // merchantName: '',
          // dispatchOrderStatus: '', // 派单状态
          grade: '', //年级
          curriculumId: '',

          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        // 年级列表
        // gradeList: [
        //   { value: 18, label: '幼儿园' },
        //   { value: 1, label: '一年级' },
        //   { value: 2, label: '二年级' },
        //   { value: 3, label: '三年级' },
        //   { value: 4, label: '四年级' },
        //   { value: 5, label: '五年级' },
        //   { value: 6, label: '六年级' },
        //   { value: 7, label: '初一' },
        //   { value: 8, label: '初二' },
        //   { value: 9, label: '初三' },
        //   { value: 10, label: '高一' },
        //   { value: 11, label: '高二' },
        //   { value: 12, label: '高三' },
        //   { value: 13, label: '大一' },
        //   { value: 14, label: '大二' },
        //   { value: 15, label: '大三' },
        //   { value: 16, label: '大四' },
        //   { value: 17, label: '其他' }
        // ],
        weeklist: [
          { value: 1, label: '周一' },
          { value: 2, label: '周二' },
          { value: 3, label: '周三' },
          { value: 4, label: '周四' },
          { value: 5, label: '周五' },
          { value: 6, label: '周六' },
          { value: 7, label: '周日' }
        ],
        options02: regionData, // 区域列表
        addVisible: false, // 日期选择器
        timeslot: '', // 期望试课时间段
        dateslot: '',
        timeList: [], // 时间段
        coursrList: [], // 课程规划
        reviewWeekList: [], // 复习时间
        dialogAbutment: false, //试课单弹窗
        editForm: {}, //试课单表单
        editFormId: '', //试课单id
        classesList: [], // 班级列表

        isSubmit: false, // 指派按钮loading
        assignDialog: false, // 指派弹框
        assignForm: { classId: '', studentName: '', studentCode: '', merchantCode: '', merchantName: '' }, // 指派班级弹窗

        deliverMerchantLoad: false, // 交付中心加载状态
        deliverOptions: [], // 交付中心列表
        deliverMerchantObj: { pageNum: 1, pageSize: 20, merchantName: '' }, // 交付中心筛选对象

        classLoad: false, // 交付中心加载状态
        classList2: false, // 班级列表

        multipleSelection: [], // 选中的数据
        addClassList: [], // 创建班级的数据
        tableLoading: false, // 表格加载状态
        isSwitch: false, // 是否在切换
        tableList: [], // 表格数据
        headerSettings: [
          { name: '学员姓名', value: 'name' },
          { name: '学员编号', value: 'studentCode' },
          { name: '上课时间', value: 'extendTime' },
          { name: '课程类型', value: 'curriculumId' },
          { name: '年级', value: 'grade' },
          { name: '联系方式', value: 'phone' },
          // { name: '门店账号', value: 'merchantCode' },
          // { name: '门店名称', value: 'merchantName' },
          // { name: '门店手机号', value: 'merchantPhone' },
          { name: '操作', value: 'operate' }
        ],
        HeaderSettingsStyle: false, // 列表属性弹框
        tableHeaderList: [], // 获取表头数据
        isTeamLeader: false, // 是否是交付中心组长
        isAdmin: false // 是否是管理员
      };
    },
    created() {
      this.getbvstatusList(); // 获取课程类型列表
      this.getHeaderlist(); // 获取表头数据
      this.initData();
    },
    mounted() {},
    methods: {
      // // 获取课程类型列表
      getbvstatusList() {
        getOneToMoreClassList({}).then((res) => {
          this.courseList = res.data;
          // console.log('🚀 ~ bvstatusList ~ this.courseList:', this.courseList);
        });
      },
      // 重置搜索框
      rest() {
        this.$refs.query.resetFields();
        this.initData01();
      },
      // 搜索
      initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      async initData() {
        // 判断为null的时候赋空
        if (!this.timeAll) {
          this.timeAll = [];
        }
        this.tableLoading = true;
        let { data } = await getStudentList2(this.searchNum);
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.tableList = data.data;
        this.tableList.forEach((item) => {
          item.name = item.realName;
          if (item.extendTime.length > 14) {
            item.extendTime = item.extendTime.trim().replace(' ', '\n');
          }
        });
        // 切换保持回显
        this.$nextTick(() => {
          this.changeStatusBtn();
        });
      },
      // 数字转换星期
      getWeekName(week) {
        let normalWeekData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

        return normalWeekData[Number(week)];
      },
      // 日期格式化
      getFormatToService(date, week) {
        if (date) {
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
          } else {
            allStr = str.replace('&', '');
          }
          return allStr;
        }
        return '-';
      },
      onChild(e) {
        console.log('获取子组件传过来的值：', e);
        this.dateslot = e;
        if (this.dateslot != '' && this.timeslot != '') {
          this.editForm.expectTime = this.dateslot + ' ' + this.timeslot + ':00';
          console.log(this.editForm.expectTime);
        }
      },
      //修改时间事件
      changeTimeslot(e) {
        if (e.length == 4) {
          e = '0' + e;
        }
        let time = e + ':00';
        this.editForm.expectTime = this.dateslot + ' ' + time;
      },
      // 获取可用时间段
      async getCurrentHourFn() {
        let { data } = await getCurrentHour();
        return data.value;
      },
      //试课单
      async fillTableNormalData(row) {
        // console.log(item)

        const that = this;
        let data = await updateDetail(row.id);
        data.data.grade = data.data.grade * 1;
        this.editForm = data.data;
        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }
        if (!this.editForm.experienceObject) {
          this.$message({
            showClose: true,
            message: '请先填写试课单',
            type: 'error'
          });
          return;
        }

        if (this.editForm.experienceObject == 0) {
          this.editForm.experienceObject = '';
        }
        console.log(this.editForm);
        this.addVisible = true;
        if (this.editForm.customerType == 1) {
          this.editForm.customerType = '1';
        } else if (this.editForm.customerType == 2) {
          this.editForm.customerType = '2';
        }
        if (this.editForm.isAsk == 1) {
          this.editForm.isAsk = '1';
        } else if (this.editForm.isAsk == 2) {
          this.editForm.isAsk = '2';
        }
        if (this.editForm.isDeal == 1) {
          this.editForm.isDeal = '1';
        } else if (this.editForm.isDeal == 2) {
          this.editForm.isDeal = '2';
        }
        if (this.editForm.province && this.editForm.city && this.editForm.area) {
          let arr = [];
          arr.push(this.editForm.province);
          arr.push(this.editForm.city);
          arr.push(this.editForm.area);
          if (this.editForm.province === this.editForm.city) {
            this.editForm.city = '市辖区';
          }
          if (TextToCode[this.editForm.province]) {
            this.editForm.address = TextToCode[this.editForm.province].code;
            if (TextToCode[this.editForm.province][this.editForm.city]) {
              this.editForm.address = TextToCode[this.editForm.province][this.editForm.city].code;
              if (TextToCode[this.editForm.province][this.editForm.city][this.editForm.area]) {
                this.editForm.address = TextToCode[this.editForm.province][this.editForm.city][this.editForm.area].code;
              }
            }
          }
        }
        this.dialogAbutment = true;
      },
      // 打开上试课单
      openEdit(row) {
        // this.fillTableNormalData(row);
        this.editFormId = row.id;
        // console.log('🚀 ~ openEdit ~ this.editFormId:', this.editFormId);
        this.dialogAbutment = true;
      },
      closeEdit() {
        // this.editForm = {};
        this.dialogAbutment = false;
        this.addVisible = false;
      },
      // 获取 班级
      async getClass() {
        this.classLoad = true;
        try {
          let curriculumId = this.assignForm.curriculumId;
          let deliverMerchant = this.assignForm.deliverMerchant;
          let grade = this.assignForm.grade;
          let type = 1; // 类型 1: 试课 2: 正式课

          let allData = await getNotFullClassList({ curriculumId, deliverMerchant, grade, type });
          console.log('🚀 ~ getClass ~ allData:', allData);

          this.classLoad = false;
          this.classList2 = allData.data;
        } catch (error) {
          this.classLoad = false;
        }
      },
      // 修改 班级
      async changeClass(e) {
        if (e == '') {
        } else {
        }
      },
      // 关闭弹窗
      clearSearchRecord() {
        setTimeout(() => {
          this.deliverOptions = [];
          this.deliverMerchantObj.pageNum = 1;
          this.deliverMerchantObj.merchantName = '';
          //   this.getDeliverMerchant();
        }, 500);
      },
      // 获取交付中心
      async getDeliverMerchant() {
        this.deliverMerchantLoad = true;
        try {
          let allData = await searchDeliverCenter(this.deliverMerchantObj);
          this.deliverMerchantLoad = false;
          this.deliverMerchantObj.totalPage = allData.data.totalPage;
          this.deliverOptions = this.deliverOptions.concat(allData.data.data);
        } catch (error) {
          this.deliverMerchantLoad = false;
        }
      },
      // 搜索筛选交付中心
      deliverMerchantFilterValue(value) {
        this.deliverOptions = [];
        this.deliverMerchantObj.pageNum = 1;
        this.deliverMerchantObj.merchantName = value;
        this.getDeliverMerchant();
      },
      // 触底加载
      loadmoreDeliverMerchant() {
        if (!this.deliverMerchantLoad) {
          if (this.deliverMerchantObj.pageNum < this.deliverMerchantObj.totalPage) {
            this.deliverMerchantObj.pageNum++;
            this.getDeliverMerchant(); // 查询数据合并
          }
        }
      },
      // 修改
      async changeDeliverMerchant(e) {
        console.log('🚀 ~ changeDeliverMerchant ~ e:', e);
        if (e == '') {
          this.deliverOptions = [];
          this.deliverMerchantObj.pageNum = 1;
          this.deliverMerchantObj.merchantName = '';
          await this.getDeliverMerchant();
        } else {
          // this.assignForm.merchantName = e.merchantName;
          // this.assignForm.merchantCode = e.merchantCode;
          // console.log('🚀 ~ changeClass ~ this.assignForm.merchantCode:', this.assignForm);
          this.getClass();
        }
      },
      // 指派提交
      async submitAssign() {
        console.log(this.assignForm);
        // return;
        if (!this.assignForm.deliverMerchant) {
          return this.$message.warning('请选择交付中心');
        }
        if (!this.assignForm.classId) {
          return this.$message.warning('请选择班级');
        }
        let data = {};
        this.isSubmit = true;
        data = {
          flag: false,
          curriculumId: this.assignForm.curriculumId,
          classId: this.assignForm.classId,
          studentId: this.assignForm.studentId,
          studentName: this.assignForm.studentName,
          studentCode: this.assignForm.studentCode,
          merchantCode: this.assignForm.merchantCode,
          merchantName: this.assignForm.merchantName
        };

        try {
          await assignClass(data);
          this.$message.success('指派成功');
          this.isSubmit = false;
          this.initData();
          this.closeAssgin();
        } catch (error) {
          this.isSubmit = false;
        }
        // console.log(res)
      },

      // 关闭指派弹框
      closeAssgin() {
        this.assignForm = {
          classId: '',
          studentName: '',
          studentCode: '',
          merchantCode: '',
          merchantName: ''
        };
        this.clearSearchRecord();
        this.assignDialog = false;
      },
      //指派方法-打开
      async onAssign(row) {
        this.assignForm = {
          ...this.assignForm,
          ...row
        };
        this.assignForm.courseName = this.getCourseId(row.curriculumId);
        this.assignForm.studentName = row.name;
        this.assignForm.studentId = row.id;
        this.assignForm.deliverMerchant = '';

        this.assignDialog = true;
      },
      // 选择开始时间
      changeTime(val) {
        console.log('🚀 ~ changeTime ~ val:', val);
        if (!val) return;
        let arr = val.split(':');
        this.addForm.endTime = (arr[0] - -1 + '').padStart(2, '0') + ':' + arr[1];
      },
      // 格式化周 [1,2] => 1,2
      reviewWeekChange(val) {
        console.log(val);
        this.addForm.dayOfWeek = this.weeklist
          .filter((i) => val.includes(i.value)) // 过滤星期几
          .map((i) => i.value) // 取出星期几值
          .join(','); // 星期几值拼接成字符串
        // console.log('🚀 ~ reviewWeekChange ~ this.addForm:', this.addForm);
      },
      // 新增班级
      handleAdd() {
        this.$refs.addForm.validate((valid) => {
          if (!valid) return;
          const loading = this.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          let list = this.addClassList.filter((item) => {
            return this.addForm.studentList.some((i) => i === item.studentCode);
          });
          this.addForm.studentCode = list.map((item) => {
            return {
              studentId: item.id,
              studentName: item.name,
              studentCode: item.studentCode,
              merchantCode: item.merchantCode,
              merchantName: item.merchantName
            };
          });
          //   console.log('🚀 ~ this.addForm.studentCode=this.addClassList.map ~ this.addForm.studentCode:', this.addForm.studentCode);
          //   console.log('🚀 ~ this.addForm.studentCode=this.addClassList.map ~ this.addForm.studentCode:', this.addForm);
          // return;
          let form = {
            classTimeConfigId: this.addForm.classTimeConfigId, // 班级时间配置id
            dayOfWeek: this.addForm.dayOfWeek, //"1,2,3"
            dayOfWeekName: this.weeklist
              .filter((i) => this.addForm.dayOfWeek.includes(i.value)) // 过滤星期几
              .map((i) => i.label) // 取出星期几值
              .join(','), // "周一,周二,周三"
            lessonStartTime: this.addForm.startTime + ':00', // "09:00:00"
            lessonEndTime: this.addForm.endTime + ':00', // "09:30:00"
            curriculumId: this.addForm.curriculumId,
            grade: this.addForm.grade, // "4,5,6"
            gradeName: this.gradeList
              .filter((i) => this.addForm.grade.split(',').some((ii) => ii == i.value)) // 过滤星期几
              .map((i) => i.label) // 取出星期几值
              .join('、'), // "四年级、五年级、六年级",
            studentCode: this.addForm.studentCode
          };
          //   console.log('🚀 ~ this.addForm.studentCode=this.addClassList.map ~ this.addForm.studentCode:', form);
          createClass(form, 1)
            .then((res) => {
              this.$message.success('新增班级成功');
              loading.close();
              this.initData();
              this.multipleSelection = []; // 重置选中数据
              this.addClassList = []; // 重置新增班级数据
              this.handleCloseAdd(); // 关闭新增班级弹窗
            })
            .catch((err) => {
              loading.close();
            });
        });
      },
      // 关闭新增班级弹窗
      handleCloseAdd() {
        this.dialogVisibleAdd = false; // 关闭新增班级弹窗
        this.deleteUniqueData(); // 同步删除的选中数据
        this.addForm = { studentList: [], courseName: '', week: [], grade: '', startTime: '', endTime: '', limitNum: [], gradeList: [] };
        // 清除校验
        this.$nextTick(() => {
          this.$refs.addForm.clearValidate();
        });
      },
      // 打开新增班级弹窗
      handleOpenAddClass() {
        if (this.addClassList <= 0 && this.multipleSelection.length <= 0) return this.$message.warning('请至少选择一个学员');
        this.addUniqueData(this.addClassList, this.multipleSelection);
        this.addForm.studentList = this.addClassList.map((i) => i.studentCode);
        this.addForm.curriculumId = this.addClassList[0].curriculumId;

        let changeGrade = this.addClassList.map((i) => i.grade); // 获取年级 [1,2]
        // 筛选出存在的年级并转字符串 [1,2] => 1,2
        let gradeList = this.handleGradeChange(changeGrade); //[1,2] => 1,2
        gradeList = gradeList.length > 0 ? gradeList.split(',') : []; // 转成数字数组
        this.addForm.gradeList = gradeList; // [1,2]

        this.dialogVisibleAdd = true; // 打开新增班级弹窗
      },
      // 修改年级
      handleGradeChange(val) {
        //筛选出存在的年级并转字符串 [1,2] => 1,2
        this.addForm.grade = this.gradeList
          .filter((i) => val.some((ii) => ii == i.value))
          .map((i) => i.value)
          .join(',');
        return JSON.parse(JSON.stringify(this.addForm.grade));
      },
      henbdleClassTimeConfigIdChange(obj) {
        console.log('🚀 ~ henbdleClassTimeConfigIdChange ~ obj:', obj);
        if (!obj.endTime || !obj.startTime || !obj.week) {
          this.addForm.dayOfWeek = '';
          this.addForm.startTime = '';
          this.addForm.endTime = '';
          return;
        }
        this.addForm.dayOfWeek = obj.week.split('').join(','); // "123" =>"1,2,3"
        this.addForm.startTime = obj.startTime;
        this.addForm.endTime = obj.endTime;
      },
      // 主页列表选中数据
      handleSelectionChange(val) {
        console.log('🚀 ~ handleSelectionChange ~ val:', val);
        if (val.length > 0) {
          if (val.length - this.multipleSelection.length > 1) {
            // 查看课程是否有冲突
            let isConflict = val.some((i) => i.curriculumId != val[0].curriculumId);
            if (isConflict) {
              this.$refs.multipleTable.clearSelection(); // 取消选择
              return this.$message.warning('所选班级课程类型不一致，请重新选择');
            }
          }

          // 限制选择班级课程类型一致
          let item = val[val.length - 1];
          let isConflict = this.multipleSelection.some((i) => i.curriculumId != item.curriculumId) || this.addClassList.some((i) => i.curriculumId != item.curriculumId);
          console.log('🚀 ~ handleSelectionChange ~ isConflict:', isConflict, item);

          if (isConflict) {
            this.$refs.multipleTable.toggleRowSelection(item); // 取消选择
            return this.$message.warning('所选班级课程类型不一致，请重新选择');
          }
        }
        this.multipleSelection = val;
      },
      // 添加并排除重复数据
      addUniqueData(array, dataList) {
        console.log('🚀 ~ handleSelectionChange ~ addClassList:', this.addClassList);
        if (dataList.length <= 0) return array;
        dataList.forEach((data) => {
          if (!array.some((i) => i.id === data.id)) {
            array.push(data);
          }
        });
        console.log('🚀 ~ handleSelectionChange ~ addClassList:', this.addClassList);
      },
      // 同步删除的选中数据 // addClassList
      deleteUniqueData() {
        // addForm.studentList
        this.addClassList = this.addClassList.filter((item) => this.addForm.studentList.some((i) => i === item.studentCode));
        console.log('🚀 ~ changeStatusBtn ~ data:', this.addClassList, this.multipleSelection);
        this.changeStatusBtn();
      },
      // 切换保持回显 //multipleSelection
      changeStatusBtn() {
        let indexList = []; // 索引数组
        // this.multipleSelection = []; // 重置选中数据
        this.tableList.forEach((data) => {
          // console.log('🚀 ~ changeStatusBtn ~ data:', data);
          let index = this.addClassList.findIndex((i) => i.id === data.id);
          if (index !== -1) {
            this.$refs.multipleTable.toggleRowSelection(data, true);
            // this.multipleSelection.push(data);
            indexList.push(index);
          } else {
            this.$refs.multipleTable.toggleRowSelection(data, false);
          }
        });

        this.addClassList = this.addClassList.filter((i, ind) => !indexList.includes(ind));
        // console.log('🚀 ~ changeStatusBtn ~ data:', this.addClassList, this.multipleSelection);
      },
      // 分页
      handleSizeChange(val) {
        this.addUniqueData(this.addClassList, this.multipleSelection);
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.addUniqueData(this.addClassList, this.multipleSelection);
        this.searchNum.pageNum = val;
        this.initData();
      },
      //拿到课程类型
      getCourseId(val) {
        let classType = this.courseList.find((item) => {
          return item.id == val;
        });

        return classType && classType.enName;
      },
      //拿到课程类型
      getCourse(val) {
        let classType = this.courseList.find((item) => {
          return item.id == val;
        });

        return classType && classType.enName;
      },
      // 拿到周几
      getWeek(val) {
        let week = this.weeklist.filter((item) => {
          return val.includes(item.value);
        });
        week = week.map((item) => item.label).join('、');

        return week;
      },
      // 拿到年级
      getGrade(val) {
        let grade = this.gradeList.find((item) => {
          return item.value == val;
        });

        return grade && grade.label;
      },
      // 获取宽度
      getWidth(val) {
        if (val == 'extendTime') {
          return '300px';
        } else if (val == 'operate') {
          return '300px';
        } else if (val == 'dispatchOrderTime') {
          return '180px';
        } else {
          return '150px';
        }
      },
      // 获取表头设置
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      // 接收子组件数据动态控制表头弹窗
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'WaitingBuildingClassTrial',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      }, // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'WaitingBuildingClassTrial'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            let tableHeaderList = JSON.parse(res.data.value);
            this.tableHeaderList = tableHeaderList.filter((item) => item);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
    ::v-deep.el-date-editor.el-input {
      width: 100%;
    }
    ::v-deep.el-input-number.is-controls-right .el-input__inner {
      text-align: left;
    }
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }
</style>
