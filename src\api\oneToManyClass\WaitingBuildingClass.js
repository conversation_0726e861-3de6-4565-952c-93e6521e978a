// 新班级列表/等待成班中 接口
import request from '@/utils/request';

/**
 * -----------正式课
 */

// 获取学员列表
export function getStudentList(params) {
  return request({
    url: '/deliver/web/oneMore/getOneMoreWaitDeliverStudentList',
    method: 'get',
    params: {
      ...params
    }
  });
}

/**
 * -----------试课
 */

// 获取学员列表
export function getStudentList2(params) {
  return request({
    url: '/deliver/web/oneMore/getOneMoreWaitExperienceStudentList',
    method: 'get',
    params: {
      ...params
    }
  });
}
// zx查询试课单
export function getTrialInfo(orderId) {
  return request({
    url: '/zx/exp/getInfo',
    method: 'get',
    params: {
      orderId
    }
  });
}
