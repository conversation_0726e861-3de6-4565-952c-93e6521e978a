<!--交付中心-正式学员管理-学习课程表-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form :label-width="screenWidth > 1300 ? '120px' : '90px'" ref="searchNum" :model="searchNum" :inline="true">
        <!-- 1 -->
        <el-row align="left">
          <el-col :span="6" :xs="24">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" :disabled="!!stuudentCode" clearable placeholder="请输入" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="课程模块:" prop="isSetModule">
              <el-select v-model="searchNum.isSetModule" clearable placeholder="请选择" @change="changeCourse">
                <el-option v-for="(item, index) in options" :label="item.label" :value="item.value" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="课程分类:" prop="courseType">
              <el-select v-model="searchNum.courseType" clearable placeholder="请选择" style="width: 10vw">
                <el-option label="鼎英语" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="6" :xs="24">
            <el-form-item label="状态:" prop="studyStatus">
              <el-select v-model="searchNum.studyStatus" clearable placeholder="请选择">
                <el-option label="已上课" value="2"></el-option>
                <el-option label="已上课未反馈" value="1"></el-option>
                <el-option label="未上课" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" :xs="24">
            <el-form-item label="教练老师:" prop="teacherName">
              <el-select
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                :filter-method="filterValue"
                clearable
                v-model="searchNum.teacherName"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
              >
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.label"></el-option>
              </el-select>
              <!-- <el-input v-model="searchNum.teacherName" clearable placeholder="请输入" size="small"></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item v-if="searchNum.planId" label="排课编号:" prop="planId">
              <el-input v-model="searchNum.planId" clearable placeholder="请输入" size="small" style="width: 10vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="所属学管师:" prop="learnTubeTeacherName">
              <el-select
                ref="xueGuanRef"
                v-el-select-loadmore="handleLoadxueguan"
                :loading="loadingShip"
                :filter-method="filterXueguan"
                clearable
                v-model="searchNum.learnTubeTeacherName"
                filterable
                remote
                reserve-keyword
                placeholder="请选择"
                @input="changeTeacherName"
                @blur="clearStudentManagement"
                @change="studentManagement"
              >
                <el-option v-for="item in xgsList" :key="item.userId" :label="item.realName" :value="item.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="时间筛选:" prop="timeAll">
              <el-date-picker
                v-model="timeAll"
                :style="{ width: screenWidth > 1300 ? '16vw' : '90%' }"
                size="small"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
                align="right"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 3 -->
        <el-row>
          <el-col v-if="isAdmin" :span="6" :xs="24">
            <el-form-item label="交付名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="isAdmin" :span="6" :xs="24">
            <el-form-item label="交付编号:" prop="deliverMerchant">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-form-item label="门店账号:" prop="merchantCode">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请输入门店账号" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="班级:" prop="deliverClass">
              <el-input v-model="searchNum.deliverClass" placeholder="请输入1-999"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="24">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
            <el-button type="warning" icon="el-icon-download" size="mini" @click="exportExcel" :loading="exportLoading">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin: 20px 0 20px 20px">列表显示属性</el-button>
    <el-button type="danger" icon="el-icon-delete" @click="delloadfun" :loading="delloading" v-if="!isAdmin">批量删除</el-button>
    <el-table
      v-loading="tableLoading"
      :data="luyouclassCard"
      style="width: 100%"
      id="out-table"
      :header-cell-style="getRowClass"
      :cell-style="{ 'text-align': 'center' }"
      @selection-change="handleSelectionChangeDepartment"
      :row-key="getRowKey"
    >
      <el-table-column type="selection" width="55" :reserve-selection="true" :selectable="checkSelect" v-if="!isAdmin"></el-table-column>
      <el-table-column
        v-for="(item, index) in getCustomTableHeaders"
        :width="item.value == 'operate' || item.value == '' ? '360' : item.value == 'referrerEvaluateVoList' || item.value == 'parentEvaluateVoList' ? '500' : '160'"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <!-- <el-button type="danger" v-if="row.status == 2" size="mini" @click="LeaveBtn(row)">请假处理</el-button> -->
            <!-- <el-button type="primary" size="mini" v-if="row.studyStatus === 0" @click="paikeBtn(row)">调课</el-button> -->
            <el-button
              type="warning"
              size="mini"
              @click="deduct(row)"
              v-if="(roles[0].val == 'admin' || roles[0].val == 'DeliveryCenter') && row.canDeductWage && row.experience != true"
              :disabled="!row.canDeductWage"
            >
              扣绩效
            </el-button>
            <el-button type="success" size="mini" v-if="row.studyStatus === 2" @click="lookBtn(row)">数据查看</el-button>
            <el-button type="danger" size="mini" v-if="row.studyStatus === 0" @click="deleteStudy(row)">删除</el-button>
            <el-button
              type="primary"
              size="mini"
              @click="finishCourseFn(row)"
              v-if="roles[0].val == 'DeliveryCenter' && (row.needDeliver == 0 || row.oneToManyType == 1) && row.studyStatus == 0"
              style="background: #a96eff; border-color: #a96eff"
            >
              已完成上课
            </el-button>

            <el-button type="warning" size="mini" @click="weiteFeedback(row)" v-if="row.studyStatus == 1 && roles[0].val == 'DeliveryCenter'">帮助教填写反馈</el-button>
          </div>

          <div v-else-if="item.value == 'classTime'">
            <div>{{ row.date }}</div>
            <div>
              <span>{{ row.format + ' ' + row.time }}</span>
              <span v-if="row.endTime">-{{ row.endTime }}</span>
            </div>
          </div>
          <div v-else-if="item.value == 'actualCourse'">
            <span v-if="row.actualCourse">{{ row.actualCourse[0] }}</span>
          </div>
          <div v-else-if="item.value == 'hours'">{{ row.hours }}/时</div>
          <div v-else-if="item.value == 'teacher'">
            <el-tag v-if="row.teacherType == 1" size="mini" effect="dark" type="success">上</el-tag>
            <el-tag v-if="row.teacherType == 0" size="mini" effect="dark" type="warning">试</el-tag>
            <el-tag v-if="row.teacherType == 3" size="mini" effect="dark" type="">代</el-tag>
            {{ row.teacher }}
            <i class="el-icon-edit" style="color: #66b1ff; margin-left: 10px" @click="onChangeTeacher(row)" v-if="row.hasChange"></i>
          </div>
          <div v-else-if="item.value == 'studyStatus'">
            <span v-if="row.studyStatus == 0 || row.studyStatus == 1 || row.studyStatus == 2">
              {{ row.studyStatus != 0 ? (row.studyStatus == 1 ? '已上课未反馈' : '已完成') : '未上课' }}
            </span>
          </div>
          <div v-else-if="item.value == 'type'">
            <span v-if="row.type == 1">学管</span>
            <span v-if="row.type == 2">助教</span>
            <span v-if="row.type == 3">转换</span>
          </div>

          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ status(row) }}</span>
          </div>
          <!-- deliverClass -->
          <div v-else-if="item.value == 'deliverClass'">
            <el-tag v-if="!!row.deliverClass" size="mini" effect="dark" type="" style="background: #a96eff; border-color: #a96eff">
              {{ row.deliverClass != 0 ? row.deliverClass + '班' : '班' }}
            </el-tag>
            <span v-else>{{ '-' }}</span>
          </div>

          <div v-else-if="item.value == 'wage'">
            <span
              :style="{
                color: row.deductWage != '' && row.deductWage > 0 ? '#ff6d6d' : ''
              }"
            >
              {{ row.wage || 0 }}
            </span>
          </div>
          <div v-else-if="item.value == 'referrerEvaluateVoList'">
            <!-- 推荐人评价 -->
            <div v-if="!!row.referrerEvaluateVoList">
              <div v-for="(val, idx) in row.referrerEvaluateVoList" :key="idx" class="rate">
                <el-rate v-model="val.star" disabled :key="'star' + idx" style="flex: 1"></el-rate>
              </div>

              <div v-for="(val, idx) in row.referrerEvaluateVoList" :key="idx" class="rate">
                <div>{{ val.content }}</div>
              </div>
            </div>
          </div>
          <div v-else-if="item.value == 'parentEvaluateVoList'">
            <!-- 家长评价 -->
            <div v-if="!!row.parentEvaluateVoList">
              <div v-for="(val, idx) in row.parentEvaluateVoList" :key="idx" class="rate">
                <el-rate v-model="val.star" disabled :key="'star' + idx" style="flex: 1"></el-rate>
              </div>
              <div v-for="(val, idx) in row.parentEvaluateVoList" :key="idx" class="rate">
                <div>{{ val.content }}</div>
              </div>
            </div>
          </div>
          <!-- 实际上课时间、会议在线时间、填写反馈时间 -->
          <div v-else-if="item.value == 'actualClassTime' || item.value == 'meetingOnlineTime' || item.value == 'feedbackTime'">
            <template v-if="row[item.value] && item.value != 'feedbackTime'">
              <div>{{ row[item.value].split(' ')[0] }}</div>
              <div>{{ row[item.value].split(' ')[1] }} {{ row[item.value].split(' ')[2] ? '-' : '' }} {{ row[item.value].split(' ')[3] ? row[item.value].split(' ')[3] : '' }}</div>
            </template>
            <template v-else>
              <span>{{ row[item.value] ? row[item.value] : '-' }}</span>
            </template>
          </div>
          <!-- 教练上课状态：0-正常，1-迟到，2-早退, 3-迟到早退' -->
          <span v-else-if="item.value == 'teacherClassStatus'">
            {{ getTeacherClassStatusTxt(row[item.value]) }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableIshow"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>

    <el-row align="left" style="height: 60px; padding-left: 30px; margin-top: 20px">
      学时统计：
      <span v-if="totalTubeHours !== ''">
        <span v-if="searchNum.learnTubeTeacherName != ''">
          学管师{{ xueGuanName }}在{{ searchNum.startTime }}至{{ searchNum.endTime }}内，共产生{{ totalTubeHours ? totalTubeHours : 0 }}个学时
        </span>
        <span v-else>教练{{ searchNum.teacherName }}在{{ searchNum.startTime }}至{{ searchNum.endTime }}内，共产生{{ totalTubeHours ? totalTubeHours : 0 }}个学时</span>
      </span>
      <span v-else>请搜索学管老师/助教老师和筛选相对应的时间才可统计学时</span>
    </el-row>

    <el-dialog title="扣绩效" :visible.sync="dialogPerformance" :width="screenWidth > 1300 ? '30%' : '90%'" :close-on-click-modal="false">
      <div style="display: flex">
        <span>扣款金额：</span>
        <el-input ype="number" :min="0" v-model="input" placeholder="请输入扣款金额" style="width: 300px" @change="deductPrice"></el-input>
        <span style="margin-left: 20px">元</span>
      </div>
      <div style="display: flex; margin-top: 30px">
        <span>扣款原因：</span>
        <el-input type="textarea" :rows="2" placeholder="请输入扣款原因" v-model="textarea" @change="deductReason" style="width: 300px"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogPerformance = false">取 消</el-button>
        <el-button type="primary" @click="getDeduction">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delloading" width="40%" title="批量删除课程表" @close="close">
      <div>
        <div style="margin-left: 10px">课程数量：{{ delList.length }}</div>
        <ul>
          <li v-for="(item, index) in delList" :key="index">
            <!-- 学员:{{ rowData.createTime }},教练老师:{{ rowData.createTime }},上课时间:{{  }} -->
            学员 : {{ item.name }}({{ item.studentCode }}) 教练老师 : {{ item.teacher }} 上课时间 : {{ item.date }} {{ item.format }}
            {{ item.time }}
          </li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="delloading = false">取 消</el-button>
        <el-button type="primary" @click="deletelist">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 更换教练 -->
    <el-dialog title="更换教练" :visible.sync="teacherDialog" width="30%" :before-close="closeTeacher">
      <el-form ref="teacherForm" :model="teacherForm" label-width="80px">
        <el-form-item label="当前教练 ">
          <el-input v-model="teacherForm.oldTeacher" disabled></el-input>
        </el-form-item>
        <el-form-item label="更换教练">
          <!-- v-el-select-loadmore="loadmoreTeam" :loading="loadingTeam" -->
          <el-select v-model="teacherForm.newTeacher" placeholder="">
            <el-option v-for="(item, index) in canChangeList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeTeacher">取 消</el-button>
        <el-button type="primary" @click="submitChange">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="学习反馈" :visible.sync="feekbookIsShow" width="50%" top="3vh" :close-on-click-modal="false">
      <div v-loading="feedBackLoading" style="display: flex; flex-direction: column">
        <WriteFeedbook ref="foodbook" @close="foodbookClose" :row="feekbookRow" :rowVideo="videobookRow" :type="feedbookType" v-if="feekbookIsShow"></WriteFeedbook>
        <div slot="footer" style="margin: 20px 0 0 0">
          <el-button type="primary" @click="feekbookOk">确定</el-button>
          <el-button @click="foodbookClose1">取消</el-button>
        </div>
      </div>
    </el-dialog>

    <dataLookDialog @dataLooker="dataLooker" :dataLookerStyle="dataLookerStyle" :direction="direction" ref="dataLookDialog" />
    <classCardDialog @changeDrawer="changeDrawer" :classCardstyle="classCardstyle" :direction="direction" @updateList="initData" ref="rowshuju" />
    <LeaveProcessingDialog
      @LeaveDialog="LeaveDialog"
      :LeaveStyle="LeaveStyle"
      :direction="direction"
      ref="LeaveDialog"
      @fMethod="paikeBtn"
      @qjstartTime="qjstartTime1"
      @qjendTime="qjendTime1"
    />
    <numberLookDialog @lookDrawer="lookDrawer" :lookstyle="lookstyle" :direction="direction" ref="numberLook" />

    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="editHeaderSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
  </div>
</template>

<script>
  import WriteFeedbook from './writeFeedbook';
  import { DeletePlanStudy, DeletePlanStudyList } from '@/api/zhujiao';
  import { selAllTeacher, xueguanListApi, useableList, changeTeacher, geteExperienceInfo, getFeedbackInfo1, finishCourse, getVideoInfo } from '@/api/studentClass/changeList';
  import LeaveProcessingDialog from '../pclass/components/LeaveProcessingDialog.vue';
  import classCardDialog from '../pclass/components/classCardDialog.vue';
  import numberLookDialog from '../pclass/components/numberLookDialog.vue';
  import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue';
  import { deletePlanStudy, getFeedbackInfo } from '@/api/paikeManage/LearnManager';
  import dataLookDialog from '../pclass/components/dataLookDialog.vue';
  import {
    getAdjustInfo,
    getTimetable,
    getTotalHours,
    getVacationInfo,
    studentStudyExport,
    getDeductWage,
    getCourseModule,
    getCourseModulelist,
    getTableTitleSet,
    setTableList,
    bvstatusListOne
  } from '@/api/paikeManage/classCard';
  import { getCourseclassList, getDeliverCodes } from '@/api/FinanceApi/assistantWages';
  import { mapGetters } from 'vuex';
  import dayjs from 'dayjs';
  import ls from '@/api/sessionStorage';
  import { start } from 'nprogress';
  import checkPermission from '@/utils/permission';
  import { file } from 'jszip/lib/object';
  export default {
    name: 'classCard',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    components: {
      WriteFeedbook,
      classCardDialog,
      dataLookDialog,
      LeaveProcessingDialog,
      numberLookDialog,
      HeaderSettingsDialog
    },
    computed: {
      ...mapGetters(['roles']),
      getCustomTableHeaders() {
        if (checkPermission(['admin', 'DeliveryCenter'])) {
          return this.tableHeaderList;
        } else {
          let customColumns = ['actualClassTime', 'meetingOnlineTime', 'feedbackTime', 'teacherClassStatus'];
          return this.tableHeaderList.filter((item) => !customColumns.includes(item.value));
        }
      },
      editHeaderSettings() {
        if (checkPermission(['admin', 'DeliveryCenter'])) {
          return this.headerSettings;
        } else {
          let customColumns = ['actualClassTime', 'meetingOnlineTime', 'feedbackTime', 'teacherClassStatus'];
          return this.headerSettings.filter((item) => !customColumns.includes(item.value));
        }
      }
    },
    data() {
      return {
        delList: [],
        delloading: false,
        screenWidth: window.screen.width,
        stuudentCode: null,
        dataLookerStyle: false,
        // 日期组件
        pickerOptions: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                // const end = new Date();
                // const start = new Date();
                // picker.$emit('pick', [start, end]);
                const temp = new Date();
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '昨天',
              onClick(picker) {
                const temp = new Date();
                temp.setTime(temp.getTime() - 3600 * 1000 * 24);
                picker.$emit('pick', [new Date(temp.setHours(0, 0, 0, 0)), new Date(temp.setHours(23, 59, 59, 0))]);
              }
            },
            {
              text: '最近七天',
              onClick(picker) {
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                picker.$emit('pick', [start, end]);
              }
            }
          ]
        },
        feekbookIsShow: false,
        feedBackLoading: false,
        feedbookType: 1,
        feekbookRow: null,
        videobookRow: null,
        qjstartTime: '',
        qjendTime: '',
        tableIshow: true,
        redLeave: [],
        lookstyle: false,
        LeaveStyle: false,
        classCardstyle: false, //抽屉状态
        LeaveId: '',
        searchNum: {
          merchantCode: '',
          courseType: '',
          name: '',
          endTime: '',
          startTime: '',
          teacherName: '',
          teacherId: '',
          studentCode: '',
          studyStatus: '',
          lastStudyTime: '',
          learnTubeTeacherName: '',
          pageNum: 1,
          pageSize: 10,
          planId: undefined,
          isSetModule: '',
          curriculumId: '',
          deliverClass: ''
        }, //搜索参数
        tableLoading: false,
        direction: 'rtl', //超哪边打开
        teacher: '',
        timeAll: [],
        contentType: '',
        total: null,
        luyouclassCard: [],
        leaveApplication: {
          id: '',
          type: 1
        },
        getFeedback: {
          //详情的参数
          id: '',
          type: '1'
        },
        isAdmin: false,
        exportLoading: false,
        totalTubeHours: '', //学时统计学时数量
        options: [
          {
            value: '0',
            label: '未选择课程模块'
          },
          {
            value: '1',
            label: '已选择课程模块'
          }
        ],
        value: '',
        dialogPerformance: false,
        deductId: '', // 扣绩效id

        textarea: '',
        input: '',

        lessonList: [],
        actualCourseModule: '', // 课程模块
        studyId: '', // 学习课程id

        dataShow: false,
        delarry: [],

        option: [],
        loadingShip: false,
        selectObj: {
          pageNum: 1,
          pageSize: 20,
          name: ''
        },

        xueguanList: {
          pageNum: 1,
          pageSize: 10,
          realName: ''
        }, //学管师页面渲染
        xgsList: [],

        //学管名称
        xueGuanName: '',

        HeaderSettingsStyle: false, // 列表属性弹框
        headerSettings: [
          {
            name: '姓名',
            value: 'name'
          },
          {
            name: '学员来源',
            value: 'merchantName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '门店账号',
            value: 'merchantCode'
          },
          {
            name: '学员编号',
            value: 'studentCode'
          },
          {
            name: '课程类型',
            value: 'curriculumName'
          },
          {
            name: '上课模块',
            value: 'actualCourse'
          },
          {
            name: '上课时间',
            value: 'classTime'
          },
          {
            name: '实际上课时间',
            value: 'actualClassTime'
          },
          {
            name: '会议在线时间',
            value: 'meetingOnlineTime'
          },
          {
            name: '填写反馈时间',
            value: 'feedbackTime'
          },
          {
            name: '状态',
            value: 'studyStatus'
          },
          {
            name: '教练上课状态',
            value: 'teacherClassStatus'
          },
          {
            name: '使用时长',
            value: 'hours'
          },
          {
            name: '教练老师',
            value: 'teacher'
          },
          {
            name: '所属学管',
            value: 'learnTubeName'
          },
          {
            name: '班级',
            value: 'deliverClass'
          },
          {
            name: '添加人',
            value: 'type'
          },
          {
            name: '课程状态',
            value: 'status'
          },
          {
            name: '月请假次数',
            value: 'vacationCount'
          },
          {
            name: '工资',
            value: 'wage'
          },
          {
            name: '推荐人评价',
            value: 'referrerEvaluateVoList'
          },
          {
            name: '家长评价',
            value: 'parentEvaluateVoList'
          }
        ],

        tableHeaderList: [], // 获取表头数据
        teacherForm: {
          oldTeacher: '',
          oldId: '',
          newTeacher: ''
        },
        teacherDialog: false,

        canChangeList: [],
        courseList: []
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
      if (this.isAdmin) {
        this.headerSettings.splice(0, 0, {
          name: '交付中心编号',
          value: 'deliverMerchant'
        });
        this.headerSettings.splice(1, 0, {
          name: '交付中心名称',
          value: 'deliverName'
        });
      }

      this.initData();
      this.getTeacherList();
      // this.getCourseList();
      this.stuudentCode = this.$route.query.classCard;
      this.searchNum.planId = this.$route.query.planId;
      this.searchNum.studentCode = this.stuudentCode ? this.stuudentCode : '';
      this.getDatetime();
      this.xueguanBtn();
      this.getHeaderlist();
    },
    mounted() {
      this.getListShow();
      this.getbvstatusList();
    },
    methods: {
      checkPermission,
      getbvstatusList() {
        bvstatusListOne().then((res) => {
          this.courseList = res.data;
        });
      },
      closeTeacher() {
        this.teacherForm = {
          oldTeacher: '',
          oldId: '',
          newTeacher: ''
        };
        this.teacherDialog = false;
      },
      getTimeYMDHMS(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以要加1，并确保两位数字
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);

        return year + '-' + month + '-' + day + ' ' + hours + '：' + minutes;
      },
      //换算时间
      getoldDatetime(a, b) {
        let Start = new Date(a).getTime();
        let end = new Date(b).getTime();
        if (Start > end) {
          end = new Date(a).getTime() + 24 * 60 * 60 * 1000;
          return [this.getTimeYMDHMS(Start), this.getTimeYMDHMS(end)];
        } else {
          return [a, b];
        }
      },
      foodbookClose1() {
        this.feekbookRow = null;
        this.videobookRow = null;
        this.feedBackLoading = false;
        this.feekbookIsShow = false;
      },
      foodbookClose() {
        // this.$mess;
        this.initData();
        this.feekbookRow = null;
        this.videobookRow = null;
        this.feekbookIsShow = false;
      },
      async finishCourseFn(row) {
        let that = this;
        let res = await finishCourse({
          studyId: row.id
        });
        if (res.success) {
          that.initData();
          this.$message.success('操作成功');
        } else {
          that.$message({
            type: 'error',
            message: res.data.message
          });
        }
      },
      async weiteFeedback(row) {
        console.log(row, 'row');

        let Start = row.date + ' ' + row.time;
        let end = row.date + ' ' + row.endTime;
        let arr = this.getoldDatetime(Start, end);
        let obj = {
          id: row.id,
          type: 1,
          actualStart: arr[0],
          actualEnd: arr[1]
        };
        let videoDataRes = '';
        try {
          videoDataRes = await getVideoInfo({ studentCode: row.studentCode, curriculumId: row.curriculumId });
        } catch (error) {
          videoDataRes = '';
        }
        this.feedBackLoading = true;
        this.feekbookIsShow = true;
        console.log(videoDataRes, 'videoDataRes');
        this.videobookRow = videoDataRes.data ? videoDataRes.data : [];
        try {
          if (row.experience) {
            this.feedbookType = '1';
            let res = await geteExperienceInfo(obj);
            this.feekbookRow = res.data;
            this.feekbookRow.teacher = row.teacher;
            this.feekbookRow.curriculumName = row.curriculumName;
            this.feekbookRow.id = row.id;
          } else {
            this.feedbookType = '2';
            let res = await getFeedbackInfo1(obj);
            this.feekbookRow = res.data;
            this.feekbookRow.teacher = row.teacher;
            this.feekbookRow.curriculumName = row.curriculumName;
            this.feekbookRow.id = row.id;
          }
          this.feekbookRow.experience = row.experience;
          setTimeout(() => {
            this.feedBackLoading = false;
          }, 500);
        } catch (error) {
          setTimeout(() => {
            this.foodbookClose1();
          }, 500);
        }
      },
      feekbookOk() {
        if (this.feedbookType == 1) {
          this.$refs.foodbook.submit1();
        } else {
          this.$refs.foodbook.submit();
        }
      },
      submitChange() {
        // console.log(this.teacherForm);
        if (!this.teacherForm.newTeacher) return this.$message.warning('请选择需要更换的教练');
        changeTeacher({
          id: this.teacherForm.oldId,
          teacherId: this.teacherForm.newTeacher
        })
          .then((res) => {
            console.log(res, 'ssssssss');
            this.$message.success('更换成功');
            this.initData();
            this.closeTeacher();
          })
          .catch((err) => {
            // return this.$message.error('操作失败')
            console.log(err, 'eeeeeeee');
          });
      },
      async onChangeTeacher(row) {
        let { data } = await useableList({ id: row.id });
        console.log(data, '-----------------------');
        this.canChangeList = data;
        let res = JSON.parse(JSON.stringify(row));
        this.teacherForm.oldTeacher = res.teacher;
        this.teacherForm.oldId = res.id;
        this.teacherDialog = true;
      },
      close(v) {
        this.refundDetailShow = false;
      },
      //删除所选项
      deletelist() {
        DeletePlanStudy(this.delarry).then((res) => {
          // console.log(res);
          if (res.code == 20000) {
            this.$message({
              message: res.message,
              type: 'success'
            });
            this.delloading = false;
            this.initData();
          } else {
            this.$message({
              message: res.message,
              type: 'error'
            });
          }
        });
      },
      //删除弹窗
      delloadfun() {
        if (this.delarry.length == 0) {
          this.$message({
            message: '批量删除数据至少为一条以上',
            type: 'warning'
          });
        } else {
          this.delloading = true;
          DeletePlanStudyList(this.delarry).then((res) => {
            // console.log(res);
            this.delList = res.data.data;
            // if (res.code == 20000) {
            //   this.$message({
            //     message: res.message,
            //     type: 'success'
            //   })
            //   this.initData()
            // }else{
            //   this.$message({
            //     message: res.message,
            //   type: 'error'
            // })
            // }
          });
        }
      },
      //删除
      DeletePlanStudy() {
        DeletePlanStudy(this.delarry).then((res) => {
          // console.log(res);
          if (res.code == 20000) {
            this.$message({
              message: res.message,
              type: 'success'
            });

            this.initData();
          } else {
            this.$message({
              message: res.message,
              type: 'error'
            });
          }
        });
      },
      //勾选置灰
      checkSelect(row, index) {
        let isChecked = true;
        if (row.studyStatus == 0 && row.experience == false) {
          isChecked = true;
        } else {
          isChecked = false;
        }
        return isChecked;
      },
      getRowKey(row) {
        return row.id;
      },
      // 用于保存选中的行
      handleSelectionChangeDepartment(val) {
        this.delarry = val.map((item) => item.id);
        // console.log(this.delarry);
      },
      headerList() {
        if (this.tableHeaderList.length > 0) {
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 获取教练
      async getTeacherList() {
        let allData = await selAllTeacher(this.selectObj);
        this.option = this.option.concat(allData.data.data);
      },

      filterValue(value) {
        console.log(value);
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = value;
        this.getTeacherList();
      },

      changeMessage(e) {
        this.$forceUpdate();
      },

      clearSearchRecord(event) {
        setTimeout(() => {
          if (this.searchNum.teacherName == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.selectObj.name = '';
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },

      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      },

      // 搜索
      initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), (this.totalTubeHours = '');
        this.initData();
      },
      dataLooker(v) {
        this.dataLookerStyle = v;
      },
      statusClass(status) {
        switch (status) {
          case 1:
            return '';
          case 2:
            return 'error';
        }
      },
      qjstartTime1(v) {
        this.qjstartTime = v;
      },
      qjendTime1(v) {
        this.qjendTime = v;
      },
      // 学习课程表请假处理
      async LeaveBtn(row) {
        this.redLeave = row;
        this.LeaveId = row.id;
        this.LeaveStyle = true;
        this.leaveApplication.id = row.id;
        let res = await getVacationInfo(this.leaveApplication);
        this.$refs.LeaveDialog.LeaveList = res.data;
        this.$refs.LeaveDialog.studyDate = row.date;
      },
      async initData() {
        // 判断为null的时候赋空
        if (!this.timeAll) {
          this.timeAll = [];
        }
        this.tableLoading = true;
        this.searchNum.startTime = this.timeAll[0];
        this.searchNum.endTime = this.timeAll[1];
        this.tableLoading = true;
        if (!/^\d+$/.test(this.searchNum.learnTubeTeacherName)) {
          this.searchNum.learnTubeTeacherName = '';
          this.xueGuanName = '';
        }
        let { data } = await getTimetable(this.searchNum);
        if (this.searchNum.endTime != '' && this.searchNum.endTime != undefined && (this.searchNum.teacherName != '' || this.searchNum.learnTubeTeacherName != '')) {
          let result = await getTotalHours(this.searchNum);
          this.totalTubeHours = result.data;
        } else {
          this.totalTubeHours = '';
        }
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.luyouclassCard = data.data;
      },

      LeaveDialog(v) {
        this.LeaveStyle = v;
      },
      changeDrawer(v) {
        this.classCardstyle = v;
      },
      lookDrawer(v) {
        this.lookstyle = v;
      },
      // 转文字
      teachingType(val) {
        if (val.teachingType == 1) {
          return '远程';
        } else if (val.teachingType == 2) {
          return '线下';
        } else if (val.teachingType == 3) {
          return '远程和线下';
        } else {
          return '暂无';
        }
      },
      courseType(val) {
        if (val.courseType == 1) {
          return '鼎英语';
        }
      },
      status(val) {
        if (val.status == 1) {
          return '正常';
        } else if (val.status == 2) {
          return '请假';
        } else if (val.status == 3) {
          return '请假已处理';
        }
      },
      studyStatus(val) {
        if (val.studyStatus == 0) {
          return '未上课';
        } else if (val.studyStatus == 1) {
          return '已上课未反馈';
        } else if (val.studyStatus == 2) {
          return '已完成';
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      // 编辑按钮
      async paikeBtn(row) {
        if (row) {
          let reslist = await getAdjustInfo(row.id);
          this.$refs.rowshuju.classCardnum = reslist.data;
          this.$refs.rowshuju.studentList = row;
          this.$refs.rowshuju.teacherId = reslist.data.teacherId;
          this.classCardstyle = true;
          this.$refs.rowshuju.planStudy.studentCode = row.studentCode;
          this.$refs.rowshuju.getTeachlist();
        } else {
          let reslist = await getAdjustInfo(this.LeaveId);
          this.$refs.rowshuju.classCardnum = reslist.data;
          this.$refs.rowshuju.studentList = this.redLeave;
          this.$refs.rowshuju.teacherId = reslist.data.teacherId;
          this.classCardstyle = true;
          this.$refs.rowshuju.getTeachlist();
          if (this.qjstartTime !== '' && this.qjstartTime != undefined && this.qjstartTime != null) {
            this.$refs.rowshuju.classCardnum.startStudyTime = this.qjstartTime;
          }
          if (this.qjendTime !== '' && this.qjendTime != undefined && this.qjendTime != null) {
            this.$refs.rowshuju.classCardnum.endStudyTime = this.qjendTime;
          }
        }
        // let reslist = await getAdjustInfo(row.id);
        // this.$refs.rowshuju.classCardnum = reslist.data;
        // this.$refs.rowshuju.studentList = this.redLeave;
        // this.$refs.rowshuju.teacherId = reslist.data.teacherId;
        // this.classCardstyle = true;
        // this.$refs.rowshuju.getTeachlist();
      },
      // 数据查看按钮
      async lookBtn(row) {
        if (row.experience) {
          // 体验结果反馈弹框
          this.$refs.dataLookDialog.experienceId = row.id;
          this.$refs.dataLookDialog.initData();
        } else {
          // 数据查看弹框
          this.$refs.numberLook.teacher = row.teacher;
          this.$refs.numberLook.studyList.studentCode = row.studentCode;
          this.getFeedback.id = row.id;
          this.$refs.numberLook.reviewTotal.id = row.id;
          this.$refs.numberLook.reviewTotal.planId = row.planId;
          let res = await getFeedbackInfo(this.getFeedback);
          if (res.code == 20000) {
            this.lookstyle = true;
          }
          this.$refs.numberLook.studyList1 = res.data;
          console.log(this.$refs.numberLook.studyList1);
        }
      },
      dateDiff(d1, d2) {
        d1 = new Date(d1.replace(/-/g, '/'));
        d2 = new Date(d2.replace(/-/g, '/'));
        var obj = {};
        obj.s = Math.floor((d2 - d1) / 1000); //差几秒
        obj.m = Math.floor(obj.s / 60); //差几分钟
        obj.h = Math.floor(obj.m / 60); //差几小时
        obj.D = Math.floor(obj.h / 24); //差几天
        return obj.D;
      },
      //定义导出Excel表格事件
      exportExcel() {
        // timeAll
        if (this.timeAll.length <= 0) {
          this.$message({
            message: '请先选择时间，再进行数据导出，最多可选一个月',
            type: 'warning'
          });
          return;
        } else {
          //同一月内不校验  跨月校验30天
          let a = this.timeAll[0].split('-')[1];
          let b = this.timeAll[1].split('-')[1];
          let c = this.timeAll[0].split('-')[0];
          let d = this.timeAll[1].split('-')[0];
          if (a != b || c != d) {
            if (this.dateDiff(this.timeAll[0], this.timeAll[1]) > 30) {
              this.$message.error('最多可导出一个月数据，请重新选择时间');
              return;
            }
          }
        }
        this.exportLoading = true;
        let obj = { ...this.searchNum };
        obj.startTime = this.timeAll[0];
        obj.endTime = this.timeAll[1];

        studentStudyExport(obj)
          .then((res) => {
            this.$message({
              message: '该导出为异步导出，请前往下载中心即可下载',
              type: 'success'
            });
            this.exportLoading = false;
          })
          .catch(() => {
            this.exportLoading = false;
          });
      },
      //删除学习课程
      async deleteStudy(row) {
        await this.$confirm('您确定要删除学习课程吗?');
        await deletePlanStudy(row.id);
        this.$message.success('删除成功');
        await this.initData();
      },

      deduct(row) {
        this.input = '';
        this.textarea = '';
        this.deductId = row.id;
        this.dialogPerformance = true;
      },

      deductPrice(e) {
        this.input = e;
        this.$forceUpdate();
      },

      deductReason(e) {
        this.textarea = e;
        this.$forceUpdate();
      },

      async getDeduction() {
        let data = {
          deductReason: this.textarea,
          deductWage: this.input,
          studyId: this.deductId
        };
        getDeductWage(data).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.dialogPerformance = false;
          this.initData();
        });
      },

      // 教练工资课程列表
      // async getCourseList() {
      //   let that = this;
      //   await getCourseclassList().then((res) => {
      //     that.lessonList = res.data;
      //     for (let i = 0; i < that.lessonList.length; i++) {
      //       if (that.lessonList[i].name == 'WORD') {
      //         that.lessonList[i]["label"] = '单词';
      //       } else if (that.lessonList[i].name == 'NOVICE_GRAMMAR') {
      //         that.lessonList[i]["label"] = '小学语法';
      //       } else if (that.lessonList[i].name == 'INTERMEDIATE_GRAMMAR') {
      //         that.lessonList[i]["label"] = '初中语法';
      //       } else if (that.lessonList[i].name == 'ADVANCED_GRAMMAR') {
      //         that.lessonList[i]["label"] = '高中语法';
      //       } else {
      //         that.lessonList[i]["label"] = '阅读';
      //       }
      //     }
      //     console.log(that.lessonList)
      //   });
      // },

      // 设置课程模块
      async setCourseModule() {
        let that = this;
        let data = {
          studyId: this.studyId,
          courseModule: this.actualCourseModule
        };
        await getCourseModule(data).then((res) => {
          that.$message({
            type: 'success',
            message: '操作成功'
          });
          this.initData();
        });
      },

      changeLesson(id, e) {
        this.studyId = id;
        this.actualCourseModule = e;
        this.setCourseModule();
      },

      async getModulelist(id, e) {
        let data = {
          planId: id
        };
        let that = this;
        await getCourseModulelist(data).then((res) => {
          console.log(res);
          that.lessonList = res.data;
          this.$forceUpdate();
        });
      },

      // 判断扣绩效时间是否过期
      getDatetime() {
        let times = Date.now(); //时间戳
        // let data = dayjs().date(14).hour(23).minute(59).second(59).format('YYYY-MM-DD HH: mm :ss');
        // let datatime = dayjs().date(14).hour(23).minute(59).second(59).unix() * 1000;
        // let time = dayjs().date(14).endOf('day').format('YYYY-MM-DD HH: mm :ss');
        let datetime = dayjs().date(14).endOf('day').unix() * 1000;
        if (times > datetime) {
          return true;
        }
      },

      changeCourse(e) {
        console.log(e);
        this.searchNum.isSetModule = e;
      },

      //重置
      rest() {
        this.$refs.searchNum.resetFields();
        this.timeAll = [];
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
        this.initData();
      },

      // 判断课程模块是否禁用
      getDisabled(row) {
        let time = '2023-09-28 14:45:36';
        let datetime = row.date + ' ' + row.time + ':00';
        if (datetime < time) {
          return true;
        } else if (row.experience == true) {
          return true;
        }
      },

      getListShow() {
        getDeliverCodes().then((res) => {
          this.dataShow = res.data;
          // if (this.dataShow) {
          //   this.headerSettings.push({
          //     name: '工资',
          //     value: 'wage'
          //   })
          // }
        });
      },

      // 学管接口
      // async xueguancc() {
      //   this.xueguanList.pageNum = 1,
      //   this.xueguanList.pageSize = 10,
      //   this.xueguanBtn()
      // },

      // 下拉加载
      handleLoadxueguan() {
        if (!this.loadingShip) {
          this.xueguanList.pageNum++;
          this.xueguanBtn();
        }
      },

      async xueguanBtn() {
        let { data } = await xueguanListApi(this.xueguanList);
        this.xgsList = this.xgsList.concat(data.data);
        // this.xgsList.totalItems = Number(data.totalItems);
        // console.log(this.xgsList);
      },

      filterXueguan(value) {
        this.xgsList = [];
        this.xueguanList.pageNum = 1;
        this.xueguanList.realName = value;
        this.xueguanBtn();
      },

      clearStudentManagement() {
        this.searchNum.learnTubeTeacherName = this.xueguanList.realName;
        setTimeout(() => {
          if (this.searchNum.learnTubeTeacherName == '') {
            this.xgsList = [];
            this.xueguanList.pageNum = 1;
            this.xueguanList.realName = '';
            this.searchNum.learnTubeTeacherName = '';
            this.xueGuanName = '';
            this.xueguanBtn();
          }
        }, 200);
        this.$forceUpdate();
      },

      studentManagement(e) {
        if (e == '') {
          this.xgsList = [];
          this.xueguanList.pageNum = 1;
          this.xueguanList.realName = '';
          this.xueguanBtn();
        } else {
          this.$nextTick(function () {
            this.xueGuanName = this.$refs.xueGuanRef.selected.label;
          });
        }
      },
      changeTeacherName() {
        this.$forceUpdate();
      },

      // 接收子组件选择的表头数据
      selectedItems(arr) {
        let data = {
          type: 'classCard',
          value: JSON.stringify(arr)
        };
        this.setHeaderSettings(data);
      },
      removeNullValues(jsonStr) {
        const obj = JSON.parse(jsonStr);
        const cleanObj = JSON.parse(JSON.stringify(obj)); // 创建一个干净的对象副本
        let newJson = cleanObj.filter((item) => item !== null);
        return JSON.stringify(newJson); // 返回去除null值后的JSON字符串
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'classCard'
        };
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            // this.tableHeaderList = JSON.parse(res.data.value);
            // console.log(this.tableHeaderList);
            let Json = this.removeNullValues(res.data.value);
            this.tableHeaderList = JSON.parse(Json);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 获取上课状态文字
      getTeacherClassStatusTxt(value) {
        // const statusList = value.split(';');

        // let result = [];
        // if (statusList.length > 0) {
        //   for (let i = 0; i < statusList.length; i++) {
        //       result.push(statusList[i] === '0' ? '正常' : statusList[i] === '1' ? '迟到' : statusList[i] === '2' ? '早退' : statusList[i] === '3' ? '迟到早退' :'-');
        //   }
        // }
        // return result.join('、');
        let result = '';
        switch (value) {
          case '0':
            result = '正常';
            break;
          case '1':
            result = '迟到';
            break;
          case '2':
            result = '早退';
            break;
          case '3':
            result = '迟到早退';
            break;
          default:
            result = '-';
            break;
        }
        return result;
      }
    }
  };
</script>

<style scoped>
  ::v-deep .el-dialog__title {
    font-size: 30px;
    letter-spacing: 3px;
  }

  ::v-deep .el-dialog__header {
    text-align: center;
  }

  ::v-deep .row[data-v-4dedd696] {
    font-size: 19px;
  }

  body {
    background-color: #f5f7fa;
  }

  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  body {
    background-color: #f5f7fa;
  }

  ul {
    list-style: none;
    padding-left: 0 !important;
  }

  li {
    margin: 10px;
  }
</style>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  .rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: space-around;
  }
</style>
