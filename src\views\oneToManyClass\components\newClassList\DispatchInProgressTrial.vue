<!-- 一对多-新班级列表-试课派单中 -->
<template>
  <div>
    <el-form :model="searchNum" ref="query" label-width="100px" class="container-card my-form" :inline="true" size="small">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="40">
        <!-- <el-col :span="6">
          <el-form-item label="姓名:" prop="studentName">
            <el-input v-model="searchNum.studentName" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学员编号:" prop="studentCode">
            <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" size="small"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('派单状态') && isAdmin">
          <el-form-item label="派单状态:">
            <el-select v-model="searchNum.dispatchOrderStatus" clearable size="small" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option label="进行中" value="1"></el-option>
              <el-option label="无人接单" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('交付中心编号')">
          <el-form-item label="交付中心编号:" prop="deliverMerchant">
            <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6" v-if="hasNoPermissionField('交付中心名称')">
          <el-form-item label="交付中心名称:" prop="deliverName">
            <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="班级:" prop="classCodeOrName">
            <el-input v-model="searchNum.classCodeOrName" clearable placeholder="请输入班级名称或编号" size="small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年级:" prop="grade">
            <el-select v-model="searchNum.grade" size="small" placeholder="请选择" clearable>
              <el-option v-for="item in gradeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <el-form-item label="时间筛选:">
            <el-date-picker
              v-model="timeAll"
              size="small"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              :picker-options="pickerOptions"
              align="right"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="4" :xs="20" style="margin-left: auto">
          <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-row style="margin: 20px 0 20px 20px">
      <el-col :span="2">
        <el-button type="primary" @click="headerList()">列表显示属性</el-button>
      </el-col>
    </el-row>
    <!--  -->
    <el-table
      v-loading="tableLoading"
      :data="tableList"
      style="width: 100%"
      id="out-table"
      :header-cell-style="{ background: '#f5f7fa' }"
      :cell-style="{ 'text-align': 'center' }"
      height="400"
    >
      <el-table-column
        v-for="(item, index) in tableHeaderList"
        :key="`${index}-${item.id}`"
        :prop="item.value"
        :label="item.name"
        header-align="center"
        :width="getWidth(item.value)"
        :show-overflow-tooltip="['grade'].includes(item.value)"
      >
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" @click="openEdit(row)">试课单</el-button>
            <el-button type="danger" v-if="isAdmin" size="mini" @click="onAssign(row)">强制指派</el-button>
            <el-button type="warning" v-else size="mini" @click="onAssign(row)">指派</el-button>
          </div>
          <!-- 期望上课时间 -->
          <div v-else-if="item.value == 'studyDate'">
            <el-row style="white-space: break-spaces">
              {{ row.studyDate }}
            </el-row>
          </div>
          <!-- 学员 -->
          <div v-else-if="item.value == 'studentList'">
            <el-tooltip
              v-if="row.studentList"
              :disabled="row.studentList.length < 3"
              class="item"
              effect="dark"
              :content="row.studentList.map((item) => `${item.studentName}(${item.studentCode})`).join(', ')"
              placement="top"
            >
              <el-row>
                <el-col :span="24" v-for="(row, index) in (row.studentList || []).slice(0, 3)" :key="index">
                  <span>{{ `${row.studentName}(${row.studentCode})` }}</span>
                </el-col>
                {{ row.studentList.length > 3 ? '...' : '' }}
              </el-row>
            </el-tooltip>
            <el-row v-else>-</el-row>
          </div>
          <!-- 年级 -->
          <span v-else-if="item.value == 'grade'">
            {{ row.gradeName || '-' }}
          </span>
          <!-- 派单状态 -->
          <div v-else-if="item.value == 'dispatchOrderStatus'">
            <span>
              {{ row.dispatchOrderStatus == 1 ? '进行中' : row.dispatchOrderStatus == 2 ? '无人接单' : '已接单' }}
            </span>
          </div>
          <!-- 派单时间 -->
          <el-row :gutter="20" type="flex" justify="center" align="middle" v-else-if="item.value == 'dispatchOrderDate'">
            <el-col :span="18" :offset="0">
              <div>{{ row[item.value] }}</div>
            </el-col>
            <el-col :span="6" :offset="0" v-if="isAdmin">
              <el-button type="text" @click="getDetail(row.id)">详情</el-button>
            </el-col>
          </el-row>
          <!-- 课程类型 -->
          <div v-else-if="item.value == 'curriculumId'">
            <span>
              {{ getCourse(row.curriculumId) || '-' }}
            </span>
          </div>
          <!-- 剩余接单时间 -->
          <span v-else-if="item.value == 'timeOut'">
            <statistic
              v-if="row.timeOut && getTrueTime(row.timeOut)"
              ref="statistic"
              format="HH:mm:ss"
              :valueClass="statusClass(row.timeOut)"
              :value="getResidueTime(row['timeOut'])"
              :key="'statistic' + row['timeOut']"
              time-indices
            ></statistic>
            <span v-else>{{ '-' }}</span>
          </span>
          <!-- 状态 -->
          <div v-else-if="item.value == 'status'">
            <el-tag v-if="row.status == 1" type="success" effect="dark" size="mini" style="width: 55px">正常</el-tag>
            <el-tag v-else type="danger" effect="dark" size="mini" style="width: 55px">已停用</el-tag>
          </div>
          <!-- 班级人数 -->
          <div v-else-if="item.value == 'classStudentCount'">
            <span style="padding-right: 10px">{{ row.classStudentCount }}</span>
            <el-tag v-if="row.classStudentCount >= row.studentCount" type="danger" effect="dark" size="mini">满</el-tag>
          </div>
          <!-- 其他 -->
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页器 -->
    <el-row v-if="tableList.length > 0" type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        v-if="tableList"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 指派弹框 -->
    <el-dialog :visible.sync="assignDialog" :width="screenWidth > 1300 ? '60%' : '90%'" :title="isAdmin ? '强制指派交付中心' : '指派交付小组'">
      <el-form ref="form" label-width="150px">
        <el-form-item :label="isAdmin ? '选择交付中心' : '选择交付小组'">
          <el-select
            v-if="isAdmin"
            placeholder="请选择"
            :popper-append-to-body="false"
            :loading="deliverMerchantLoad"
            v-model="assignForm.deliverMerchant"
            remote
            filterable
            :filter-method="deliverMerchantFilterValue"
            v-el-select-loadmore="loadmoreDeliverMerchant"
            reserve-keyword
            clearable
            @change="changeDeliverMerchant"
            @focus="changeDeliverMerchant(deliverOptions.length > 0 ? '非空' : '')"
          >
            <el-option v-for="item in deliverOptions" :key="item.merchantCode" :label="item.merchantName" :value="item.merchantCode"></el-option>
          </el-select>
          <el-select v-else placeholder="请选择" :popper-append-to-body="false" :loading="deliverMerchantLoad" v-model="assignForm.deliverMerchant" filterable>
            <el-option v-for="item in deliverOptions" :key="item.id" :label="item.teamName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-row type="flex" justify="end" style="margin-top: 2.5vh">
        <el-button type="primary" style="width: 100px" plain size="small" @click="closeAssgin">取消</el-button>
        <el-button type="primary" style="width: 100px" size="small" :loading="isSubmit" @click="submitAssign">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 修改试课单 -->
    <el-dialog
      :visible.sync="dialogAbutment"
      v-if="editFormList.length > 0"
      top="2vh"
      :close-on-click-modal="false"
      :width="screenWidth > 1300 ? '60%' : '90%'"
      title="试课单"
      @close="closeEdit"
    >
      <el-row style="margin: 20px 0 20px 20px">
        <el-tabs v-model="editFormListIndex" @tab-click="handleTabsClick">
          <el-tab-pane v-for="(item, index) in editFormList" :label="item.studentName" :name="index + ''" :key="index"></el-tab-pane>
        </el-tabs>
      </el-row>
      <TrialClass v-if="dialogAbutment" :id="editFormId" :reqType="2" @close="closeEdit"></TrialClass>
      <el-row type="flex" justify="center" style="margin-top: 2.5vh">
        <el-button type="primary" style="margin-right: 1.5vw" size="small" @click="closeEdit">确定</el-button>
      </el-row>
    </el-dialog>
    <!-- 表头设置 -->
    <HeaderSettingsDialog
      @HeaderSettingsLister="HeaderSettingsLister"
      :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings"
      ref="HeaderSettingsDialog"
      @selectedItems="selectedItems"
    />
    <!-- 历史派单记录弹窗 -->
    <ClasstHistoryOrderDispatchDialog :visible.sync="historyOrderDispatchDialogVisible" :classId="historyOrderDispatchClassId" />
  </div>
</template>

<script>
  import { selectTeam } from '@/api/orderManage';
  import { mapGetters } from 'vuex';

  import { getTableTitleSet, setTableList } from '@/api/paikeManage/classCard';
  import { getOneToMoreClassList, searchDeliverCenter } from '@/api/oneToManyClass/newClassList';

  import { regionData } from 'element-china-area-data';
  import orderApi from '@/api/oneToManyClass/DispatchInProgress';
  import checkPermission from '@/utils/permission';

  import statistic from '../statistic.vue';
  import HeaderSettingsDialog from '../../../pclass/components/HeaderSettingsDialog.vue';
  import TrialClass from '../TrialClass.vue'; // 试课单
  import ClasstHistoryOrderDispatchDialog from '../classList/ClasstHistoryOrderDispatchDialog.vue';

  export default {
    name: 'DispatchInProgressTrial',
    components: {
      HeaderSettingsDialog,
      statistic,
      TrialClass,
      ClasstHistoryOrderDispatchDialog
    },
    props: {
      gradeList: {
        type: Array,
        default: () => []
      }
    },
    computed: {
      ...mapGetters(['code'])
    },
    data() {
      return {
        screenWidth: window.screen.width, //屏幕宽度
        courseList: [], // 课程类型
        classlist: [{ label: '全部', value: '0' }],
        total: 0, // 总数
        searchNum: {
          //   studentName: '', // 姓名
          //   studentCode: '', // 学员编号
          deliverMerchant: '', // 交付中心
          deliverName: '', // 交付中心名称
          dispatchOrderStatus: '', // 派单状态
          classCodeOrName: '', // 班级名称或编号
          grade: '', //年级
          curriculumId: '',
          classCode: '', //班级

          pageNum: 1,
          pageSize: 10
        }, //搜索参数
        options02: regionData, // 区域列表
        addVisible: false, // 日期选择器
        timeslot: '', // 期望试课时间段
        dateslot: '',
        timeList: [], // 时间段
        coursrList: [], // 课程规划
        reviewWeekList: [], // 复习时间
        dialogAbutment: false, //上课信息对接表弹窗
        editFormList: [], // 上课信息对接表数据
        editFormId: null, // 上课信息对接表id
        editFormListIndex: '0', // 上课信息对接表数据索引

        deliverMerchantLoad: false, // 交付中心加载状态
        deliverOptions: [], // 交付中心列表
        deliverMerchantObj: { pageNum: 1, pageSize: 20, deliverName: '' }, // 交付中心筛选对象

        isSubmit: false, // 指派按钮loading
        assignDialog: false, // 指派弹框
        //指派的交付中心编号
        assignForm: {
          id: '',
          deliverMerchant: ''
        },

        historyOrderDispatchDialogVisible: false, // 历史派单记录弹框
        historyOrderDispatchClassId: '', // 历史派单记录班级id

        tableLoading: false, // 表格加载状态
        tableList: [], // 表格数据
        headerSettings: [
          { name: '班级名称', value: 'className' },
          { name: '班级编号', value: 'classCode' },
          { name: '期望上课时间', value: 'studyDate' },
          { name: '课程类型', value: 'curriculumId' },
          { name: '派单交付小组', value: 'deliverGroup' },
          { name: '派单状态', value: 'dispatchOrderStatus' },
          { name: '派单时间', value: 'dispatchOrderDate' },
          { name: '剩余接单时间', value: 'timeOut' },
          { name: '年级', value: 'grade' },
          { name: '学员', value: 'studentList' },
          { name: '班级人数', value: 'classStudentCount' },
          { name: '操作', value: 'operate' }
        ],
        HeaderSettingsStyle: false, // 列表属性弹框
        tableHeaderList: [], // 获取表头数据
        isTeamLeader: false, // 是否是交付中心组长
        isAdmin: false // 是否是管理员
      };
    },
    created() {
      //   this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      // this.isTeamLeader = ls.getItem('rolesVal') === 'DeliverTeamLeader';
      this.isTeamLeader = localStorage.getItem('role') === 'DeliverTeamLeader';
      this.isAdmin = checkPermission(['admin', 'JiaofuManager']);
      if (this.isAdmin) {
        this.headerSettings.push(
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          }
          //   {
          //     name: '门店账号',
          //     value: 'merchantCode'
          //   },
          //   {
          //     name: '门店名称',
          //     value: 'merchantName'
          //   },

          //   {
          //     name: '门店手机号',
          //     value: 'merchantPhone'
          //   }
        );
      } else {
        this.getGroupList();
      }
    },
    mounted() {
      this.getbvstatusList(); // 获取课程类型列表
      this.getHeaderlist(); // 获取表头数据
      this.initData(); // 初始化数据
    },
    methods: {
      checkPermission,
      // // 获取课程类型列表
      getbvstatusList() {
        getOneToMoreClassList({}).then((res) => {
          this.courseList = res.data;
          console.log('🚀 ~ bvstatusList ~ this.courseList:', this.courseList);
        });
      },
      // 重置搜索框
      rest() {
        this.$refs.query.resetFields();
        this.initData01();
      },
      // 搜索
      initData01() {
        (this.searchNum.pageNum = 1), (this.searchNum.pageSize = 10), this.initData();
      },
      async initData() {
        // 判断为null的时候赋空
        // if (!this.timeAll) {
        //   this.timeAll = [];
        // }
        this.tableLoading = true;
        let { data } = await orderApi.getClassList2(this.searchNum);
        this.tableLoading = false;
        this.total = Number(data.totalItems);
        this.tableList = data.data;

        this.tableList.forEach((item) => {
          if (item.studyDate.length > 14) {
            let start = item.studyDate.slice(0, item.studyDate.length - 11);
            let end = item.studyDate.slice(item.studyDate.length - 11);
            item.studyDate = `${start}\n${end}`;
          }
        });
      },
      // 获取宽度
      getWidth(val) {
        if (val == 'studyDate') {
          return '300px';
        } else if (val == 'studentList') {
          return '260px';
        } else if (val == 'operate') {
          return '300px';
        } else if (val == 'dispatchOrderDate') {
          return '180px';
        } else {
          return '150px';
        }
      },
      // 打开上课信息对接表
      openEdit(row) {
        this.fillTableNormalData(row);
        this.dialogAbutment = true;
      },
      closeEdit() {
        this.dialogAbutment = false;
        this.addVisible = false;
      },
      //上课信息对接表
      async fillTableNormalData(item) {
        // console.log(item)
        const that = this;
        if (item.studentList.length == 0) {
          this.$message({
            message: '该班级没有学员',
            type: 'warning'
          });
          return;
        }
        console.log('🚀 ~ fillTableNormalData ~ item.studentList:', item.studentList);

        this.editFormListIndex = '0'; // 重置
        this.editFormList = item.studentList;
        this.handleTabsClick();
      },
      // 切换试课单信息
      async handleTabsClick() {
        let that = this;
        this.editFormId = this.editFormList[this.editFormListIndex].studentDeliverId;
      },
      // tab切换下一个学生的试课单
      handleTabsNext() {
        let that = this;
        if (that.editFormList.length <= 1) return;
        // console.log(that.editFormListIndex, that.editFormListIndex * 1 + 1, that.editFormList.length, that.editFormListIndex * 1 + 1 >= that.editFormList.length);

        if (that.editFormListIndex * 1 + 1 >= that.editFormList.length) {
          that.editFormListIndex = '0';
        } else {
          that.editFormListIndex = that.editFormListIndex * 1 + 1 + '';
        }
        that.handleTabsClick();
      },
      // 指派提交
      async submitAssign() {
        // console.log(this.assignForm)
        let data = {};
        this.isSubmit = true;
        if (this.assignForm.deliverMerchant) {
          data = {
            classId: this.assignForm.classId,
            deliverMerchant: this.assignForm.deliverMerchant
          };
        } else {
          this.isSubmit = false;
          return this.isAdmin ? this.$message.warning('请选择交付中心') : this.$message.warning('请选择交付小组');
        }
        try {
          await (this.isAdmin ? orderApi.assignCenter(data) : orderApi.assignCenterTeam(data));
          this.$message.success('指派成功');
          this.isSubmit = false;
          this.initData();
          this.closeAssgin();
        } catch (error) {
          this.isSubmit = false;
        }
        // console.log(res)
      },
      // 关闭指派弹框
      closeAssgin() {
        this.assignForm = {
          classId: '',
          deliverMerchant: '' //指派的交付中心编号
        };
        this.assignDialog = false;
        this.clearSearchRecord();
      },
      // 关闭弹窗
      clearSearchRecord() {
        setTimeout(() => {
          if (this.isAdmin) this.deliverOptions = [];
          this.deliverMerchantObj.pageNum = 1;
          this.deliverMerchantObj.merchantName = '';
          //   this.getDeliverMerchant();
        }, 500);
      },
      async getGroupList() {
        let allData = await selectTeam(this.code);
        this.deliverOptions = allData.data;
        console.log('🚀 ~ getGroupList ~ this.deliverOptions:', this.deliverOptions);
      },
      // 获取交付中心
      async getDeliverMerchant() {
        if (this.isAdmin) {
          this.deliverMerchantLoad = true;
          try {
            let allData = await searchDeliverCenter(this.deliverMerchantObj);
            this.deliverMerchantObj.totalPage = allData.data.totalPage;
            this.deliverOptions = this.deliverOptions.concat(allData.data.data);
            this.deliverMerchantLoad = false;
          } catch (error) {
            this.deliverMerchantLoad = false;
          }
        }
      },
      // 搜索筛选交付中心/交付小组
      deliverMerchantFilterValue(value) {
        this.deliverOptions = [];
        this.deliverMerchantObj.pageNum = 1;
        this.deliverMerchantObj.merchantName = value;
        this.getDeliverMerchant();
      },
      // 触底加载交付中心/交付小组
      loadmoreDeliverMerchant() {
        if (!this.deliverMerchantLoad) {
          if (this.deliverMerchantObj.pageNum < this.deliverMerchantObj.totalPage) {
            this.deliverMerchantObj.pageNum++;
            this.getDeliverMerchant(); // 查询数据合并
          }
        }
      },
      // 修改交付中心/交付小组
      async changeDeliverMerchant(e) {
        if (e == '' && !this.deliverMerchantLoad) {
          this.deliverOptions = [];
          this.deliverMerchantObj.pageNum = 1;
          this.deliverMerchantObj.merchantName = '';
          await this.getDeliverMerchant();
        } else {
          // this.assignForm.merchantName = e.merchantName;
          // this.assignForm.merchantCode = e.merchantCode;
          console.log('🚀 ~ changeClass ~ this.assignForm.merchantCode:', this.assignForm);
        }
      },
      //指派方法
      async onAssign(row) {
        console.log('🚀 ~ onAssign ~ row:', row);
        this.assignForm.classId = row.id;
        this.assignDialog = true;
        if (this.isAdmin) this.deliverOptions = []; // 清空数组
      },
      // async onForceAssign(id, isForced) {
      //   this.assignForm.id = id;
      //   this.assignForm.isForced = isForced;
      //   const { data } = await belongDeliverAndAllDeliver(id);
      //   this.deliverOptions = data.deliverList;
      //   this.assignDialog = true;
      // },
      // 倒计时
      // 动态class
      statusClass(time, source) {
        let normTime = 10 * 60 * 1000;
        let newTime = new Date(time).getTime() - Date.now();
        if (newTime <= normTime) {
          return 'error';
        } else {
          return 'normal';
        }
      },
      // 时间转换方法
      getResidueTime(time) {
        if (time) {
          let time1 = new Date(time).getTime();
          // let time2 = Date.now()
          // let newTime = time1 - time2
          // if (newTime <= 0) {
          //   return 0
          // } else {
          //   return newTime
          // }
          return time1;
        } else {
          return 0;
        }
      },
      getTrueTime(time) {
        if (time) {
          let year = new Date(time).getFullYear();
          let newTime = new Date(time).getTime() - Date.now();
          if (year > 6000 || newTime < 0) {
            return false;
          } else {
            return true;
          }
        } else {
          return false;
        }
      },
      // 关闭历史派单弹框
      closeDialog() {
        this.historys = [];
        this.dialogHistory = false;
      },
      // 派单历史
      async getDetail(id) {
        this.historyOrderDispatchClassId = id;
        this.historyOrderDispatchDialogVisible = true;
      },
      // 拿到年级
      getGrade(val) {
        let grade = this.gradeList.find((item) => {
          return item.value == val;
        });

        return grade && grade.label;
      },
      //拿到课程类型
      getCourse(val) {
        let classType = this.courseList.find((item) => {
          return item.id == val;
        });

        return classType && classType.enName;
      },
      // 分页
      handleSizeChange(val) {
        this.searchNum.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.searchNum.pageNum = val;
        this.initData();
      },
      // 获取表头设置
      headerList() {
        if (this.tableHeaderList.length > 0) {
          // let arr = this.tableHeaderList.map((item) => item?.value);
          // console.log(arr.filter((item) => item));
          this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map((item) => item?.value); // 回显
        }
        this.HeaderSettingsStyle = true;
      },
      // 接收子组件数据动态控制表头弹窗
      HeaderSettingsLister(e) {
        this.HeaderSettingsStyle = e;
      },
      // 接收子组件选择的表头数据
      selectedItems(arr) {
        if (arr) {
          let data = {
            type: 'DispatchInProgressTrial',
            value: JSON.stringify(arr)
          };
          this.setHeaderSettings(data);
        }
      },

      // 设置表头
      async setHeaderSettings(data) {
        await setTableList(data).then((res) => {
          this.$message.success('操作成功');
          this.HeaderSettingsStyle = false;
          this.getHeaderlist();
        });
      },
      // 获取表头设置
      async getHeaderlist() {
        let data = {
          type: 'DispatchInProgressTrial'
        };
        console.log('🚀 ~ getHeaderlist ~ data:', data);
        await getTableTitleSet(data).then((res) => {
          if (res.data) {
            let tableHeaderList = JSON.parse(res.data.value);
            this.tableHeaderList = tableHeaderList.filter((item) => item);
          } else {
            this.tableHeaderList = this.headerSettings;
          }
        });
      },
      // 查看表头列表是否有该字段
      hasNoPermissionField(field) {
        let has = this.tableHeaderList.some((i) => {
          if (i.name == field || i.value == field) {
            return true;
          }
          return false;
        });
        return has;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }

  .btnFalses {
    background: #fff !important;
    color: #67c23a !important;
  }
  .timeClass {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    background-color: #fff;
    box-sizing: border-box;
    margin-left: 20px;
  }

  .week {
    border: 1px solid #dfe4ed;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 7px 20px;
  }
  .vocabulary {
    position: absolute;
    top: 6px;
    right: -1px;

    height: 24px;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    border-radius: 3px;
    padding: 0 4px;
    background-color: #46a6ff;
  }

  .thesaurus {
    position: relative;
  }
  .nomore {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  // .el-button--success {
  //   color: #ffffff;
  //   background-color: #6ed7c4;
  //   border-color: #6ed7c4;
  // }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
  }
</style>
