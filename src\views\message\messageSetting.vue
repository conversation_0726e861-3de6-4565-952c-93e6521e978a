<!--交付中心-消息配置-->
<template>
  <div>
    <!-- 消息配置 -->
    <div style="padding: 10px 0 20px 0">
      <el-form ref="form" :model="querydata" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6" :offset="0">
            <el-form-item label="消息类型：">
              <el-select v-model="querydata.messageType" placeholder="placeholder">
                <el-option label="全部" value=""></el-option>
                <el-option label="通知" value="1"></el-option>
                <el-option label="评价" value="2"></el-option>
                <el-option label="统计" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
      <!-- 申请按钮 -->
      <div class="btn-add">
        <el-button size="small" type="primary" @click="clickAdd">添加消息</el-button>
      </div>
      <!-- 暂无数据 -->
      <div class="nomore" v-if="tableData.length == 0">
        <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
        <div style="color: #999; margin-top: 20px">无数据</div>
      </div>
      <!-- 表格 -->
      <div class="" style="padding: 0 10px" v-if="tableData.length > 0">
        <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }" v-loading="tableLoading">
          <el-table-column v-for="(item, index) in tableHeaderList" :key="`${item.id}+${index}`" :prop="item.value" :label="item.name" header-align="center">
            <template v-slot="{ row }">
              <div v-if="item.value == 'operate'">
                <el-button type="primary" size="mini" @click="getDetail(row)">编辑</el-button>
              </div>
              <div v-else-if="item.value == 'recipientType'">
                <span>{{ row.messageType == 1 ? '通知类' : row.messageType == 2 ? '评价类' : '统计类' }}</span>
              </div>
              <div v-else-if="item.value == 'importance'">
                <span>{{ row.importance == 'NORMAL' ? '普通消息' : '重要消息' }}</span>
              </div>
              <div v-else-if="item.value == 'event'">
                <span>{{ filterEvent(row.event) }}</span>
              </div>
              <div v-else-if="item.value == 'recipientBy'">
                <span v-if="row.recipientRelation == 1">{{ filterRecept(row.recipientBy) }}</span>
                <span v-else>{{ row.recipientName }}</span>
              </div>
              <div v-else-if="item.value == 'message'">
                <span>{{ filterMsg(row.message) }}</span>
              </div>
              <!-- <div v-else-if="item.value == 'time'">
                <span>{{item.periodicTime}}</span>
              </div> -->
              <div v-else-if="item.value == 'messagePicture'">
                <el-image v-if="row.messagePicture" style="width: 40px; height: 40px" :src="row.messagePicture" :preview-src-list="[row.messagePicture]" />
              </div>
              <span v-else>{{ row[item.value] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-row type="flex" justify="center" align="middle" style="height: 60px">
          <!-- 3个变量：每页数量、页码数、总数  -->
          <!-- 2个事件：页码切换事件、每页数量切换事件-->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="querydata.pageNum"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="querydata.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </el-row>
      </div>

      <!-- 消息弹窗 -->
      <el-dialog :title="dialogForm.id ? '编辑消息' : '添加消息'" :visible.sync="dialogShow" width="30%" :before-close="handleClose" top="1vh" :close-on-click-modal="false">
        <el-form :model="dialogForm" label-width="130px" label-position="left" v-loading="editLoading">
          <el-form-item label="消息类型:">
            <el-select size="small" v-model="dialogForm.messageType" style="width: 100%" placeholder="" @change="changeMsgType">
              <el-option :value="1" label="通知类" />
              <el-option :value="2" label="评价类" />
              <el-option :value="3" label="统计类" />
            </el-select>
          </el-form-item>
          <el-form-item label="事件:" v-if="dialogForm.messageType != 3">
            <el-select size="small" v-model="dialogForm.event" style="width: 100%" placeholder="" @change="changeMsg">
              <el-option v-for="(item, index) in events" :key="index" :label="item.name" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="接收人:">
            <!-- <el-radio-group v-model="dialogForm.recipientType" size="small" @change="openRecipy">
              <el-radio-button label="1">个人</el-radio-button>
              <el-radio-button label="2">群组</el-radio-button>
            </el-radio-group> -->
            <el-button :type="dialogForm.recipientType == 1 ? 'primary' : ''" size="mini" style="width: 70px" @click="openRecipy(1)">个人</el-button>
            <el-button :type="dialogForm.recipientType == 2 ? 'primary' : ''" size="mini" style="width: 70px" @click="openRecipy(2)" v-show="dialogForm.messageType == 1">
              群组
            </el-button>
          </el-form-item>
          <el-form-item label="时效:" label-width="120px">
            <!-- <el-button :type="dialogForm.timeType==1?'primary':''" size="mini"
              @click="changeTimeType(1)">固定时间</el-button>
            <el-button :type="dialogForm.timeType==2?'primary':''" size="mini"
              @click="changeTimeType(2)">事件发生后多久</el-button>
            <el-button :type="dialogForm.timeType==2?'primary':''" size="mini" @click="changeTimeType(3)"
              v-if="dialogForm.messageType==3">周期</el-button> -->
            <el-radio-group v-model="dialogForm.timeType" size="small">
              <el-radio-button label="1" v-if="dialogForm.messageType != 2">固定时间</el-radio-button>
              <el-radio-button label="2">事件发生后多久</el-radio-button>
              <el-radio-button label="3" v-if="dialogForm.messageType == 3">周期</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="">
            <div v-if="dialogForm.timeType == 3" class="flex">
              <span style="margin-right: 10px">每隔</span>
              <el-input v-model.number="dialogForm.periodicTime" size="small" :min="0" :controls="false" style="width: 80%">
                <template slot="suffix">分钟</template>
              </el-input>
              <span style="margin-left: 10px">发送</span>
            </div>
            <div v-if="dialogForm.timeType == 2" class="flex">
              <el-input v-model.number="dialogForm.periodicTime" size="small">
                <template slot="suffix">分钟</template>
              </el-input>
            </div>
            <div class="flex" v-if="dialogForm.timeType == 1">
              <el-date-picker v-model="time1" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" size="small"></el-date-picker>
              <el-time-picker v-model="time2" value-format="HH:mm" format="HH:mm" placeholder="任意时间点" size="small"></el-time-picker>
            </div>
          </el-form-item>
          <el-form-item label="重要程度:" v-if="dialogForm.messageType != 2">
            <el-select v-model="dialogForm.importance" style="width: 100%" size="small" placeholder="">
              <el-option value="NORMAL" label="普通消息" />
              <el-option value="IMPORTANCE" label="重要消息" />
            </el-select>
          </el-form-item>
          <el-form-item label="消息内容:">
            <el-select v-model="dialogForm.message" style="width: 100%" size="small" placeholder="" v-if="dialogForm.messageType == 1">
              <el-option v-for="(item, index) in messageOptions" :key="index" :value="item.value" :label="item.name" />
            </el-select>
            <el-select v-model="dialogForm.message" style="width: 100%" size="small" placeholder="" v-if="dialogForm.messageType == 2" @change="changeMessage">
              <el-option value="SELECT" label="选择项" />
              <el-option value="RATING" label="评星" />
            </el-select>
            <el-select v-model="dialogForm.message" style="width: 100%" size="small" placeholder="" v-if="dialogForm.messageType == 3">
              <el-option value="TEACHER_SUMMARY" label="教练总结" />
              <el-option value="TEAM_SUMMARY" label="交付小组总结" />
              <el-option value="DELIVER_SUMMARY" label="交付中心总结" />
            </el-select>
          </el-form-item>
          <el-form-item label="消息标题">
            <el-input v-model="dialogForm.messageTitle" size="small"></el-input>
          </el-form-item>
          <el-form-item label="消息主图">
            <MyUpload :fileList="fileList" :limit="1" @handleSuccess="handleSuccess"></MyUpload>
          </el-form-item>
          <el-form-item label="文本" v-if="dialogForm.message == 'USER_DEFINED'">
            <el-input type="textarea" key="messageText" v-model="msg" autosize placeholder="" maxlength="200" show-word-limit></el-input>
          </el-form-item>
          <div v-if="dialogForm.message == 'SELECT'">
            <el-form-item v-for="(item, index) in evaluateOptionDtos" :label="index == 0 ? '选择项' : ''" :key="index" :prop="item.optionText">
              <div class="flex">
                <el-input size="small" v-model="item.optionText" :key="'input' + index"></el-input>
                <i class="el-icon-minus button" v-if="index != 0 && index != 1" :key="'del' + index" @click="delInput(index)"></i>
                <i class="el-icon-plus button" v-if="index != 0" :key="'add' + index" @click="addInput"></i>
              </div>
            </el-form-item>
          </div>
          <div v-if="dialogForm.message == 'RATING'">
            <el-form-item v-for="(item, index) in evaluateOptionDtos" :label="index == 0 ? '打星评价' : ''" :key="index" :prop="item.optionText">
              <div class="flex">
                <el-input size="small" v-model="item.optionText" :key="'input' + index"></el-input>
                <i class="el-icon-minus button" v-if="index != 0 && index != 1" :key="'del' + index" @click="delInput(index)"></i>
                <i class="el-icon-plus button" v-if="index != 0" :key="'add' + index" @click="addInput"></i>
              </div>
            </el-form-item>
            <el-form-item label="是否需要文本评价">
              <el-checkbox v-model="dialogForm.needText" :true-label="1" :false-label="0">是</el-checkbox>
              <el-checkbox v-model="dialogForm.needText" :true-label="0" :false-label="1">否</el-checkbox>
            </el-form-item>
          </div>
        </el-form>
        <div slot="footer">
          <el-button plain @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 定</el-button>
        </div>

        <el-dialog
          :title="dialogForm.recipientType == 1 ? '个人' : '群组'"
          :visible.sync="recipientDialog"
          width="30%"
          top="1vh"
          :before-close="recipientDialogClose"
          append-to-body
          :close-on-click-modal="false"
        >
          <div>
            <el-form ref="recipientDialogForm" :model="dialogForm" label-width="80px" v-loading="loadingTree">
              <el-form-item label="类型:">
                <el-checkbox v-model="dialogForm.recipientRelation" :true-label="1" :false-label="2">从属</el-checkbox>
                <!--<el-checkbox v-model="dialogForm.recipientRelation" :true-label="2" :false-label="1" @change="healthTypeChange">具体</el-checkbox> -->
                <!-- <el-checkbox-group v-model="dialogForm.recipientRelation" :max='1' @change="healthTypeChange">
                  <el-checkbox false-label='null' :true-label="1" name="status">从属</el-checkbox>
                  <el-checkbox false-label='null' :true-label="2" name="status">具体</el-checkbox>
                </el-checkbox-group> -->
              </el-form-item>
              <!-- 个人 -->
              <div v-show="dialogForm.recipientType == 1">
                <el-form-item label="请选择:" v-show="dialogForm.recipientRelation == 1">
                  <el-select v-model="recipientBy_person" placeholder="">
                    <el-option v-for="(item, index) in recipientOptions1" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-show="dialogForm.recipientRelation == 2">
                  <el-input v-model="filterP">
                    <el-button slot="append" icon="el-icon-search"></el-button>
                  </el-input>
                  <div class="tree" id="tree">
                    <el-tree
                      ref="tree"
                      :data="userTree"
                      node-key="qywechatUserId"
                      :props="props"
                      show-checkbox
                      check-strictly
                      default-expand-all
                      @check-change="handleCheckedChange"
                      :filter-node-method="filterNode1"
                    ></el-tree>
                  </div>
                </el-form-item>
              </div>
              <!-- 群主 -->
              <div v-show="dialogForm.recipientType == 2">
                <el-form-item label="请选择:" v-show="dialogForm.recipientRelation == 1">
                  <el-select v-model="recipientBy_group" placeholder="">
                    <el-option v-for="(item, index) in recipientOptions2" :key="index" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <div v-show="dialogForm.recipientRelation == 2">
                  <el-form-item label="群名">
                    <el-input size="small" v-model="groupData.title" placeholder=""></el-input>
                  </el-form-item>
                  <el-form-item label="群主">
                    <el-input size="small" v-model="groupData.leaderName" placeholder=""></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="feachGroup">查询</el-button>
                    <el-button plain type="primary" @click="resetGroup">重置</el-button>
                  </el-form-item>
                  <el-form-item>
                    <div class="table">
                      <el-table
                        :data="groupList"
                        size="mini"
                        border
                        ref="refsTable"
                        highlight-current-row
                        :row-key="getRowKey"
                        @row-click="onSelectOp"
                        @selection-change="contractResource"
                      >
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column v-for="(item, index) in groupHead" :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center" />
                      </el-table>
                      <!-- 分页器 -->
                      <el-row type="flex" justify="center" align="middle" style="height: 60px">
                        <!-- 3个变量：每页数量、页码数、总数  -->
                        <!-- 2个事件：页码切换事件、每页数量切换事件-->
                        <el-pagination
                          @size-change="handleSizeChange1"
                          @current-change="handleCurrentChange1"
                          :current-page="groupData.pageNum"
                          :page-sizes="[10, 20, 30, 40, 50]"
                          :page-size="groupData.pageSize"
                          layout="total, sizes, prev, pager, next, jumper"
                          :total="groupTotal"
                        ></el-pagination>
                      </el-row>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div slot="footer">
            <el-button @click="recipientDialogClose">取 消</el-button>
            <el-button type="primary" @click="onSure">确 定</el-button>
          </div>
        </el-dialog>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import MyUpload from '@/components/Upload/MyUpload.vue';
  import { getUserTree } from '@/api/peizhi/peizhi';
  import { getMsgList, saveMsgList, getGroupList } from '@/api/message';
  export default {
    name: 'messageSetting',
    components: {
      MyUpload
    },
    data() {
      return {
        isAdmin: false,
        loading: false,
        loadingTree: false,
        editLoading: false,
        querydata: {
          pageNum: 1,
          pageSize: 10, //页容量
          messageType: ''
        },
        groupData: {
          pageNum: 1,
          pageSize: 10, //页容量
          title: '',
          leaderName: ''
        },
        groupTotal: 0,
        groupList: [],
        selectedRow: null,
        groupHead: [
          {
            name: '群名',
            value: 'title'
          },
          {
            name: '群主',
            value: 'leaderName'
          }
        ],
        // 表格头
        tableHeaderList: [
          {
            name: '消息标题',
            value: 'messageTitle'
          },
          {
            name: '消息类型',
            value: 'recipientType'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '接收人',
            value: 'recipientBy'
          },

          {
            name: '事件',
            value: 'event'
          },
          {
            name: '消息主图',
            value: 'messagePicture'
          },
          {
            name: '消息内容',
            value: 'message'
          },
          {
            name: '文案',
            value: 'messageText'
          },
          {
            name: '重要程度',
            value: 'importance'
          },
          {
            name: '时效',
            value: 'time'
          }
        ],
        total: 0,
        //表格分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        tableData: [], //表格数据
        dialogShow: false,
        dialogForm: {
          messageType: 1,
          recipientRelation: 1,
          recipientBy: '',
          recipientType: ''
        },
        recipientBy_person: '',
        recipientBy_personId: '',
        recipientBy_personName: '',
        filterP: '',
        recipientBy_group: '',
        recipientBy_groupId: '',
        recipientBy_groupName: '',
        // qywechatUserId: '',
        // qywechatChatId: '',
        msg: '',
        time1: '',
        time2: '',
        fileList: [],
        copyData: '',
        copy: [],
        // 事件
        events: [
          { value: 'BUY_COURSE', name: '购买课程后' },
          { value: 'ADD_WECHAT', name: '家长添加企业微信为外部联系人后' },
          { value: 'CREAT_CHAT_DELIVER', name: '系统创建正式课群聊后' },
          { value: 'CREAT_CHAT_EXPERIENCE', name: '系统创建试课群聊后' },
          { value: 'CHANGE_TEACHER', name: '更换教练' },
          { value: 'TEACHER_REPORT_EXPERIENCE', name: '上完试课，教练填写完反馈' },
          { value: 'VOCABULARY_SUBMIT', name: '提交词汇量检测' },
          { value: 'ANTI_AMNESIA', name: '完成21天抗遗忘后' },
          { value: 'EXPERIENCE_REVIEW', name: '试课三次复习后' },
          { value: 'LAST_EXPERIENCE', name: '试课开始后15天后' },
          { value: 'FEEDBACK_SUMMARIZE', name: '上完正式课，教练填写完反馈' },
          { value: 'GRAMMAR_COURSE', name: '上完语法课' },
          { value: 'READ_COURSE', name: '上完阅读课' },
          { value: 'VOCABULARY_COURSE', name: '上完单词课' },
          { value: 'BUY_COURSE_EXPERIENCE', name: '购买试课课程后' }
          // { value: 'TEACHER_COPY_GROUP_NOTICE', name: "教练复制群公告后" },
        ],
        // 消息内容
        messageOptions: [{ value: 'USER_DEFINED', name: '自定义' }],
        // 个人从属
        recipientOptions1: [
          { label: '推荐人', value: 'REFERRER' },
          { label: '合伙人 ', value: 'PARTNER ' },
          { label: '教练', value: 'TEACHER' },
          { label: '交付小组组长', value: 'TEAM_LEADER' }
        ],
        // 群聊从属
        recipientOptions2: [
          { label: '班级群', value: 'CLASS_CHAT' },
          { label: '合伙人群 ', value: 'PARTER_CHAT ' },
          { label: '交付小组群', value: 'TEACHER_CHAT' }
        ],
        // 自定义input
        evaluateOptionDtos: [
          {
            evaluateType: 1,
            optionText: ''
          },
          {
            evaluateType: 1,
            optionText: ''
          }
        ],
        recipientDialog: false,
        userTree: [],
        props: {
          label: 'name',
          children: 'children'
        },
        tableLoading: false
      };
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      this.initData();
      // this.getUsers();
      this.initGroup();
    },
    watch: {
      filterPerson(val) {
        this.$refs.tree.filter(val);
      },
      'dialogForm.messageType'(val) {
        switch (val) {
          case 1:
            this.events = [
              { value: 'BUY_COURSE', name: '购买课程后' },
              { value: 'ADD_WECHAT', name: '家长添加企业微信为外部联系人后' },
              { value: 'CREAT_CHAT_DELIVER', name: '系统创建正式课群聊后' },
              { value: 'CREAT_CHAT_EXPERIENCE', name: '系统创建试课群聊后' },
              { value: 'CHANGE_TEACHER', name: '更换教练' },
              { value: 'TEACHER_REPORT_EXPERIENCE', name: '上完试课，教练填写完反馈' },
              { value: 'VOCABULARY_SUBMIT', name: '提交词汇量检测' },
              { value: 'ANTI_AMNESIA', name: '完成21天抗遗忘后' },
              { value: 'EXPERIENCE_REVIEW', name: '试课三次复习后' },
              { value: 'LAST_EXPERIENCE', name: '试课开始后15天后' },
              { value: 'FEEDBACK_SUMMARIZE', name: '上完正式课，教练填写完反馈' },
              { value: 'GRAMMAR_COURSE', name: '上完语法课' },
              { value: 'READ_COURSE', name: '上完阅读课' },
              { value: 'VOCABULARY_COURSE', name: '上完单词课' },
              { value: 'BUY_COURSE_EXPERIENCE', name: '购买试课课程后' }
              // { value: 'TEACHER_COPY_GROUP_NOTICE', name: "教练复制群公告后" },
            ];
            this.recipientOptions1 = [
              { label: '推荐人', value: 'REFERRER' },
              { label: '合伙人 ', value: 'PARTNER ' },
              { label: '教练', value: 'TEACHER' },
              { label: '交付小组组长', value: 'TEAM_LEADER' }
            ];
            break;
          case 2:
            this.events = [
              { value: 'TEACHER_REPORT_EXPERIENCE', name: '上完试课，教练填写完反馈' },
              { value: 'FEEDBACK_SUMMARIZE', name: '上完正式课，教练填写完反馈' },
              { value: 'TEACHER_COPY_GROUP_NOTICE', name: '教练复制群公告后' }
            ];
            this.recipientOptions1 = [
              { label: '推荐人', value: 'REFERRER' },
              { label: '合伙人 ', value: 'PARTNER ' },
              { label: '教练', value: 'TEACHER' },
              { label: '交付小组组长', value: 'TEAM_LEADER' }
            ];
            break;
          case 3:
            this.events = [
              { value: 'TEACHER_SUMMARY', name: '教练总结' },
              { value: 'TEAM_SUMMARY', name: '交付小组总结  ' },
              { value: 'DELIVER_SUMMARY', name: '交付中心总结' }
            ];
            this.recipientOptions1 = [
              { label: '交付中心管理员', value: 'DELIVER_ADMIN' },
              { label: '交付中心', value: 'DELIVER_CENTRE' },
              { label: '交付小组组长', value: 'TEAM_LEADER' }
            ];
            break;
        }
      },
      'dialogForm.event'(val) {
        if (val) {
          let arr = [];
          switch (val) {
            case 'BUY_COURSE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'NOTIFY_REF', name: '家长报课后，推送给推荐人' }];
              break;
            case 'ADD_WECHAT':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'FILL_EXPERIENCE', name: '推送消息给推荐人，让推荐人填写试课单' }];
              break;
            case 'CREAT_CHAT_DELIVER':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [
                { value: 'GROUP_NOTICE_DELIVER', name: '正式课群公告' },
                { value: 'TEACHER_INTRODUCE_DELIVER', name: '正式课教练自我介绍' }
              ];
              break;
            case 'CREAT_CHAT_EXPERIENCE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [
                { value: 'GROUP_NOTICE_EXPERIENCE', name: '试课群公告' },
                { value: 'TEACHER_INTRODUCE_EXPERIENCE', name: '试课教练自我介绍' }
              ];
              break;
            case 'CHANGE_TEACHER':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'NEW_TEACHER_INTRODUCE', name: '新教练自我介绍' }];
              break;
            case 'TEACHER_REPORT_EXPERIENCE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [
                { value: 'EXPERIENCE_REPORT', name: '试课报告' },
                { value: 'EXPERIENCE_SUMMARIZE', name: '试课反馈总结' },
                { value: 'THRICE_RESULT', name: '三次效果见证' },
                { value: 'RATING_EXPERIENCE', name: '试课结束后评星' }
              ];
              break;
            case 'VOCABULARY_SUBMIT':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'VOCABULARY_REPORT', name: '词汇量检测报告' }];
              break;

            case 'ANTI_AMNESIA':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [
                { value: 'REVIEW_REPORT', name: '复习报告' },
                { value: 'REVIEW_SUMMARIZE', name: '复习反馈总结' },
                { value: 'REVIEW_RECORD', name: '往期复习记录' }
              ];
              break;
            case 'EXPERIENCE_REVIEW':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'REVIEW_FINISH', name: '复习结束' }];
              break;
            case 'LAST_EXPERIENCE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'DISSOLVE_GROUP', name: '解散群聊' }];
              break;
            case 'FEEDBACK_SUMMARIZE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [
                { value: 'DELIVER_REPORT', name: '正式课报告' },
                { value: 'RATING_DELIVER', name: '正式课结束后评星' }
              ];
              break;
            case 'VOCABULARY_COURSE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'STUDY_PRINT_WORD', name: '学习打印，单词模块' }];
              break;
            case 'READ_COURSE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'STUDY_PRINT_READING', name: '学习打印，阅读模块' }];
              break;
            case 'GRAMMAR_COURSE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'STUDY_PRINT_GRAMMAR', name: '学习打印，语法模块' }];
              break;
            case 'BUY_COURSE_EXPERIENCE':
              this.messageOptions = [{ value: 'USER_DEFINED', name: '自定义' }];
              arr = [{ value: 'FILL_DELIVER', name: '家长报课后，推送给推荐人' }];
              break;
          }
          this.messageOptions = arr.concat(this.messageOptions);
        }
      }
    },
    methods: {
      searchData() {
        this.querydata.pageNum = 1;
        this.initData();
      },
      rest() {
        this.querydata.messageType = '';
        this.initData();
      },
      feachGroup() {
        this.groupData.pageNum = 1;
        this.initGroup();
      },
      resetGroup() {
        this.groupData.leaderName = '';
        this.groupData.title = '';
        this.initGroup();
      },
      /** 保存选中的数据编号 */
      getRowKey(row) {
        return row.id;
      },

      /** 复选框调整成单选框 */
      contractResource(rows) {
        if (rows.length > 1) {
          const newRows = rows.filter((it, index) => {
            if (index == rows.length - 1) {
              this.$refs.refsTable.toggleRowSelection(it, true);
              return true;
            } else {
              this.$refs.refsTable.toggleRowSelection(it, false);
              return false;
            }
          });
          this.selectedRow = newRows;
        } else {
          this.selectedRow = rows;
        }
      },

      /** 复选框调整成单选框 */
      onSelectOp(row) {
        this.$refs.refsTable.clearSelection();
        this.$refs.refsTable.toggleRowSelection(row, true);
        this.resourceSelection = [];
        this.resourceSelection.push(row);
        console.log(row);
        this.selectedRow = row;
      },
      async initGroup() {
        let { data } = await getGroupList(this.groupData);
        // console.log(data)
        this.groupList = data.data;
        this.groupTotal = Number(data.totalItems);
      },

      delInput(index) {
        //  var index = this.evaluateOptionDtos.indexOf(item)
        //   if (index !== -1) {
        //     this.dynamicValidateForm.domains.splice(index, 1)
        //   }
        this.evaluateOptionDtos.splice(index, 1);
      },
      addInput() {
        if (this.evaluateOptionDtos.length == 5) return;

        this.evaluateOptionDtos.push({
          evaluateType: 1,
          optionText: ''
          // key: Date.now()
        });
      },

      // 抽屉关闭
      handleClose() {
        this.dialogForm = {
          messageType: 1,
          recipientRelation: 1,
          recipientBy: '',
          recipientType: 1
        };
        this.time1 = '';
        this.time2 = '';
        this.fileList = [];
        this.recipientDialogClose();
        this.dialogShow = false;
        this.loading = false;
      },
      changeMessage(e) {
        // console.log(e, copyData);
        let arr = [
          {
            evaluateType: 1,
            optionText: ''
          },
          {
            evaluateType: 1,
            optionText: ''
          }
        ];
        if (this.dialogForm.messageType == 2) {
          if (e == this.copyData) {
            this.evaluateOptionDtos = this.copy;
          } else {
            this.evaluateOptionDtos = arr;
          }
        }
      },
      changeMsgType(e) {
        console.log(e);
        if (e) {
          if (e == 2) {
            this.$set(this.dialogForm, 'timeType', '2');
          } else {
            this.$set(this.dialogForm, 'timeType', '');
          }
          this.$set(this.dialogForm, 'event', '');
          this.$set(this.dialogForm, 'message', '');
        }
      },
      changeMsg(e) {
        console.log(e);
        if (e) {
          this.$set(this.dialogForm, 'message', '');
        }
      },
      filterNode1(value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      onSure() {
        if (this.dialogForm.recipientType == 1) {
          this.dialogForm.recipientBy = this.dialogForm.recipientRelation == 1 ? this.recipientBy_person : this.recipientBy_personId;
          this.dialogForm.recipientName = this.dialogForm.recipientRelation == 1 ? this.recipientBy_person : this.recipientBy_personName;
        } else {
          if (this.dialogForm.recipientRelation == 2) {
            if (!this.selectedRow) return this.$message.warning('请选择具体群');
            this.recipientBy_groupId = this.selectedRow[0].chatId;
            this.recipientBy_groupName = this.selectedRow[0].title;
          }
          this.dialogForm.recipientBy = this.dialogForm.recipientRelation == 1 ? this.recipientBy_group : this.recipientBy_groupId;
          this.dialogForm.recipientName = this.dialogForm.recipientRelation == 1 ? this.recipientBy_group : this.recipientBy_groupName;
        }
        if (this.dialogForm.recipientRelation == 1) {
          let msg = this.dialogForm.recipientType == 1 ? '请选择从属人' : '请选择从属群';
          if (!this.dialogForm.recipientBy) return this.$message.warning(msg);
        } else {
          if (!this.dialogForm.recipientBy) return this.$message.warning('请选择具体人');
        }
        this.recipientDialog = false;
      },
      // changeTimeType(type) {
      //   this.dialogForm.timeType = Number(type)
      // },
      openRecipy(type) {
        // console.log(type)
        this.dialogForm.recipientType = Number(type);
        this.recipientDialog = true;
        this.loadingTree = true;
        setTimeout(() => {
          this.loadingTree = false;
        }, 1000);
        // this.dialogForm.recipientRelation = Number(type)
      },
      healthTypeChange(e) {
        if (e == 2) {
          this.loadingTree = true;

          //  首次执行
          if (this.dialogForm.recipientType == 2) {
            this.initGroup();
          }
          setTimeout(() => {
            this.loadingTree = false;
          }, 1000);
        }
      },
      handleCheckedChange(data, checked, indeterminate) {
        if (checked) {
          this.$refs.tree.setCheckedKeys([data.qywechatUserId]);
          this.recipientBy_personId = data.qywechatUserId;
          this.recipientBy_personName = data.name;
          // console.log(data.name)
          // this.qywechatUserId = data.qywechatUserId
        }
      },
      async getUsers() {
        const res = await getUserTree();
        // console.log(res, '================')
        // this.userTree = res.data
        this.userTree = Object.freeze(res.data);
      },
      recipientDialogClose() {
        // this.dialogForm.recipientType = 1
        if (this.dialogForm.id) {
          this.recipientDialog = false;
        } else {
          this.dialogForm.recipientType = '';
          this.recipientBy_person = '';
          this.recipientBy_personId = '';
          this.recipientBy_personName = '';
          this.recipientBy_group = '';
          this.recipientBy_groupName = '';
          this.recipientBy_groupId = '';
          this.dialogForm.recipientRelation = 1;
          this.recipientDialog = false;
        }
      },
      handleSuccess(e) {
        console.log(e);
        this.dialogForm.messagePicture = 'https://document.dxznjy.com/' + e;
      },
      //添加操作
      clickAdd() {
        this.dialogShow = true;
      },
      getDetail(row) {
        let that = this;
        that.editLoading = true;
        that.dialogShow = true;
        // const { data } = await getRobotDetail(id)
        console.log(row);
        that.copyData = JSON.parse(JSON.stringify(row.message));
        that.copy = JSON.parse(JSON.stringify(row.evaluateOptionDtos));
        if (row.timeType == 1) {
          let arr = row.time.split(' ');
          that.time1 = arr[0];
          that.time2 = arr[1];
        }
        if (row.messagePicture) {
          let url = row.messagePicture.replace('http://dx-applet-mall.oss-cn-shanghai.aliyuncs.com', 'https://document.dxznjy.com');
          let obj = {
            status: 'success',
            uid: 1715328473600,
            url: url
          };
          that.fileList = [obj];
        }

        if (row.evaluateOptionDtos && row.evaluateOptionDtos.length > 0) {
          this.evaluateOptionDtos = row.evaluateOptionDtos;
        }
        // this.$set(this.dialogForm, 'message', '')
        if (row.recipientType == 1) {
          if (row.recipientRelation == 1) {
            that.recipientBy_person = row.recipientBy;
          } else {
            that.recipientBy_personId = row.recipientBy;
            that.recipientBy_personName = row.recipientName;
            that.filterP = row.recipientName;
            console.log(that);
            that.$nextTick(() => {
              setTimeout(() => {
                that.$refs.tree.setCheckedKeys([that.recipientBy_personId], true);
              }, 1000);
            });
          }
        } else {
          if (row.recipientRelation == 1) {
            that.recipientBy_group = row.recipientBy;
          } else {
            that.recipientBy_groupId = row.recipientBy;
            that.recipientBy_groupName = row.recipientName;
          }
        }
        that.dialogForm = JSON.parse(JSON.stringify(row));
        delete that.dialogForm.createTime;
        setTimeout(() => {
          that.editLoading = false;
        }, 1000);
      },
      // 初始化列表
      async initData() {
        let that = this;
        that.tableLoading = true;
        let { data } = await getMsgList(this.querydata);
        this.tableData = data.data;
        this.tableData.forEach((item) => {
          item.time = item.fixedTime ? item.fixedTime : item.periodicTime + '(分钟)';
        });
        this.total = Number(data.totalItems);
        that.tableLoading = false;
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
      },
      // 添加消息
      onSubmit() {
        if (!this.dialogForm.event && this.dialogForm.messageType != 3) return this.$message.warning('请选择事件');
        if (!this.dialogForm.recipientType) return this.$message.warning('请选择接收人');
        if (this.dialogForm.timeType == 1) {
          if (!this.time1) return this.$message.warning('请选择日期');
          if (!this.time2) return this.$message.warning('请选择时间');
        } else {
          if (!(Number(this.dialogForm.periodicTime) >= 0)) return this.$message.warning('请输入正确的时间');
        }
        // return console.log(this.dialogForm);
        if (this.dialogForm.messageType == 2) {
          if (this.dialogForm.message == 'SELECT') {
            this.evaluateOptionDtos.forEach((i) => (i.evaluateType = 1));
          } else {
            this.evaluateOptionDtos.forEach((i) => (i.evaluateType = 2));
          }
          this.dialogForm.evaluateOptionDtos = this.evaluateOptionDtos;
        }
        if (this.dialogForm.timeType == 1) {
          this.dialogForm.fixedTime = this.time1 + ' ' + this.time2;
        }
        this.dialogForm.messageText = this.msg ? this.msg : '';
        saveMsgList(this.dialogForm).then((res) => {
          console.log(res);
          if (res.success) {
            this.$message.success('操作成功');
            this.initData();
            this.handleClose();
          }
        });
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      filterMsg(val) {
        let arr = [
          { value: 'USER_DEFINED', name: '自定义' },
          { value: 'NOTIFY_REF', name: '家长报课后，推送给推荐人' },
          { value: 'FILL_EXPERIENCE', name: '推送消息给推荐人，让推荐人填写试课单' },
          { value: 'GROUP_NOTICE_DELIVER', name: '正式课群公告' },
          { value: 'TEACHER_INTRODUCE_DELIVER', name: '正式课教练自我介绍' },
          { value: 'GROUP_NOTICE_EXPERIENCE', name: '试课群公告' },
          { value: 'TEACHER_INTRODUCE_EXPERIENCE', name: '试课教练自我介绍' },
          { value: 'NEW_TEACHER_INTRODUCE', name: '新教练自我介绍' },
          { value: 'EXPERIENCE_REPORT', name: '试课报告' },
          { value: 'VOCABULARY_REPORT', name: '词汇量检测报告' },
          { value: 'EXPERIENCE_SUMMARIZE', name: '试课反馈总结' },
          { value: 'THRICE_RESULT', name: '三次效果见证' },
          { value: 'RATING_EXPERIENCE', name: '试课结束后评星' },
          { value: 'REVIEW_REPORT', name: '复习报告' },
          { value: 'REVIEW_SUMMARIZE', name: '复习反馈总结' },
          { value: 'REVIEW_RECORD', name: '往期复习记录' },
          { value: 'REVIEW_FINISH', name: '复习结束' },
          { value: 'DISSOLVE_GROUP', name: '解散群聊' },
          { value: 'DELIVER_REPORT', name: '正式课报告' },
          { value: 'RATING_DELIVER', name: '正式课结束后评星' },
          { value: 'STUDY_PRINT_WORD', name: '学习打印，单词模块' },
          { value: 'STUDY_PRINT_READING', name: '学习打印，阅读模块' },
          { value: 'STUDY_PRINT_GRAMMAR', name: '学习打印，语法模块' },
          { value: 'GROUP_NOTICE', name: '群公告' },
          { value: 'TEACHER_INTRODUCE', name: '教练自我介绍' },
          { value: 'REVIEW_FEEDBACK', name: '复习反馈总结' },
          { value: 'CHANGE_TEACHER', name: '更换教练' },
          { value: 'STUDY_PRINT', name: '学习打印' },
          { value: 'TEXT_REVIEW', name: '文字版反馈复习模板' },
          { value: 'SYNTAX_REPORT', name: '语法报告' },
          { value: 'READ_REPORT', name: '阅读报告' },
          { value: 'SELECT', name: '选项' },
          { value: 'RATING', name: '评星' },
          { value: 'SUMMARY_NOTICE', name: '总结通知' },
          { value: 'NEW_STUDY_ORDER', name: '来新单啦' },
          { value: 'SEND_DELAY', name: '消息延迟未发送' },
          { value: 'TEACHER_COPY_GROUP_NOTICE', name: '教练复制群公告后' },
          { value: 'USER_DEFINED', name: '自定义' },
          { value: 'DELAY_MESSAGE', name: '延迟发送消息' },
          { value: 'TEACHER_SUMMARY', name: '教练总结' },
          { value: 'TEAM_SUMMARY', name: '交付小组总结  ' },
          { value: 'DELIVER_SUMMARY', name: '交付中心总结' },
          { value: 'FILL_DELIVER', name: '家长报课后，推送给推荐人' }
        ];
        if (val) {
          let fil = arr.filter((i) => i.value == val);
          // return fil[0].name
          if (fil.length > 0) {
            return fil[0].name;
          } else {
            return '';
          }
        } else {
          return '';
        }
      },
      filterRecept(val) {
        let arr = [
          { label: '推荐人', value: 'REFERRER' },
          { label: '合伙人 ', value: 'PARTNER ' },
          { label: '教练', value: 'TEACHER' },
          { label: '交付小组组长', value: 'TEAM_LEADER' },
          { label: '班级群', value: 'CLASS_CHAT' },
          { label: '合伙人群 ', value: 'PARTER_CHAT ' },
          { label: '班级群', value: 'TEACHER_CHAT' },
          { label: '交付中心管理员', value: 'DELIVER_ADMIN' },
          { label: '交付中心', value: 'DELIVER_CENTRE' }
        ];
        if (val) {
          let fil = arr.filter((i) => i.value == val);
          if (fil.length > 0) {
            return fil[0].label;
          } else {
            return '';
          }
        } else {
          return '';
        }
      },
      filterEvent(val) {
        let arr = [
          { value: 'BUY_COURSE', name: '购买课程后' },
          { value: 'ADD_WECHAT', name: '家长添加企业微信为外部联系人后' },
          { value: 'CREAT_CHAT_DELIVER', name: '系统创建正式课群聊后' },
          { value: 'CREAT_CHAT_EXPERIENCE', name: '系统创建试课群聊后' },
          { value: 'CHANGE_TEACHER', name: '更换教练' },
          { value: 'TEACHER_REPORT_EXPERIENCE', name: '上完试课，教练填写完反馈' },
          { value: 'VOCABULARY_SUBMIT', name: '提交词汇量检测' },
          { value: 'ANTI_AMNESIA', name: '完成21天抗遗忘后' },
          { value: 'EXPERIENCE_REVIEW', name: '试课三次复习后' },
          { value: 'LAST_EXPERIENCE', name: '试课开始后15天后' },
          { value: 'FEEDBACK_SUMMARIZE', name: '上完正式课，教练填写完反馈' },
          { value: 'TEACHER_COPY_GROUP_NOTICE', name: '教练复制群公告后' },
          { value: 'EXPERIENCE_FINISH', name: '上完试课' },
          { value: 'DELIVER_FINISH', name: '上完正式课' },
          { value: 'TEACHER_SUMMARY', name: '教练总结' },
          { value: 'TEAM_SUMMARY', name: '交付小组总结  ' },
          { value: 'DELIVER_SUMMARY', name: '交付中心总结' },
          { value: 'GRAMMAR_COURSE', name: '上完语法课' },
          { value: 'READ_COURSE', name: '上完阅读课' },
          { value: 'VOCABULARY_COURSE', name: '上完单词课' },
          { value: 'BUY_COURSE_EXPERIENCE', name: '购买试课课程后' }
        ];
        if (val) {
          let fil = arr.filter((i) => i.value == val);
          if (fil.length > 0) {
            return fil[0].name;
          } else {
            return '';
          }
        } else {
          return '';
        }
      },

      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      // 分页
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      // 分页
      handleSizeChange1(val) {
        this.groupData.pageSize = val;
        this.initGroup();
      },
      // 分页
      handleCurrentChange1(val) {
        this.groupData.pageNum = val;
        this.initGroup();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .flex {
    display: flex;
    align-items: center;
  }
  .btn-add {
    // margin-top: 20px;
    padding: 0 20px 20px 20px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }
  .tree {
    height: 300px;
    overflow-y: auto;
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    cursor: unset !important;
  }

  // 自定义表格边框
  ::v-deep #out-table .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:first-child {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(10) {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep .el-dialog__title {
    font-size: 14px;
    font-weight: 600;
  }
  ::v-deep .el-dialog__headerbtn {
    background-color: #ccc;
    border-radius: 50%;
  }
  ::v-deep.el-radio-button .el-radio-button__inner {
    border-radius: 4px !important;
    border: 1px solid #dcdfe6 !important;
    margin: 0 10px;
  }
  .button {
    cursor: pointer;
    font-size: 18px;
  }
  .flex-end {
    display: flex;
    justify-content: flex-end;
  }
  /**找到表头那一行，然后把里面的复选框隐藏掉**/
  ::v-deep .el-table__header-wrapper .el-table__header .el-checkbox {
    display: none;
  }
</style>
