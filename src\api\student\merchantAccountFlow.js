/**
 * 财务管理相关接口
 */
import { param } from '@/utils';
import request from '@/utils/request';

export default {
  // 更改下载状态
  excelDownload(id) {
    return request({
      url: '/znyy/excelDownload/excelDownload/' + id,
      method: 'GET'
    });
  },
  // 商户资金流水分类分页查询
  merchantAccountFlow(pageNum, pageSize, data) {
    return request({
      url: '/znyy/excelDownload/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  }, // 商户资金流水分页查询
  merchantAccountFlowList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/merchant/code/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  //城市服务商下的资金流水
  merchantAccountFlowListDealer(pageNum, pageSize, data) {
    return request({
      url: '/znyy/areas/school/agent/flow/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  //课程流水明细
  schoolCourseList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/school/course/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  //学员课程明细
  studentCourseFlowList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/studentSalesRecord/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  //学员课程资金
  detailsOfStudentCourseFlowChanges(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/to/funds/runningWater/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  //学校课程流水导出
  simpleExecl(listQuery) {
    return request({
      url: '/znyy/finance/school/export',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //课程包变动明细流水导出
  simplePackageExecl(listQuery) {
    return request({
      url: '/znyy/finance/coursePackage/export',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //学员课程流水导出
  studentSimpleExecl(listQuery) {
    return request({
      url: '/znyy/finance/to/export/runningWater',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //学员课程流水导出
  studentSimpleExeca(listQuery) {
    return request({
      url: '/znyy/finance/to/export/runningWater',
      method: 'GET',
      params: listQuery
    });
  },
  //商户流水分类存入阿里云
  fundsStreamDepositAliyun(listQuery) {
    return request({
      url: '/znyy/finance/merchant/to/exportClassification',
      method: 'GET',
      // responseType: 'blob',
      params: listQuery
    });
  },
  //商户流水导出
  simpleMerchantExecl(listQuery) {
    return request({
      url: '/znyy/finance/merchant/to/export',
      method: 'GET',
      responseType: 'blob',
      params: listQuery
    });
  },
  //商户流水导出
  simpleMerchantExeca(listQuery) {
    return request({
      url: '/znyy/finance/merchant/to/export',
      method: 'GET',
      params: listQuery
    });
  },

  //获取充值账户
  getSelectResult(roleTag) {
    return request({
      url: '/znyy/finance/byRoleTag/' + roleTag,
      method: 'GET'
    });
  },
  //分公司获取充值账户
  getBvMerchantList(roleTag) {
    return request({
      url: '/znyy/finance/company/roleTag/' + roleTag,
      method: 'GET'
    });
  },
  //根据角色获取
  accordingToTheRole(data) {
    return request({
      url: '/znyy/finance/roleTag',
      method: 'GET',
      params: data
    });
  },
  //获取角色
  getRoleTagList() {
    return request({
      url: '/znyy/finance/get/roletag',
      method: 'GET'
    });
  },

  // /merchant/merchantCapitalInquiry/list/{pageNum}/{pageSize}
  merchantCapitalInquiry(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/merchant/merchantCapitalInquiry/list/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },
  merchantChange(pageNum, pageSize, data) {
    return request({
      url: '/znyy/finance/school/classFlowChang/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    });
  },

  //二级密码验证
  checkSecondPwd(secondPassWord) {
    return request({
      url: '/znyy/finance/checkSecondPwd/' + secondPassWord,
      method: 'GET'
    });
  },
  // // 体验店级别编辑
  // updateDealerRank(data) {
  //   return request({
  //     url: '/znyy/dealer/rank',
  //     method: 'PUT',
  //     data
  //   })
  // },
  // // 体验店级别查询
  // queryActive(id) {
  //   return request({
  //     url: '/znyy/dealer/rank/getDealerRank/' + id,
  //     method: 'GET'
  //   })
  // },
  // //删除体验店级别
  // deleteDealerRankList(id){
  //  return request({
  //      url:'/znyy/dealer/rank/'+id,
  //      method: 'DELETE'
  //  })
  // },
  //在线充值
  onlineRecharge(data) {
    return request({
      url: '/znyy/finance',
      method: 'POST',
      data
    });
  },
  //查看账户余额
  checkOver(merchantCode) {
    return request({
      url: '/znyy/finance/check/over/' + merchantCode,
      method: 'GET'
    });
  },
  //扣除学时和余额
  onlineDeduction(data) {
    return request({
      url: '/znyy/finance/over',
      method: 'PUT',
      data
    });
  },
  //获取账户详细信息
  getDetails(merchantCode) {
    return request({
      url: '/znyy/finance/bv/detail/' + merchantCode,
      method: 'GET'
    });
  },
  roleTagCompany() {
    return request({
      url: '/znyy/branch/office/company/roleTag',
      method: 'GET'
    });
  },
  getCurrentTime() {
    var _this = this;
    let yy = new Date().getFullYear();
    let mm = new Date().getMonth() + 1;
    let dd = new Date().getDate();
    let hh = new Date().getHours();
    let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
    let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
    _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
    return _this.gettime;
  }
};
