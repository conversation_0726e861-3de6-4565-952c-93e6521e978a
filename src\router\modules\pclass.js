import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const pclassRouter = {
  path: '/pclass/index',
  component: Layout,
  name: 'formalstudentclass',
  meta: {
    perm: 'm:use:pclass',
    title: '正式学员管理',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: '',
      component: _import('pclass/index'),
      name: 'paike',
      meta: {
        perm: 'm:pclass:index',
        title: '学员列表',
        icon: 'studentList',
        noCache: false,
        roles: ['admin']
      }
    },
    {
      path: '/pclass/classInformation',
      component: _import('pclass/classInformation'),
      name: 'classInformation',
      meta: {
        perm: 'm:pclass:classInformation',
        title: '待完善上课信息表',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/pclass/reviewSchedule',
      component: _import('pclass/reviewSchedule'),
      name: 'reviewSchedule',
      meta: {
        perm: 'm:pclass:reviewSchedule',
        title: '待完善复习时间表',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/classCard',
      component: _import('classCard/index'),
      name: 'classCard',
      meta: {
        perm: 'm:classCard:index',
        title: '学习课程表',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/pclass/reviewList',
      component: _import('pclass/reviewList'),
      name: 'reviewList',
      meta: {
        perm: 'm:pclass:reviewList',
        title: '复习课程表',
        icon: 'el-icon-s-data',
        noCache: false
      }
    },
    {
      path: '/pclass/Coursefeedback',
      component: _import('pclass/Coursefeedback'),
      name: 'Coursefeedback',
      meta: {
        perm: 'm:pclass:Coursefeedback',
        title: '课程反馈处理',
        icon: 'el-icon-s-claim',
        noCache: false
      }
    },
    {
      path: '/checkOrder',
      component: _import('checkOrder/index'),
      name: 'checkOrder',
      meta: {
        perm: 'm:checkOrder:index',
        title: '教练费退款审核',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/refundReview',
      component: _import('refundReview/index'),
      name: 'refundReview',
      meta: {
        perm: 'm:refundReview:index',
        title: '甄选退款审核',
        icon: 'card_dzy',
        noCache: false
      }
    },
    {
      path: '/students/areasStudentCourseRecord',
      component: _import('students/areasStudentCourseRecord'),
      name: 'areasStudentCourseRecord',
      hidden: true,
      meta: {
        perm: 'm:students:areasStudentCourseRecord',
        title: '学员课程记录',
        noCache: false
      }
    },
    {
      path: '/students/areasStudentCourseFlow',
      component: _import('students/areasStudentCourseFlow'),
      name: 'areasStudentCourseFlow',
      hidden: true,
      meta: {
        perm: 'm:students:areasStudentCourseFlow',
        title: '学员销课记录',
        noCache: false
      }
    },
    {
      path: '/students/areaStudentWordReviewPrint',
      component: _import('students/areaStudentWordReviewPrint'),
      name: 'areaStudentWordReviewPrint',
      hidden: true,
      meta: {
        perm: 'm:students:areaStudentWordReviewPrint',
        title: '21天抗遗忘复习计划',
        noCache: false
      }
    },
    {
      path: '/students/areasStudentTestResultList',
      component: _import('students/areasStudentTestResultList'),
      name: 'areasStudentTestResultList',
      hidden: true,
      meta: {
        perm: 'm:students:areasStudentTestResultList',
        title: '学员词汇测试',
        noCache: false
      }
    },
    {
      path: '/students/areasStudentWordPrintList',
      component: _import('students/areasStudentWordPrintList'),
      name: 'areasStudentWordPrintList',
      hidden: true,
      meta: {
        perm: 'm:students:areasStudentWordPrintList',
        title: '学员测验打印',
        noCache: false
      }
    },
    {
      path: '/students/components/studentWordsTest',
      component: _import('students/components/studentWordsTest'),
      hidden: true,
      name: '学员词汇测试',
      meta: {
        perm: 'm:students:studentWordsTest',
        title: '学员词汇测试',
        noCache: false
      }
    },
    {
      path: '/students/components/studentWordReviewPrint',
      component: _import('students/components/studentWordReviewPrint'),
      hidden: true,
      name: '21天抗遗忘打印',
      meta: {
        perm: 'm:students:studentWordReviewPrint',
        title: '21天抗遗忘打印',
        noCache: false
      }
    },
    {
      path: '/students/components/studentWordReviewList',
      component: _import('students/components/studentWordReviewList'),
      hidden: true,
      name: '21天抗遗忘记录',
      meta: {
        perm: 'm:students:studentWordReviewList',
        title: '21天抗遗忘记录',
        noCache: false
      }
    },
    {
      path: '/students/components/studentWordViewList',
      component: _import('students/components/studentWordViewList'),
      hidden: true,
      name: '查看详情',
      meta: {
        perm: 'm:students:studentWordViewList',
        title: '查看详情',
        noCache: false
      }
    },
    {
      path: '/students/components/studentTestPrint',
      component: _import('students/components/studentTestPrint'),
      hidden: true,
      name: '学员测验打印',
      meta: {
        perm: 'm:students:studentTestPrint',
        title: '学员测验打印',
        noCache: false
      }
    },
    {
      path: '/students/components/studentTestPrintReading',
      component: _import('students/components/studentTestPrintReading'),
      hidden: true,
      name: '学员测验打印-阅读理解',
      meta: {
        perm: 'm:students:studentTestPrintReading',
        title: '学员测验打印-阅读理解',
        noCache: false
      }
    },
    {
      path: '/students/assistantFlowList',
      component: _import('students/assistantFlowList'),
      name: 'assistantFlowList',
      hidden: false,
      meta: {
        perm: 'm:students:assistantFlowList',
        icon: 'el-icon-s-data',
        title: '学员流水记录',
        noCache: false
      }
    },
    {
      path: '/pclass/deliveryChecklist',
      component: _import('pclass/deliveryChecklist'),
      name: 'deliveryChecklist',
      meta: {
        perm: 'm:pclass:deliveryChecklist',
        title: '正式学员交付清单',
        icon: 'el-icon-s-claim',
        noCache: false
      }
    },
    {
      path: '/students/components/studentTestPrintReport',
      component: _import('students/components/studentTestPrintReport'),
      hidden: true,
      name: '学员测验打印-结业报告',
      meta: {
        perm: 'm:students:studentTestPrintReport',
        title: '学员测验打印-结业报告',
        noCache: false
      }
    },
    {
      path: '/ruku/zhujiao/index',
      component: _import('ruku/zhujiao/index'),
      hidden: true,
      meta: {
        perm: 'm:ruku:zhujiao',
        noCache: false
      }
    },
    {
      path: '/ruku/xueguan/index',
      component: _import('ruku/xueguan/index'),
      hidden: true,
      meta: {
        perm: 'm:ruku:xueguan',
        noCache: false
      }
    },
    {
      path: '/ruku/zuzhang/index',
      component: _import('ruku/zuzhang/index'),
      hidden: true,
      meta: {
        perm: 'm:ruku:zuzhang',
        noCache: false
      }
    },
    {
      path: '/ruku/preparationCoachReview/index',
      component: _import('ruku/preparationCoachReview/index'),
      hidden: true,
      meta: {
        perm: 'm:ruku:preparationCoachReview',
        noCache: false
      }
    },
    {
      path: '/pclass/components/studentsList',
      component: _import('pclass/components/studentsList'),
      hidden: true,
      name: '学员信息表',
      meta: {
        perm: 'm:students:studentsList',
        title: '学员信息表'
      }
    },
    {
      path: '/pclass/components/changeClassList',
      component: _import('pclass/components/changeClassList'),
      hidden: true,
      name: '试课记录表',
      meta: {
        perm: 'm:students:changeClassList',
        title: '试课记录表'
      }
    },
    {
      path: '/students/areasOpenCourse',
      component: _import('students/areasOpenCourse'),
      hidden: true,
      name: '开通课程',
      meta: {
        perm: 'm:students:areasOpenCourse',
        title: '开通课程',
        noCache: false
      }
    },
    {
      path: '/students/areasOpenListenCourse',
      component: _import('students/areasOpenListenCourse'),
      hidden: true,
      name: '开通全能听力课程',
      meta: {
        perm: 'm:students:areasOpenListenCourse',
        title: '开通全能听力课程',
        noCache: false
      }
    },
    {
      path: '/students/areasOpenSuperReadCourse',
      component: _import('students/areasOpenSuperReadCourse'),
      hidden: true,
      name: '开通超级阅读课程',
      meta: {
        perm: 'm:students:areasOpenSuperReadCourse',
        title: '开通超级阅读课程',
        noCache: false
      }
    },
    {
      path: '/layout/personMe/personMy',
      component: _import('layout/personMe/personMy'),
      hidden: true,
      name: '个人信息',
      meta: {
        perm: 'm:layout:personMy',
        title: '个人信息',
        noCache: false
      }
    },
    {
      path: '/layout/personMe/realName',
      component: _import('layout/personMe/realName'),
      hidden: true,
      name: '实名认证',
      meta: {
        perm: 'm:layout:realName',
        title: '实名认证',
        noCache: false
      }
    },
    // 4.2.2
    {
      path: '/pclass/continueAdmin',
      component: _import('pclass/continueAdmin'),
      hidden: false,
      name: '续费提醒列表',
      meta: {
        perm: 'm:continueAdmin:index',
        title: '续费提醒列表',
        noCache: false,
        icon: 'xufei'
      }
    },
    {
      path: '/pclass/continue',
      component: _import('pclass/continue'),
      hidden: false,
      name: '低交付课时学员列表',
      meta: {
        perm: 'm:continue:index',
        title: '低交付课时学员列表',
        noCache: false,
        icon: 'xufei'
      }
    },
    {
      path: '/pclass/changeTeamAud',
      component: _import('pclass/changeTeamAud'),
      hidden: false,
      name: '更换交付小组审核',
      meta: {
        perm: 'm:changeTeamAud:index',
        title: '更换交付小组审核',
        noCache: false,
        icon: 'xufei'
      }
    }
  ]
};

export default pclassRouter;
