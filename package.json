{"name": "vue-element-admin", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "SET NODE_OPTIONS= && vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "cross-env NODE_OPTIONS= && vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@polyv/vod-upload-js-sdk": "^1.10.1", "ali-oss": "^6.10.0", "less": "^3.12.2", "less-loader": "^7.1.0", "axios": "0.18.1", "bpmn-js-token-simulation": "^0.10.0", "clipboard": "2.0.4", "core-js": "3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "echarts": "4.2.1", "element-china-area-data": "^5.0.2", "element-ui": "^2.13.2", "file-saver": "^2.0.1", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.6.0", "js-cookie": "2.2.0", "js-md5": "^0.8.3", "jspdf": "^2.3.1", "jszip": "3.2.1", "lodash": "^4.17.21", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "react": "^17.0.2", "react-dom": "^17.0.2", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-i18n": "7.3.2", "vue-router": "3.0.2", "vue-social-captcha": "^0.1.1", "vue-video-player": "^5.0.2", "vuera": "^0.2.7", "vuex": "3.1.0", "xlsx": "^0.14.1"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "autoprefixer": "9.5.1", "sass": "1.26.2", "sass-loader": "8.0.2", "svg-sprite-loader": "4.1.3", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "babel-jest": "23.6.0", "bpmn-js": "^7.4.0", "chalk": "2.4.2", "chokidar": "2.1.5", "cross-env": "^7.0.3", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "4.3.2", "svgo": "1.2.0", "vue-template-compiler": "2.6.10", "script-ext-html-webpack-plugin": "2.1.3"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "license": "MIT", "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}