<template>
  <div>
    <!-- 正式课/工资配置 -->
    <div v-loading="tableLoading">
      <div class="box-card" style="text-align: center; margin-top: 150px; height: 30vh" v-if="formalWages.length <= 0">
        <div class="no_data" style="height: 100%">
          <el-image style="width: 100px; height: 100px" :src="url"></el-image>
          <div style="color: #999; margin-top: 20px">暂无数据</div>
          <el-button type="primary" size="small" style="margin-top: 25px" @click="openTearcherWages('add', null, true)">新增正式课工资</el-button>
        </div>
      </div>
      <div v-if="formalWages.length > 0">
        <el-button type="primary" size="small" style="margin-top: 15px" @click="openTearcherWages('add')">新增正式课工资</el-button>
        <div class="warning-title-css">
          <i class="el-icon-warning" style="color: #f89728"></i>
          <span>注:未设置课程类型的正式课工资，都按默认发!</span>
        </div>
        <div v-if="formalWages.length > 0">
          <el-table
            v-loading="tableLoading"
            :data="formalWages"
            style="width: 100%"
            id="out-table"
            :header-cell-style="{ background: '#e2eaf6' }"
            :cell-style="{ 'text-align': 'center' }"
          >
            <el-table-column
              v-for="(item, index) in tableHeaderList"
              :key="`${index}-${item.id}`"
              :prop="item.value"
              :label="item.name"
              header-align="center"
              :width="item.value == 'operate' ? 200 : ''"
            >
              <template v-slot="{ row, $index }">
                <div v-if="item.value == 'operate'">
                  <el-button type="primary" size="mini" icon="el-icon-edit" @click="openTearcherWages('update', row)">编辑</el-button>
                  <el-button type="danger" size="mini" icon="el-icon-delete" v-if="row.configExtra != 'default'" @click="singleDelete(row.name, row, $index)">删除</el-button>
                </div>
                <!-- 工资 -->
                <span v-else-if="item.value == 'wage'">{{ (Number(row.value) + Number(row.configState)).toFixed(2) }}</span>
                <!-- 保底工资 -->
                <span v-else-if="item.value == 'minimumWage'">{{ (Number(row.guaranteedBaseSalary) + Number(row.guaranteedPerformanceSalary)).toFixed(2) }}</span>
                <!-- 状态 -->
                <span v-else-if="item.value == 'effectStatusDesc'" :class="{ green: row.effectStatusDesc == '生效中' }">{{ row[item.value] }}</span>
                <!-- 其他 -->
                <span v-else>{{ row[item.value] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleFormSizeChange"
            style="margin-top: 20px"
            @current-change="handleFormCurrentChange"
            :current-page="formalWagesQuery.pageNum"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="formalWagesQuery.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="formalWagesQuery.total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 正式课/工资配置-新增弹窗 -->
    <el-dialog :title="`${isAdd ? '新增' : '编辑'}正式课工资`" :visible.sync="dialogVisibleAdd" width="33%" :before-close="handleCloseAdd">
      <el-row>
        <el-form label-width="120px" label-position="right" ref="addForm" :model="addForm" :rules="addFormRules" inline class="my-form">
          <el-form-item label="课程类型:" prop="curriculumId">
            <el-input disabled value="默认" v-if="addForm.configExtra == 'default' || isDefault"></el-input>
            <el-select v-model="addForm.curriculumId" placeholder="请选择" v-else @change="changeCurriculumId">
              <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="基本工资:" prop="pay">
            <el-row>
              <el-col :span="19">
                <el-input v-model="addForm.pay" @input="formatDecimal($event, 'addForm.pay')" placeholder="请输入基本工资"></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/学员</el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="绩效工资:" prop="meritPay">
            <el-row>
              <el-col :span="19">
                <el-input v-model="addForm.meritPay" @input="formatDecimal($event, 'addForm.meritPay')" placeholder="请输入绩效工资"></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/学员</el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="工资:">
            <el-row>
              <el-col :span="19">
                <el-input v-model="addForm.wages" disabled placeholder="请输入基本工资及绩效工资后自动计算"></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/学员</el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="保底基本工资:" prop="guaranteedBaseSalary">
            <el-row>
              <el-col :span="19">
                <el-input v-model="addForm.guaranteedBaseSalary" @input="formatDecimal($event, 'addForm.guaranteedBaseSalary')" placeholder="请输入保底基本工资"></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/节课</el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="保底绩效工资:" prop="guaranteedPerformanceSalary">
            <el-row>
              <el-col :span="19">
                <el-input
                  v-model="addForm.guaranteedPerformanceSalary"
                  @input="formatDecimal($event, 'addForm.guaranteedPerformanceSalary')"
                  placeholder="请输入保底绩效工资"
                ></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/节课</el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="保底总工资:" prop="minimumWage">
            <el-row>
              <el-col :span="19">
                <el-input
                  v-model="addForm.minimumWage"
                  @input="formatDecimal($event, 'addForm.minimumWage')"
                  disabled
                  placeholder="请输入保底基本工资及绩效工资后自动计算"
                ></el-input>
              </el-col>
              <el-col :span="4" :offset="1">元/节课</el-col>
            </el-row>
          </el-form-item>
          <!-- 提示 -->
          <el-form-item label=" ">
            <el-row>
              <el-col :span="1">
                <i class="el-icon-warning" style="color: #fbab16"></i>
              </el-col>
              <el-col :span="23">
                <b>
                  注：配置的是一节课一个学员的工资，若该节课有N个学员
                  <br />
                  上课该节课的工资 = 1个学员的工资 × N（学员数）
                  <br />
                  若该节课工资低于保底工资则按保底工资发放
                </b>
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseAdd">取 消</el-button>
        <el-button type="primary" @click="handleAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard';
  import { getCurriculumTeacherWageConfig, addOrUpdateTeacherWageConfig, getOneToMoreClassList, deleteCurriculumTeacherWageConfig } from '@/api/FinanceApi/assistantWagesOneToMany';
  import { set } from 'nprogress';
  import { filter } from 'lodash';

  export default {
    name: 'OneToMany',
    data() {
      return {
        addFormRules: {
          curriculumId: {
            required: true,
            message: '请选择课程大类',
            trigger: 'blur'
          },
          pay: {
            required: true,
            message: '请输入基本工资',
            trigger: 'blur'
          },
          meritPay: {
            required: true,
            message: '请输入绩效工资',
            trigger: 'blur'
          }
          //   guaranteedBaseSalary: {
          //     required: true,
          //     message: '请输入保底基本工资',
          //     trigger: 'blur'
          //   },
          //   guaranteedPerformanceSalary: {
          //     required: true,
          //     message: '请输入保底绩效工资',
          //     trigger: 'blur'
          //   }
        },
        addForm: {
          name: '', // 课程类型名称
          curriculumId: '', // 课程大类id
          pay: '', // 正课工资
          meritPay: '', // "正课绩效工资"
          wages: '', // 正课工资

          guaranteedBaseSalary: '', // 保底基本工资
          guaranteedPerformanceSalary: '', // 保底绩效工资
          minimumWage: '' // 保底总工资
        },
        isAdd: false, // 是否新增
        isDefault: false, // 是否为默认配置
        dialogVisibleAdd: false, // 新增弹窗
        url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
        tableLoading: false, // 表格加载状态
        formalWages: [], // 正式课工资列表
        trialWages: [], // 试课工资列表
        tableHeaderList: [
          {
            name: '课程类型',
            value: 'name'
          },
          {
            name: '基本工资/学员',
            value: 'value'
          },
          {
            name: '绩效工资/学员',
            value: 'configState'
          },
          {
            name: '工资/学员',
            value: 'wage'
          },
          {
            name: '保底基本工资/节课',
            value: 'guaranteedBaseSalary'
          },
          {
            name: '保底绩效工资/节课',
            value: 'guaranteedPerformanceSalary'
          },
          {
            name: '保底工资/节课',
            value: 'minimumWage'
          },
          {
            name: '状态',
            value: 'effectStatusDesc'
          },
          {
            name: '生效时间',
            value: 'effectTime'
          },
          {
            name: '操作',
            value: 'operate'
          }
        ],
        formalWagesQuery: {
          pageNum: 1,
          pageSize: 10,
          total: 0
        },
        courseList: [] // 课程类型
      };
    },
    watch: {
      'addForm.pay'(val) {
        this.addForm.wages = (Number(this.addForm.pay) + Number(this.addForm.meritPay) || 0).toFixed(2);
      },
      'addForm.meritPay'(val) {
        this.addForm.wages = (Number(this.addForm.pay) + Number(this.addForm.meritPay) || 0).toFixed(2);
      },
      'addForm.guaranteedBaseSalary'(val) {
        this.addForm.minimumWage = (Number(this.addForm.guaranteedBaseSalary) + Number(this.addForm.guaranteedPerformanceSalary) || 0).toFixed(2);
      },
      'addForm.guaranteedPerformanceSalary'(val) {
        this.addForm.minimumWage = (Number(this.addForm.guaranteedBaseSalary) + Number(this.addForm.guaranteedPerformanceSalary) || 0).toFixed(2);
      }
    },
    created() {
      this.getbvstatusList();
      this.initData();
    },
    methods: {
      initDataRally() {
        this.formalWagesQuery.pageNum = 1;
        this.initTable();
      },
      initData() {
        this.tableLoading = true;
        // 请求
        getCurriculumTeacherWageConfig({ pageNum: this.formalWagesQuery.pageNum, pageSize: this.formalWagesQuery.pageSize }).then((res) => {
          this.tableLoading = false;
          this.formalWages = res.data.data || [];
          this.formalWagesQuery.total = Number(res.data.totalItems);
        });
      },
      // 修改课程大类
      changeCurriculumId(e) {
        if (e) {
          this.addForm.name = this.courseList.filter((item) => item.id == e)[0].enName;
        }
      },
      // 确认新增
      handleAdd() {
        this.$refs.addForm.validate((valid) => {
          if (!valid) {
            return false;
          }
          console.log(this.addForm, 'this.addForm');
          addOrUpdateTeacherWageConfig(this.addForm)
            .then((result) => {
              if (this.isAdd) {
                this.$message.success('新增成功');
              } else {
                this.$message.success('编辑成功');
              }
              this.handleCloseAdd();
              this.initData();
            })
            .catch((err) => {});
        });
      },
      // 关闭新增
      handleCloseAdd() {
        this.dialogVisibleAdd = false;
        this.addForm = {
          name: '', // 课程类型名称
          curriculumId: '', // 课程大类id
          pay: '', // 正课工资
          meritPay: '', // "正课绩效工资"
          wages: '', // 正课工资

          guaranteedBaseSalary: '', // 保底基本工资
          guaranteedPerformanceSalary: '', // 保底绩效工资
          minimumWage: '' // 保底总工资
        };
        // 清除校验
        this.$refs.addForm.clearValidate();
      },
      // 打开新增/编辑弹窗
      openTearcherWages(type, res, showDefault = false) {
        this.isDefault = false;
        if (type === 'add') {
          this.isAdd = true;
          if (showDefault) {
            this.isDefault = true;
            this.addForm.curriculumId = 'default';
            this.addForm.name = '默认配置';
          }
        } else if (type === 'update') {
          this.isAdd = false;

          this.addForm = { ...this.addForm, ...res };
          this.addForm.pay = res.value; // 正课工资
          this.addForm.meritPay = res.configState; // "正课绩效工资"
          this.addForm.curriculumId = res.configExtra; // 课程大类id
        }
        this.dialogVisibleAdd = true;
      },
      singleDelete(name, row, index) {
        const that = this;
        console.log(name, row, index);
        this.$confirm(`确定删除《 ${name} 》课程类型的配置吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const loading = this.$loading({
            lock: true,
            text: '删除中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          deleteCurriculumTeacherWageConfig(row.id)
            .then((res) => {
              loading.close();
              this.$message.success('删除成功');
              this.initData();
            })
            .catch((err) => {
              loading.close();
            });
        });
      },
      getbvstatusList() {
        getOneToMoreClassList({}).then((res) => {
          this.courseList = res.data;
          //   console.log('🚀 ~ bvstatusList ~ this.courseList:', this.courseList);
        });
      },
      //正式课工资分页
      handleFormSizeChange(val) {
        this.formalWagesQuery.pageSize = val;
        this.initData();
      },
      handleFormCurrentChange(val) {
        this.formalWagesQuery.pageNum = val;
        this.initData();
      },
      /**
       *
       * @param el 元素
       * @param path 路径 如：'addForm.value'
       */
      formatDecimal(el, path) {
        let value = JSON.parse(JSON.stringify(el));
        // 1. 移除非数字和非法字符
        let cleaned = value.replace(/[^\d.]/g, '');

        // 2. 分割整数和小数部分
        let parts = cleaned.split('.');
        if (parts.length > 1) {
          // 限制小数部分最多两位
          parts[1] = parts[1].slice(0, 2);
          cleaned = parts[0] + '.' + parts[1];
        }

        // 根据 digit 值创建路径数组
        let that = path.split('.');
        // 更新输入框的值
        setNestedValue(this, that, cleaned);
        // 递归函数来访问嵌套属性
        function setNestedValue(obj, path, value) {
          let i;
          for (i = 0; i < path.length - 1; i++) {
            obj = obj[path[i]];
          }
          obj[path[i]] = value;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .warning-title-css {
    line-height: 60px;
    span {
      display: inline-block;
      margin-left: 10px;
      color: #f89728;
    }
  }
  .my-form {
    ::v-deep.el-form-item--small.el-form-item {
      display: flex;
    }
    ::v-deep.el-form-item--small .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep.el-form-item--small .el-form-item__content {
      flex: 1;
    }
    ::v-deep.el-range-editor--small.el-input__inner {
      width: auto;
    }
    ::v-deep.el-select {
      width: 100%;
    }
  }
</style>
