import request from '@/utils/request'

// 排课学员列表
export const getStudentList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getStudentList',
    method: 'get',
    params: data
  })
}
// 排课详情
export const getPlanCourse = (id) => {
  return request({
    url: '/deliver/web/learnManager/getPlanCourse/' + id,
    method: 'GET'
  })
}
//上课信息对接表
export const getStudentContactInfoDetail = (data) => {
  return request({
    url: '/deliver/web/student/contact/info/getStudentContactInfoDetail',
    method: 'get',
    params: data
  })
}
export const queryStudentContactInfoDetail = (data) => {
  return request({
    url: '/dyf/math/wab/extend/queryStudentContactInfoDetail',
    method: 'get',
    params: data
  })
}

// 获取学员已购交付学时详情
export const getStudentDeliverHoursVo = (data) => {
  return request({
    url: '/deliver/web/learnManager/getStudentDeliverHoursVo',
    method: 'GET',
    params: data
  })
}
//排课接口
export const addPlanCourse = (data) => {
  return request({
    url: '/deliver/web/learnManager/addPlanCourse',
    method: 'post',
    data
  })
}
//排课是否有冲突
export const isTimeConflicts = (timeList) => {
  return request({
    url: '/znyy/stopServiceTimeConfig/isDuringStopServiceTime',
    method: 'post',
    data: {
      timeList
    }
  })
}
// 搜索教练列表
export const screenTeacherList = (data) => {
  return request({
    // url: '/deliver/web/teacher/screenTeacherList',
    url: '/deliver/web/teacher/screenTeacherListByStudentCode',
    method: 'post',
    data: data
  })
}

// 搜索教练列表并获取学时数量
export const screenTeacherListAndHours = (data) => {
  return request({
    url: '/deliver/web/teacher/screenTeacherListAndHoursByStudentCode',
    // url: '/deliver/web/teacher/screenTeacherListAndHours',
    method: 'post',
    data: data
  })
}

// 根据日期获取课程时间列表
export const getPlanTimeList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getPlanTimeList',
    method: 'GET',
    params: data
  })
}

// 获取反馈详情
export const getFeedbackInfo = (data) => {
  return request({
    url: '/deliver/web/learnManager/getFeedbackInfo',
    method: 'GET',
    params: data

  })
}
// 调课接口
export const adjustPlanStudy = (data) => {
  return request({
    url: '/deliver/web/learnManager/adjustPlanStudy',
    method: 'post',
    params: data
  })
}

// 数据查看-反馈详情（数据统计/总）
export const getTotalStatistics = (data) => {
  return request({
    url: '/deliver/web/learnManager/getTotalStatistics',
    method: 'get',
    params: data
  })
}

// 数据查看-根据年月获取课程日期列表
export const getStudyDateList = (data) => {
  return request({
    url: '/deliver/web/learnManager/getStudyDateList',
    method: 'get',
    params: data
  })
}

// 删除学习课程
export const deletePlanStudy = (id) => {
  return request({
    url: '/deliver/web/learnManager/deletePlanStudy',
    method: 'delete',
    params: { studyId: id }
  })
}

// 删除复习课程
export const deletePlanReview = (id) => {
  return request({
    url: '/deliver/web/learnManager/deletePlanReview',
    method: 'delete',
    params: { reviewId: id }
  })
}

//排课计划发送
export const sendPlanCourse = (planId, sendType) => {
  return request({
    url: '/deliver/web/learnManager/sendPlanCourse',
    method: 'get',
    params: { planId: planId, sendType: sendType }
  })
}

//取消排课
export const cancelPlanCourse = (planId, sendType) => {
  return request({
    url: '/deliver/web/learnManager/cancelPlanCourse',
    method: 'get',
    params: { planId: planId }
  })
}

// 编辑获取学员信息
export const selectStudent = (studentCode) => {


  return request({
    url: '/deliver/web/learnManager/selectStudentInfo',
    method: 'get',
    params: { studentCode: studentCode }
  })
}

//编辑学员信息
export const updateStudent = (studentCode, realName, grade, school) => {
  return request({
    url: '/deliver/web/learnManager/updateStudentInfo',
    method: 'post',
    params: { studentCode: studentCode, realName: realName, grade: grade, school: school }
  })
}

// 编辑备注
export const modifyRemarks = (data) => {
  return request({
    url: '/deliver/web/learnManager/setReferrerRemark',
    method: 'put',
    params: data
  })
}

// 试课报告详情
export const getTrialclass = (data) => {
  return request({
    url: '/deliver/web/learnManager/getCourseInfo/' + data,
    method: 'GET'
  })
}


export const findTeacherById = (data) => {
  return request({
    url: '/deliver/web/teacher/findTeacherById',
    method: 'GET',
    params: data
  })
}
export const findTeachersByDeliverMerchant = (data) => {
  return request({
    url: '/deliver/web/teacher/findTeachersByDeliverMerchant',
    method: 'GET',
    params: data
  })
}
export const changestudyTeacher = (data) => {
  return request({
    url: '/deliver/web/teacher/changeStudyTeacher',
    method: 'POST',
    data
  })
}
export const changePlaceTeacher = (data) => {
  return request({
    url: '/deliver/web/teacher/changePlaceTeacher',
    method: 'POST',
    data
  })
}





