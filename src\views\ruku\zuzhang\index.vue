<template>
  <el-form label-width="140px" :model="formData" ref="editForm" style="margin-top: 2vh" :rules="rules">
    <el-form-item label="所属交付中心:" prop="deliverName" v-if="isAdmin">
      <el-select v-el-select-loadmore="handleLoadmore" style="width: 30%" :loading="loadingShip" remote clearable
        v-model="deliverName" filterable reserve-keyword placeholder="请选择" @input="changeMessage"
        @blur="clearSearchRecord" @change="changeTeacher" :disabled="!!formData.id">
        <el-option v-for="(item,index) in option" :key="index" :label="item.deliverMerchantName"
          :value="item.deliverMerchantName">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="姓名:" prop="name">
      <el-input style="width: 30%" placeholder="请输入" v-model.trim="formData.name"></el-input>
    </el-form-item>
    <el-form-item label="性别:" prop="sex">
      <el-select style="width: 30%" v-model="formData.sex" placeholder="请选择" clearable>
        <el-option label="男" :value="1"></el-option>
        <el-option label="女" :value="2"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="联系方式:" prop="mobile">
      <el-input style="width: 30%" placeholder="请输入" v-model.trim="formData.mobile"
        :disabled="!!formData.id"></el-input>
    </el-form-item>
    <el-form-item label="性格特点:" prop="personality">
      <el-input style="width: 30%" placeholder="请输入" v-model.trim="formData.personality"></el-input>
    </el-form-item>
    <el-form-item label="备注:" prop="comment">
      <el-input type="textarea" style="width: 30%" placeholder="请输入" v-model.trim="formData.comment"></el-input>
    </el-form-item>
    <!-- 按钮最后一行 -->
    <el-row type="flex" justify="center" style="margin-top: 12vh">
      <el-button @click="quxiao" style="margin-right: 6vh">取 消</el-button>
      <el-button @click="onSubmit" type="primary">确 定</el-button>
    </el-row>
    <div style="height: 40vh"></div>
  </el-form>
</template>

<script>
import ls from "@/api/sessionStorage";
import { deliverlist } from "@/api/peizhi/peizhi"
import { getLeaderDetail, editLeader, addLeader } from "@/api/rukuManage/zuzhang";
export default {
  name: "zuzhang",
  components: {},
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        SELECTWRAP_DOM.addEventListener("scroll", function () {
          //临界值的判断滑动到底部就触发
          const condition =
            this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      rowData: {},
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        sex: [
          { required: true, message: '请选择性别', trigger: 'change' },
        ],
        mobile: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        personality: [
          { required: true, message: '请输入性格特点', trigger: 'blur' },
        ],

      },
      deliverName: "",
      merchantCode: 0,
      isAdmin: false,
      loadingShip: false,
      option: [],
      optionTotal: 0,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
      },
      formData: {}
    };
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin';
    if (this.isAdmin) {
      this.getTeacherList()
    }
    // console.log(this.$route.query)
    if (this.$route.query.data.id) {
      this.rowData = this.$route.query.data;
      this.initData(this.rowData.id)
    } else {
      this.rowData = {}
    }
  },

  methods: {
    quxiao() {
      this.$refs.editForm.clearValidate()
      this.$refs.editForm.resetFields()
      this.formData = {}
      this.$router.go(-1);
    },
    // 获取交付中心
    async getTeacherList() {
      let allData = await deliverlist(this.selectObj);
      this.option = this.option.concat(allData.data.data);
      this.optionTotal = Number(allData.data.totalPage)
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        if (this.selectObj.pageNum == this.optionTotal) return//节流防抖
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    changeMessage() {
      this.$forceUpdate();
    },
    clearSearchRecord() {
      setTimeout(() => {
        if (this.deliverName == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        }
      }, 500)
      this.$forceUpdate();
    },
    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.getTeacherList();
      } else {
        let arr = this.option.filter(i => i.deliverMerchantName == e)
        this.merchantCode = arr[0].deliverMerchantCode
      }
    },
    async initData(id) {
      const { data } = await getLeaderDetail(id)
      console.log(data, '==================');
      this.formData = data
      this.deliverName = data.merchantName
    },
    // 编辑/新增提交事件
    onSubmit() {
      this.$refs.editForm.validate(async vaild => {
        if (vaild) {
          let obj = {}
          obj = !!this.rowData.id ? {
            id: this.rowData.id,
            memberCode: this.rowData.memberCode,
            name: this.formData.name,
            sex: this.formData.sex,
            mobile: this.formData.mobile,
            personality: this.formData.personality,
            comment: this.formData.comment,
          } : {
            name: this.formData.name,
            sex: this.formData.sex,
            mobile: this.formData.mobile,
            personality: this.formData.personality,
            comment: this.formData.comment
          }
          // 判断是否为管理员
          if (this.isAdmin) {
            if (this.merchantCode) {
              obj.merchantCode = this.merchantCode
            } else {
              if (!this.formData.id) {
                return this.$message.warning('请选择交付中心')
              }
            }
          }
          !!this.rowData.id ? await editLeader(obj) : await addLeader(obj)
          this.$message.success('操作成功')
          setTimeout(() => {
            this.quxiao()
          }, 500);
        } else {
          this.$message.error({ showClose: true, message: '请按规范填写数据', type: 'error' })
          return
        }
      })

    }
  }
};
</script>
