import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const robotSettingRouter = {
  path: '/robotSetting/index',
  component: Layout,
  name: 'robotSetting',
  meta: {
    perm: 'm:robotSetting:index',
    title: '机器人管理',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: 'robotSetting_index',
      component: _import('robotSetting/index'),
      name: 'index',
      meta: {
        perm: 'm:robotSetting:index',
        title: '机器人管理',
        icon: 'divisionList',
        noCache: false
      }
    }
  ]
};

export default robotSettingRouter;
