/**
 * 系统配置相关接口
 */
import request from '@/utils/request'

export default {
  // 分页查询    录屏配置
  systemList(data) {
    return request({
      url: '/deliver/bvStatus/queryPage',
      method: 'GET',
      params: data
    })
  },

   // 新增 录屏配置
   addsystem(data) {
    return request({
      url: '/deliver/bvStatus/saveBvStatus',
      method: 'POST',
      params: data
    })
  },
    // 删除 录屏配置
    deletesystem(id) {
      return request({
        url: `/deliver/bvStatus/delete?id=`+id,
        method: 'get',
      })
    },
    // 修改 录屏配置
     eidtDispositio(data){
      return request({
        url: '/deliver/bvStatus/editBvStatus',
        method: 'post',
        params:data
      })
     },



// 回显 录屏配置
seeDetails(id) {
      return request({
        url: `/deliver/bvStatus/seeDetails?id=`+ id,
        method: 'GET'
      })
    },
  coursePriceList(pageNum, pageSize, data) {
    return request({
      url: '/znyy/bSysConfig/list/course/price/' + pageNum + '/' + pageSize,
      method: 'GET',
      params: data
    })
  },
 
  // 新增
  addsystemCoursePrice(data) {
    return request({
      url: '/znyy/bSysConfig/course/price',
      method: 'POST',
      data
    })
  },
  // 编辑
  updatesystem(data) {
    return request({
      url: '/znyy/appversion',
      method: 'PUT',
      data
    })
  },
  // 修改回显
  queryActive(id) {
    return request({
      url: '/znyy/bSysConfig/seeDetails/' + id,
      method: 'GET'
    })
  },
  // 修改回显
  queryDetail(configGroup) {
    return request({
      url: '/znyy/bSysConfig/detail',
      method: 'GET',
      params:{'configGroup':configGroup}
    })
  },
  // 修改回显
  queryCourseMax(city) {
    return request({
      url: '/znyy/bSysConfig/course/price/max',
      method: 'GET',
      params:{'city':city}
    })
  },
  // 修改回显
  queryCoursePrice(id) {
    return request({
      url: '/znyy/bSysConfig/seeDetails/course/price/' + id,
      method: 'GET'
    })
  },

  // 删除
  deleteCourse(id) {
    return request({
      url: '/znyy/bSysConfig/course/price/' + id,
      method: 'DELETE'
    })
  },
  //修改密码
  updatePassWord(data){
    return request({
      url: '/znyy/bSysConfig/update/pwd',
      method: 'PUT',
      data
    })
  }





// 录屏修改

// deliver/bvStatus/editBvStatus
}
