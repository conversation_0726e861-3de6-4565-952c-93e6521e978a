<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <el-image
          :src="(JlbInfo && JlbInfo.logoEnable && JlbInfo.avatar) || avatar"
          class="sidebar-logo"
          fit="contain"
        />
        <h1 class="sidebar-title">
          {{ JlbInfo && JlbInfo.customName }}鼎校甄选
        </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <
        <el-image
          :src="(JlbInfo && JlbInfo.logoEnable && JlbInfo.avatar) || avatar"
          class="sidebar-logo"
          fit="contain"
        />
        <el-tooltip
          v-if="JlbInfo.customName.length > 3"
          class="item"
          effect="dark"
          :content="JlbInfo.customName + '鼎校甄选'"
          placement="bottom"
        >
          <h1 class="sidebar-title">
            {{ JlbInfo && JlbInfo.customName }}鼎校甄选
          </h1>
        </el-tooltip>
        <h1 v-else class="sidebar-title">
          {{ JlbInfo && JlbInfo.customName }}鼎校甄选
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      title: "Vue Element Admin",
      logo: "https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png",
    };
  },
  computed: {
    ...mapGetters(["JlbInfo"]),
  },
  mounted() { 
    if (this.JlbInfo.logoEnable) {
      document.title = this.JlbInfo.customName + "鼎校甄选";
    }
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background: #304156;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #e5e5e5;
      font-weight: 400;
      line-height: 24px;
      font-size: 15px;
      font-family: SourceHanSansSC, SourceHanSansSC;
      vertical-align: middle;
      max-width: 118px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
