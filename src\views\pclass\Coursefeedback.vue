<!--交付中心-正式学员管理-课程反馈处理-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" ref="searchNum" :model="searchNum">
        <!-- 1 -->
        <el-row style="display: flex; flex-wrap: wrap; align-items: center">
          <el-col :span="4">
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="searchNum.name" clearable placeholder="请选择" style="width: 13vw"
                size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="1">
            <el-form-item label="学员编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入" style="width: 13vw"
                size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="1">
            <el-form-item label="课程内容:" prop="courseType">
              <el-select v-model="searchNum.courseType" clearable  placeholder="请选择" size="small" style="width: 13vw">
                <el-option label="鼎英语" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="1">
            <el-form-item label="小组组长:" prop="teamLeaderName">
              <el-input style="width: 13vw" v-model="searchNum.teamLeaderName" clearable placeholder="请输入"
              size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" :offset="1">
            <el-form-item label="教练老师:" prop="teacherName">
              <el-select v-el-select-loadmore="handleLoadmore" :loading="loadingShip" :filter-method="filterValue"
                clearable v-model="searchNum.teacherName" filterable remote reserve-keyword placeholder="请选择"
                @input="changeMessage" @blur="clearSearchRecord" @change="changeTeacher" size="small">
                <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.label">
                </el-option>
              </el-select>
              <!-- <el-input v-model="searchNum.teacherName" clearable placeholder="请输入" style="width: 13vw"
                size="small"></el-input> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="6" v-if="isAdmin">
            <el-form-item label="交付中心编号:" prop="deliverMerchant">
              <el-input style="width: 13vw" v-model="searchNum.deliverMerchant" clearable placeholder="请输入"
                size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isAdmin">
            <el-form-item label="交付中心名称:" prop="deliverName">
              <el-input style="width: 13vw" v-model="searchNum.deliverName" clearable placeholder="请输入"
                size="small"></el-input>
            </el-form-item>
          </el-col>
     
          <el-col :span="isAdmin?8:20">
            <el-form-item label="上课时间:" prop="">
              <el-date-picker v-model="timeAll" type="datetimerange" format="yyyy-MM-dd HH:mm" size="small"
                value-format="yyyy-MM-dd HH:mm" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                @change="changeTime">
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:10px 0 20px 20px">列表显示属性</el-button>

    <el-table v-loading="tableLoading" :data="luyouclassCard" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column width="50" align="center"></el-table-column>
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value"
        :label="item.name" header-align="center" :width="item.value== 'operate'?200:''">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="danger" v-if="row.status == 1" size="mini" @click="handleFn(row)">处理</el-button>
            <el-button type="primary" size="mini" v-else @click="lookFn(row)">查看</el-button>
            <el-button type="success" size="mini" @click="lookDataFn(row)">数据查看</el-button>
          </div>

          <div v-else-if="item.value == 'courseType'">
            <span>{{ row.courseType == 1 ? '鼎英语' : '-' }}</span>
          </div>
          <div v-else-if="item.value == 'teamLeaderName'">
            <span>{{ row.teamLeaderName  ? row.teamLeaderName : '-' }}</span>
          </div>
          <div v-else-if="item.value == 'teachingType'">
            <span v-if="row.teachingType == 1 || row.teachingType == 2 || row.teachingType == 3">{{ row.teachingType
              != 1 ? (row.teachingType == 2 ? '线下' : '远程和线下') : '远程' }}</span>
            <span v-else>暂无</span>
          </div>

          <div v-else-if="item.value == 'status'">
            <span v-if="row.status == 1">待处理</span>
            <span v-if="row.status == 2">已处理</span>
          </div>

          <div v-else-if="item.value == 'newRate'">
            <span v-if="row.newRate == null || row.newRate === 0">-</span>
            <span v-else :class="{ redClass: row.newRate > 10, GreenClass: row.newRate < 10 }">
              {{row.newRate }}%
            </span>
          </div>

          <div v-else-if="item.value == 'reviewRate'">
            <span v-if="row.reviewRate == null || row.reviewRate === 0">-</span>
            <span v-else :class="{ redClass: row.reviewRate > 10, GreenClass: row.reviewRate < 10 }">
              {{ row.reviewRate }}%
            </span>
          </div>

          <div v-else-if="item.value == 'twentyOneRate'">
            <span v-if="row.twentyOneRate == null || row.twentyOneRate === 0">-</span>
            <span v-else :class="{ redClass: row.twentyOneRate > 10, GreenClass: row.twentyOneRate < 10 }">
              {{ row.twentyOneRate }}%
            </span>
          </div>

          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]" :page-size="searchNum.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>
    <FeedbackListDialog @LeaveDialog="LeaveDialog" :LeaveViewStyle="LeaveViewStyle" ref="FeedbackListDialog"
      :direction="direction" @initList="initData" />
    <FeedbackDialog @LeaveyDialog="LeaveyDialog" :LeaveViewStyley="LeaveViewStyley" ref="FeedbackDialog"
      :direction="direction" />
    <CoursefeedbackDialog ref="Coursefeedback" @reviewDialog="reviewDialog" :reviewStyle="reviewStyle"
      :direction="direction" />

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import { selAllTeacher, getshareProfit } from "@/api/studentClass/changeList"
import FeedbackDialog from "./components/FeedbackDialog.vue";
import { getFeedbackInfo } from "@/api/paikeManage/LearnManager";
import { getTableTitleSet, setTableList } from "@/api/paikeManage/classCard";
import CoursefeedbackDialog from "./components/CoursefeedbackDialog.vue";
import FeedbackListDialog from "../pclass/components/FeedbackListDialog.vue";
import { getWarningInfo, getWarningList } from "@/api/studentClass/changeList";
import ls from "@/api/sessionStorage";
import HeaderSettingsDialog from '../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: 'Coursefeedback',
  components: {
    FeedbackDialog,
    FeedbackListDialog,
    CoursefeedbackDialog,
    HeaderSettingsDialog
  },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          //临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  data() {
    return {
      reviewStyle: false,
      LeaveViewStyle: false,
      LeaveViewStyley: false,
      direction: "rtl", //超哪边打开
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date();
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const temp = new Date();
              temp.setTime(temp.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0))
              ]);
            }
          },
          {
            text: "最近七天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      tableLoading: false,
      timeAll: [],
      luyouclassCard: [],
      total: null,
      warningId: "",
      searchNum: {
        courseType: "",
        name: "",
        endTime: "",
        startTime: "",
        teacherName: "",
        studentCode: "",
        teacherId: "",
        teachingType: "",
        lastStudyTime: "",
        pageNum: 1,
        pageSize: 10
      }, //搜索参数
      leaveApplication: {
        id: "",
        type: ''
      },
      isAdmin: false,


      chooseTime: '',// 筛选时间

      option: [],
      loadingShip: false,
      selectObj: {
        pageNum: 1,
        pageSize: 20,
        name: ""
      },
      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '交付中心编号',
          value: 'deliverMerchant'
        },
        {
          name: '交付中心名称',
          value: 'deliverName'
        },
        {
          name: '操作',
          value: 'operate'
        },
        {
          name: '姓名',
          value: 'name'
        },
        {
          name: '门店名称',
          value: 'merchantName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        },
        {
          name: '课程分类',
          value: 'courseType'
        },
        {
          name: '授课方式',
          value: 'teachingType'
        },
        {
          name: '上课时间',
          value: 'studyTime'
        },
        {
          name: '教练老师',
          value: 'teacher'
        },
        {
          name: '小组组长',
          value: 'teamLeaderName'
        },
        {
          name: '处理状态',
          value: 'status'
        },
        {
          name: '学新词汇遗忘率',
          value: 'newRate'
        },
        {
          name: '复习遗忘率',
          value: 'reviewRate'
        },
        {
          name: '21天抗遗忘遗忘率',
          value: 'twentyOneRate'
        }],

      tableHeaderList: [], // 获取表头数据

    };
  },
  //计算属性
  computed: {
    classCardstyle_: {
      get() {
        return this.LeaveViewStyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("LeaveDialog", v);
      }
    },
    classCardstyley: {
      get() {
        return this.LeaveViewStyley;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("LeaveyDialog", v);
      }
    }
  },
  created() {
    this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager';
    this.initData();
    this.getTeacherList();
    this.getHeaderlist();

  },
  methods: {
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 下拉加载
    handleLoadmore() {
      if (!this.loadingShip) {
        this.selectObj.pageNum++;
        this.getTeacherList();
      }
    },
    // 获取教练
    async getTeacherList() {
      let allData = await selAllTeacher(this.selectObj);
      this.option = this.option.concat(allData.data.data);
    },

    filterValue(value) {
      console.log(value)
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = value;
      this.getTeacherList();
    },

    changeMessage() {
      this.$forceUpdate();
    },

    clearSearchRecord(event) {
      setTimeout(() => {
        if (this.searchNum.teacherName == '') {
          this.option = [];
          this.selectObj.pageNum = 1;
          this.selectObj.name = '';
          this.getTeacherList();
        }
      }, 500)
      this.$forceUpdate();
    },

    changeTeacher(e) {
      if (e == '') {
        this.option = [];
        this.selectObj.pageNum = 1;
        this.selectObj.name = '';
        this.getTeacherList();
      }
    },

    // 数据查看
    async lookDataFn(row) {
      this.leaveApplication.id = row.studyId;
      this.leaveApplication.type = row.studyType;
      this.$refs.Coursefeedback.reviewTotal.id = row.id
      this.$refs.Coursefeedback.reviewTotal.planId = row.planId
      console.log(row.planId);
      let res = await getFeedbackInfo(this.leaveApplication);
      if (res.code == 20000) {
        this.reviewStyle = true;
      }
      this.$refs.Coursefeedback.studyList1 = res.data;
      this.$refs.Coursefeedback.teacher = row.teacher;
      this.$refs.Coursefeedback.studyType = row.studyType;
    },
    // 处理按钮
    async handleFn(row) {
      this.LeaveViewStyle = true;
      this.warningId = row.id;
      let res = await getWarningInfo(this.warningId);
      this.$refs.FeedbackListDialog.warningList = res.data;
      this.$refs.FeedbackListDialog.addwarningList.warningId = res.data.id;
      this.initData();
    },
    // 查看
    async lookFn(row) {
      console.log(row);
      this.LeaveViewStyley = true;
      this.warningId = row.id;
      let res = await getWarningInfo(this.warningId);
      this.$refs.FeedbackDialog.warningList = res.data;
    },
    LeaveDialog(v) {
      this.LeaveViewStyle = v;
    },
    LeaveyDialog(v) {
      this.LeaveViewStyley = v;
    },
    async initData01() {
      this.searchNum.pageNum = 1,
        this.searchNum.pageSize = 10,
        this.initData();
    },
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = [];
      }
      this.tableLoading = true;
      this.searchNum.startTime = this.timeAll[0];
      this.searchNum.endTime = this.timeAll[1];
      let { data } = await getWarningList(this.searchNum);
      this.tableLoading = false;
      this.luyouclassCard = data.data;
      this.total = Number(data.totalItems);
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return "远程";
      } else if (val.teachingType == 2) {
        return "线下";
      } else if (val.teachingType == 3) {
        return "远程和线下";
      } else {
        return "暂无";
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return "鼎英语";
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return "未上课";
      } else if (val.studyStatus == 2) {
        return "已上课";
      }
    },
    status(val) {
      if (val.status == 1) {
        return "待处理";
      } else if (val.status == 2) {
        return "已处理";
      }
    },
    reviewDialog(v) {
      this.reviewStyle = v;
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.searchNum.startTime = '';
      this.searchNum.endTime = '';
      this.timeAll = [];
      this.option = [];
      this.selectObj.pageNum = 1;
      this.selectObj.name = "";
      this.getTeacherList();
      this.initData();
    },

    changeTime(e) {
      this.searchNum.startTime = this.chooseTime[0];
      this.searchNum.endTime = this.chooseTime[1];
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "Coursefeedback",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'Coursefeedback'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
          this.$forceUpdate();
        } else {
          this.tableHeaderList = this.headerSettings;
          this.$forceUpdate();
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  }
};
</script>
<style lang="scss" scoped>
.redClass {
  color: red;
}

.GreenClass {
  color: green;
}
</style>
