/**
 * 学员测验打印-单词相关接口
 */
 import request from '@/utils/request'

 export default {
   // 分页查询
   studentList(data) {
     return request({
       url: '/znyy/deliver/student/printWords',
       method: 'GET',
       params:data
     })
   },
   // 导出
   printExport(wordPrintCode) {
     return request({
       url: '/znyy/areas/student/word/print/to/excel?wordPrintCode=' + wordPrintCode,
       method: 'GET',
       responseType: 'blob',
     })
   },
 }
 