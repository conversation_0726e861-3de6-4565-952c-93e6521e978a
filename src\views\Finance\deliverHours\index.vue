<!--交付中心-财务管理-集中交付清单-->
<template>
  <div>
    <el-card class="frame" shadow="never">
      <el-form label-width="100px" ref="searchNum" :model="searchNum">
        <!-- 1 -->
        <el-row>
          <el-col :span="6">
            <el-form-item label="订单号:" prop="orderId">
              <el-input v-model="searchNum.orderId" clearable placeholder="请选择" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="学生编号:" prop="studentCode">
              <el-input v-model="searchNum.studentCode" clearable placeholder="请输入学员编号" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="时间筛选:" prop="timeAll">
              <el-date-picker v-model="timeAll" style="width: 18vw" size="small" format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions" align="right" type="datetimerange"
                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="isAdmin ? 6 : 20">
            <el-form-item label="门店商户号:" prop="merchantCode">
              <el-input v-model="searchNum.merchantCode" clearable placeholder="请输入门店商户号" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="课程类型:" prop="curriculumId">
              <el-select v-model="searchNum.curriculumId" size="small" placeholder="请选择" clearable>
                <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="isAdmin" label="交付中心编号:" prop="deliverMerchant">
              <el-input v-model="searchNum.deliverMerchant" clearable placeholder="请输入交付中心编号" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="isAdmin" label="交付中心名称:" prop="deliverName">
              <el-input v-model="searchNum.deliverName" clearable placeholder="请输入交付中心名称" size="small"
                style="width: 13vw"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="mini" icon="el-icon-search" @click="initData01">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" @click="headerList()" style="margin:20px">列表显示属性</el-button>

    <!-- 表格 -->
    <el-table v-loading="tableLoading" :data="reviewLister" style="width: 100%" id="out-table"
      :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">

      <el-table-column prop="curriculumName" min-width="80" label="课程类型" header-align="center" />
      <el-table-column prop="deliverMerchant" v-if="isAdmin" min-width="80" label="交付中心编号" header-align="center" />
      <!-- <el-table-column prop="deliverName" v-if="isAdmin" min-width="110" label="交付中心名称" header-align="center"/>
      <el-table-column prop="merchantName" label="门店名称" header-align="center"></el-table-column>
      <el-table-column prop="merchantCode" label="门店商户号" header-align="center"></el-table-column>
      <el-table-column prop="orderId" label="订单号" header-align="center"></el-table-column> -->
      <el-table-column prop="merchantName" label="门店名称" header-align="center"></el-table-column>
      <el-table-column prop="merchantCode" label="门店商户号" header-align="center"></el-table-column>
      <el-table-column prop="orderId" label="交付订单号" header-align="center"></el-table-column>
      <el-table-column prop="hours" label="交付学时数" header-align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.hours }}/时</div>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="交付学时金额" header-align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.amount }}/元</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="时间" header-align="center"></el-table-column>
      <el-table-column prop="studentName" label="交付学员" header-align="center" />
      <el-table-column prop="studentCode" label="学员编号" header-align="center" />
      <el-table-column v-if="isAdmin" prop="deliverMerchantName" label="所属交付中心" header-align="center" />
    </el-table>
    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="searchNum.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
    </el-row>

    <!-- 表头设置 -->
    <HeaderSettingsDialog @HeaderSettingsLister="HeaderSettingsLister" :HeaderSettingsStyle="HeaderSettingsStyle"
      :headerSettings="headerSettings" ref="HeaderSettingsDialog" @selectedItems="selectedItems" />
  </div>
</template>

<script>
import { pageDeliverHoursList } from "@/api/FinanceApi/Finance";
import { getTableTitleSet, setTableList, bvstatusList } from '@/api/paikeManage/classCard'
import FileSaver from "file-saver";
import XLSX from "xlsx";
import ls from "@/api/sessionStorage";
import HeaderSettingsDialog from '../../pclass/components/HeaderSettingsDialog.vue'

export default {
  name: 'deliverHours',
  components: {
    HeaderSettingsDialog
  },
  data() {
    return {
      // 日期组件
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              // const end = new Date();
              // const start = new Date();
              // picker.$emit('pick', [start, end]);
              const temp = new Date();
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0)),
              ]);
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const temp = new Date();
              temp.setTime(temp.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", [
                new Date(temp.setHours(0, 0, 0, 0)),
                new Date(temp.setHours(23, 59, 59, 0)),
              ]);
            },
          },
          {
            text: "最近七天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      reviewStyle: false,
      LeaveViewStyle: false,
      direction: "rtl", //超哪边打开
      LeaveId: "",
      searchNum: {
        endTime: "",
        startTime: "",
        merchantCode: "",
        orderId: "",
        curriculumId: '',
        pageNum: 1,
        pageSize: 10,
      }, //搜索参数
      tableLoading: false,
      teacher: "",
      timeAll: [],
      contentType: "",
      total: null,
      reviewLister: [],
      isAdmin: false,

      HeaderSettingsStyle: false, // 列表属性弹框
      headerSettings: [
        {
          name: '门店名称',
          value: 'merchantName'
        },
        {
          name: '门店商户号',
          value: 'merchantCode'
        },
        {
          name: '交付订单号',
          value: 'orderId'
        },
        {
          name: '交付学时数',
          value: 'hours'
        },
        {
          name: '交付学时金额',
          value: 'amount'
        },
        {
          name: '时间',
          value: 'createTime'
        },
        {
          name: '交付学员',
          value: 'studentName'
        },
        {
          name: '学员编号',
          value: 'studentCode'
        }],

      tableHeaderList: [], // 获取表头数据
      courseList: [],
    };
  },
  created() {
    this.isAdmin = ls.getItem("rolesVal") === "admin" || ls.getItem("rolesVal") === "JiaofuManager";
    if (this.isAdmin) {
      this.headerSettings.splice(0, 0, {
        name: '交付中心编号',
        value: 'deliverMerchant'
      })
      this.headerSettings.splice(this.headerSettings.length, 0, {
        name: '所属交付中心',
        value: 'deliverMerchantName'
      })
    }
    this.initData();
    this.getHeaderlist();
  },
  mounted() {
    this.getbvstatusList()
  },
  methods: {
    getbvstatusList() {
      bvstatusList({}).then((res) => {
        this.courseList = res.data;
      });
    },
    headerList() {
      if (this.tableHeaderList.length > 0) {
        this.$refs.HeaderSettingsDialog.checkList = this.tableHeaderList.map(item => item.value); // 回显
      }
      this.HeaderSettingsStyle = true;
    },
    HeaderSettingsLister(e) {
      this.HeaderSettingsStyle = e;
    },
    // 搜索
    initData01() {
      (this.searchNum.pageNum = 1),
        (this.searchNum.pageSize = 10),
        this.initData();
    },
    // 列表数据
    async initData() {
      // 判断为null的时候赋空
      if (!this.timeAll) {
        this.timeAll = [];
      }
      this.tableLoading = true;
      this.searchNum.startTime = this.timeAll[0];
      this.searchNum.endTime = this.timeAll[1];
      let { data } = await pageDeliverHoursList(this.searchNum);
      this.tableLoading = false;
      this.total = Number(data.totalItems);
      this.reviewLister = data.data;
    },
    statusClass(status) {
      switch (status) {
        case 1:
          return "";
        case 2:
          return "error";
      }
    },
    LeaveDialog(v) {
      this.LeaveViewStyle = v;
    },
    reviewDialog(v) {
      this.reviewStyle = v;
    },
    // 转文字
    teachingType(val) {
      if (val.teachingType == 1) {
        return "远程";
      } else if (val.teachingType == 2) {
        return "线下";
      } else if (val.teachingType == 3) {
        return "远程和线下";
      } else {
        return "暂无";
      }
    },
    studyStatus(val) {
      if (val.studyStatus == 0) {
        return "未复习";
      } else if (val.studyStatus == 2) {
        return "已复习";
      }
    },
    status(val) {
      if (val.status == 1) {
        return "正常";
      } else if (val.status == 2) {
        return "请假";
      } else if (val.status == 3) {
        return "请假已处理";
      }
    },
    reviewStatus(val) {
      if (val.reviewStatus == 0) {
        return "未复习";
      } else if (val.reviewStatus == 2) {
        return "已复习";
      }
    },
    courseType(val) {
      if (val.courseType == 1) {
        return "鼎英语";
      }
    },
    // 动态class
    getRowClass({ rowIndex, columnIndex }) {
      if (rowIndex == 0) {
        return "background:#f5f7fa";
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchNum.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.searchNum.pageNum = val;
      this.initData();
    },
    //定义导出Excel表格事件
    exportExcel() {
      /* 从表生成工作簿对象 */
      var wb = XLSX.utils.table_to_book(document.querySelector("#out-table"));
      /* 获取二进制字符串作为输出 */
      var wbout = XLSX.write(wb, {
        bookType: "xlsx",
        bookSST: true,
        type: "array",
      });
      try {
        FileSaver.saveAs(
          //Blob 对象表示一个不可变、原始数据的类文件对象。
          //Blob 表示的不一定是JavaScript原生格式的数据。
          //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
          //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
          new Blob([wbout], { type: "application/octet-stream" }),
          //设置导出文件名称
          "sheetjs.xlsx"
        );
      } catch (e) {
        if (typeof console !== "undefined") console.log(e, wbout);
      }
      return wbout;
    },

    //重置
    rest() {
      this.$refs.searchNum.resetFields();
      this.initData();
    },

    // 接收子组件选择的表头数据
    selectedItems(arr) {
      let data = {
        type: "deliverHours",
        value: JSON.stringify(arr),
      }
      this.setHeaderSettings(data);
    },


    // 获取表头设置
    async getHeaderlist() {
      let data = {
        type: 'deliverHours'
      }
      await getTableTitleSet(data).then(res => {
        if (res.data) {
          this.tableHeaderList = JSON.parse(res.data.value);
        } else {
          this.tableHeaderList = this.headerSettings;
        }
      })
    },

    // 设置表头
    async setHeaderSettings(data) {
      await setTableList(data).then(res => {
        this.$message.success("操作成功");
        this.HeaderSettingsStyle = false;
        this.getHeaderlist();
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.frame {
  margin-top: 0.5vh;
  background-color: rgba(255, 255, 255);
}
</style>
