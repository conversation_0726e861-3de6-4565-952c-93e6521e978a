import request from '@/utils/request'

  /**
   * 查询试课日期配置工作日周日节假日
   * @param data
   */export const findExperienceDateInfos = (data) => {
    return request({
      url: '/deliver/web/experience/findExperienceDateInfos',
      method: 'GET',
      params: data
    })
  }

    /**
   * 修改试课日期配置工作日周日节假日
   * @param data
   */export const updateExperienceDateInfos = (data) => {
    return request({
        url: '/deliver/web/experience/updateExperienceDateInfos',
        method: 'put',
        data
      })
    }
    /**
   * 查询可预约时间人数
   * @param data
   */export const findAllExperienceUsableTimeByType = (data) => {
    return request({
        url: '/deliver/web/experience/findAllExperienceUsableTimeByType',
        method: 'GET',
        params: data
      })
    }

    /**
   *设置可预约时间人数
   * @param data
   */export const setExperienceUsableTimeNumByType = (data) => {
    return request({
        url: '/deliver/web/experience/setExperienceUsableTimeNumByType',
        method: 'post',
        data
    })
    }
