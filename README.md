# 鼎校交付后台

## 项目信息

git 项目名称：**delivery_system**

[git 地址](https://codeup.aliyun.com/5ee323d293b16cdfea1276ae/dxznyy/delivery_system)

[仓库地址]: *********************:5ee323d293b16cdfea1276ae/dxznyy/delivery_system.git

## 项目目录

Ctrl +单击 跳转对应页面/目录

│ [App.vue](./src/App.vue) <!--主文件-->
│ [errorLog.js](./src/errorLog.js) <!--错误日志-->
│ [main.js](./src/main.js) <!--主文件-->
│ [permission.js](./src/permission.js) <!--权限控制-->
│ [settings.js](./src/settings.js) <!--设置-->
│
├─[api](./src/api) <!--接口api文件夹(后续开发建议按模块抽离封装接口文档)-->
│ │ [alibaba.js](./src/api/alibaba.js)
│ │ [auth.js](./src/api/auth.js)
│ │ [authentication.js]([./src/api/authentication.js)
│ │ [bstatus.js](./src/api/bstatus.js)
│ │ [deductionRconciliation.js](./src/api/deductionRconciliation.js)
│ │ ...
├─[assets](./src/assets) <!--静态资源-->
│
├─[components](./src/components) <!--组件-->
│ ├─BarChart <!--柱状图-->
│ ├─Breadcrumb <!--面包屑导航-->
│ ├─ErrorLog <!--错误日志-->
│ ├─Hamburger <!-- 折叠按钮-->
│ ├─LangSelect <!--语言切换-->
│ ├─ScrollPane <!--滚动条-->
│ ├─SizeSelect <!--尺寸选择-->
│ ├─SvgIcon <!--svg 图标-->
│ ├─Upload <!--上传-->
│ │ MyUpload.vue <!--图片上传-->
│ │ UploadFile.vue <!--文件上传-->
│ ├─UploadExcel <!--excel上传-->
│ ├─UploadVideo <!--视频上传-->
│ └─verifition <!--验证码-->
│
├─[directive](./src/directive) <!--指令文件夹-->
│ │ sticky.js <!--拖拽-->
│ │
│ ├─clipboard<!--复制-->
│ │
│ ├─el-drag-dialog <!--拖拽-->
│ │
│ ├─el-dragDialog <!--拖拽-->
│ │
│ ├─el-table <!--表格自适应-->
│ │
│ ├─lazy <!--图片懒加载-->
│ │
│ ├─permission <!--权限控制-->
│ │
│ └─waves <!--点击波纹效果-->
│
├─[filters](./src/filters) <!--过滤器-->
│
├─[icons](./src/icons) <!--svg 图标文件夹-->
│
├─[lang](./src/lang)<!--多语言文件-->
│
├─[layout](./src/layout)<!--布局-->
│ │ Layout.vue
│ │
│ ├─components <!--组件-->
│ │ │ AppMain.vue <!--主内容-->
│ │ │ index.js <!--动态路由主文件-->
│ │ │ Navbar.vue <!--顶部导航栏-->
│ │ │ TagsView.vue <!--标签页-->
│ │ │
│ │ └─Sidebar <!--侧边栏-->
│ │ FixiOSBug.js <!--屏幕自适应-->
│ │ index.vue <!--侧边栏主文件-->
│ │ Item.vue <!--菜单-->
│ │ Link.vue <!--链接-->
│ │ Logo.vue <!--logo-->
│ │ SidebarItem.vue <!--菜单-->
│ │
│ └─mixin
│ ResizeHandler.js <!--屏幕自适应-->
│
├─plugs <!--打印 js-->
│ print.js <!--打印js-->
│
├─[router](./src/router)<!--路由文件-->
│ │ index.js <!--静态路由及其他路由主文件-->
│ │ \_import_development.js <!--开发环境-->
│ │ \_import_production.js <!--生产环境-->
│ │
│ └─modules <!--不同模块动态路由进行统一抽离封装-->
│ TrialClassList.js <!--试课学员列表-->
│...
├─[store](./src/store) <!--vuex-->
│ │ getters.js <!--getters-->
│ │ index.js <!--vuex主文件-->
│ │
│ └─modules <!--vuex模块-->
│ app.js <!--用户信息-->
│ errorLog.js <!--错误日志-->
│ permission.js <!--权限控制-->
│ settings.js <!--设置-->
│ tagsView.js <!--标签页-->
│ user.js <!--用户信息-->
│
├─[styles](./src/styles)<!--全局样式封装-->
│ │ btn.scss <!--按钮样式-->
│ │ element-ui.scss <!--element-ui样式-->
│ │ element-variables.scss <!--element-ui变量-->
│ │ index.scss <!--全局样式-->
│ │ mixin.scss <!--mixin-->
│ │ sidebar.scss <!--侧边栏样式-->
│ │ transition.scss <!--过渡动画-->
│ │ variables.scss <!--全局变量-->
│ │
│ └─activiti <!--流程图样式-->
│
├─[utils](./src/utils)<!--公共方法-->
│ │ auth.js <!--权限控制-->
│ │ base64.js <!--base64-->
│ │ clipboard.js <!--复制-->
│ │ code.js <!--验证码-->
│ │ constants.js <!--参数-->
│ │ error-log.js <!--错误日志-->
│ │ get-page-title.js <!--页面标题-->
│ │ htmlToPdf.js <!--导出pdf-->
│ │ i18n.js <!--国际化-->
│ │ index.js <!--公共方法-->
│ │ moment.js <!--时间格式化-->
│ │ open-window.js <!--新窗口打开-->
│ │ permission.js <!--权限控制-->
│ │ request.js <!--axios封装-->
│ │ requestuser.js <!--用户信息-->
│ │ scroll-to.js <!--滚动条-->
│ │ validate.js <!--验证-->
│ │ widget.js <!--组件封装-->
│ │ zh-cn.js <!--多语言-->
│ │
│ └─directives <!--指令-->
│ └─loadmore <!--加载更多-->
│
├─[vendor](./src/vendor) <!--第三方插件-->
│ Export2Excel.js <!--导出excel-->
│ Export2Zip.js <!--导出zip-->
│
└─[views](./src/views) <!--页面-->
│
├─checkOrder <!--教练费退款审核-->
│
├─classCard <!--学习课程表-->
│ index.vue<!--学习课程表-->
│ writeFeedbook.vue <!--帮助教填写反馈-->
│
├─deductionRconciliation <!--合同扣款对账-->
│
├─downloadList <!--下载列表-->
│
├─error-log <!--错误日志-->
│ │
│ └─components
│ ErrorTestA.vue <!--错误日志测试-->
│ ErrorTestB.vue <!--错误日志测试-->
│
├─error-page <!--错误页面-->
│ │ 401.vue <!--错误日志测试-->
│ │ 404.vue <!--错误日志测试-->
│ │
│ └─vendor
│ Export2Excel.js <!--导出excel-->
│ Export2Zip.js <!--导出zip-->
│
├─Finance <!--财务管理-->
│ ├─assistantWages <!--工资管理-->
│ │
│ ├─coachTurnover <!--教练工资流水-->
│ │
│ ├─deliverHours <!--集中交付清单-->
│ │ index.vue<!--集中交付清单-->
│ │ studentDeliver.vue <!--学员集中交付流水-->
│ │
│ ├─personFinan <!--个人流水-->
│ │
│ ├─reviewSalaryPool <!--工资结算-->
│ │
│ └─trialclassDividend <!--分润设置-->
│
├─forgot <!--忘记密码-->
│ index.vue
│
├─layout <!--布局-->
│ │ Layout.vue
│ │
│ ├─components <!--组件-->
│ │ │ AppMain.vue <!--主内容-->
│ │ │ index.js
│ │ │ Navbar.vue <!--导航栏-->
│ │ │ TagsView.vue <!--标签页-->
│ │ │
│ │ └─Sidebar <!--侧边栏-->
│ │ FixiOSBug.js <!--屏幕自适应-->
│ │ index.vue <!--侧边栏-->
│ │ Item.vue <!--侧边栏子项-->
│ │ Link.vue <!--链接-->
│ │ Logo.vue <!--logo-->
│ │ SidebarItem.vue <!--侧边栏子项-->
│ │
│ ├─mixin <!--混入-->
│ │ ResizeHandler.js <!--屏幕自适应-->
│ │
│ └─personMe <!--个人中心-->
│ personMy.vue <!--个人信息-->
│ realName.vue <!--实名认证-->
│
├─login <!--登录-->
│ │ auth-redirect.vue <!--授权重定向-->
│ │ auto_login.vue <!--自动登录-->
│ │ index.vue <!--登录-->
│
├─message <!--消息管理-->
│ messageSetting.vue <!--消息配置-->
│
├─oneToManyClass <!--一对多学员管理-->
│ ├─components <!--组件-->
│ │ ├─classList <!--班级列表相关组件-->
│ │ │ ├─BaseMultipleTimeRange.vue <!--多时间段选择-->
│ │ │ └─ClassHistoryOrderDispatchDialog.vue <!--一对多-班级列表-历史派单记录弹框-->
│ │ │
│ │ ├─newClassList <!--新班级列表相关组件-->
│ │ │ ├─DispatchInProgress.vue <!--一对多-新班级列表-正课派单中-->
│ │ │ ├─DispatchInProgressTrial.vue <!--一对多-新班级列表-试课派单中-->
│ │ │ ├─WaitingBuildingClass.vue <!--一对多-新班级列表-正课等待成班中-->
│ │ │ └─WaitingBuildingClassTrial.vue <!--一对多-新班级列表-试课等待成班中-->
│ │ │
│ │ ├─pendingCompletionClassInfo <!--新班级列表相关组件-->
│ │ │ └─BaseClassStudyTimeSelect.vue <!--查询班级上课时间下拉框组件-->
│ │ │
│ │ ├─studentList <!--学员列表相关组件-->
│ │ │ ├─BaseElSelectLoadmore.vue <!--分页下拉加载组件-->
│ │ │ ├─StudentListAssignClassDialog.vue <!--一对多-学生列表-指派班级弹窗-->
│ │ │ └─StudentListClassConnectionDialog.vue <!--一对多-学生列表-上课信息对接表-->
│ │ │
│ │ ├─studySchedule <!--学习课程表相关组件-->
│ │ │ └─StudyScheduleDataLook.vue <!--一对多-学习课程表-数据查看弹窗-->
│ │ │
│ │ ├─ElInputTextNumber.vue <!--输入框数字格式化组件-->
│ │ ├─FormalClass.vue <!--一对多-新班级列表-上课信息对接表弹窗-->
│ │ ├─lodash.js <!--lodash-->
│ │ ├─statistic.vue <!--倒计时组件-->
│ │ └─TrialClass.vue <!--一对多-新班级列表-试课单弹窗-->
│ │
│ ├─ClassList.vue <!--一对多-班级列表-->
│ ├─classTimeManagement.vue <!--一对多-班级上课时间管理-->
│ ├─newClassList.vue <!--一对多-新班级列表-->
│ ├─PendingCompletionClassInfo.vue <!--一对多-试课单与待完善上课信息表-->
│ ├─PendingCompletionClassInfoFormal.vue <!--一对多-试课单与待完善上课信息表-正课-->
│ ├─PendingCompletionClassInfoTrial.vue <!--一对多-试课单与待完善上课信息表-试课-->
│ ├─StudentList.vue <!--一对多-学员列表-->
│ ├─StudentListFormal.vue <!--一对多-学员列表-正课-->
│ ├─StudentListTrial.vue <!--一对多-学员列表-试课-->
│ └─StudySchedule.vue <!--一对多-学习课程表-->
│
├─orderManage <!--接单管理-->
│ ├─components <!--组件-->
│ │ lodash.js <!--lodash-->
│ │ statistic.vue <!--倒计时组件-->
│ │
│ ├─deliveryTeam <!--交付小组配置-->
│ │
│ ├─formalStudent <!--正式课新学员列表-->
│ │
│ ├─lossTable <!--流失单列表-->
│ │

│ ├─orderAuditing <!--承单量审核-->
│ │
│ ├─orderSetting <!--承单量配置-->
│ │
│ ├─StoreteamConfig <!--门店小组配置-->
│ │
│ ├─testStudent <!--试课新学员列表-->
│ │
│ └─turnSort <!--轮排配置-->
│
├─pclass
│ │ changeTeamAud.vue <!--更换交付小组审核-->
│ │ classInformation.vue <!--待完善上课信息表-->
│ │ continue.vue<!--低交付课时学员列表-->
│ │ continueAdmin.vue <!--续费提醒列表-->
│ │ Coursefeedback.vue <!--课程反馈处理-->
│ │ deliveryChecklist.vue <!--正式学员交付清单-->
│ │ index.vue <!--正式学员列表-->
│ │ reviewList.vue <!--复习课程表-->
│ │ reviewSchedule.vue <!--待完善复习时间表-->
│ │ shareProfitList.vue <!--试课奖励分润名单-->
│ │ StudentFeedback.vue <!--试课满意度列表-->
│ │ trialClassConfigure.vue <!--试课预约人数配置-->
│ │ TrialClassList.vue <!--试课学员列表-->
│ │ TrialClassListNopay.vue <!--试课学员未支付列表-->
│ │ TrialClassOrderList.vue <!--待完善试课信息表-->
│ │
│ └─components <!--组件-->
│ changeClassList.vue <!--试课记录表-->
│ classCardDialog.vue <!--学员课程表弹窗-->
│ CoursefeedbackDialog.vue <!--课程反馈 数据查看-->
│ CreateLessonTestForm.vue <!--填写试课单-->
│ dataLookDialog.vue <!--体验结果反馈-->
│ ExperienceClassDialog.vue <!--体验排课-->
│ FeedbackDialog.vue <!--数据报警处理-->
│ FeedbackListDialog.vue <!--数据报警处理-->
│ HeaderSettingsDialog.vue <!--表头设置-->
│ LeaveLookDialog.vue <!--请假处理-->
│ LeaveProcessingDialog.vue <!--请假处理-->
│ numberLookDialog.vue <!--数据查看-->
│ paikeDialog.vue <!--排课弹窗-->
│ reviewListLookDialog.vue <!--复习列表查看弹窗-->
│ studentFeedBackDetail.vue <!--学员反馈详情-->
│ studentFeedBackReply.vue <!--反馈回复-->
│ studentsList.vue <!--学员信息表-->
│ studentTransfer.vue <!--学员转移-->
│ sumlookDialog.vue <!--课程反馈查看-->
│ testReport.vue <!--体验反馈结果-->
│ translateDialog.vue <!--学员档案打印-->
│ trialDate.vue <!--日期选择器-->
│
├─peizhi <!--交付中心配置-->
│ index.vue
│
├─redirect <!--重定向-->
│ index.vue
│
├─refundReview <!--甄选退款审核-->
│ index.vue
│
├─robotSetting <!--机器人设置-->
│ index.vue
│
├─ruku <!--入库-->
│ │ assistManage.vue <!--教练管理-->
│ │ index.vue
│ │ preparationCoachReview.vue <!--预备教练审核-->
│ │ screenGonfiguration.vue <!--录屏配置-->
│ │ teacherGrade.vue <!--教练等级设置-->
│ │
│ ├─components
│ │ preCoach.vue <!--人员入库-教练页面header-->
│ │ teamLeader.vue <!--人员入库-小组组长页面header-->
│ │ xueguanshi.vue <!--人员入库-学管师页面header-->
│ │ zhujiaoshi.vue <!--人员入库-助教页面header-->
│ │
│ ├─dialog
│ │ tiaokeDialog.vue <!--调课-->
│ │
│ ├─preparationCoachReview <!--预备教练审核-->
│ │ index.vue
│ │
│ ├─xueguan <!--学管-->
│ │ index.vue
│ │
│ ├─zhujiao <!--助教-->
│ │ index.vue
│ │
│ └─zuzhang <!--组长-->
│ index.vue
│
├─students <!--学员管理-->
│ │ areasOpenCourse.vue <!--开通课程-->
│ │ areasOpenListenCourse.vue <!--开通全能听力课程-->
│ │ areasStudentCourseFlow.vue <!--学员销课记录-->
│ │ areasStudentCourseRecord.vue <!--学员课程记录-->
│ │ areasStudentTestResultList.vue <!--学员词汇测试-->
│ │ areasStudentWordPrintList.vue <!--学员测验打印-->
│ │ areaStudentWordReviewPrint.vue <!--21天抗遗忘复习计划-->
│ │ assistantFlowList.vue <!--学员流水记录-->
│ │
│ └─components
│ studentTestPrint.vue <!--学员测验打印-结业报告-->
│ studentTestPrintReading.vue <!--学员测验打印-阅读理解-->
│ studentTestPrintReport.vue <!--学员测验打印-结业报告-->
│ studentWordReviewList.vue <!--21天抗遗忘记录-->
│ studentWordReviewPrint.vue <!--21天抗遗忘打印-->
│ studentWordsTest.vue <!--学员词汇测试-->
│ studentWordViewList.vue <!--查看详情-->
│
├─system <!--系统管理-->
│ │ tree.js
│ ├─perm <!--权限管理-->
│ │
│ ├─role <!--角色管理-->
│ │
│ └─user <!--用户管理-->

├─trainingCenter <!--学习中心-->
│ ├─learnCenter <!--学习中心跳转页面-->
│ │ learnCenter.vue <!--学习中心跳转页面-->
│
├─transferOrder <!--资转订单-->
│ index.vue
