import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const rukuRouter = {
  path: '/ruku/index',
  component: Layout,
  name: 'classes',
  meta: {
    perm: 'm:user:ruku',
    title: '入库管理',
    icon: 'divisionList',
    noCache: false
  },
  children: [
    {
      path: 'ruku_index',
      component: _import('ruku/index'),
      name: 'rukuperson',
      meta: {
        perm: 'm:ruku:index',
        title: '人员入库',
        icon: 'divisionList',
        noCache: false
      }
    },
    {
      path: '/ruku/assistManage',
      component: _import('ruku/assistManage'),
      name: 'assistManage',
      meta: {
        perm: 'm:ruku:assistManage',
        title: '教练管理',
        icon: 'el-icon-s-custom',
        noCache: false
      }
    },
    {
      path: '/ruku/teacherGrade',
      component: _import('ruku/teacherGrade'),
      name: 'teacherGrade',
      meta: {
        perm: 'm:ruku:teacherGrade',
        title: '教练等级设置',
        icon: 'el-icon-s-custom',
        noCache: false
      }
    },
    {
      path: '/ruku/screenGonfiguration',
      component: _import('ruku/screenGonfiguration'),
      name: 'screenGonfiguration',
      meta: {
        perm: 'm:ruku:screenGonfiguration',
        title: '录屏配置',
        icon: 'el-icon-s-custom',
        noCache: false
      }
    },
    /* 预备教练入库审核 */
    {
      path: '/ruku/preparationCoachReview',
      component: _import('ruku/preparationCoachReview'),
      name: 'preparationCoachReview',
      meta: {
        perm: 'm:ruku:preparationCoachReview',
        title: '预备教练审核',
        icon: 'el-icon-s-custom',
        noCache: false
      }
    }
  ]
};

export default rukuRouter;
