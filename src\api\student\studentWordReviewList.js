/**
 * 学员管理相关接口
 */
 import request from '@/utils/request'

 export default {
   // 分页查询
   testResultList(pageNum, pageSize, studentCode, data) {
     console.log(data)
     return request({
       url: '/znyy/areas/student/course/student/word/review/' + pageNum + '/' + pageSize+'/'+studentCode,
       method: 'GET',
       params: data
     })
   },
   // 查询
 /*  queryActive(id) {
     return request({
       url: '/znyy/areas/student/course/student/word/check/' + id,
       method: 'GET'
     })
   },*/
 
   getStudyRank() {
     return request({
       url: '/znyy/areas/student/test/result/study/rank',
       method: 'GET',
     })
   },
 
 
 
 }
 