<!--交付中心-下载列表-->
<template>
  <div>
    <!-- 下载列表 -->
    <div style="padding: 0 0 20px 0">
      <div class="btn-add">
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="initData">刷新</el-button>
      </div>
      <!-- 暂无数据 -->
      <div class="nomore" v-if="tableData.length == 0">
        <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
        <div style="color: #999; margin-top: 20px">无数据</div>
      </div>
      <div class="" style="padding: 0 10px" v-if="tableData.length > 0">
        <el-table v-loading="loading" :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
          <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center">
            <template v-slot="{ row }">
              <div v-if="item.value == 'operate'">
                <el-button type="primary" size="mini" v-if="row.status == 20" @click="getDetail(row.filePath)" :loading="downLoading">下载</el-button>
                <span v-if="row.status == 10" style="color: #db8d2f">{{ row.statusDesc }}</span>
                <span v-if="row.status == 30" style="color: #fe4848">{{ row.statusDesc }}</span>
              </div>
              <span v-else>{{ row[item.value] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页器 -->
        <el-row type="flex" justify="center" align="middle" style="height: 60px">
          <!-- 3个变量：每页数量、页码数、总数  -->
          <!-- 2个事件：页码切换事件、每页数量切换事件-->
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="querydata.pageNum"
            :page-sizes="[10, 20, 30, 40, 50]"
            :page-size="querydata.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
  import { getDownloadList } from '@/api/downloadList';
  export default {
    name: 'downloadList',
    data() {
      return {
        isAdmin: false,
        loading: false,
        downLoading: false,
        querydata: {
          pageNum: 1,
          pageSize: 10 //页容量
        },
        // 表格头
        tableHeaderList: [
          {
            name: '创建时间',
            value: 'createTime'
          },
          {
            name: '列表名称',
            value: 'listTypeDesc'
          },
          {
            name: '操作',
            value: 'operate'
          }
        ],
        total: 0,
        //表格分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableLoading: false,
        tableData: [] //表格数据
      };
    },
    created() {
      this.initData();
    },
    methods: {
      // 下载
      async getDetail(id) {
        this.downLoading = true;
        window.open(id, '_blank');
        this.downLoading = false;
      },
      // 初始化列表
      async initData() {
        let that = this;
        this.loading = true;
        let { data } = await getDownloadList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        this.loading = false;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },

      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      // 分页
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      statusClass(status) {
        switch (status) {
          case 0:
            return '';
          case 1:
            return 'normal';
          case 2:
            return 'error';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn-add {
    // margin-top: 20px;
    padding: 20px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    cursor: unset !important;
  }

  // 自定义表格边框
  ::v-deep #out-table .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:first-child {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(8) {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep .el-dialog__title {
    font-size: 14px;
    font-weight: 600;
  }
  ::v-deep .el-dialog__headerbtn {
    background-color: #ccc;
    border-radius: 50%;
  }
</style>
