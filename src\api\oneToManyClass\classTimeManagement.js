// 班级上课时间管理 相关接口
import request from '@/utils/request';

// 获取班级上课时间列表
export const getClassTimeList = (params) =>
  request({
    url: '/deliver/web/classSchooltimeMangement/getClassSchooltimeList',
    method: 'get',
    params
  });

// 新增班级上课时间

export const addClassTime = (data) =>
  request({
    url: '/deliver/web/classSchooltimeMangement/saveClassTime',
    method: 'post',
    data
  });

// 修改班级上课时间

export const updateClassTime = (data) =>
  request({
    url: '/deliver/web/classSchooltimeMangement/updateClasTime',
    method: 'post',
    data
  });

// 修改班级上课时间 状态
export const updateClassTimeStatus = (params) =>
  request({
    url: '/deliver/web/classSchooltimeMangement/blockUp',
    method: 'post',
    params
  });

// 获取1v多课程大类
export const getOneToMoreClassList = () => {
  return request({
    url: '/znyy/curriculum/allNew',
    method: 'GET',
    params: {
      curriculumType: 1
    }
  });
};
