<template>
  <el-tabs v-model="orderSettingType">
    <el-tab-pane label="1V1" name="oneToOne">
      <!-- 交付中心-接单管理-承单量配置-1v1 -->
      <div v-if="orderSettingType === 'oneToOne'">
        <!-- 管理员页面 -->
        <div v-if="isAdmin">
          <div class="app-container frame">
            <el-form label-width="110px" ref="querydata" :model="querydata">
              <el-row>
                <el-col :span="4">
                  <el-form-item label="交付中心编号:">
                    <el-input v-model="querydata.deliverCode" @change="changeInput" size="small" placeholder=""></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="交付中心名称:">
                    <el-select
                      size="small"
                      v-el-select-loadmore="handleLoadmore"
                      :loading="loadingShip"
                      remote
                      clearable
                      v-model="querydata.deliverName"
                      filterable
                      reserve-keyword
                      placeholder="请选择"
                      @input="changeMessage"
                      @blur="clearSearchRecord"
                      @change="changeTeacher"
                    >
                      <el-option v-for="(item, index) in option" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="padding-left: 20px">
                  <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
                  <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div style="height: 20px; background: #f7f8fc; margin-bottom: 30px"></div>

          <el-table :data="tableData" style="width: 100%" id="out-table" :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
            <el-table-column header-align="center" align="center" prop="deliverName" label="交付中心名称"></el-table-column>
            <el-table-column header-align="center" align="center" prop="deliverMerchant" label="交付中心编号"></el-table-column>
            <!-- 课程类型1 -->
            <el-table-column header-align="center" label="课程类型" align="center" v-if="courseLength > 0">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[0].curriculumName }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="试课承单量" align="center" v-if="courseLength > 0">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[0].experienceNum }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="正式课承单量" align="center" v-if="courseLength > 0">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[0].learnNum }}</span>
              </template>
            </el-table-column>
            <!-- 课程类型1 -->
            <!-- 课程类型2 -->
            <el-table-column header-align="center" label="课程类型" align="center" v-if="courseLength > 1">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[1].curriculumName }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="试课承单量" align="center" v-if="courseLength > 1">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[1].experienceNum }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="正式课承单量" align="center" v-if="courseLength > 1">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[1].learnNum }}</span>
              </template>
            </el-table-column>
            <!-- 课程类型2 -->
            <!-- 课程类型3 -->
            <el-table-column header-align="center" label="课程类型" align="center" v-if="courseLength > 2">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[2].curriculumName }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="试课承单量" align="center" v-if="courseLength > 2">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[2].experienceNum }}</span>
              </template>
            </el-table-column>
            <el-table-column header-align="center" label="正式课承单量" align="center" v-if="courseLength > 2">
              <template v-slot="{ row }">
                <span>{{ row.deliverCurriculumConfigVoList[2].learnNum }}</span>
              </template>
            </el-table-column>
            <!-- 课程类型3 -->
            <el-table-column header-align="center" align="center" label="操作">
              <template v-slot="{ row }">
                <el-button type="primary" size="mini" v-if="isAdmin" @click="getDetail(row.id)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页器 -->
          <el-row type="flex" justify="center" align="middle" style="height: 60px">
            <!-- 3个变量：每页数量、页码数、总数  -->
            <!-- 2个事件：页码切换事件、每页数量切换事件-->
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="querydata.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="querydata.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </el-row>
          <!-- 审核抽屉 -->
          <el-drawer :with-header="false" :visible.sync="adminDrawer" direction="rtl" size="90%" :before-close="handleClose1">
            <div class="setting">
              <div class="closeIcon" @click="handleClose1">X</div>
              <div class="formItem">
                <div class="label" style="width: 100px; margin-right: 10px">交付中心名称:</div>
                <el-input v-model="drawForm.deliverName" disabled size="small" style="width: 200px" placeholder=""></el-input>
              </div>
              <div class="formItem">
                <div class="label" style="width: 100px; margin-right: 10px">交付中心编号:</div>
                <el-input v-model="drawForm.deliverMerchant" disabled size="small" style="width: 200px" placeholder=""></el-input>
              </div>

              <div class="form">
                <div v-for="(item, index) in formList1" :key="index">
                  <div class="formItem">
                    <div class="label">课程类型:</div>
                    <el-input v-model="item.curriculumName" disabled size="small" class="input" placeholder=""></el-input>
                  </div>
                  <div class="formItem">
                    <div class="formItem">
                      <div class="label">试课:</div>
                      <el-input v-model="item.experienceNum" :min="0" size="small" class="input" type="number" disabled placeholder="">
                        <template slot="suffix">单</template>
                      </el-input>
                    </div>
                    <div class="formItem">
                      <div class="label">正式课:</div>
                      <el-input v-model="item.learnNum" :min="0" size="small" type="number" class="input" disabled placeholder="">
                        <template slot="suffix">单</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
              <h5>预约人数设置</h5>
              <div class="table">
                <div v-for="(item, index) in timeList1" :key="index">
                  <div style="display: flex; align-items: center">
                    <span style="margin-right: 20px; width: 100px">
                      {{ item.usableHourStart + ':' + item.usableMinuteStart + ' ~' + item.usableHourEnd + ':' + item.usableMinuteEnd }}
                    </span>
                    <el-input-number v-model="item.canReserveNum" :min="0" label="请选择" size="mini" disabled :controls="false"></el-input-number>
                  </div>
                </div>
              </div>
            </div>
          </el-drawer>
        </div>
        <!-- 交付中心页面 -->
        <div style="padding: 0 0 20px 0" v-else>
          <!-- 申请中通知条 -->
          <div class="notice" v-if="configData.applyStatus == 2">
            <div class="notice-title">
              <div class="notice-title1">承单量申请中</div>
              <div class="notice-title2">如需修改：请先撤销后修改</div>
            </div>
            <div class="notice-btns">
              <el-button size="small" type="primary" @click="getConfigDetail">查看详情</el-button>
              <el-button type="warning" v-if="configData.applyStatus == 2" @click="revoke">撤销</el-button>
            </div>
          </div>
          <!-- 被拒绝通知条 -->
          <div v-else>
            <div class="notice" v-if="noticeShow && (configData.status == 1 || configData.applyStatus == 4)">
              <div style="display: flex; align-items: center">
                <i class="el-icon-warning" style="color: red; font-size: 20px; margin-right: 10px" v-if="configData.applyStatus == 4"></i>
                <i class="el-icon-success" style="color: green; font-size: 20px; margin-right: 10px" v-else></i>
                <div class="notice-title">
                  <div class="notice-title1">{{ configData.applyStatus == 4 ? '承单量申请未通过，请重新申请' : '承单量申请已通过，并且已生效' }}</div>
                  <div class="notice-title2" v-if="configData.applyStatus == 4">{{ '拒绝理由：' + configData.remark }}</div>
                </div>
              </div>
              <div class="notice-btns">
                <div class="closeIcon" @click="closeNotice">X</div>
              </div>
            </div>
          </div>
          <!-- 申请按钮 -->
          <div class="btn-add" style="margin-bottom: 10px" v-if="configData.applyStatus != 2">
            <el-button size="small" type="primary" @click="clickAdd">承单量申请</el-button>
          </div>
          <!-- 暂无数据 -->
          <div class="nomore" v-if="configData.status == 0">
            <el-image style="width: 100px; height: 100px" src="https://document.dxznjy.com/automation/1728442200000"></el-image>
            <div style="color: #999; margin-top: 20px">无数据</div>
          </div>
          <div class="" style="padding-left: 10px" v-if="configData.status == 1">
            <el-table :data="formList2" style="width: 100%" :header-cell-style="getRowClass">
              <el-table-column align="center" prop="curriculumName" label="课程类型"></el-table-column>
              <el-table-column align="center" prop="experienceNum" label="试课单承单量"></el-table-column>
              <el-table-column align="center" prop="learnNum" label="正式课单承单量"></el-table-column>
              <el-table-column align="center" label="状态">
                <template>
                  <span :class="statusClass(configData.status)">{{ '生效中' }}</span>
                </template>
              </el-table-column>
            </el-table>
            <h5>预约人数设置</h5>
            <div class="table">
              <div v-for="(item, index) in timeList2" :key="index">
                <div style="display: flex; align-items: center">
                  <span style="margin-right: 20px; width: 100px; text-align: right">
                    {{ item.usableHourStart + ':' + item.usableMinuteStart + ' ~' + item.usableHourEnd + ':' + item.usableMinuteEnd }}
                  </span>
                  <el-input-number v-model="item.canReserveNum" :min="0" label="请选择" size="mini" disabled :controls="false"></el-input-number>
                  <!-- <el-input v-model="item.canReserveNum" size="small" style="width:120px" disabled
                placeholder=""></el-input> -->
                </div>
              </div>
            </div>
          </div>
          <!-- 承单量配置 -->
          <el-drawer :with-header="false" :visible.sync="drawer" :wrapperClosable="false" direction="rtl" size="90%" :before-close="handleClose">
            <div class="setting" v-loading="loading">
              <div class="closeIcon" @click="handleClose">X</div>
              <h5>承单量申请</h5>
              <div class="form">
                <div v-for="(item, index) in formList" :key="index">
                  <div class="formItem">
                    <div class="label">课程类型:</div>
                    <el-input v-model="item.curriculumName" disabled size="small" class="input" placeholder=""></el-input>
                  </div>
                  <div class="formItem">
                    <div class="formItem">
                      <div class="label">试课:</div>
                      <!-- <el-input v-model="item.experienceNum" :min="0" size="small" class="input" type="number"
                    :disabled="configData.applyStatus==2" placeholder=""> -->
                      <!-- </el-input> -->
                      <el-input-number v-model="item.experienceNum" :min="0" size="small" class="input" :disabled="configData.applyStatus == 2" :controls="false"></el-input-number>
                      <span>单</span>
                    </div>
                    <div class="formItem">
                      <div class="label">正式课:</div>
                      <!-- <el-input v-model="item.learnNum" :min="0" size="small" type="number" class="input"
                    :disabled="configData.applyStatus==2" placeholder="">
                    <template slot="suffix">单</template>
                  </el-input> -->
                      <el-input-number v-model="item.learnNum" :min="0" size="small" class="input" :disabled="configData.applyStatus == 2" :controls="false"></el-input-number>
                      <span>单</span>
                    </div>
                  </div>
                </div>
              </div>
              <h5>预约人数设置</h5>
              <div class="table">
                <div v-for="(item, index) in timeList" :key="index">
                  <div style="display: flex; align-items: center">
                    <span style="margin-right: 20px">{{ item.usableHourStart + ':' + item.usableMinuteStart + ' ~' + item.usableHourEnd + ':' + item.usableMinuteEnd }}</span>
                    <el-input-number
                      v-model="item.canReserveNum"
                      :min="0"
                      label="请选择"
                      size="mini"
                      :disabled="configData.applyStatus == 2"
                      :controls="configData.applyStatus != 2"
                    ></el-input-number>
                  </div>
                </div>
              </div>
              <div class="btns">
                <el-button type="primary" plain style="width: 100px" @click="drawer = false" v-if="configData.applyStatus == 0 || configData.applyStatus == 4">取消</el-button>
                <el-button type="primary" style="width: 100px" v-if="configData.applyStatus == 0 || configData.applyStatus == 4" @click="applyFn">申请</el-button>
                <el-button type="warning" style="width: 100px" v-if="configData.applyStatus == 2" @click="revoke">撤销</el-button>
              </div>
            </div>
          </el-drawer>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="1V多" name="oneToMany" v-if="isDeliveryCenter" lazy>
      <!-- 交付中心-接单管理-承单量配置-1v多 -->
      <OneToManyOrderSetting v-if="orderSettingType === 'oneToMany'" />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import { pageParamNames } from '@/utils/constants';
  import { getCurrentConfig, saveConfig, revokeConfig, getList, getCourseCateList, getByConfigId } from '@/api/orderManage';
  import { deliverlist } from '@/api/peizhi/peizhi';
  import OneToManyOrderSetting from '@/views/orderManage/orderSetting/OneToManyOrderSetting';
  export default {
    name: 'orderSetting',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    components: { OneToManyOrderSetting },
    data() {
      return {
        orderSettingType: 'oneToOne',
        isAdmin: false,
        isDeliveryCenter: false,
        loading: false,
        querydata: {
          pageNum: 1,
          pageSize: 10, //页容量
          deliverCode: '',
          deliverName: ''
        },
        //抽屉-表
        drawForm: {
          deliverName: '',
          deliverMerchant: ''
        },
        // 表格头
        tableHeaderList: [
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '审核状态',
            value: 'status'
          },
          {
            name: '申请时间',
            value: 'name'
          },
          {
            name: '拒绝原因',
            value: 'mobile'
          }
        ],
        total: 0,
        //表格分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        drawer: false, //抽屉显示与否
        adminDrawer: false, //抽屉显示与否
        timeList: [], //预约人数
        formList: [], // 课程类型
        timeList1: [], //预约人数
        formList1: [], // 课程类型
        timeList2: [], //预约人数
        formList2: [], // 课程类型
        tableLoading: false,
        tableData: [], //表格数据
        configData: {},
        // 新增课程
        addCourseData: {
          categoryName: '',
          isPay: '0'
        },
        noticeShow: true,
        courseLength: 0,
        loadingShip: false,
        option: [],
        optionTotal: 1,
        selectObj: {
          pageNum: 1,
          pageSize: 20
        }
      };
    },
    watch: {
      formList: {
        immediate: true,
        deep: true,
        handler(val) {
          // console.log(val)
          val.forEach((item) => {
            // console.log(item.canReserveNum)
            if (item.experienceNum == undefined || item.experienceNum == '') {
              // console.log(item)
              item.experienceNum = 0;
            }
            if (item.learnNum == undefined || item.learnNum == '') {
              // console.log(item)
              item.learnNum = 0;
            }
          });
        }
      },
      timeList: {
        immediate: true,
        deep: true,
        handler(val) {
          val.forEach((item) => {
            if (item.canReserveNum === undefined || item.canReserveNum === '') {
              // console.log(item.canReserveNum, '================')
              item.canReserveNum = 0;
            }
          });
        }
      }
    },
    created() {
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      // 是否是交付中心
      this.isDeliveryCenter = ls.getItem('rolesVal') == 'DeliveryCenter';
      // this.getReservationTime()
      if (this.isAdmin) {
        this.initData();
        this.getCourseList();
        this.getTeacherList();
      } else {
        this.getSetting();
      }
    },
    methods: {
      changeNum(e) {
        console.log(e);
        if (e == 'undefined') {
          e = 0;
        }
      },
      async getConfigDetail() {
        const { data } = await getByConfigId(this.configData.auditingId);
        this.timeList = data.deliverUsableTimeConfigVoList;
        this.formList = data.deliverCurriculumConfigVoList;
        setTimeout(() => {
          this.drawer = true;
        }, 500);
      },
      // 管理员查看详情
      async getDetail(id) {
        const { data } = await getByConfigId(id);
        this.drawForm = data;
        this.timeList1 = data.deliverUsableTimeConfigVoList;
        this.formList1 = data.deliverCurriculumConfigVoList;
        setTimeout(() => {
          this.adminDrawer = true;
        }, 500);
      },
      //  获取课程类型列表长度
      async getCourseList() {
        const { data } = await getCourseCateList({
          pageNum: 1,
          pageSize: 10
        });
        // console.log(data.totalItems)
        this.courseLength = data.totalItems;
      },
      // 获取配置
      async getSetting() {
        let { data } = await getCurrentConfig();
        this.timeList = data.deliverUsableTimeConfigVoList;
        this.formList = data.deliverCurriculumConfigVoList;
        this.timeList2 = data.deliverUsableTimeConfigVoList;
        this.formList2 = data.deliverCurriculumConfigVoList;
        let obj = {
          applyStatus: data.applyStatus, //状态 1:适用中 2审核中 3已撤销 4已拒绝
          auditingId: data.auditingId, //	审核中数据id
          remark: data.remark, //拒绝原因
          status: data.status //拒绝原因
        };
        this.configData = obj;
      },
      searchData() {
        this.querydata.pageNum = 1;
        this.querydata.pageSize = 10;
        this.initData();
      },
      // 初始化列表
      async initData() {
        let that = this;
        let { data } = await getList(this.querydata);
        this.tableData = data.data;

        this.total = Number(data.totalItems);
        // pageParamNames.forEach(name => that.$set(that.tablePage, name, parseInt(data[name])))
      },
      // 申请
      applyFn() {
        let isEmpty = this.formList.some((item) => item.experienceNum != 0);
        let isTrue = this.timeList.every((i) => i.canReserveNum == 0);
        if (isEmpty) {
          if (isTrue) {
            // return this.$message.warning('预约试课人数设置至少有一个不能为0!')
            return this.$message.warning('需要设置试课预约人数');
          }
        }

        let obj = {
          deliverCurriculumConfigDtoList: this.formList,
          deliverUsableTimeConfigDtoList: this.timeList
        };
        saveConfig(JSON.stringify(obj)).then((res) => {
          this.loading = true;
          this.$message.success('申请成功');
          this.handleClose();
        });
      },
      // 撤销
      async revoke() {
        let id = this.configData.auditingId;
        await this.$confirm('您确定撤销申请吗?');
        await revokeConfig(id);
        this.$message.success('撤销成功');
        this.noticeShow = false;
        this.handleClose();
      },
      closeNotice() {
        this.noticeShow = false;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // // 抽屉打开-调接口拿数据
      // beforeOpen() {
      //   // this.getReservationTime()
      //   this.loading = false
      // },
      // 管理员--关闭抽屉
      handleClose1() {
        this.drawForm = {
          deliverName: '',
          deliverMerchant: ''
        };
        this.adminDrawer = false;
        this.loading = false;
      },
      // 抽屉关闭
      handleClose() {
        this.getSetting();
        this.drawer = false;
        this.loading = false;
      },
      //添加操作
      clickAdd() {
        this.drawer = true;
      },
      // 重置
      rest() {
        this.querydata = {
          pageNum: 1,
          pageSize: 10, //页容量
          deliverCode: '',
          deliverName: ''
        };
        this.initData();
      },
      // 获取交付中心
      async getTeacherList() {
        let allData = await deliverlist(this.selectObj);
        this.option = this.option.concat(allData.data.data);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 失去焦点
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.deliverName == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      // 改变交付中心编号同时改变交付中心名称
      changeInput(e) {
        console.log(e);
        if (!!e) {
          let arr = this.option.filter((i) => i.deliverMerchantCode == e);
          this.querydata.deliverName = arr[0].deliverMerchantName;
        }
      },
      // 改变交付中心名称同时改变交付中心编号
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.querydata.deliverCode = '';
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        } else {
          let arr = this.option.filter((i) => i.deliverMerchantName == e);
          this.querydata.deliverCode = arr[0].deliverMerchantCode;
        }
      },
      // 刷新
      changeMessage() {
        this.$forceUpdate();
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      // 分页
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      },
      statusClass(status) {
        switch (status) {
          case 0:
            return '';
          case 1:
            return 'normal';
          case 2:
            return 'error';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-tabs__header {
    margin-top: 20px;
    margin-left: 30px;
  }
  ::v-deep .el-tabs__nav-wrap::after {
    width: 0;
  }
  .setting {
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow-y: auto;
    .closeIcon {
      position: absolute;
      right: 100px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
    }
  }
  ::v-deep .el-drawer__body {
    overflow-y: auto !important;
  }
  .table {
    width: 70%;
    display: grid;
    font-size: 14px;
    grid-template-columns: repeat(4, 1fr); /* 将容器分为4列，每列平均占据剩余空间 */
    grid-gap: 10px; /* 设置格子之间的间隔 */
    grid-row-gap: 35px;
  }
  .form {
    width: 65%;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 将容器分为4列，每列平均占据剩余空间 */
    grid-gap: 10px; /* 设置格子之间的间隔 */
    grid-row-gap: 35px;
  }

  .formItem {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-top: 10px;
    .input {
      // width: 5%;
      margin-left: 10px;
    }
    .label {
      text-align: right;
      width: 80px;
    }
  }
  ::v-deep .el-input__suffix {
    color: #000;
    top: 8px;
  }
  .btns {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  .nomore {
    width: 100%;
    height: 100%;
    padding-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .notice {
    // position: absolute;
    // top: 0;
    // left: 0;
    background-color: #ffefe8;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    .notice-title1 {
      color: #000;
      font-size: 14px;
    }
    .notice-title2 {
      color: #c3c3c3;
      font-size: 12px;
      margin-top: 10px;
    }
    .notice-btns {
      display: flex;
      align-items: center;
    }
    .closeIcon {
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
    }
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }
  ::v-deep .el-input.is-disabled .el-input__inner {
    cursor: unset !important;
  }

  // 自定义表格边框
  ::v-deep #out-table .el-table__header-wrapper th {
    border-top: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:nth-child(1) {
    border-left: 1px solid #d3dce6 !important;
  }

  ::v-deep #out-table .el-table__header-wrapper th:nth-child(2),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(5),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(8),
  ::v-deep #out-table .el-table__header-wrapper th:nth-child(11) {
    border-right: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__row td:nth-child(1) {
    border-left: 1px solid #d3dce6 !important;
  }
  ::v-deep #out-table .el-table__row td:nth-child(0),
  ::v-deep #out-table .el-table__row td:nth-child(2),
  ::v-deep #out-table .el-table__row td:nth-child(5),
  ::v-deep #out-table .el-table__row td:nth-child(8),
  ::v-deep #out-table .el-table__row td:nth-child(11) {
    border-right: 1px solid #d3dce6 !important;
  }
</style>
