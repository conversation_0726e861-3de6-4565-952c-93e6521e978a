import Layout from '../../views/layout/Layout';

const _import = require('../_import_' + process.env.NODE_ENV);
const deductionRconciliationRouter = {
  path: '/deductionRconciliation',
  component: Layout,
  name: 'deductionRconciliation',
  meta: {
    perm: 'm:deductionRconciliation:index',
    title: '合同扣款对账',
    icon: 'studentList',
    noCache: false
  },
  children: [
    {
      path: 'deductionRconciliation_index',
      component: _import('deductionRconciliation/index'),
      name: 'deductionRconciliationIndex',
      meta: {
        perm: 'm:deductionRconciliation:index',
        title: '合同扣款对账',
        icon: 'studentList',
        noCache: false
      }
    }
  ]
};

export default deductionRconciliationRouter;
