<template>
  <el-form class="dialog">
    <el-drawer style="margin-top: 5vw" title="学员档案打印" :visible="translateDle_" :direction="direction"
      @close="handleClose" :size="screenWidth > 1300 ? '30%' : '80vw'">
      <div style="margin: 3vw 2vw">
        学员信息登记表
        <el-button type="primary" plain size="mini" @click="studentInfoFn">打印<span
            class="el-icon-arrow-right"></span></el-button>
      </div>
      <div style="margin: 3vw 2vw">
        学员试课记录表
        <el-button type="primary" plain size="mini" @click="studenChange">
          打印<span class="el-icon-arrow-right"></span>
        </el-button>
      </div>
      <div style="margin: 3vw 2vw">
        词汇量检测报告(学前,学后)
        <el-button type="primary" plain size="mini" @click="wordsDayin">
          打印<span class="el-icon-arrow-right"></span>
        </el-button>
      </div>
      <div style="margin: 3vw 2vw">
        结业报告
        <el-button type="primary" plain size="mini" @click="GraduationFn()">
          打印<span class="el-icon-arrow-right"></span>
        </el-button>
      </div>
    </el-drawer>
  </el-form>
</template>

<script>
export default {
  name: "translateFn",
  props: {
    // 控制弹窗显示
    translateDle: {
      type: Boolean,
      default: false, //这里默认为false
    },
    // 学员列表的信息viewTime
    rowlist: {
      default: false, //这里默认为false
    },

    details: {
      default: false,
    },

  },
  data() {
    return {
      screenWidth: window.screen.width,
      studentCode: "",
      merchantCode: "",
      tableData: "",
      itemcount: 1,
      drawer: false,
      direction: "rtl",
      teacherNum: {
        id: "",
        teacherId: "",
      }, //列表老师用的
      dayinDateList: [],
      timer: "",

    };
  },
  computed: {
    translateDle_: {
      get() {
        return this.translateDle;
      },
      //值变化的时候会被调用
      set(v) {
        this.$emit("transDrawer", false);
      },
    },
  },
  methods: {
    // 学员信息表打印
    studentInfoFn() {
      window.localStorage.setItem("studentCodeS", this.studentCode);
      window.localStorage.setItem("merchantCodeS", this.merchantCode);
      this.$router.push({
        path: "/pclass/components/studentsList",
        query: {
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
        }
      });
    },
    // 试课信息表打印
    studenChange() {
      window.localStorage.setItem("studentCodeL", this.studentCode);
      window.localStorage.setItem("merchantCodeL", this.merchantCode);
      this.$router.push({
        path: "/pclass/components/changeClassList",
        query: {
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
        }
      });
    },
    // 词汇检测报告
    wordsDayin() {
      window.localStorage.setItem("studentCode", this.studentCode);
      window.localStorage.setItem("merchantCode", this.merchantCode);
      this.$router.push({
        path: "/students/areasStudentTestResultList",
        query: {
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
        }
      });
    },
    // 结业报告
    GraduationFn() {
      window.localStorage.setItem("studentCode", this.studentCode);
      window.localStorage.setItem("merchantCode", this.merchantCode);
      this.$router.push({
        path: "/students/areasStudentWordPrintList",
        query: {
          realName: this.dayinDateList.name,
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
        }
      });
    },
    // 关闭弹窗
    handleClose() {
      this.translateDle_ = false;
    },
    quxiaoBtn() {
      this.translateDle_ = true;
    },
  },
};
</script>

<style lang="scss">
// .paike {
//   margin-left: 2vw;
//   &:last-child {
//     margin-bottom: 30px;
//   }
//   &:first-child {
//     margin-top: 30px;
//   }
// }
// .paikeTwo {
//   width: 90%;
// }
.xubtn {
  margin-left: 7vw;
}
.cc {
  height: 0px;
  margin: 0 1.5vw 0 1.5vw;
  border-bottom: 1px solid #000;
}
</style>



<style scoped>
div/deep/.el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}
::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}
::v-deep :focus {
  outline: 0;
}
::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}

/* 2.隐藏滚动条，太丑了
::v-deep .el-drawer__container ::-webkit-scrollbar{
    display: none;
} */
</style>
