<!--合同扣款对账-->
<template>
  <div class="app-container">
    <p class="card">
      <i class="el-icon-bell"></i>
      &emsp;订单于每月10号上午10:00生成，请注意及时支付
    </p>
    <el-row style="margin-bottom: 10px;">
      <el-button size="small" type="primary" icon="el-icon-refresh" @click="fetchData">刷新</el-button>
    </el-row>
    <!-- height="400px" -->
    <el-table
      class="common-table"
      height="400px"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
      :header-cell-style="{ color: '#666' ,height:'50px'}"
      :row-style="{height:'40px'}"
    >
      <el-table-column prop="month" label="月份">
        <template v-slot="{ row }">
          <!-- <span>{{ splitMonth(row) }}</span> -->
          <span>{{ row.month }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="总金额">
        <template v-slot="{ row }">
          <span>{{ row.amount * 0.01 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态">
        <template v-slot="{ row }">
          <el-tag type="info" v-if="row.state == 1">待支付</el-tag>
          <el-tag type="warning" v-else-if="row.state == 2">支付中</el-tag>
          <el-tag type="success" v-else-if="row.state == 3">支付成功</el-tag>
          <el-tag type="danger" v-else-if="row.state == 4">支付失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column label="操作">
        <template v-slot="{ row }">
          <el-button type="text" @click="openStoreDetails(row)">门店详情</el-button>
          <el-button type="text" v-if="row.state == 1" @click="goPay(row)">去支付</el-button>
          <el-button type="text" v-else-if="row.state == 4" @click="goPay(row)">重新支付</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <!-- 门店详情 -->
    <el-dialog title="门店详情" :visible.sync="dialogVisible" width="40%">
      <el-table
        class="common-table"
        height="385px"
        v-loading="storeTableLoading"
        :data="storeDetailsData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ list: 'children', hasChildren: 'true' }"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#666' }"
      >
        <el-table-column prop="merchantCode" label="门店编号"></el-table-column>
        <el-table-column prop="merchantName" label="门店名称"></el-table-column>
        <el-table-column prop="signTime" label="签署时间"></el-table-column>
      </el-table>
      <!-- 分页 -->

      <el-row>
        <el-col :span="24" style="overflow-x: auto" :xs="24">
          <el-pagination
            :current-page="storeDetailsDataPage.currentPage"
            :page-sizes="[10, 20, 30, 40, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="storeDetailsDataPage.totalItems"
            @size-change="handleDetailsSizeChange"
            @current-change="handleDetailsCurrentChange"
          />
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderApi from '@/api/deductionRconciliation';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import store from '@/store';
  export default {
    data() {
      return {
        token: store.getters.token,
        dialogVisible: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10
        },
        //门店详情-分页
        storeDetailsDataPage: {
          currentPage: 1,
          size: 10
        },
        // 查询条件
        dataQuery: {
          merchantCode: '',
          merchantName: '',
          isEnable: ''
        },
        tableData: [],
        storeDetailsData: [],
        regTime: '',
        tableLoading: false,
        storeTableLoading: false
      };
    },
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    mounted() {
      this.fetchData();
      ossPrClient();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      handleDetailsSizeChange(val) {
        this.storeDetailsDataPage.size = val;
        this.openStoreDetails();
      },
      handleDetailsCurrentChange(val) {
        this.storeDetailsDataPage.currentPage = val;
        this.openStoreDetails();
      },
      // 分离月数
      splitMonth(row) {
        return row.month.split('-')[1];
      },
      // 查询提现列表
      fetchData() {
        const that = this;
        that.tableLoading = true;
        var a = that.regTime;
        console.log(a);

        console.log(that.roleTag);
        orderApi.queryList(this.tablePage).then((res) => {
          this.tableData = res.data.data.reverse();
          this.tableLoading = false;
          // 设置分页
          pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
        });
      },
      goPay(row) {
        console.log(row.id);
        orderApi.goPay(row.id).then((res) => {
          // console.log(res.data);
          const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
          res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
          let params = JSON.stringify(res.data);
          let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
          //需要编码两遍，避免出现+号等
          var encode = Base64.encode(Base64.encode(req));
          window.open(this.setpayUrl + 'product?' + encode, '_blank');
        });
      },
      openStoreDetails(row) {
        this.dialogVisible = true;
        if (row) this.row = row;
        this.storeTableLoading = true;
        orderApi.queryStoreDetails(this.storeDetailsDataPage, this.row.id).then((res) => {
          // this.storeDetailsData = res.data.data.reverse();
          this.storeDetailsData = res.data.data;
          this.storeTableLoading = false;
          // this.tableLoading = false;
          // 设置分页
          pageParamNames.forEach((name) => this.$set(this.storeDetailsDataPage, name, Number(res.data[name])));
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .card {
    background-color: #fef0f0;
    color: #f56c6c;
    // background-color: #f4f4f5;
    // color: #909399;
    padding: 10px 15px;
    border-radius: 5px;
  }
  .card:hover {
    // background-color: #ffeded;
    // color: #ff4949;
    background-color: #f4f4f5;
    color: #909399;
  }
  .info {
    color: #dcdfe6 !important;
  }
  .image {
    width: 80% !important;
    margin: 0 auto;
    display: block;
  }
  p {
    margin: 0 0 10px;
  }
  .w100 {
    width: 100% !important;
    display: block !important;
  }
  .el-form-item.w100 > :last-child {
    width: calc(100% - 120px) !important;
    // :last-child {
    //   width: 100% !important;
    // }
    .el-select {
      width: 100% !important;
    }
  }
</style>
