<!-- 分页下拉加载组件 -->
<template>
  <el-select
    class="el-select-loadmore"
    v-model="selectValue"
    v-loading="inputLoading"
    v-el-select-loadmore="handleLoadmore"
    :filter-method="filterValue"
    filterable
    remote
    :readOnly="!afterMounted"
    clearable
    :popper-class="'el-select-loadmore-popper-' + this.popperId"
    placeholder="请选择"
    :disabled="disabled"
    :style="styleData"
    @visible-change="clearSearchRecord"
    @change="change"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
  </el-select>
</template>

<script>
  export default {
    name: 'BaseElSelectLoadmore',
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            // 临界值的判断滑动到底部就触发
            const isAtBottom = Math.abs(this.scrollHeight - this.scrollTop - this.clientHeight) < 1;
            if (isAtBottom) {
              binding.value();
            }
          });
        }
      }
    },
    model: {
      prop: 'value',
      event: 'input'
    },
    props: {
      value: {
        type: [String, Number],
        default: ''
      },
      pageSize: {
        type: Number,
        default: 20
      },
      /**
       * 下拉查询方法
       * @param {Object} query 查询参数
       * @param {Number} query.pageNum 当前页码
       * @param {Number} query.pageSize 每页条数
       * @param {String} query.name 查询关键字
       * @returns {Promise<{lable:string,value:string}>} 选项列表 数组结构：[{lable: '', value: ''}]
       */
      searchFunc: {
        type: Function,
        default: () => Promise.resolve()
      },
      valueProp: {
        type: String,
        default: 'id'
      },
      labelProp: {
        type: String,
        default: 'name'
      },
      disabled: {
        type: Boolean,
        default: false
      },
      needInitLoading: {
        type: Boolean,
        default: false
      },
      styleData: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        popperId: Date.now().toString(),
        afterMounted: false,
        selectQuery: {
          pageNum: 1,
          pageSize: 10
        },
        isLastPageNum: false,
        inputLoading: false,
        loading: false,
        options: [],
        isPopperShow: false,
        popperLoadingVNode: null
      };
    },
    computed: {
      selectValue: {
        set(value) {
          this.$emit('input', value);
        },
        get() {
          return this.afterMounted ? this.value : '';
        }
      }
    },
    watch: {
      loading(val) {
        if (!val && this.popperLoadingVNode) {
          this.$nextTick(() => {
            this.popperLoadingVNode.style.display = 'none';
          });
        }
      }
    },
    mounted() {
      this.selectQuery.pageSize = this.pageSize;
      this.$set(this.selectQuery, this.valueProp, this.value);
      this.$set(this.selectQuery, this.labelProp, '');
      this.getOptionList({ init: true });
    },
    methods: {
      /**
       * 下拉加载
       */
      handleLoadmore() {
        if (!this.loading && !this.isLastPageNum) {
          this.selectQuery.pageNum++;
          this.getOptionList();
        }
      },
      getOptionList({ init, reset } = {}) {
        this.inputLoading = this.needInitLoading && init;
        this.loading = true;
        const VNodeArray = document.getElementsByClassName('el-select-loadmore-popper-' + this.popperId);
        if (VNodeArray && VNodeArray.length > 0) {
          if (this.popperLoadingVNode) {
            this.popperLoadingVNode.style.display = 'flex';
          } else {
            this.popperLoadingVNode = this.createLoadingElement();
            VNodeArray[0].appendChild(this.popperLoadingVNode);
          }
        }
        this.searchFunc(this.selectQuery)
          .then((res) => {
            if (Array.isArray(res)) {
              setTimeout(() => {
                this.isLastPageNum = res.length == 0;
                this.options = reset ? res : this.options.concat(res);
                this.loading = false;
                this.inputLoading = false;
              }, 500);
            }
            this.selectQuery[this.valueProp] = '';
            setTimeout(() => {
              this.afterMounted = true;
            }, 520);
          })
          .catch(() => {
            this.loading = false;
            this.inputLoading = false;
            this.afterMounted = true;
          });
      },
      filterValue(value) {
        this.selectQuery.pageNum = 1;
        this.selectQuery[this.labelProp] = value;
        this.getOptionList({ reset: true });
      },
      change(val) {
        let label = '';
        if (val == '') {
          this.selectQuery.pageNum = 1;
          this.selectQuery[this.labelProp] = '';
          this.getOptionList({ reset: true });
        } else {
          label = this.options.find((item) => item.value == val)?.label;
        }
        this.$emit('change', val, label);
      },
      clearSearchRecord(show) {
        this.isPopperShow = show;
        if (show) return;
        if (this.value == '') {
          this.selectQuery.pageNum = 1;
          this.selectQuery[this.labelProp] = '';
          this.getOptionList({ reset: true });
        }
        this.$forceUpdate();
      },
      createLoadingElement() {
        const porrerLoadingVNode = document.createElement('div');
        porrerLoadingVNode.id = 'el-select-loadmore-popper-loading-' + this.popperId;
        porrerLoadingVNode.textContent = '加载中...';
        porrerLoadingVNode.style.position = 'absolute';
        porrerLoadingVNode.style.top = '0';
        porrerLoadingVNode.style.width = '100%';
        porrerLoadingVNode.style.height = '100%';
        porrerLoadingVNode.style.borderRadius = '4px';
        porrerLoadingVNode.style.display = 'flex';
        porrerLoadingVNode.style.justifyContent = 'center';
        porrerLoadingVNode.style.alignItems = 'center';
        porrerLoadingVNode.style.color = '#999999';
        porrerLoadingVNode.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        return porrerLoadingVNode;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-select-loadmore::v-deep.el-select {
    .el-select__caret:first-child::before {
      content: '\e6e1';
    }
    .is-focus {
      .el-select__caret:first-child {
        transform: rotateZ(0deg);
      }
    }
  }
</style>
