<template>
  <div style="background-color: #fff;height:100vh">
    <div v-if="signContractStatus!=11">
     <div style="display:flex">
      <div style="display:flex;line-height:2vw;margin-left:1vw">
        <div>登录账号</div>
        <div>{{telName}}</div>
      </div>
      <div style="margin-left:30%">
        <h1>个人实名认证</h1>
      </div>
     </div>
     <div style="margin-left:25%">
      <el-steps :active="active" finish-status="success" style="width:40vw;">
        <el-step title="第一步"></el-step>
        <el-step title="第二步"></el-step>
        <el-step title="第三步"></el-step>
      </el-steps>
     </div>
     <!-- 第一步 -->
     <div v-if="active==0">
      <div style="margin-left:25%;margin-top:5vh">
        姓名：
        <el-input v-model="realName" placeholder="请输入内容" style="width:15vw"></el-input>
      </div>
      <div style="margin-left:24%;margin-top:2vh">
        身份证：
        <el-input v-model="identityNo" placeholder="请输入内容" style="width:15vw"></el-input>
      </div>
     </div>
     <!-- 第二步 -->
     <div v-if="active==1">
      <div style="margin-left:25%;margin-top:5vh">
        银行卡号：
        <el-input v-model="backList.cardNo" placeholder="请输入内容" style="width:15vw"></el-input>
      </div>
      <div style="margin-left:24.5%;margin-top:2vh">
        预留手机号:
        <el-input v-model="backList.cardPhone" placeholder="请输入内容" style="width:15vw"></el-input>
      </div>
      <div style="margin-left:25.5%;margin-top:2vh;display:flex">
        <div style="font-size:18px;color:#000;line-height:36px">验证码：</div>
        <div style="display:flex">
          <el-input
            onkeyup="value=value.replace(/[^\d\.]/g,'')"
            maxlength="6"
            placeholder="请输入验证码"
            v-model="backList.verificationCode"
          ></el-input>
          <el-button
            size="mini"
            class="telBtn"
            type="primary"
            @click="getcodes()"
            v-if="show"
            style="margin-left:.5vw"
          >发送验证码</el-button>
          <el-button
            size="mini"
            class="telBtn"
            type="primary"
            style="margin-left:.5vw"
            v-if="!show"
          >{{count}}s后再次获取</el-button>
        </div>
      </div>
     </div>
     <!-- 第三步 -->
     <div v-if="active==2">
      <div style="margin-left:25%;margin-top:5vh">
        手机号：
        <el-input v-model="mobile" placeholder="请输入内容" style="width:15vw"></el-input>
      </div>
      <div style="margin-left:25.5%;margin-top:2vh;display:flex">
        <div style="font-size:18px;color:#000;line-height:36px">验证码：</div>
        <div style="display:flex">
          <el-input
            onkeyup="value=value.replace(/[^\d\.]/g,'')"
            maxlength="6"
            placeholder="请输入验证码"
            v-model="smscode"
          ></el-input>
          <el-button
            size="mini"
            class="telBtn"
            type="primary"
            @click="Tgetcodes()"
            v-if="show1"
            style="margin-left:.5vw"
          >发送验证码</el-button>
          <el-button
            size="mini"
            class="telBtn"
            type="primary"
            style="margin-left:.5vw"
            v-if="!show1"
          >{{count}}s后获取</el-button>
        </div>
      </div>
     </div>
     <!-- <el-button type="primary" @click="bangdingFn(url)" v-if="active==2">绑定</el-button> -->
     <div style="margin-left:34%;margin-top:10vh">
      <el-button type="primary" @click="nextFn" v-if="active!=2">下一步</el-button>
      <el-button type="primary" @click="contractFn(url)" v-if="active==2">签约</el-button>
      <el-button style="margin-left: 15vw;" type="primary" @click="delFn">取消</el-button>
     </div>
    </div>
    <div v-else style="margin-left:40%;margin-top:10%">
      <img src="../../../assets/duigou.png" class="shadow" />
      <h1>实名认证成功</h1>
      <el-button type="primary" @click="backFn" style="margin-left:2.5vw">返回</el-button>
    </div>
  </div>
</template>

<script>
import { idCard, isName } from "@/utils/validate";
import {
  codeApi,
  userCodeApi,
  verifiedApi,
  minApi,
  bankApi,
  telApi,
  contractApi,
  telbangdingApi
} from "@/api/realName/realName";
export default {
  name: "realName",
  data() {
    return {
      signContractStatus:'',
      urll:'http://localhost:9529/#/layout/personMe/realName',
      show:true,
      count:"",
      timer:null,
      show1:true,
      count1:"",
      timer1:null,
      verificationCode: "", //短信验证码
      sendmsg: "发送验证码",
      isSend: true,
      realName: "", //用户名
      identityNo: "", //身份证
      telName: "", //左上角用户名字
      bankCard: "", //第二步银行卡号
      mobile1: "", //第二步手机号
      userPhone: "", //绑定手机号
      active: 0, //进度条第几步参数
      url: "",
      userCode: "",
      // 实名认证
      renzhen: {
        bizUserId: "",
        identityNo: "",
        name: "",
        providerType: "ALLIN_PAY"
      },
      // 计时器
      minuteList: {
        cardNo: "",
        cardPhone: "",
        providerType: "ALLIN_PAY"
      },
      // 绑银行卡
      backList: {
        bizUserId: "",
        cardNo: "",
        cardPhone: "",
        providerType: "ALLIN_PAY",
        tranceNum: "",
        verificationCode: ""
      },
      telApiList: {
        phone: ""
      },
      // 绑定手机号
      telbangding: {
        providerType: "ALLIN_PAY",
        tranceNum: "",
        verificationCode: ""
      },
      // 签约跳转
      contractList: {
        backStyle: "DUBBO",
        source: 1,
        jumpType: 2,
        bizUserId: "",
        jumpUrl:'',
        noContractUrl:'',
      },
      mobile: "", //最后绑手机
      smscode: "", //最后绑验证码
      tranceNum: "" //验证手机号返的
    };
  },
  created() {
    this.telName = window.localStorage.getItem("Name");
    this.initData();
  },
  methods: {
    backFn(){
      this.$router.go(-1)//返回上一层
    },
    async initData() {
      // 先获取usrcode
      let codeLin = await codeApi();
      let codecanshu = codeLin.data.find(item => {
        if (item.userType == "Member") {
          return item;
        }
      });
      // 再获取是否实名认证
      let res = await userCodeApi(codecanshu.userCode);
      this.renzhen.bizUserId = res.data.bizUserId;
      if(res.data.signContractStatus==1){
        this.signContractStatus=11
      }
      // 判断跳转
      if (res.success && res.data != null) {
          if (res.data.certStatus == "1"&&res.data.signContractStatus!=1) {
          if (res.data.bindCardStatus != 1) {
            //已经通过实名认证 未绑定银行卡
            this.active = 1;
            this.$message.success("您已完成实名认证,请绑定银行卡信息");
          } else if ((res.data.bindPhoneStatus = 1&&res.data.signContractStatus!=1)) {
            //未绑定手机
            this.active = 2;
            this.$message.success("您已完成实名认证,请绑定手机号");
          }
        }
      }
    },
    // 下一步
    nextFn() {
      let _this = this;
      if (_this.active++ > 2) _this.active = 0;
      if (_this.active == 1) {
        _this.submit_s();
      } else if (_this.active == 2) {
        _this.submit_bank();
      }
    },
    // 第四步发送验证码
    async Tgetcodes() {
      let _this = this;
      if (this.mobile.length != 11) {
        return this.$message.error("请输入登录手机号");
      }
      _this.telApiList.phone = _this.mobile;
      _this.telApiList.bizUserId = this.renzhen.bizUserId;
      const res = await telApi(_this.telApiList);
      if (res.success) {
        this.$message.success("验证码发送成功，请注意接收！");
        this.tranceNum = res.data.tranceNum;
      }
      _this.getPhoneCode1();
    },
    // 签约
    async contractFn() {
      this.contractList.bizUserId = this.renzhen.bizUserId
      this.contractList.jumpUrl = this.urll
      this.contractList.noContractUrl = this.urll
      let res = await contractApi(this.contractList);
      this.url = res.data
      window.location.href = this.url
    },
    // 绑定手机号
    async bangdingFn() {
      this.telbangding.bizUserId = this.renzhen.bizUserId;
      this.telbangding.phone = this.mobile;
      this.telbangding.tranceNum = this.tranceNum;
      this.telbangding.verificationCode = this.smscode;
      let res = await telbangdingApi(this.telbangding);
    },
    // 第一步实名认证
    async submit_s() {
      let _this = this;
      if (!isName(_this.realName)) {
        this.$message.error("姓名不符合要求！");
        return (_this.active = 0);
      }
      if (!idCard(_this.identityNo)) {
        this.$message.error("身份证号不符合要求！");
        return (_this.active = 0);
      }
      this.renzhen.name = this.realName;
      this.renzhen.identityNo = this.identityNo;
      let result = await verifiedApi(this.renzhen); //支付服务商 ALLIN_PAY-通联 WX-微信
    },
    // 第二步绑定银行卡
    async submit_bank() {
      let _this = this;
      if (!_this.backList.cardNo) {
        this.$message.error("请输入银行卡号");
        return (_this.active = 1);
      }
      if (!_this.backList.cardPhone) {
        this.$message.error("请输入预留手机号");
        return (_this.active = 1);
      }
      if (!_this.backList.verificationCode) {
        this.$message.error("请输入验证码");
        return (_this.active = 1);
      }
      _this.backList.bizUserId = _this.renzhen.bizUserId;
      const res = await bankApi(this.backList);
    },
    // 取消验证用
    delFn() {
      this.$router.go(-1);
    },
    // 验证码绑定银行卡
    async getcodes() {
      let _this = this;
      if (!_this.backList.cardNo) {
        return this.$message.error("请输入银行卡号");
      }
      if (!_this.backList.cardPhone) {
        return this.$message.error("请输入预留手机号");
      }
      if (_this.yanStatus) {
        return;
      }
      _this.minuteList.bizUserId = _this.renzhen.bizUserId;
      _this.minuteList.cardNo = _this.backList.cardNo;
      _this.minuteList.cardPhone = _this.backList.cardPhone;
      const res = await minApi(_this.minuteList);
      if (res) {
        _this.yanStatus = true;
        _this.backList.tranceNum = res.data
        this.$message.success("验证码发送成功，请注意接收！");
      }
      _this.getPhoneCode()
    },
    // 验证码计时器
    getPhoneCode(){
      let TIME_COUNT = 60;
      if (!this.timer) {
        this.count = TIME_COUNT;
        this.show = false;
        this.timer = setInterval(()=>{
          if (this.count > 0 && this.count <= TIME_COUNT){
            this.count--;
          }else {
            this.show = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        },1000)
      }
    },
    // 验证码计时器1
    getPhoneCode1(){
      let TIME_COUNT = 60;
      if (!this.timer1) {
        this.count1 = TIME_COUNT;
        this.show1 = false;
        this.timer1 = setInterval(()=>{
          if (this.count1 > 0 && this.count1 <= TIME_COUNT){
            this.count1--;
          }else {
            this.show1 = true;
            clearInterval(this.timer1);
            this.timer1 = null;
          }
        },1000)
      }
    }
  }
};
</script>

<style scoped>
::v-deep.app-main {
  background-color: #fff;
}
</style>>

