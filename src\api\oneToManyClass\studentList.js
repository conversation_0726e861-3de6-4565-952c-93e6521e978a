// 一对多学员列表 相关接口
import request from '@/utils/request';

export const getAllClass = (data) => {
  return request({
    url: '/deliver/web/deliverClass/getAllClass',
    method: 'GET',
    params: data
  });
};
/**
 * 获取试课学员分页数据
 * @param {*} pageNum
 * @param {*} pageSize
 * @param {*} data
 * @returns
 */
export const getStudentTrialData = (data) => {
  return request({
    url: '/deliver/web/oneMore/getOneMoreExperienceStudentList',
    method: 'GET',
    params: data
  });
};
/**
 * 获取正课学员分页数据
 * @param {*} pageNum
 * @param {*} pageSize
 * @param {*} data
 * @returns
 */
export const getStudentFormalData = (data) => {
  return request({
    url: '/deliver/web/oneMore/getOneMoreStudentList',
    method: 'GET',
    params: data
  });
};
/**
 * 根据交付中心编号、课程大类id、年级及课程分类获取教练已经接单且未满的班级列表数据
 * @param {*} param0
 * @returns
 */
export const getClassByCurriculumIdAndDeliverMerchant = (deliverMerchant, curriculumId, type, grade) => {
  return request({
    url: '/deliver/web/oneMore/getDeliverClassByCurriculumGrade',
    method: 'GET',
    params: { deliverMerchant, curriculumId, type, grade, teacherType: 1 }
  });
};
/**
 * 指派/更改学员所属班级
 * @param {*} data
 * @returns
 */
export const setAssignClassData = (data) => {
  return request({
    url: '/deliver/web/oneMore/changeStudentListToClass',
    method: 'POST',
    data
  });
};
