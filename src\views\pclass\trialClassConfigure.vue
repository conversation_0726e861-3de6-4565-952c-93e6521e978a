<template>
  <div>
    <el-form :inline="true" class="container-card" label-width="110px" ref="searchInfo" :model="searchInfo">
      <el-form-item label="课程类型:" prop="studentName">
        <el-select v-model="searchInfo.curriculumId" size="small" placeholder="请选择" clearable @change="curriculumChange">
          <el-option v-for="item in courseList" :key="item.id" :label="item.enName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label=" " prop="studentName">
        <el-popover placement="bottom" width="820" v-model="showPopover" trigger="click">
          <div class="calendar-content-css">
            <div class="title-main-css">节假日周末配置</div>
            <div class="calendar-list-css">
              <div class="calendar-item calendar-item-left">
                <div class="calendar-header-css">
                  <div class="year-css border-bg-css">{{ dateObj.year }}年</div>
                  <div class="month-css border-bg-css">{{ dateObj.month }}月</div>
                </div>
                <el-calendar>
                  <template slot="dateCell" slot-scope="{ date, data }">
                    <div @click.stop="">
                      <div v-for="(item, index) in dateList" :key="index">
                        <div v-if="compareTime(data, item.time)" @click.stop="selectCalendar(item)" :class="item.isSelect ? 'select-css' : ''">
                          <div class="calendar-item-height" v-if="item.dateType == 1">{{ data.day.split('-')[2] }}</div>
                          <div class="color-red calendar-item-height" v-if="item.dateType == 2">{{ data.day.split('-')[2] }}</div>
                          <div class="color-red calendar-item-background calendar-item-height" v-if="item.dateType == 3">
                            <div class="text-xiu-css">休</div>
                            {{ data.day.split('-')[2] }}
                          </div>
                        </div>
                      </div>
                      <div v-if="getlist(data, dateList)" @click.stop="" class="calendar-item-height">
                        {{ data.day.split('-')[2] }}
                      </div>
                    </div>
                  </template>
                </el-calendar>
              </div>
              <div class="calendar-item calendar-item-right">
                <div class="calendar-header-css">
                  <div class="border-bg-css"></div>
                  <div class="month-css border-bg-css">{{ dateObj.nextMonth }}月</div>
                </div>
                <el-calendar v-model="dateRigth">
                  <template slot="dateCell" slot-scope="{ date, data }">
                    <div @click.stop="">
                      <div v-for="(item, index) in nextDateList" :key="index">
                        <div v-if="compareTime(data, item.time)" @click.stop="selectCalendar(item)" :class="item.isSelect ? 'select-css' : ''">
                          <div class="calendar-item-height" v-if="item.dateType == 1">{{ data.day.split('-')[2] }}</div>
                          <div class="color-red calendar-item-height" v-if="item.dateType == 2">{{ data.day.split('-')[2] }}</div>
                          <div class="color-red calendar-item-background calendar-item-height" v-if="item.dateType == 3">
                            <div class="text-xiu-css">休</div>
                            {{ data.day.split('-')[2] }}
                          </div>
                        </div>
                      </div>
                      <div @click.stop="" v-if="getlist(data, nextDateList)" class="calendar-item-height">
                        {{ data.day.split('-')[2] }}
                      </div>
                    </div>
                  </template>
                </el-calendar>
              </div>
            </div>
            <div class="button-content-css">
              <el-button type="primary" @click="setAllUpdateDate(1)" class="background-one-css">设置为工作日</el-button>
              <el-button type="primary" @click="setAllUpdateDate(2)" class="background-two-css">设置为周末</el-button>
              <el-button type="primary" @click="setAllUpdateDate(3)" class="background-three-css">设置为节假日</el-button>
            </div>
            <div class="button-botttom-css">
              <el-button class="width-css-buttom" @click="updateDateType" type="primary">确定</el-button>
              <el-button class="width-css-buttom" @click="cencleDateType">取消</el-button>
            </div>
          </div>
          <el-button type="primary" slot="reference" size="mini">节假日周末配置</el-button>
        </el-popover>
      </el-form-item>
    </el-form>
    <div class="delivery-content-css">
      <el-row type="flex" justify="end" style="margin-bottom: 20px">
        <el-button v-if="!editStatus" type="primary" size="small" @click="editStatus = !editStatus">编辑</el-button>
        <el-button v-if="editStatus" size="small" @click="cencleCanReserveNum">取消</el-button>
        <el-button v-if="editStatus" type="primary" size="small" @click="setCanReserveNum">保存</el-button>
      </el-row>
      <div v-loading="loading">
        <div class="delivery-item-css" v-for="item in reservationList" :key="item.id">
          <div class="delivery-title-css">{{ item.title }}</div>
          <div class="delivery-list-css">
            <div class="delivery-info-css" v-for="(info, index) in item.timeList" :key="index">
              <span style="width: 90px; display: inline-block">{{ info.startTime }}~{{ info.endTime }}</span>
              <el-input style="width: 120px; margin-left: 10px" v-model="info.canReserveNum" :disabled="!editStatus" placeholder="请输入"></el-input>
              <span class="delivery-text">交付中心总量 {{ info.deliverTotalNum }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <el-dialog :visible.sync="dialogTableVisible" width="800px" append-to-body>
            <div>1111111111111111111</div>
        </el-dialog> -->
  </div>
</template>
<script>
  import { bvstatusList } from '@/api/paikeManage/classCard';
  import { findExperienceDateInfos, updateExperienceDateInfos, findAllExperienceUsableTimeByType, setExperienceUsableTimeNumByType } from '@/api/pclass/trialClassConfigure';
  export default {
    data() {
      return {
        searchInfo: {
          curriculumId: ''
        },
        courseList: [],
        loading: false,
        dialogTableVisible: false,
        numberChange: '',
        dateRigth: '',
        reservationList: [
          {
            title: '工作日配置',
            id: 1,
            timeList: [{ canReserveNum: '' }]
          },
          {
            title: '周末配置',
            id: 2,
            timeList: [{ canReserveNum: '' }]
          },
          {
            title: '节假日配置',
            id: 3,
            timeList: [{ canReserveNum: '' }]
          }
        ],
        showPopover: false,
        editStatus: false,
        setIndex: 0,
        dateList: [],
        selectDate: [],
        changeSelectDate: [],
        dateListCopy: [],
        nextDateList: [],
        nextDateListCopy: [],
        dateObj: {
          month: 0,
          year: 2023,
          nextMonth: 1
        }
      };
    },
    created() {
      this.getbvstatusList();
      this.getDateList();
    },
    directives: {
      'only-numbers': {
        bind(el, binding, vnode) {
          el.addEventListener('input', function (event) {
            let value = event.target.value;
            value = value.replace(/\D/g, '');
            event.target.value = value;
            vnode.componentInstance.$emit('input', value);
          });
        }
      }
    },
    methods: {
      cencleCanReserveNum() {
        this.editStatus = false;
        this.getExperienceUsableTimeByType(this.searchInfo.curriculumId);
      },
      // 处理返回日期格式
      handleBackDatelist(list) {
        let arr = [];
        list.forEach((item) => {
          let obj = { ...item };
          let formattedMonth = item.month < 10 ? `0${item.month}` : `${item.month}`;
          let day = item.day < 10 ? `0${item.day}` : `${item.day}`;
          obj.time = item.year + '-' + formattedMonth + '-' + day;
          arr.push(obj);
        });
        return arr;
      },
      // 处理提交时的日期格式
      handleSubmitDatelist(list) {
        let arr = [];
        list.forEach((item) => {
          let obj = { ...item };
          delete obj.time;
          arr.push(obj);
        });
        return arr;
      },
      //修改承担量
      async setCanReserveNum() {
        const loading = this.$loading({
          lock: true,
          text: '修改中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let arr = [...this.reservationList[0].timeList, ...this.reservationList[1].timeList, ...this.reservationList[2].timeList];

        try {
          await setExperienceUsableTimeNumByType(arr);
          loading.close();
          this.$message({
            message: '修改成功',
            type: 'success'
          });

          this.editStatus = false;
          this.getExperienceUsableTimeByType(this.searchInfo.curriculumId);
        } catch (error) {
          loading.close();
        }
      },
      // 日期取消
      cencleDateType() {
        this.dateList = [...this.dateListCopy];
        this.nextDateList = [...this.nextDateListCopy];
        this.showPopover = false;
      },
      // 修改日期
      async updateDateType() {
        const loading = this.$loading({
          lock: true,
          text: '配置中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.nextDateListCopy.forEach((item) => {
          this.nextDateList.forEach((e) => {
            if (item.time == e.time) {
              item.dateType = e.dateType;
            }
          });
        });
        this.dateListCopy.forEach((item) => {
          this.dateList.forEach((e) => {
            if (item.time == e.time) {
              item.dateType = e.dateType;
            }
          });
        });

        let arr = [this.handleSubmitDatelist(this.dateListCopy), this.handleSubmitDatelist(this.nextDateListCopy)];
        try {
          await updateExperienceDateInfos(arr);
          this.$message({
            message: '配置成功',
            type: 'success'
          });
          loading.close();
          this.getDateList();
          this.showPopover = false;
        } catch (error) {}
      },
      // 获取日期列表
      async getDateList() {
        const now = new Date();
        const year = now.getFullYear();
        this.dateObj.year = year;
        // 获取当前的月份，注意getMonth()返回的月份是从0开始的，所以需要+1
        const month = now.getMonth() + 1;
        let month1 = now.getMonth() + 2;
        const year2 = now.getFullYear() + 1;
        this.dateObj.month = month;
        if (month1 == 13) {
          month1 = 1;
        }
        this.dateObj.nextMonth = month1;
        if (month1 == 1) {
          this.dateRigth = new Date(year2 + '-' + month1 + '-01');
        } else {
          this.dateRigth = new Date(year + '-' + month1 + '-01');
        }

        // 格式化月份，确保始终是两位数字
        const formattedMonth = month < 10 ? `0${month}` : `${month}`;
        const formattedMonth1 = month1 < 10 ? `0${month1}` : `${month1}`;
        const dateList = await findExperienceDateInfos({
          year: year,
          month: formattedMonth
        });
        const nextDateList = await findExperienceDateInfos({
          year: month1 == 1 ? year2 : year,
          month: formattedMonth1
        });
        console.log(nextDateList);
        console.log(dateList);
        this.setIndex = 0;
        this.selectDate = [];
        this.dateList = [];
        this.nextDateList = [];
        this.nextDateListCopy = [];
        this.dateListCopy = [];
        this.dateListCopy = this.handleBackDatelist(dateList.data);
        this.nextDateListCopy = this.handleBackDatelist(nextDateList.data);
        this.nextDateListCopy.forEach((item) => {
          this.nextDateList.push({ ...item });
        });
        this.dateListCopy.forEach((item) => {
          this.dateList.push({ ...item });
        });
      },
      // 初始化 获取课程大类
      async getbvstatusList() {
        let res = await bvstatusList({});
        this.courseList = res.data;
        this.searchInfo.curriculumId = this.courseList[0].id;
        this.getExperienceUsableTimeByType(this.searchInfo.curriculumId);
      },
      getlist(data, list) {
        let data1 = new Date(data.day);
        var index = list.findIndex((item) => {
          let data2 = new Date(item.time);
          if (data1 - data2 == 0) {
            return true;
          }
        });
        if (index >= 0) {
          return false;
        } else {
          return true;
        }
      },
      compareTime(data, date) {
        let date1 = new Date(data.day);
        let date2 = new Date(date);
        if (date1 - date2 == 0) {
          return true;
        }
      },
      setAllUpdateDate(key) {
        this.setIndex += 1;
        this.setUpdateDate(key, this.dateList);
        this.setUpdateDate(key, this.nextDateList);
      },
      //设置日期
      setUpdateDate(key, list) {
        list.forEach((item) => {
          this.selectDate.forEach((info) => {
            if (item.time == info.time) {
              if (info.setIndex == this.setIndex - 1) {
                info.dateType = key;
                item.dateType = key;
              }
              item.isSelect = false;
            }
          });
        });
        list.forEach((item) => {
          this.selectDate.forEach((info) => {
            if (item.time == info.time) {
              if (info.setIndex == this.setIndex - 1) {
                info.dateType = key;
                item.dateType = key;
              }
              item.isSelect = false;
            }
          });
        });
      },
      selectCalendar(data) {
        let index = this.selectDate.findIndex((item) => {
          if (data.time == item.time) {
            return true;
          }
        });
        if (index >= 0) {
          if (data.isSelect) {
            this.selectDate.splice(index, 1);
          } else {
            this.selectDate[index].setIndex = this.setIndex;
          }
        } else {
          this.selectDate.push({ ...data, setIndex: this.setIndex });
        }
        this.$set(data, 'isSelect', !data.isSelect);
      },
      curriculumChange(e) {
        this.getExperienceUsableTimeByType(e);
      },
      async getExperienceUsableTimeByType(id) {
        this.loading = true;
        let res = await findAllExperienceUsableTimeByType({
          curriculumId: id,
          dateType: 1
        });
        this.reservationList[0].timeList = res.data;
        let res1 = await findAllExperienceUsableTimeByType({
          curriculumId: id,
          dateType: 2
        });
        this.reservationList[1].timeList = res1.data;
        let res2 = await findAllExperienceUsableTimeByType({
          curriculumId: id,
          dateType: 3
        });
        this.reservationList[2].timeList = res2.data;
        this.loading = false;
      }
    }
  };
</script>
<style scoped lang="scss">
  .delivery-content-css {
    padding: 32px;
    .delivery-title-css {
      padding: 10px 0;
    }
    .delivery-item-css {
      margin-bottom: 20px;
      padding-top: 10px;
      border-top: 1px solid #ececec;
    }
  }

  .delivery-list-css {
    color: #555;
    font-size: 16px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    .delivery-info-css {
      margin-right: 30px;
      margin-top: 10px;
    }
    .delivery-text {
      display: inline-block;
      margin-left: 10px;
      padding: 7px 15px;
      color: #747474;
      font-size: 14px;
      background: #ececec;
      line-height: 30rpx;
      border-radius: 4px;
    }
  }
  //日历样式
  ::v-deep {
    .el-calendar__header {
      display: none;
    }
    .el-calendar-table tr:first-child td {
      border-top: none !important;
    }
    .el-calendar-table tr td:first-child {
      border-top: none !important;
    }
    .el-calendar-table .el-calendar-day {
      height: 38px !important;
      width: 38px !important;
      padding: 0 !important;
      line-height: 38px !important;
      text-align: center !important;
    }
    .el-calendar-table td {
      border: none !important;
    }
    .el-calendar-table td.is-selected {
      background-color: #fff !important;
    }
    .el-calendar-table .el-calendar-day:hover {
      background-color: #fff !important;
    }
    .el-calendar-table__row {
      padding-left: -20px !important;
    }
    .el-calendar-table {
      height: 295px;
    }
  }
  .calendar-content-css {
    padding: 0 30px;
    padding-top: 20px;
    .title-main-css {
      font-size: 20px;
      font-weight: bold;
    }
    .calendar-list-css {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      border: 1px solid #3894ff;
      padding-top: 20px;
      padding-left: 15px;
      .calendar-item {
        width: 350px;
      }
      .calendar-item-right {
        width: 360px;
        padding-left: 15px;
      }
    }
    .calendar-header-css {
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      padding-right: 38px;
      .border-bg-css {
        line-height: 32px;
        width: 60px;
        border-radius: 4px;
        text-align: center;
      }
      .year-css {
        background-color: #e7e7e7;
        width: 70px;
      }
      .month-css {
        background-color: #3894ff;
        color: #fff;
      }
    }
    .calendar-item-left {
      border-right: 1px solid #ececec;
      // padding-right: 10px;
    }
    .color-red {
      color: #ed1c24;
    }
    .calendar-item-height {
      width: 38px;
      height: 38px;
      margin-left: -11px;
    }
    .select-css {
      .calendar-item-height {
        background-color: #f2f8fe;
      }
    }
    .calendar-item-height:hover {
      background-color: #f2f8fe;
    }
    .calendar-item-background {
      background: #feebed;
      position: relative;
      .text-xiu-css {
        font-size: 8px;
        height: 10px;
        line-height: 9px;
        color: #ed1c24;
        position: absolute;
        top: 3px;
        right: 3px;
      }
    }
    .button-content-css {
      text-align: center;
      padding: 30px 0;
      .background-one-css {
        background: #ebf4ff;
        border: 1px solid #3894ff;
        color: #3894ff;
      }
      .background-two-css {
        background: #e6f5ed;
        border: 1px solid #0d9f53;
        color: #0d9f53;
      }
      .background-three-css {
        background: #feebed;
        border: 1px solid #f53d4e;
        color: #f53d4e;
      }
    }
    .button-botttom-css {
      text-align: center;
      padding-bottom: 20px;
      .width-css-buttom {
        width: 100px;
        line-height: 20px;
      }
    }
  }
</style>
