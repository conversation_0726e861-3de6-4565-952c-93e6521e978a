<template>
  <div class="dialog">
    <el-drawer style="margin-top:6vw" title="复习查看" :visible.sync="lookstyle_" :direction="direction"
      @close="handleClose">
      <el-row style="margin-top:1vw">
        <div style="margin-left:20vw">
          <span class="Second" v-if="studyStatus">
            <button type="button" style="forecolor: blue" @click="studyFn">
              <span style="color: skyblue">日</span>/
              <span>总</span>
            </button>
          </span>
          <span class="Second" v-else>
            <button type="button" style="forecolor: blue" @click="studyBtn">
              <span style="color: skyblue">总</span>/
              <span>日</span>
            </button>
          </span>
        </div>
      </el-row>
      <el-col class="paike" style="margin-top:1vw">
        姓名：
        <span>{{ fuxiList.studentName }}</span>
      </el-col>
      <el-col class="paike">
        年级：
        <span>{{ fuxiList.gradeName }}</span>
      </el-col>
      <el-col class="paike">
        复习内容：
        <span>{{ fuxiList.studyBooks }}</span>
      </el-col>
      <el-col class="paike">
        教练：
        <span>{{ teacher }}</span>
      </el-col>
      <el-col class="paike">
        复习词汇：
        <span>{{ fuxiList.reviewWords }}个</span>
      </el-col>
      <el-col class="paike">
        复习遗忘词汇：
        <span>{{ fuxiList.forgetWords }}个</span>
      </el-col>
      <el-col class="paike">
        遗忘率：
        <span>{{ fuxiList.forgetRate }}%</span>
      </el-col>
      <el-col class="paike" v-if="studyStatus">
        教练评语：
        <span>{{ fuxiList.feedback }}</span>
      </el-col>
    </el-drawer>
  </div>
</template>

<script>
import {
  getTotalStatistics,
  getFeedbackInfo
} from "@/api/paikeManage/LearnManager";
import { getStudyDateList } from "@/api/paikeManage/classCard";
export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    reviewStyle: {
      type: Boolean,
      default: false
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl"
    }
  },
  name: "lookDialog",
  data() {
    return {
      // loading: true,加载框用的
      studyStatus: true,
      value: "",
      drawer: false,
      activeName: "second",
      day: true,
      always: false,
      firstStudy: true,
      aganStudy: false,
      BtnAdialog: true,
      BtnBdialog: false,
      studyList: {
        type: 2, //复习学习的反馈
        date: "",
        studentCode: ""
      },
      fuxiList: "",
      teacher: "",
      reviewTotal: {
        id: "",
        planId: "",
        type: 2
      },
      leaveApplication: {
        id: "",
        type: 2
      }
    };
  },
  //计算属性
  computed: {
    lookstyle_: {
      get() {
        return this.reviewStyle;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("reviewDialog", v);
      }
    }
  },
  methods: {
    async studyFn() {
      this.fuxiList = [];
      this.studyStatus = false;
      this.reviewTotal.type = 2;
      let res = await getTotalStatistics(this.reviewTotal);
      this.fuxiList = res.data;
    },
    async studyBtn() {
      let res = await getFeedbackInfo(this.leaveApplication);
      this.fuxiList = res.data;
      this.studyStatus = true;
    },
    handleClose() {
      this.studyStatus = true;
      this.$emit("reviewDialog", false);
      this.fuxiList = "";
    },
    async numberLookFn() {
      let res = await getStudyDateList(this.studyList);
      this.fuxiList = res.data;
    }
  }
};
</script>

<style lang="scss" scoped>
.borders {
  margin: 1vw 1vw;
  // width: 28vw;
  height: 28vw;
  border: 1px solid #cac8c8;
  border-radius: 20px;
}

.paike {
  margin-bottom: 20px;
  margin-left: 2vw;

  &:first-child {
    margin-top: 0.5vw;
  }
}

div ::v-deep .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}
::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}
::v-deep :focus {
  outline: 0;
}
::v-deep .el-drawer__body {
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
