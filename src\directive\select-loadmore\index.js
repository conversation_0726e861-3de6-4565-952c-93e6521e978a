export default {
  bind(el, binding) {
    const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
    SELECTWRAP_DOM.addEventListener('scroll', function () {
      //临界值的判断滑动到底部就触发
      // const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
      const condition = Math.abs(this.scrollHeight - (this.scrollTop + this.clientHeight)) < 1;
      if (condition) {
        binding.value();
      }
    });
  }
};
