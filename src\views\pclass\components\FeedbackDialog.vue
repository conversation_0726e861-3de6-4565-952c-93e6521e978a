<template>
  <div class="dialog">
    <el-drawer title="数据报警处理" :visible.sync="classCardstyley" :direction="direction" @close="handleClose">
      <div class="borders">
        <el-row>
          <el-col style="margin-top: 2vw; margin-left: 1vw">
            教练姓名:{{ warningList.teacher }}
          </el-col>

          <el-col style="margin-top: 2vw; margin-left: 1vw">课程名称:{{ warningList.courseType }}</el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">
            联系方式: {{ warningList.mobile }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">上学时间:{{ warningList.studyTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">授课方式:{{ warningList.teachingType }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">学员名称:{{ warningList.studentName }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">报警时间:{{ warningList.warningTime }}
          </el-col>
          <el-col style="margin-top: 2vw; margin-left: 1vw">处理反馈:
            <div style="margin: 1vw">
              <el-input style="width: 24vw" type="textarea" :rows="2" placeholder="请输入内容" disabled
                v-model="warningList.feedback">
              </el-input>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  //传值
  props: {
    //父组件向子组件传 drawer；这里默认会关闭状态
    LeaveViewStyley: {
      type: Boolean,
      default: false,
    },
    //Drawer 打开的方向
    direction: {
      type: String,
      default: "rtl",
    },
  },
  name: "fdbackDialog",
  data() {
    return {
      value1: "",
      textarea: "",
      warningList: [], //预警列表
      studentList: {}, //接受授课类型用
      teacherList: {}, //排课获得的老师列表
    };
  },
  //计算属性
  computed: {
    classCardstyley: {
      get() {
        return this.LeaveViewStyley;
      },
      //值一改变就会调用set【可以用set方法去改变父组件的值】
      set(v) {
        //   console.log(v, 'v')
        this.$emit("LeaveyDialog", v);
      },
    },
  },
  methods: {
    //子组件向父组件传方法，传布尔值；请求父组件关闭抽屉
    handleClose() {
      this.$emit("LeaveyDialog", false);
    },
    timestudy1() {
      if (this.classCardnum.endStudyTime != "") {
        this.getTeachlist();
      }
    },
    timestudy2() {
      if (this.classCardnum.startStudyTime != "") {
        this.getTeachlist();
      }
    },
  },
};
</script>

<style lang="scss">
.borders {
  margin: 1vw 1vw;
  width: 28vw;
  height: 43vw;
}
</style>


<style scoped>
::v-deep .el-textarea__inner {
  height: 8vw;
  margin-top: 0.2vw;
}
div /deep/ .el-drawer__container {
  position: relative;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 25px;
  width: 100%;
}

::v-deep .el-drawer__header {
  color: #000;
  font-size: 22px;
  text-align: center;
  font-weight: 900;
  margin-bottom: 0;
}

::v-deep :focus {
  outline: 0;
}

::v-deep .el-drawer__body {
  overflow: auto;
  /* overflow-x: auto; */
}
</style>
