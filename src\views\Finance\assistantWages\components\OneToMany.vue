<template>
  <div style="padding-left: 10px">
    <el-tabs v-model="courseType" @tab-click="handleClick">
      <el-tab-pane label="正式课工资" name="1"></el-tab-pane>
      <el-tab-pane label="试课工资" name="2"></el-tab-pane>
      <el-tab-pane label="试课奖励" name="3"></el-tab-pane>
    </el-tabs>
    <formalWages v-if="courseType === '1'"></formalWages>
    <trialWages v-if="courseType === '2'"></trialWages>
    <trialRewards v-if="courseType === '3'"></trialRewards>
  </div>
</template>

<script>
  import formalWages from './OneToMany/formalWages.vue';
  import trialWages from './OneToMany/trialWages.vue';
  import trialRewards from './OneToMany/trialRewards.vue';

  export default {
    name: 'OneToMany',
    components: {
      formalWages,
      trialWages,
      trialRewards
    },
    data() {
      return {
        isAdd: false, // 是否新增
        url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
        courseType: '1' // 当前选中类型
      };
    },
    methods: {
      handleClick() {}
    }
  };
</script>

<style lang="scss" scoped>
  .warning-title-css {
    line-height: 60px;
    span {
      display: inline-block;
      margin-left: 10px;
      color: #f89728;
    }
  }
</style>
