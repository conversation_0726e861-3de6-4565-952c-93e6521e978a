@font-face {
  font-family: "iconfont"; /* Project id 3263154 */
  src: url('iconfont.woff2?t=1699251058392') format('woff2'),
       url('iconfont.woff?t=1699251058392') format('woff'),
       url('iconfont.ttf?t=1699251058392') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-quanping:before {
  content: "\e8fa";
}

.icon-tuichuquanping:before {
  content: "\e8fb";
}

.icon-luyin:before {
  content: "\e600";
}

