<!--交付中心-接单管理-承单量管理员审核-->
<template>
  <div>
    <div class="frame">
      <el-form label-width="120px" ref="querydata" :model="querydata">
        <!-- 1 -->
        <el-row>
          <el-col :span="4">
            <el-form-item label="交付中心编号:">
              <el-input v-model="querydata.deliverMerchant" @change="changeInput" size="small" placeholder=""></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="交付中心名称:">
              <el-select
                size="small"
                v-el-select-loadmore="handleLoadmore"
                :loading="loadingShip"
                remote
                clearable
                v-model="querydata.deliverName"
                filterable
                reserve-keyword
                placeholder="请选择"
                @input="changeMessage"
                @blur="clearSearchRecord"
                @change="changeTeacher"
                style="width: 170px"
              >
                <el-option v-for="(item, index) in option" :key="index" :label="item.deliverMerchantName" :value="item.deliverMerchantName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="申请时间:">
              <el-date-picker
                v-model="times"
                size="small"
                style="width: 350px"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="changeTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="审核状态:">
              <el-select v-model="querydata.status" clearable size="small" placeholder="请选择" style="width: 10vw">
                <el-option label="全部" value=""></el-option>
                <el-option label="未审核" value="0"></el-option>
                <el-option label="已审核" value="1"></el-option>
                <el-option label="已拒绝" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" style="padding-left: 20px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchData">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh-left" @click="rest()">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div style="height: 20px; background: #f7f8fc; margin-bottom: 30px"></div>
    <el-table :data="tableData" id="oneToMany-orderAuditing-table" v-loading="tableDataLoading" border :header-cell-style="getRowClass" :cell-style="{ 'text-align': 'center' }">
      <el-table-column v-for="(item, index) in tableHeaderList" :key="`${index}-${item.id}`" :prop="item.value" :label="item.name" header-align="center">
        <template v-slot="{ row }">
          <div v-if="item.value == 'operate'">
            <el-button type="primary" size="mini" v-if="isAdmin && row.status == 0" @click="openDrawer(row)">审核</el-button>
            <span v-else>{{ '-' }}</span>
          </div>
          <div v-else-if="item.value == 'status'">
            <span :class="statusClass(row.status)">{{ statusNameFormat(row.status) }}</span>
          </div>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <el-row type="flex" justify="center" align="middle" style="height: 60px">
      <!-- 3个变量：每页数量、页码数、总数  -->
      <!-- 2个事件：页码切换事件、每页数量切换事件-->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="querydata.pageNum"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="querydata.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </el-row>
    <!-- 审核抽屉 -->
    <el-drawer :with-header="false" :visible.sync="auditDrawerVisable" direction="rtl" size="90%" :before-close="handleClose">
      <div class="setting">
        <div class="closeIcon" @click="handleClose">X</div>
        <div class="title">
          1v多承单量申请——
          <span style="color: #409eff">{{ formList.curriculumName }}</span>
        </div>
        <el-row>
          <el-col :span="6">
            <div class="formItem">
              <div class="label" style="width: 150px">交付中心名称:</div>
              <el-input v-model="formList.deliverName" disabled size="small" style="width: 200px" placeholder=""></el-input>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="formItem">
              <div class="label" style="width: 150px">交付中心编号:</div>
              <el-input v-model="formList.deliverMerchant" disabled size="small" style="width: 200px" placeholder=""></el-input>
            </div>
          </el-col>
        </el-row>
        <div class="form">
          <el-row>
            <el-col :span="6">
              <div class="formItem">
                <div class="label">试课:</div>
                <el-input v-model="formList.experienceNum" :min="0" size="small" class="input" type="number" disabled placeholder="">
                  <template slot="suffix">班</template>
                </el-input>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="formItem">
                <div class="label">正式课:</div>
                <el-input v-model="formList.learnNum" :min="0" size="small" type="number" class="input" disabled placeholder="">
                  <template slot="suffix">班</template>
                </el-input>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-if="formList.classTimeWeekVoList1 && formList.classTimeWeekVoList1.length > 0">
          <h4>试课预约班级数设置</h4>
          <div v-for="(weeksItem, index) in formList.classTimeWeekVoList1" :key="index" style="display: flex; align-items: flex-start; margin-top: 18px; line-height: 30px">
            <div style="margin: 0 30px 18px 0; width: 355px">{{ weeksItem.dayOfWeek }}</div>
            <div style="display: flex; flex-wrap: wrap; width: 1020px">
              <div v-for="(timeItem, index) in weeksItem.classTimeNumVoList" :key="index" style="display: flex; margin-right: 20px; margin-bottom: 18px">
                <span style="margin-right: 12px">{{ timeItem.startTime + '-' + timeItem.endTime }}</span>
                <el-input-number v-model="timeItem.canReserveNum" :min="0" size="small" disabled :controls="false"></el-input-number>
                <span style="margin-left: 6px">班</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="formList.classTimeWeekVoList2 && formList.classTimeWeekVoList2.length > 0">
          <h4>正式课预约班级数设置</h4>
          <div v-for="(weeksItem, index) in formList.classTimeWeekVoList2" :key="index" style="display: flex; align-items: flex-start; margin-top: 18px; line-height: 30px">
            <div style="margin: 0 30px 18px 0; width: 355px">{{ weeksItem.dayOfWeek }}</div>
            <div style="display: flex; flex-wrap: wrap; width: 1020px">
              <div v-for="(timeItem, index) in weeksItem.classTimeNumVoList" :key="index" style="display: flex; margin-right: 20px; margin-bottom: 18px">
                <span style="margin-right: 12px">{{ timeItem.startTime + '-' + timeItem.endTime }}</span>
                <el-input-number v-model="timeItem.canReserveNum" :min="0" size="small" disabled :controls="false"></el-input-number>
                <span style="margin-left: 6px">班</span>
              </div>
            </div>
          </div>
        </div>
        <div class="btns">
          <el-button type="success" style="width: 100px; background: #21bb33" @click="onSure">通过</el-button>
          <el-button type="danger" style="width: 100px" @click="refuseDialog = true">拒绝</el-button>
        </div>

        <!-- 审核拒绝弹窗 -->
        <el-dialog title="拒绝原因" :visible.sync="refuseDialog" :close-on-click-modal="false" append-to-body width="30%" :before-close="dialogBeforeClose">
          <div>
            <el-input type="textarea" v-model="formList.remark" placeholder="请输入"></el-input>
          </div>
          <div slot="footer">
            <el-button @click="dialogBeforeClose">取 消</el-button>
            <el-button type="primary" @click="onRefuse">确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import ls from '@/api/sessionStorage';
  import { deliverlist } from '@/api/peizhi/peizhi';
  import { auditConfigList, auditConfig, getManyConfigDetailData } from '@/api/orderManageOneToMany';
  export default {
    name: 'OneToManyOrderAuditing',
    components: {},
    directives: {
      'el-select-loadmore': {
        bind(el, binding) {
          const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
          SELECTWRAP_DOM.addEventListener('scroll', function () {
            //临界值的判断滑动到底部就触发
            const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
            if (condition) {
              binding.value();
            }
          });
        }
      }
    },
    data() {
      return {
        querydata: {
          startTime: '',
          endTime: '',
          deliverName: '',
          deliverMerchant: '',
          status: '',
          oneToManyType: 1,
          pageNum: 1,
          pageSize: 10 //页容量
        },
        refuseDialog: false,
        times: [],
        auditDrawerVisable: false,
        total: null,
        teacher: '',
        contentType: '',
        tableDataLoading: false,
        tableData: [],
        id: '',
        isAdmin: false,
        option: [],
        optionTotal: 0,
        loadingShip: false,
        drawForm: {
          deliverMerchant: '',
          deliverName: ''
        },
        tableHeaderList: [
          {
            name: '交付中心名称',
            value: 'deliverName'
          },
          {
            name: '交付中心编号',
            value: 'deliverMerchant'
          },
          {
            name: '课程大类',
            value: 'curriculumName'
          },
          {
            name: '操作',
            value: 'operate'
          },
          {
            name: '审核状态',
            value: 'status'
          },
          {
            name: '申请时间',
            value: 'applyTime'
          },
          {
            name: '拒绝原因',
            value: 'remark'
          }
        ],
        selectObj: {
          pageNum: 1,
          pageSize: 20
        },
        formList: []
      };
    },
    created() {
      // this.getReservationTime()
      this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') == 'JiaofuManager';
      this.getTeacherList();
      this.initData();
    },
    methods: {
      searchData() {
        this.querydata.pageNum = 1;
        this.querydata.pageSize = 10;
        this.initData();
      },
      // 审核通过
      onSure() {
        let data = { status: 1, id: this.formList.auditId, curriculumId: this.formList.curriculumId };
        auditConfig(data).then(() => {
          this.$message.success('审核已通过');
          this.initData();
          this.handleClose();
        });
      },
      // 审核拒绝
      onRefuse() {
        let data = { status: 2, id: this.formList.auditId, curriculumId: this.formList.curriculumId, remark: this.formList.remark };
        auditConfig(data).then(() => {
          this.$message.success('审核已拒绝');
          this.initData();
          this.dialogBeforeClose();
          this.handleClose();
        });
      },
      dialogBeforeClose() {
        this.refuseDialog = false;
        this.formList.remark = '';
      },
      async openDrawer(row) {
        getManyConfigDetailData(row.configId).then((res) => {
          this.formList = res.data;
          this.formList.auditId = row.id;
          this.formList.deliverMerchant = row.deliverMerchant;
          this.formList.deliverName = row.deliverName;
          this.auditDrawerVisable = true;
        });
      },
      changeTime(e) {
        if (!!e) {
          this.querydata.startTime = e[0];
          this.querydata.endTime = e[1];
        } else {
          this.querydata.startTime = '';
          this.querydata.endTime = '';
        }
      },
      statusClass(status) {
        switch (status) {
          case 0:
            return 'primary';
          case 1:
            return 'normal';
          case 2:
            return 'error';
        }
      },
      statusNameFormat(status) {
        status = Number(status);
        //状态 0:未审核 1:已通过 2:已拒绝
        switch (status) {
          case 0:
            return '未审核';
          case 1:
            return '已通过';
          case 2:
            return '已拒绝';
        }
      },
      async initData() {
        this.tableDataLoading = true;
        let { data } = await auditConfigList(this.querydata);
        this.tableData = data.data;
        this.total = Number(data.totalItems);
        this.tableDataLoading = false;
      },
      handleClose() {
        this.auditDrawerVisable = false;
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#f5f7fa';
        }
      },
      // 获取交付中心
      async getTeacherList() {
        let allData = await deliverlist(this.selectObj);
        this.option = this.option.concat(allData.data.data);
        this.optionTotal = Number(allData.data.totalPage);
      },
      // 下拉加载
      handleLoadmore() {
        if (!this.loadingShip) {
          if (this.selectObj.pageNum == this.optionTotal) return; //节流防抖
          this.selectObj.pageNum++;
          this.getTeacherList();
        }
      },
      // 改变下拉框的值
      clearSearchRecord() {
        setTimeout(() => {
          if (this.querydata.deliverName == '') {
            this.option = [];
            this.selectObj.pageNum = 1;
            this.getTeacherList();
          }
        }, 500);
        this.$forceUpdate();
      },
      changeInput(e) {
        console.log(e);
        if (!!e) {
          let arr = this.option.filter((i) => i.deliverMerchantCode == e);
          this.querydata.deliverName = arr[0].deliverMerchantName;
        }
      },
      changeTeacher(e) {
        if (e == '') {
          this.option = [];
          this.querydata.deliverMerchant = '';
          this.selectObj.pageNum = 1;
          this.getTeacherList();
        } else {
          let arr = this.option.filter((i) => i.deliverMerchantName == e);
          this.querydata.deliverMerchant = arr[0].deliverMerchantCode;
        }
      },
      changeMessage() {
        this.$forceUpdate();
      },
      //重置
      rest() {
        this.querydata = {
          startTime: '',
          endTime: '',
          deliverName: '',
          deliverMerchant: '',
          status: '',
          oneToManyType: 1,
          pageNum: 1,
          pageSize: 10 //页容量
        };
        this.times = [];
        this.initData();
      },
      // 分页
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.initData();
      },
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.initData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .primary {
    color: #46a6ff;
  }
  .normal {
    color: rgb(28, 179, 28);
  }

  .error {
    color: rgba(234, 36, 36, 1);
  }

  body {
    background-color: #f5f7fa;
  }
  // h5 {
  //   margin: 10px 0;
  // }
  .frame {
    // margin:  0 30px;
    background-color: rgba(255, 255, 255);
    padding: 20px;
  }

  .el-button--success {
    color: #ffffff;
    background-color: #6ed7c4;
    border-color: #6ed7c4;
  }
  ::v-deep .el-drawer__body {
    overflow-y: auto !important;
  }
  .setting {
    width: 100%;
    height: 100%;
    padding: 20px;
    overflow-y: auto;
    .title {
      padding: 20px 10px;
    }
    .closeIcon {
      position: absolute;
      right: 100px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 50%;
      background-color: #ccc;
      cursor: pointer;
      z-index: 9999;
    }
    ::v-deep .el-input__suffix {
      color: #000;
      top: 8px;
    }
  }
  .form {
    // width: 65%;
    margin-top: 10px;
    // display: grid;
    // grid-template-columns: repeat(2, 1fr); /* 将容器分为4列，每列平均占据剩余空间 */
    // grid-gap: 10px; /* 设置格子之间的间隔 */
    // grid-row-gap: 25px;
  }

  .formItem {
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-top: 10px;
    .input {
      width: 200px;
      // margin-left: 10px;
      // flex: 1;
    }
    .label {
      text-align: right;
      width: 150px;
      margin-right: 10px;
    }
  }

  .btns {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
</style>
