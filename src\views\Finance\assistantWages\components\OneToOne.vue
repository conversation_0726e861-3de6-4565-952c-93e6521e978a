<template>
  <div style="padding-left: 10px">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="正式课工资" name="first">
        <div v-if="wagesList.length == 0 && formalWages.length == 0" class="box-card" style="text-align: center; margin-top: 20px; height: 38vw">
          <div class="no_data" style="height: 100%">
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
            <div style="color: #999; margin-top: 20px">暂无数据</div>
            <el-button type="primary" size="small" style="margin-top: 25px" @click="openTearcherWages('add', null)">新增正式课工资</el-button>
          </div>
        </div>
        <div v-if="formalWages.length > 0 || wagesList.length > 0">
          <el-button type="primary" size="small" style="margin-top: 15px" @click="openTearcherWages('add', null)">新增正式课工资</el-button>
          <div class="warning-title-css">
            <i class="el-icon-warning"></i>
            <span>注:未设置课程类型的正式课工资，都按默认发!</span>
          </div>
          <div v-if="formalWages.length > 0">
            <el-table :data="formalWages" border style="width: 70%" :header-cell-style="getRowClass">
              <el-table-column prop="" label="课程类型" width="" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="基本工资" width="" align="center">
                <template slot-scope="scope">
                  {{ Number(scope.row.value).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="configState" label="绩效工资" width="" align="center">
                <template slot-scope="scope">
                  {{ Number(scope.row.configState).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="工资" width="" align="center">
                <template slot-scope="scope">
                  <span>{{ (Number(scope.row.value) + Number(scope.row.configState)).toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="effectStatusDesc" label="状态" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.effectStatus == 0" class="effectStatusRed">{{ scope.row.effectStatusDesc }}</span>
                  <span v-if="scope.row.effectStatus == 1" class="effectStatusWarning">{{ scope.row.effectStatusDesc }}</span>
                  <span v-if="scope.row.effectStatus == 2" class="effectStatusSuccess">{{ scope.row.effectStatusDesc }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="effectTime" label="生效时间" align="center"></el-table-column>
              <el-table-column prop="" label="操作" align="center" min-width="100px">
                <template slot-scope="scope">
                  <el-button type="primary" icon="el-icon-edit" size="mini" @click="openTearcherWages('update', scope.row)">编辑</el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    @click="singleDelete(scope.row.name, scope.row, 'formalWages', scope.$index)"
                    v-if="scope.row.configExtra != 'default'"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              @size-change="handleFormSizeChange"
              style="margin-top: 20px"
              @current-change="handleFormCurrentChange"
              :current-page="formalWagesQuery.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="formalWagesQuery.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="formalWagesQuery.total"
            ></el-pagination>
          </div>
          <!-- formalWagesQuery -->
          <el-scrollbar v-if="wagesList.length > 0" style="margin-top: 40px; padding-bottom: 40px">
            <table class="formal_classes">
              <div class="formal_classes_title">
                <div class="class_wi90">课程类型</div>
                <div class="class_wi130">正式课工资</div>
                <div v-for="(item, index) in wagesList[0].wageAddDtoList.length" :key="index" style="display: flex">
                  <div class="class_wi90">教练等级</div>
                  <div class="class_wi90">基本工资</div>
                  <div class="class_wi90">绩效工资</div>
                  <div class="class_wi90">工资</div>
                </div>
                <div class="class_wi3">模块</div>
                <div class="class_wi90">状态</div>
                <div class="class_wi130">生效时间</div>
                <div class="class_wi2">操作</div>
              </div>
              <div v-for="(item, index) in wagesList" :key="index" class="formal_classes_content class_Center">
                <div class="class_wi90 border_r flex_column">{{ item.curriculumName }}</div>
                <div class="class_wi130 border_r flex_column">{{ item.name }}</div>
                <div v-for="(val, idx) in item.wageAddDtoList" :key="idx" style="display: flex">
                  <div class="class_wi90 border_r flex_column">{{ val.name }}</div>
                  <div class="class_wi90 border_r flex_column">{{ Number(val.wage).toFixed(2) }}</div>
                  <div class="class_wi90 border_r flex_column">{{ Number(val.meritPay).toFixed(2) }}</div>
                  <div class="class_wi90 border_r flex_column">{{ (Number(val.wage) + Number(val.meritPay)).toFixed(2) }}</div>
                </div>
                <div class="class_wi3 border_r flex_column plr-10">
                  <div v-if="item.courseLevelCode.length == 0 && item.grammarCode.length == 0">-</div>
                  <div v-else class="text-ellipsis">
                    <span>{{ changeCourseLevel(item.courseLevelCode) }}</span>
                    <span v-if="item.courseLevelCode.length > 0 && item.grammarCode.length > 0">、</span>
                    <span v-if="item.grammarCode.length > 0">{{ changeGrammarlist(item.grammarCode) }}</span>
                  </div>
                </div>
                <div class="class_wi90 border_r flex_column">
                  <span v-if="item.effectStatus == 0" class="effectStatusRed">{{ item.effectStatusDesc }}</span>
                  <span v-if="item.effectStatus == 1" class="effectStatusWarning">{{ item.effectStatusDesc }}</span>
                  <span v-if="item.effectStatus == 2" class="effectStatusSuccess">{{ item.effectStatusDesc }}</span>
                </div>
                <div class="class_wi130 border_r flex_column">{{ item.effectTime }}</div>
                <div class="class_wi2 border_r lh-60">
                  <el-button type="primary" size="mini" icon="el-icon-edit" @click="openTearcherWages('update', item, '鼎英语')">编辑</el-button>
                  <el-button type="danger" size="mini" icon="el-icon-delete" @click="singleDelete('鼎英语', item, 'wagesList', index)">删除</el-button>
                </div>
              </div>
            </table>
            <!-- wagesQuery -->
            <el-pagination
              @size-change="handleWagesSizeChange"
              style="margin-top: 20px; margin-bottom: 20px"
              @current-change="handleWagesCurrentChange"
              :current-page="wagesQuery.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="wagesQuery.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="wagesQuery.total"
            ></el-pagination>
          </el-scrollbar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="试课工资" name="second">
        <div v-if="earningsList.length == 0" class="box-card" style="text-align: center; margin-top: 20px; height: 38vw">
          <div class="no_data" style="height: 100%">
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
            <div style="color: #999; margin-top: 20px">暂无数据</div>
            <!-- <el-button type="primary" size="small" style="margin-top:25px;" @click="openTearcherWages('add', null)">新增正式课工资</el-button> -->
            <el-button type="primary" size="small" style="margin-top: 25px" @click="newlAdded()">新增试课工资</el-button>
          </div>
        </div>
        <!-- 所有交付中心都可以工资设置和试课奖励 -->
        <div v-if="earningsList.length > 0 && isAdmin">
          <!-- <div > -->
          <el-button type="primary" style="margin-top: 15px" size="small" @click="newlAdded()">新增试课工资</el-button>
          <div class="warning-title-css">
            <i class="el-icon-warning"></i>
            <span>注:未设置课程类型的试课工资，都按默认发!</span>
          </div>
          <el-table :data="earningsList" border style="width: 70%" :header-cell-style="getRowClass">
            <el-table-column prop="" label="课程类型" width="" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.courseName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="基本工资" width="" align="center">
              <template slot-scope="scope">
                {{ Number(scope.row.value).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="configState" label="绩效工资" width="" align="center">
              <template slot-scope="scope">
                {{ Number(scope.row.configState).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="" label="工资" width="" align="center">
              <template slot-scope="scope">
                <span>{{ (Number(scope.row.value) + Number(scope.row.configState)).toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="valueExtend" label="补贴" width="" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.valueExtend">
                  {{ Number(scope.row.valueExtend).toFixed(2) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="effectStatusDesc" label="状态" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.effectStatus == 0" class="effectStatusRed">{{ scope.row.effectStatusDesc }}</span>
                <span v-if="scope.row.effectStatus == 1" class="effectStatusWarning">{{ scope.row.effectStatusDesc }}</span>
                <span v-if="scope.row.effectStatus == 2" class="effectStatusSuccess">{{ scope.row.effectStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="effectTime" label="生效时间" align="center"></el-table-column>
            <el-table-column prop="" label="操作" align="center" min-width="120px">
              <template slot-scope="scope">
                <el-button type="primary" icon="el-icon-edit" size="mini" @click="editDialog(scope.row, 1)">编辑</el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  v-if="scope.row.configExtra != 'default'"
                  @click="singleDelete(scope.row.courseName, scope.row, 'earningsList', scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-row align="middle" style="height: 60px; margin-top: 10px">
            <!-- 3个变量：每页数量、页码数、总数  -->
            <!-- 2个事件：页码切换事件、每页数量切换事件-->
            <el-pagination
              @size-change="handleSizeearnChange"
              @current-change="handleCurrentearnChange"
              :current-page="queryearnings.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="queryearnings.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryearnings.total"
            ></el-pagination>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="试课转正课奖励" name="third">
        <div v-if="rewardList.length == 0" class="box-card" style="text-align: center; margin-top: 20px; height: 38vw">
          <div class="no_data" style="height: 100%">
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
            <div style="color: #999; margin-top: 20px">暂无数据</div>
            <!-- <el-button type="primary" size="small" style="margin-top:25px;" @click="openTearcherWages('add', null)">新增正式课工资</el-button> -->
            <el-button type="primary" size="small" style="margin-top: 25px" @click="addRewarded()">新增试课奖励</el-button>
          </div>
        </div>
        <div v-if="rewardList.length > 0 && isAdmin">
          <el-button type="primary" style="margin-top: 15px" size="small" @click="addRewarded()">新增试课奖励</el-button>
          <div class="warning-title-css">
            <i class="el-icon-warning"></i>
            <span>注:未设置课程类型的试课奖励，都按默认发!</span>
          </div>
          <el-table :data="rewardList" border style="width: 70%" :header-cell-style="getRowClass">
            <el-table-column prop="" label="课程类型" width="" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.curriculumName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="expBonus" label="试课奖励" width="" align="center">
              <template slot-scope="scope">
                {{ Number(scope.row.expBonus).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="effectStatusDesc" label="状态" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.effectStatus == 0" class="effectStatusRed">{{ scope.row.effectStatusDesc }}</span>
                <span v-if="scope.row.effectStatus == 1" class="effectStatusWarning">{{ scope.row.effectStatusDesc }}</span>
                <span v-if="scope.row.effectStatus == 2" class="effectStatusSuccess">{{ scope.row.effectStatusDesc }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="effectTime" label="生效时间" align="center"></el-table-column>
            <el-table-column prop="" label="操作" align="center" min-width="100px">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" icon="el-icon-edit" @click="editDialog(scope.row, 2)">编辑</el-button>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  v-if="scope.row.curriculumId != 'default'"
                  @click="singleDelete(scope.row.curriculumName, scope.row, 'rewardList', scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-row align="middle" style="height: 60px; margin-top: 10px">
            <!-- 3个变量：每页数量、页码数、总数  -->
            <!-- 2个事件：页码切换事件、每页数量切换事件-->
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="querydata.pageNum"
              :page-sizes="[10, 20, 30, 40, 50]"
              :page-size="querydata.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="querydata.total"
            ></el-pagination>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="复习工资" name="four" v-if="!checkPermission(['admin'])">
        <div style="margin-bottom: 1vw; margin-top: 4vw; padding-left: 20px">
          <div>
            <span>复习工资基本设置</span>
            <el-popover placement="bottom-start" width="550" trigger="hover">
              <div>
                <p>复习工资计算=教练复习单词数÷复习公约数x公约数工资</p>
                <p>例如:公约数为1以下按照1计算，1以上向上取整</p>
                <p>Ⅰ例:公约数为40个，公约数工资为:0.8元，奖金:0.2元</p>
                <p>本轮复习单词30个</p>
                <p>情况1:教练带了1~29个单词，教练的复习工资为0.8元</p>
                <p>情况2:教练带了30个单词，教练的复习工资为0.8元+0.2元</p>
                <p>Ⅱ例:</p>
                <p>本轮复习单词240</p>
                <p>情况1:教练带了220个单词，教练工资=220÷40x0.8=4.8</p>
                <p>情况2:教练带了240个单词，教练工资=240÷40x0.8=4.8，但是由于本轮共240个单词,</p>
                <p>加上奖金0.2元，所以该教练复习工资4.8+0.2=5元</p>
              </div>
              <span slot="reference" class="rule_style">
                <i class="el-icon-question"></i>
                规则
              </span>
            </el-popover>
          </div>
        </div>
        <!-- <div v-if="reviewWagesList.length==0" style="padding-left:450px;">
            <el-button type="primary" size="small" @click="reviewWagesEdit()">新增复习工资基本设置</el-button>
          </div> -->
        <div v-if="reviewWagesList.length == 0" s class="box-card" style="text-align: center; margin-top: 20px; height: 16vw">
          <div class="no_data" style="height: 100%">
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
            <div style="color: #999; margin-top: 20px">暂无数据</div>
            <!-- <el-button type="primary" size="small" style="margin-top:25px;" @click="openTearcherWages('add', null)">新增正式课工资</el-button> -->
            <el-button type="primary" size="small" style="margin-top: 25px" @click="reviewWagesEdit()">新增复习工资基本设置</el-button>
          </div>
        </div>
        <el-table v-else :data="reviewWagesList" border style="width: 70%; margin-left: 20px" :header-cell-style="getRowClass">
          <el-table-column prop="value" label="复习公约数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.commonDivisor }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="公约数工资" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.commonDivisorSalary }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="复习完成奖金" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.completeReward }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectStatusDesc" label="状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.effectStatus == 0" class="effectStatusRed">{{ scope.row.effectStatusDesc }}</span>
              <span v-if="scope.row.effectStatus == 1" class="effectStatusWarning">{{ scope.row.effectStatusDesc }}</span>
              <span v-if="scope.row.effectStatus == 2" class="effectStatusSuccess">{{ scope.row.effectStatusDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectTime" label="生效时间" align="center"></el-table-column>
          <el-table-column prop="" label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="primary" size="small" @click="reviewWagesEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: space-between; margin-bottom: 1vw; margin-top: 4vw; width: 70%; padding-left: 20px">
          <!-- <div>复习完成率奖金设置（自然月）</div> -->
          <div>
            <span>复习完成率奖金设置（自然月）</span>
            <el-popover placement="bottom-start" width="550" trigger="hover">
              <div>
                <p>以自然月为单位，计算本月复习完成率,</p>
                <p>复习完成率=复习完成次数:总复习次数(1天多次，默认为1次)</p>
                <p>符合设置的数值区间，就可获得对应的奖金，为次月10日发送至教练端</p>
                <p>价格区间以高的计算例:</p>
                <p>1~95%，为0.2元</p>
                <p>95~100%，为0.4元</p>
                <p>95%奖金则为0.4元</p>
              </div>
              <span slot="reference" class="rule_style">
                <i class="el-icon-question"></i>
                规则
              </span>
            </el-popover>
          </div>
          <el-button type="primary" v-if="reviewRate.length > 0" size="small" @click="reviewEditOrAdd()">新增复习奖金设置</el-button>
        </div>
        <!-- <div v-if="reviewRate.length==0" style="padding-left:450px;">
            <el-button type="primary"  size="small" @click="reviewEditOrAdd()">新增复习奖金设置</el-button>
          </div> -->
        <div v-if="reviewRate.length == 0" s class="box-card" style="text-align: center; margin-top: 20px; height: 16vw">
          <div class="no_data" style="height: 100%">
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
            <div style="color: #999; margin-top: 20px">暂无数据</div>
            <!-- <el-button type="primary" size="small" style="margin-top:25px;" @click="openTearcherWages('add', null)">新增正式课工资</el-button> -->
            <el-button type="primary" size="small" style="margin-top: 25px" @click="reviewEditOrAdd()">新增复习奖金设置</el-button>
          </div>
        </div>
        <el-table v-else :data="reviewRate" border style="width: 70%; margin-left: 20px" :header-cell-style="getRowClass">
          <el-table-column prop="value" label="复习完成率" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.startingValue }}~{{ scope.row.endValue }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="奖金" align="center">
            <template slot-scope="scope">
              <span>{{ Number(scope.row.rewardAmount).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectStatusDesc" label="状态" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.effectStatus == 0" class="effectStatusRed">{{ scope.row.effectStatusDesc }}</span>
              <span v-if="scope.row.effectStatus == 1" class="effectStatusWarning">{{ scope.row.effectStatusDesc }}</span>
              <span v-if="scope.row.effectStatus == 2" class="effectStatusSuccess">{{ scope.row.effectStatusDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="effectTime" label="生效时间" align="center"></el-table-column>
          <el-table-column prop="" label="操作" align="center">
            <template slot-scope="scope">
              <el-button v-if="scope.$index > 0" type="danger" @click="delectReview(scope.$index, scope.row)" size="mini">删除</el-button>
              <el-button type="primary" size="small" @click="reviewEditOrAdd(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!-- 新增教练试课工资 -->
    <el-dialog
      class="wage-css"
      :title="earningTitleShow == true ? '新增试课工资' : '编辑试课工资'"
      :visible.sync="dialogWages"
      :width="screenWidth > 1300 ? '50%' : '90%'"
      :before-close="addwagesClose"
      :close-on-click-modal="false"
    >
      <el-form ref="addFormWages" :model="addList" label-width="100px">
        <div v-if="earningShow" class="warning-title-css">
          <i class="el-icon-warning"></i>
          <span>注:未设置课程类型的试课工资，都按默认发，未配置过试课工资的第一条配置必须为默认!</span>
        </div>
        <el-form-item label="课程类型：" class="is-required" v-if="earningShow">
          <el-input placeholder="默认" disabled></el-input>
        </el-form-item>
        <el-form-item label="课程类型：" prop="curriculumId" v-else>
          <el-select v-model="addList.curriculumId" @change="curriculumChange(addList.curriculumId, 'addList')" placeholder="">
            <el-option v-for="item in curriculumOptions" :key="item.id" :label="item.enName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="8" :xs="24">
            <el-form-item label="基本工资：" prop="pay" :rules="[{ required: true, message: '请输入基本工资', trigger: 'blur' }]">
              <el-input-number
                controls-position="right"
                :precision="2"
                :min="0"
                type="number"
                v-model="addList.pay"
                placeholder="请输入"
                style="width: 60%"
                @input="trialClassPay()"
              ></el-input-number>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="绩效工资：" prop="meritPay" :rules="[{ required: true, message: '请输入绩效工资', trigger: 'blur' }]">
              <el-input-number
                controls-position="right"
                :precision="2"
                :min="0"
                type="number"
                v-model="addList.meritPay"
                placeholder="请输入"
                style="width: 60%"
                @input="trialClassPay()"
              ></el-input-number>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="工资：" label-width="60px" prop="addAmount">
              <el-input v-model="addList.addAmount" disabled placeholder="系统计算" style="width: 60%"></el-input>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="试课补贴：">
          <el-input-number type="number" controls-position="right" v-model="addList.allowance" style="width: 88%" placeholder="请输入"></el-input-number>
          <span class="ml-half">元/学时</span>
          <!-- <el-input v-model="addList.allowance" type="number" placeholder="请输入" style="width: 88%;"></el-input><span class="ml-half">元/学时</span> -->
        </el-form-item>
        <el-form-item>
          <el-button @click="addwagesClose">取消</el-button>
          <el-button type="primary" @click="addWages('addFormWages')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 新增教练试课奖励 -->
    <el-dialog
      class="wage-css"
      :title="rewardTitleShow == true ? '编辑试课奖励' : '新增试课奖励'"
      :visible.sync="dialogReward"
      :width="screenWidth > 1300 ? '50%' : '90%'"
      :before-close="addrewardClose"
      :close-on-click-modal="false"
    >
      <el-form ref="addFormReward" :model="addList" label-width="100px">
        <div v-if="rewardShow" class="warning-title-css">
          <i class="el-icon-warning"></i>
          <span>注:未设置课程类型的试课奖励，都按默认发，未配置过试课奖励的第一条配置必须为默认!</span>
        </div>
        <el-form-item label="课程类型：" class="is-required" v-if="rewardShow">
          <el-input placeholder="默认" disabled></el-input>
        </el-form-item>
        <el-form-item label="课程类型：" class="is-required" v-else>
          <el-select v-model="addList.curriculumId" @change="curriculumChange(addList.curriculumId, 'addList')" placeholder="">
            <el-option v-for="item in curriculumOptions" :key="item.id" :label="item.enName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="试课奖励：" prop="expBonus" :rules="[{ required: true, message: '请输入试课奖励', trigger: 'blur' }]">
          <el-input-number :min="0" v-model="addList.expBonus" :precision="2" controls-position="right" style="width: 95%" placeholder="请输入"></el-input-number>
          <span style="margin-left: 20px">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="dialogReward = false">取消</el-button>
          <el-button type="primary" @click="addRewardWages('addFormReward')">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      class="wage-css"
      :title="updateShow ? '编辑正式课工资' : '新增正式课工资'"
      :visible.sync="teacherWagesVisible"
      :width="screenWidth > 1300 ? '50%' : '90%'"
      :before-close="addClose"
    >
      <el-form :model="ruleForm" ref="ruleForm" label-width="95px" class="demo-ruleForm" label-position="left">
        <div v-if="defaultShow" class="warning-title-css">
          <i class="el-icon-warning"></i>
          <span>注:未设置课程类型的正式课工资，都按默认发，未配置过工资的第一条配置必须为默认!</span>
        </div>
        <el-form-item label="课程类型：" class="is-required" v-if="defaultShow">
          <el-input placeholder="默认" disabled></el-input>
        </el-form-item>
        <el-form-item label="课程类型：" class="is-required" v-else>
          <el-select
            :disabled="ruleForm.id && ruleForm.curriculumName == '鼎英语'"
            v-model="ruleForm.curriculumId"
            @change="curriculumChange(ruleForm.curriculumId, 'ruleForm')"
            placeholder=""
          >
            <el-option v-for="item in curriculumOptions" :key="item.id" :label="item.enName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-row v-if="ruleForm.curriculumName != '鼎英语'">
          <el-col :span="8" :xs="24">
            <el-form-item label="基本工资：" prop="pay" :rules="[{ required: true, message: '请输入基本工资', trigger: 'blur' }]">
              <el-input-number
                controls-position="right"
                :precision="2"
                :min="0"
                type="number"
                v-model="ruleForm.pay"
                placeholder="请输入"
                style="width: 60%"
                @input="getCourseAmount()"
              ></el-input-number>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="绩效工资：" prop="meritPay" :rules="[{ required: true, message: '请输入绩效工资', trigger: 'blur' }]">
              <el-input-number
                controls-position="right"
                :min="0"
                :precision="2"
                type="number"
                v-model="ruleForm.meritPay"
                placeholder="请输入"
                style="width: 60%"
                @input="getCourseAmount()"
              ></el-input-number>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-form-item label="工资：" label-width="60px" prop="practiceAmount">
              <el-input v-model="ruleForm.practiceAmount" disabled placeholder="系统计算" style="width: 60%"></el-input>
              <span class="ml-half">元/学时</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-else>
          <el-form-item label="名称：" prop="name" :rules="[{ required: true, message: '请输入名称', trigger: 'blur' }]">
            <el-input v-model="ruleForm.name" placeholder="请输入名称"></el-input>
          </el-form-item>
          <div v-for="(item, index) in ruleForm.wageAddDtoList" :key="index">
            <div class="title_bold">
              <el-form-item :label="item.name" style="font-size: 20px"></el-form-item>
            </div>
            <el-row>
              <el-col :span="8" :xs="24">
                <el-form-item label="基本工资：" :prop="`wageAddDtoList[${index}].wage`" :rules="[{ required: true, message: '请输入基本工资', trigger: 'blur' }]">
                  <!-- <el-input :min="0" type="number" v-model="item.wage" placeholder="请输入" style="width: 60%;"
                      @input="getPracticeAmount(index, item)"></el-input> -->
                  <el-input-number
                    :min="0"
                    controls-position="right"
                    :precision="2"
                    v-model="item.wage"
                    placeholder="请输入"
                    style="width: 60%"
                    @input="getPracticeAmount(index, item)"
                  ></el-input-number>
                  <span class="ml-half">元/学时</span>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="绩效工资：" :prop="`wageAddDtoList[${index}].meritPay`" :rules="[{ required: true, message: '请输入绩效工资', trigger: 'blur' }]">
                  <!-- <el-input :min="0" type="number" v-model="item.meritPay" placeholder="请输入" style="width: 60%;"
                      @input="getPracticeAmount(index, item)"></el-input> -->
                  <el-input-number
                    :min="0"
                    :precision="2"
                    controls-position="right"
                    v-model="item.meritPay"
                    placeholder="请输入"
                    style="width: 60%"
                    @input="getPracticeAmount(index, item)"
                  ></el-input-number>
                  <span class="ml-half">元/学时</span>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="工资：">
                  <el-input v-model="item.practiceAmount" disabled placeholder="系统计算" style="width: 60%"></el-input>
                  <span class="ml-half">元/学时</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="repeat_icon" v-if="repeatShow">
            <i class="el-icon-warning mr-20 delete-icon"></i>
            <span style="color: #999">{{ repeatData }}</span>
          </div>
          <el-form-item v-if="showLevelorStage" label="课程学段：" class="is-required" prop="courseStageCode">
            <el-select v-model="ruleForm.courseStageCode" placeholder="请选择" multiple>
              <el-option v-for="(item, index) in courseStageType" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="showLevelorStage" label="课程等级：" class="is-required" prop="courseLevelCode">
            <el-select v-model="ruleForm.courseLevelCode" placeholder="请选择" multiple>
              <el-option v-for="(item, index) in courseLevelType" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="showLevelorStage" label="语法设置：" class="is-required" prop="grammarCode">
            <el-select v-model="ruleForm.grammarCode" placeholder="请选择" multiple>
              <el-option v-for="item in courseGrammarlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <span style="font-size: 12px">若根据学段设置正式课工资，需要同时选择“课程学段”和“课程等级”若根据语法版块设置正式工资，只需选择“语法设置”即可以上可同时选择</span>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
          <el-button @click="addClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 删除正式课 -->
    <el-dialog title="删除" :visible.sync="deleteCourseVisible" width="380px" :close-on-click-modal="false" class="delete_kit">
      <i class="el-icon-delete delete_icon"></i>
      <div>
        <div style="margin-top: 10px">
          删除课程类型：
          <span class="c-f89">{{ deleteName }}</span>
        </div>
        <div class="mt-20">
          <i class="el-icon-warning mr-20 delete-icon"></i>
          <span style="color: #999">删除后，{{ activeName == 'first' ? '工资' : activeName == 'second' ? '试课工资' : '试课奖励' }}按照默认发放！</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="deleteCourseVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="deleteTearch">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 复习工资基本设置 -->
    <el-dialog :title="reviewWagesTitle" :visible.sync="reviewWagesVisible" width="560px" :close-on-click-modal="false" :before-close="reviewWagesClose">
      <el-form ref="reviewWagesFrom" :model="reviewWagesInfo" label-width="120px">
        <el-form-item
          label="复习公约数："
          :rules="[
            { required: true, message: '请输入公约数', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
          prop="commonDivisor"
        >
          <el-input-number
            :min="0"
            type="number"
            :precision="0"
            controls-position="right"
            v-model="reviewWagesInfo.commonDivisor"
            style="width: 300px"
            placeholder="请输入"
          ></el-input-number>
          <span style="margin-left: 20px">个</span>
        </el-form-item>
        <el-form-item
          label="公约数工资："
          :rules="[
            { required: true, message: '请输入公约数工资', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
          prop="commonDivisorSalary"
        >
          <el-input-number
            :min="0"
            type="number"
            :precision="2"
            controls-position="right"
            v-model="reviewWagesInfo.commonDivisorSalary"
            style="width: 300px"
            placeholder="请输入"
          ></el-input-number>
          <span style="margin-left: 20px">元</span>
        </el-form-item>
        <el-form-item
          label="完成复习奖金："
          :rules="[
            { required: true, message: '请输入复习奖金', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
          prop="completeReward"
        >
          <el-input-number
            :min="0"
            type="number"
            :precision="2"
            controls-position="right"
            v-model="reviewWagesInfo.completeReward"
            style="width: 300px"
            placeholder="请输入"
          ></el-input-number>
          <span style="margin-left: 20px">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="reviewWagesClose">取消</el-button>
          <el-button type="primary" @click="updatereviewWagesList">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 复习奖金设置 -->
    <el-dialog :title="reviewTitle" :visible.sync="reviewVisible" width="560px" :close-on-click-modal="false" :before-close="reviewClose">
      <el-form ref="reviewFrom" :model="reviewInfo" label-width="120px">
        <el-form-item
          label="起始值："
          prop="startingValue"
          :rules="[
            { required: true, message: '请输入起始值', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
        >
          <el-input-number
            :min="0"
            type="number"
            :max="100"
            controls-position="right"
            v-model="reviewInfo.startingValue"
            style="width: 300px"
            placeholder="请输入"
          ></el-input-number>
          <span style="margin-left: 20px">%</span>
        </el-form-item>
        <el-form-item
          label="结束值："
          prop="endValue"
          :rules="[
            { required: true, message: '请输入结束值', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
        >
          <el-input-number :min="0" type="number" :max="100" controls-position="right" v-model="reviewInfo.endValue" style="width: 300px" placeholder="请输入"></el-input-number>
          <span style="margin-left: 20px">%</span>
        </el-form-item>
        <el-form-item
          label="奖金金额："
          prop="rewardAmount"
          :rules="[
            { required: true, message: '请输入奖金金额', trigger: 'blur' },
            { validator, trigger: 'blur' }
          ]"
        >
          <el-input-number
            :min="0"
            type="number"
            controls-position="right"
            :precision="2"
            v-model="reviewInfo.rewardAmount"
            style="width: 300px"
            placeholder="请输入"
          ></el-input-number>
          <span style="margin-left: 20px">元</span>
        </el-form-item>
        <el-form-item>
          <el-button @click="reviewClose">取消</el-button>
          <el-button type="primary" @click="updatereviewList">确定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog class="wages" :visible.sync="reviewDelVisible" width="560px">
      <div class="del-content-css">
        <div class="delect-title-css">
          <i class="el-icon-delete"></i>
          <span>删除</span>
        </div>
        <div class="content-delect-css">
          <span>删除复习完成率{{ delReviewInfo.startingValue }}%~{{ delReviewInfo.endValue }}%,奖金{{ delReviewInfo.rewardAmount }}</span>
        </div>
        <div class="del-bottom-css">
          <i class="el-icon-warning" style="color: #e6a23c"></i>
          <span>删除后，教练将不获得奖金</span>
        </div>
      </div>
      <div style="text-align: right; padding-right: 20px; padding-bottom: 20px">
        <el-button size="small" @click="reviewDelVisible = false">取消</el-button>
        <el-button size="small" type="primary" @click="delectReviewData">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getTeacherWageList,
    getCurriculumTeacherWageConfig,
    updateCourseClass,
    getDeliverCodes,
    getCurriculumExpCourse,
    findExpWageRewardPage,
    deleteExpWageReward,
    addOrUpdateCurriculumTeacherWageConfig,
    updateWageReward,
    addOrUpdateCurriculumExpCourseConfig,
    getTeacherLevel,
    getAddOrUpdateTeacher,
    getDeleteTeacher,
    getDeleteNoTeacher,
    deleteCurriculumTeacherWageConfig,
    getCourseGrammar,
    saveOrUpdateExpWageReward
  } from '@/api/FinanceApi/assistantWages';
  import { saveReviewSalaryConfig, saveReviewRewardConfig, deleteReviewRewardConfig, getReviewSalaryConfig, getReviewRewardConfig } from '@/api/FinanceApi/trialclassDividend';
  import enTypes from '@/api/student/bstatus';
  import { mapGetters } from 'vuex';
  import { bvstatusListOne } from '@/api/paikeManage/classCard';
  import checkPermission from '@/utils/permission';
  export default {
    data() {
      return {
        dialogVisible: false,
        wagesList: [], // 教练工资列表
        teacherWagesVisible: false,
        formalWages: [],
        formInline: '',
        value: '',
        updateList: '', // 修改
        updateId: '',
        screenWidth: window.screen.width,
        //  表头数据
        tableHead: [
          { id: 1, val: '教练等级', prop: 'level' },
          { id: 2, val: '基本工资', prop: 'wage' },
          { id: 3, val: '绩效工资', prop: 'meritPay' },
          { id: 4, val: '工资', prop: 'total' }
        ],
        url: 'https://document.dxznjy.com/alading/correcting/no_data.png',
        show: false, // 判断登录用户是否有权限
        activeName: 'first',
        // wagesDialog: false,
        editList: {
          name: '',
          pay: '',
          meritPay: '',
          id: ''
        },
        editAmount: '',
        earningsList: [], // 试课工资
        rewardList: [], // 试课奖励
        //试课奖励
        querydata: {
          pageNum: 1,
          pageSize: 10
        },
        //试课工资
        queryearnings: {
          pageNum: 1,
          pageSize: 10
        },
        //正式课（除鼎英语）
        formalWagesQuery: {
          pageNum: 1,
          pageSize: 10
        },
        //正式课（鼎英语）
        wagesQuery: {
          pageNum: 1,
          pageSize: 10
        },
        dialogStatus: false, // 弹窗类型
        rewardDialog: false,
        addStatus: false,
        addList: {
          name: '',
          pay: '',
          meritPay: ''
        },
        addAmount: '',
        dialogWages: false,
        dialogReward: false,
        // 新增正式课工资
        ruleForm: {
          name: '',
          wageAddDtoList: '',
          courseStageCode: [], // 课程学段
          courseLevelCode: [], // 课程等级
          grammarCode: [] // 语法设置
        },
        rules: {
          name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
        },
        curriculumOptions: [],
        curriculumOptionsCopy: [],
        courseLevelType: [], // 模块
        courseStageType: [], // 课程学段
        updateShow: false, // 正式课编辑还是删除
        deleteCourseVisible: false, // 正式课删除
        deleteCourseLevel: [], // 正式课删除的课程模块
        deleteCourseId: '', // 正式课删除的id
        deleteName: '',
        deletIndex: 0,
        deletList: '',
        repeatData: '', // 学段，或者模块重复数据
        repeatShow: false, // 是否显示学段、课程模块重复提醒
        reviewAddShow: false, // 新增、编辑
        showLevelorStage: false, // 正式课编辑是否显示学段、课程模块
        //试课工资
        earningShow: false,
        earningTitleShow: true,
        //试课奖励
        rewardShow: false,
        rewardTitleShow: true,
        teacherLevel: '', // 教练等级
        deleteReviewWageName: '',
        deleteCourseStage: '',
        deleteReviewId: '',
        courseGrammarlist: [], // 语法
        isAdmin: false,
        deliveryCenter: '13349184887',
        defaultShow: true,
        amountDefaultName: '',
        reviewDelVisible: false,
        reviewWagesList: [],
        reviewRate: [],
        delReviewInfo: {},
        reviewWagesVisible: false,
        reviewWagesInfo: {},
        reviewWagesTitle: '新增复习工资基本设置',
        reviewVisible: false,
        reviewInfo: {},
        reviewTitle: '新增复习奖金设置'
      };
    },
    created() {
      // this.isAdmin = ls.getItem('rolesVal') === 'admin' || ls.getItem('rolesVal') === 'JiaofuManager'
      // if (this.nick == this.deliveryCenter) {
      //     this.isAdmin = true;
      // }
      if (this.roles[0].val == 'DeliveryCenter') {
        this.isAdmin = true;
      }
    },
    computed: {
      ...mapGetters(['nick', 'roles'])
    },
    mounted() {
      this.initData();
      this.formalWagesList();
      this.getTeacherLevelList();
      this.getStady();
      this.getCourseLevel();
      this.courseGrammarList();
      this.getbvstatusList();
    },
    methods: {
      //复习工资
      validator(rule, value, callback) {
        if (value == 0) {
          if (rule.field == 'endValue' || rule.field == 'startingValue' || rule.field == 'commonDivisor') {
            callback(new Error('数量不能为 0'));
          } else {
            callback(new Error('金额不能为 0'));
          }
        } else {
          callback();
        }
      },
      checkPermission,
      //新增编辑复习工资
      updatereviewWagesList() {
        this.$refs['reviewWagesFrom'].validate((valid) => {
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '编辑中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            saveReviewSalaryConfig(this.reviewWagesInfo)
              .then((res) => {
                loading.close();
                this.$message.success('操作成功');
                this.$refs['reviewWagesFrom'].resetFields();
                this.reviewWagesVisible = false;
                if (res.data.id) {
                  this.reviewWagesList = [res.data] || [];
                } else {
                  this.reviewWagesList = [];
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //新增编辑复习完成率奖金
      updatereviewList() {
        this.$refs['reviewFrom'].validate((valid) => {
          if (valid) {
            const loading = this.$loading({
              lock: true,
              text: '编辑中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            saveReviewRewardConfig(this.reviewInfo)
              .then((res) => {
                loading.close();
                this.$message.success('操作成功');
                this.$refs['reviewFrom'].resetFields();
                this.reviewVisible = false;
                if (res.data) {
                  this.reviewRate = res.data || [];
                }
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      reviewClose() {
        this.$refs['reviewFrom'].resetFields();
        this.reviewVisible = false;
      },
      reviewWagesClose() {
        this.$refs['reviewWagesFrom'].resetFields();
        this.reviewWagesVisible = false;
      },
      reviewWagesEdit(row) {
        this.reviewWagesVisible = true;
        this.reviewWagesTitle = '新增复习工资基本设置';
        this.reviewWagesInfo = {};
        if (row) {
          this.reviewWagesTitle = '编辑复习工资基本设置';
          this.reviewWagesInfo = { ...row };
        }
      },
      reviewEditOrAdd(row) {
        this.reviewVisible = true;
        this.reviewTitle = '新增复习奖金设置';
        this.reviewInfo = {};
        if (row) {
          this.reviewTitle = '编辑复习奖金设置';
          this.reviewInfo = { ...row };
        }
      },
      //删除
      delectReview(index, info) {
        this.delReviewInfo = { ...info, index: index };
        this.reviewDelVisible = true;
      },
      //删除弹窗确定
      delectReviewData() {
        deleteReviewRewardConfig(this.delReviewInfo).then((res) => {
          if (res.code == 20000) {
            this.reviewDelVisible = false;
            this.reviewRate = res.data;
            this.$message.success('删除成功');
          }
        });
      },
      async getReviewSalaryConfig() {
        await getReviewSalaryConfig().then((res) => {
          if (res.data.id) {
            this.reviewWagesList = [res.data] || [];
          } else {
            this.reviewWagesList = [];
          }
        });
      },
      async getReviewRewardConfig() {
        await getReviewRewardConfig().then((res) => {
          if (res.data) {
            this.reviewRate = res.data || [];
          }
        });
      },
      // validator(rule, value, callback) {
      //   console.log(rule)
      //   console.log(value)
      //   console.log('..............................................')
      //   if (value == 0) {
      //     callback(new Error('金额不能为 0'));
      //   } else {
      //     callback();
      //   }
      // },
      getbvstatusList() {
        bvstatusListOne({}).then((res) => {
          this.curriculumOptionsCopy = res.data;
        });
      },
      change(e) {
        this.$forceUpdate();
      },
      changePerformance(e) {
        this.$forceUpdate();
      },
      async updatePrice() {
        let that = this;
        console.log(that.updateList);
        let params = {
          id: that.updateList.id,
          pay: that.updateList.value,
          meritPay: that.updateList.configState
        };
        if (that.updateList.value == '') {
          this.$message.error('基本工资不能为空');
          return;
        } else if (that.updateList.configState == '') {
          this.$message.error('绩效工资不能为空');
          return;
        }
        updateCourseClass(params).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.dialogVisible = false;
          this.initData();
        });
      },
      handleClick(tab, event) {
        if (tab.index == 0) {
          this.initData();
          this.formalWagesList();
        } else if (tab.index == 1) {
          this.trialClass();
        } else if (tab.index == 2) {
          this.getRewardWages();
        } else {
          this.getReviewSalaryConfig();
          this.getReviewRewardConfig();
        }
      },
      async initData() {
        let that = this;
        await getTeacherWageList({ pageNum: this.wagesQuery.pageNum, pageSize: this.wagesQuery.pageSize }).then((res) => {
          that.wagesList = res.data.data || [];
          this.wagesQuery.total = Number(res.data.totalItems);
        });
      },
      async formalWagesList() {
        let that = this;
        await getCurriculumTeacherWageConfig({ pageNum: this.formalWagesQuery.pageNum, pageSize: this.formalWagesQuery.pageSize }).then((res) => {
          that.formalWages = res.data.data || [];
          this.formalWagesQuery.total = Number(res.data.totalItems);
        });
      },
      // formalWages
      // 判断教练相关功能显示
      async getSignal() {
        await getDeliverCodes().then((res) => {
          this.show = res.data;
          if (this.show) {
            this.initData();
          } else {
            this.$message.error('您暂无该权限');
          }
        });
      },
      //试课奖励列表
      getRewardWages() {
        findExpWageRewardPage({ pageNum: this.querydata.pageNum, pageSize: this.querydata.pageSize }).then((res) => {
          this.rewardList = res.data.data || [];
          this.querydata.total = Number(res.data.totalItems);
        });
      },
      //试课奖励分页
      handleCurrentChange(val) {
        this.querydata.pageNum = val;
        this.getRewardWages();
      },
      handleSizeChange(val) {
        this.querydata.pageSize = val;
        this.getRewardWages();
      },
      //试课工资分页
      handleCurrentearnChange(val) {
        this.queryearnings.pageNum = val;
        this.trialClass();
      },
      handleSizeearnChange(val) {
        this.queryearnings.pageSize = val;
        this.trialClass();
      },
      //正式课工资分页
      handleFormSizeChange(val) {
        this.querydata.pageSize = val;
        this.formalWagesList();
      },
      handleFormCurrentChange(val) {
        this.querydata.pageNum = val;
        this.formalWagesList();
      },
      //正式课工资鼎英语分页
      handleWagesSizeChange(val) {
        this.wagesQuery.pageSize = val;
        this.initData();
      },
      handleWagesCurrentChange(val) {
        this.wagesQuery.pageNum = val;
        this.initData();
      },
      trialClass() {
        getCurriculumExpCourse({ pageNum: this.queryearnings.pageNum, pageSize: this.queryearnings.pageSize }).then((res) => {
          this.earningsList = res.data.data || [];
          this.queryearnings.total = Number(res.data.totalItems);
        });
      },
      editDialog(row, key) {
        console.log('🚀 ~ editDialog ~ row:', row);
        this.curriculumOptions = JSON.parse(JSON.stringify(this.curriculumOptionsCopy));
        if (key == 1) {
          this.addList = {
            id: row.id,
            curriculumId: row.configExtra,
            curriculumName: row.courseName,
            name: row.name,
            pay: row.value,
            meritPay: row.configState,
            allowance: row.valueExtend,
            addAmount: Number(row.value) + Number(row.configState)
          };
          this.dialogWages = true;
          if (row.configExtra != 'default') {
            this.earningShow = false;
          } else {
            this.earningShow = true;
          }
          this.earningTitleShow = false;
        } else {
          this.addList = { ...row };
          this.dialogReward = true;
          this.rewardTitleShow = true;
          if (row.curriculumId != 'default') {
            this.rewardShow = false;
          } else {
            this.rewardShow = true;
          }
        }
      },
      editWages(formName) {
        let that = this;
        that.$refs[formName].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            updateWageReward(that.editList)
              .then((res) => {
                loading.close();
                that.$message.success('操作成功');
                that.$refs[formName].resetFields();
                that.dialogWages = false;
                that.dialogReward = false;
                that.initData();
                that.trialClass();
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      curriculumChange(val, info) {
        if (val) {
          this.curriculumOptions.forEach((item) => {
            if (item.id == val) {
              console.log(info + '');
              this[info + ''].curriculumName = item.enName;
            }
          });
        } else {
          this[info + ''].curriculumName = '';
        }
      },
      handleClose() {
        if (this.dialogStatus) {
          this.$refs['updateForm'].resetFields();
        } else {
          this.$refs['editForm'].resetFields();
        }
        this.dialogWages = false;
      },
      newlAdded() {
        this.earningTitleShow = true;
        this.addList = {};
        this.addList.name = 'EXP_COURSE';
        if (this.earningsList.length == 0) {
          this.earningShow = true;
        } else {
          this.earningShow = false;
        }
        this.dialogWages = true;
        this.curriculumOptions = JSON.parse(JSON.stringify(this.curriculumOptionsCopy));
      },
      addRewarded() {
        this.addList = {};
        this.curriculumOptions = JSON.parse(JSON.stringify(this.curriculumOptionsCopy));
        this.dialogReward = true;
        this.rewardTitleShow = false;
        if (this.rewardList.length == 0) {
          this.rewardShow = true;
        } else {
          this.rewardShow = false;
        }
      },
      addWages(formName) {
        let that = this;
        this.addList.name = 'EXP_COURSE';
        delete this.addList.curriculumName;
        if (!that.earningShow) {
          if (!that.addList.curriculumId) {
            that.$message.error('请选择课程类型');
            return;
          }
        } else {
          this.addList.curriculumId = 'default';
        }
        console.log(that.addList);
        that.addList.configGroup = 'EXP_WAGE_REWARD';
        that.$refs[formName].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            addOrUpdateCurriculumExpCourseConfig(that.addList)
              .then((res) => {
                loading.close();
                that.trialClass();
                that.$message.success('操作成功');
                that.$refs[formName].resetFields();
                that.dialogWages = false;
                that.dialogReward = false;
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            return false;
          }
        });
      },
      addRewardWages(formName) {
        let that = this;
        delete this.addList.name;
        if (!that.rewardShow) {
          if (!that.addList.curriculumId) {
            that.$message.error('请选择课程类型');
            return;
          }
        } else {
          this.addList.curriculumId = 'default';
          this.addList.curriculumName = '默认';
        }
        that.$refs[formName].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            saveOrUpdateExpWageReward(that.addList)
              .then((res) => {
                loading.close();
                that.getRewardWages();
                that.$message.success('操作成功');
                that.$refs[formName].resetFields();
                that.dialogWages = false;
                that.dialogReward = false;
              })
              .catch((err) => {
                loading.close();
              });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      // 教练工资
      addwagesClose() {
        this.$refs['addFormWages'].resetFields();
        this.dialogWages = false;
      },
      // 教练试课奖励
      addrewardClose() {
        this.$refs['addFormReward'].resetFields();
        this.dialogReward = false;
      },
      classClose() {
        this.$refs['editForm'].resetFields();
        this.wagesDialog = false;
      },
      rewardClose() {
        this.$refs['updateForm'].resetFields();
        this.rewardDialog = false;
      },
      trialClassPay() {
        this.$forceUpdate();
        if (this.addList.pay && this.addList.meritPay) {
          this.addList.addAmount = (Number(this.addList.pay) + Number(this.addList.meritPay)).toFixed(2);
        }
      },
      // 动态class
      getRowClass({ rowIndex, columnIndex }) {
        if (rowIndex == 0) {
          return 'background:#e2eaf6';
        }
      },
      //获取学段下拉框
      getStady() {
        var enType = 'CourseStage';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseStageType = res.data;
        });
      },
      //获取课程等级下拉框
      getCourseLevel() {
        var enType = 'CourseLevel';
        enTypes.getEnumerationAggregation(enType).then((res) => {
          this.courseLevelType = res.data;
        });
      },
      // 正式课工资
      getPracticeAmount(index, item) {
        this.$forceUpdate();
        if (this.ruleForm.wageAddDtoList[index].wage && this.ruleForm.wageAddDtoList[index].meritPay) {
          this.ruleForm.wageAddDtoList[index].practiceAmount = (Number(item.wage) + Number(item.meritPay)).toFixed(2);
        }
      },
      //计算工资价格
      getCourseAmount() {
        this.$set(this.ruleForm, 'practiceAmount', '');
        if (this.ruleForm.pay >= 0 && this.ruleForm.meritPay >= 0) {
          var practiceAmount = (Number(this.ruleForm.pay) + Number(this.ruleForm.meritPay)).toFixed(2);
          this.$set(this.ruleForm, 'practiceAmount', practiceAmount);
        }
      },
      // 正式课列表模块展示
      changeCourseLevel(val) {
        let data = [];
        this.courseLevelType.filter((item) => {
          if (val.indexOf(item.value) > -1) {
            data.push(item.label);
          }
        });
        return data.join('、');
      },
      changeGrammarlist(val) {
        let data = [];
        this.courseGrammarlist.filter((item) => {
          if (val.indexOf(item.value) > -1) {
            data.push(item.label);
          }
        });
        return data.join('、');
      },
      // 教练等级
      getTeacherLevelList() {
        getTeacherLevel().then((res) => {
          this.teacherLevel = res.data;
        });
      },
      addClose() {
        this.$refs['ruleForm'].resetFields();
        this.teacherWagesVisible = false;
        this.teacherLevelChange();
      },
      // 教练正式课工资新增、编辑
      submitForm(formName) {
        let that = this;
        let submtInfo = JSON.parse(JSON.stringify(this.ruleForm));
        if (!that.defaultShow) {
          if (!that.ruleForm.curriculumId) {
            that.$message.error('请选择课程类型');
            return;
          }
        }

        this.ruleForm.configGroup = 'TEACHER_WAGE';
        if (that.defaultShow) {
          that.ruleForm.name = '默认设置';
          this.ruleForm.curriculumId = 'default';
        }
        if (submtInfo.curriculumName == '鼎英语') {
          if (
            submtInfo.courseStageCode.length == 0 &&
            submtInfo.courseLevelCode.length == 0 &&
            submtInfo.grammarCode.length == 0 &&
            submtInfo.length != 0 &&
            submtInfo.name != '默认设置'
          ) {
            that.$message.error('请选择课程学段、课程等级或语法设置');
            return;
          }
          if (submtInfo.courseStageCode.length == 0 && submtInfo.courseLevelCode.length != 0 && that.wagesList.length != 0 && submtInfo.name != '默认设置') {
            that.$message.error('请选择课程学段');
            return;
          }
          if (submtInfo.courseStageCode.length != 0 && submtInfo.courseLevelCode.length == 0 && that.wagesList.length != 0 && submtInfo.name != '默认设置') {
            that.$message.error('请选择课程等级');
            return;
          }
        } else {
          delete this.ruleForm.courseLevelCode;
          delete this.ruleForm.courseStageCode;
          delete this.ruleForm.grammarCode;
          delete this.ruleForm.wageAddDtoList;
          delete this.ruleForm.practiceAmount;
          if (that.ruleForm.curriculumName) {
            that.ruleForm.name = that.ruleForm.curriculumName;
          }
        }
        console.log(that.$refs[formName]);
        that.$refs[formName].validate((valid) => {
          if (valid) {
            const loading = that.$loading({
              lock: true,
              text: '加载中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            if (this.ruleForm.curriculumName != '鼎英语') {
              addOrUpdateCurriculumTeacherWageConfig(that.ruleForm)
                .then((res) => {
                  loading.close();
                  that.$message.success('操作成功');
                  that.$refs[formName].resetFields();
                  that.teacherWagesVisible = false;
                  that.formalWagesList();
                })
                .catch((err) => {
                  if (err.code === 80001) {
                    that.repeatShow = true;
                    that.repeatData = err.message;
                    setTimeout(() => {
                      that.repeatShow = false;
                    }, 2500);
                  }
                  loading.close();
                });
            } else {
              getAddOrUpdateTeacher(that.ruleForm)
                .then((res) => {
                  loading.close();
                  that.$message.success('操作成功');
                  that.$refs[formName].resetFields();
                  that.teacherWagesVisible = false;
                  that.initData();
                })
                .catch((err) => {
                  if (err.code === 80001) {
                    that.repeatShow = true;
                    that.repeatData = err.message;
                    setTimeout(() => {
                      that.repeatShow = false;
                    }, 2500);
                  }
                  loading.close();
                });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      teacherLevelChange() {
        this.ruleForm = JSON.parse(JSON.stringify(this.ruleForm));
        this.ruleForm.name = '';
        this.ruleForm.courseStageCode = [];
        this.ruleForm.courseLevelCode = [];
        this.teacherLevel.forEach((item) => {
          this.ruleForm.wageAddDtoList = this.teacherLevel.map((item) => {
            return {
              level: item.level,
              name: item.name,
              wage: '',
              meritPay: '',
              practiceAmount: ''
            };
          });
        });
      },
      // 正式课工资新增、编辑
      async openTearcherWages(status, row, src) {
        console.log('🚀 ~ openTearcherWages ~ row:', row);
        if (status == 'update') {
          this.updateShow = true;
          // JSON.parse(JSON.stringify(row));
          if (src) {
            this.updateShow = true;
            this.showLevelorStage = true;
            this.defaultShow = false;
            this.ruleForm = JSON.parse(JSON.stringify(row)); // 深拷贝防止列表数据变化
            let data = this.ruleForm.wageAddDtoList;
            this.ruleForm.curriculumId = row.configExtra;
            this.ruleForm.curriculumName = src;
            for (let i = 0; i < data.length; i++) {
              console.log(data[i]);
              data[i].practiceAmount = (Number(data[i].wage) + Number(data[i].meritPay)).toFixed(2);
            }
            this.curriculumOptions = JSON.parse(JSON.stringify(this.curriculumOptionsCopy));
          } else {
            this.ruleForm = {
              id: row.id,
              configGroup: row.configGroup,
              name: row.name,
              pay: row.value,
              meritPay: row.configState,
              curriculumId: row.configExtra,
              curriculumName: row.name,
              practiceAmount: (Number(row.configState) + Number(row.value)).toFixed(2)
            };
            this.curriculumOptions = this.curriculumOptionsCopy.filter((i) => i.enName != '鼎英语');
            if (row.configExtra != 'default') {
              this.defaultShow = false;
            } else {
              this.defaultShow = true;
            }
          }
        } else {
          this.curriculumOptions = JSON.parse(JSON.stringify(this.curriculumOptionsCopy));
          if (this.wagesList.length == 0 && this.formalWages.length == 0) {
            this.defaultShow = true;
            this.showLevelorStage = false;
          } else {
            this.defaultShow = false;
            this.showLevelorStage = true;
          }
          this.ruleForm = {
            name: '',
            wageAddDtoList: '',
            courseStageCode: [], // 课程学段
            courseLevelCode: [], // 课程等级
            grammarCode: [] // 语法设置
          };
          await this.teacherLevelChange();
          this.updateShow = false;
        }
        this.teacherWagesVisible = true;
      },
      singleDelete(type, row, src, index) {
        let data = [];
        this.deleteCourseId = row.id;
        this.deleteName = type;
        this.deletIndex = index;
        this.deletList = src;
        if (row.courseLevelCode) {
          this.courseLevelType.filter((item) => {
            if (row.courseLevelCode.indexOf(item.value) > -1) {
              data.push(item.label);
            }
          });
          this.deleteCourseLevel = data.join('、');
        }
        this.deleteCourseVisible = true;
      },
      // 正式课课程删除
      deleteTearch() {
        let that = this;
        this.deleteCourseVisible = false;
        const loading = this.$loading({
          lock: true,
          text: '删除中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        let data = {
          id: this.deleteCourseId
        };
        if (this.deletList == 'rewardList') {
          deleteExpWageReward(this[this.deletList + ''][this.deletIndex].id)
            .then((res) => {
              loading.close();
              that.$message.success('删除成功');
              that.deleteCourseVisible = false;
              // this.initData();
              that.getRewardWages();
            })
            .catch((err) => {
              loading.close();
            });
        } else if (this.deletList == 'earningsList') {
          getDeleteNoTeacher(this.deleteCourseId)
            .then((res) => {
              loading.close();
              this.$message.success('删除成功');
              this.deleteCourseVisible = false;
              this.trialClass();
            })
            .catch((err) => {
              loading.close();
            });
        } else if (this.deletList == 'formalWages') {
          deleteCurriculumTeacherWageConfig(this.deleteCourseId)
            .then((res) => {
              loading.close();
              this.$message.success('删除成功');
              this.deleteCourseVisible = false;
              this.formalWagesList();
            })
            .catch((err) => {
              loading.close();
            });
        } else if (this.deletList == 'wagesList') {
          getDeleteTeacher(data)
            .then((res) => {
              loading.close();
              this.$message.success('操作成功');
              this.deleteCourseVisible = false;
              this.initData();
            })
            .catch((err) => {
              loading.close();
            });
        }
      },
      courseGrammarList() {
        getCourseGrammar()
          .then((res) => {
            console.log(res);
            this.courseGrammarlist = res.data;
          })
          .catch((err) => {});
      }
    }
  };
</script>
<style lang="scss" scoped>
  .frame {
    margin-top: 0.5vh;
    background-color: rgba(255, 255, 255);
  }
  .rule_style {
    display: inline-block;
    margin-left: 10px;
    .el-icon-question {
      display: inline-block;
      margin-right: 5px;
      color: #f89728;
    }
  }
  .del-content-css {
    padding-bottom: 40px;
    padding-left: 30px;
    span {
      display: inline-block;
      margin-left: 5px;
    }
    .delect-title-css {
      color: #f56c6c;
      font-size: 20px;
    }
    .content-delect-css {
      margin-top: 20px;
    }
    .del-bottom-css {
      margin-top: 12px;
      span {
        color: #f56c6c;
      }
    }
  }
  .wages ::v-deep.el-dialog__body {
    padding: 0 !important;
  }
  .wage-css ::v-deep.el-dialog__body {
    padding-top: 0 !important;
  }
  ::v-deep.is-controls-right .el-input-number__increase {
    display: none !important;
  }
  ::v-deep.is-controls-right .el-input__inner {
    text-align: left !important;
    padding-left: 20 !important;
  }
  ::v-deep.is-controls-right .el-input__inner {
    padding-left: 20 !important;
  }
  ::v-deep.is-controls-right .el-input-number__decrease {
    display: none !important;
  }
  .effectStatusRed {
    color: #f56c6c;
  }
  .effectStatusWarning {
    color: #e6a23c;
  }
  .effectStatusSuccess {
    color: #67c23a;
  }
  .price {
    display: flex;
    justify-content: space-between;
    width: 280px;
    color: #c0c4cc;
    padding: 0 15px;
    background-color: #f5f7fa;
    border-radius: 5px;
    border: 1px solid #dfe4ed;
  }

  .amount {
    width: 220px;
    height: 36px;
    margin-left: 4px;
    line-height: 36px;
    padding: 0 15px;
    color: #c0c4cc;
    background-color: #f5f7fa;
    border-radius: 5px;
    box-sizing: border-box;
    border: 1px solid #dfe4ed;
  }

  .course {
    width: 300px;
    color: #c0c4cc;
    padding: 0 15px;
    background-color: #f5f7fa;
    border-radius: 5px;
    border: 1px solid #dfe4ed;
  }

  .no_data {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .el-card__body {
    height: 100%;
  }
  ::v-deep .el-scrollbar__bar.is-horizontal {
    display: none !important;
  }
  .el-icon-warning {
    color: #f89728;
  }
  .warning-title-css {
    line-height: 60px;
    span {
      display: inline-block;
      margin-left: 10px;
      color: #ff4949;
    }
  }

  .wages_title {
    margin-left: 10px;
    color: #999;
    font-size: 14px;
  }

  .flex_s {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  // .mb-20 {
  //     margin-bottom: 20px;
  // }
  .mt-30 {
    margin-top: 30px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  .flex_x {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .ml-20 {
    margin-left: 20px;
  }

  .mr-20 {
    margin-right: 20px;
  }

  .ml-half {
    margin-left: 10px;
  }

  .title_bold {
    ::v-deep .el-form-item__label {
      font-size: 16px !important;
      font-weight: bold !important;
    }
  }

  ::v-deep.delete_kit .el-dialog__title {
    margin-left: 20px !important;
  }

  .delete_icon {
    position: absolute;
    left: 1vw;
    top: 22px;
    color: #bf0502;
    font-size: 18px;
  }

  .el-icon-edit {
    color: #f89728;
  }

  .c-f89 {
    color: #f89728;
  }

  .delete-icon {
    margin: 0 10px 0 !important;
  }

  .demo-ruleForm {
    position: relative;
  }

  .repeat_icon {
    position: absolute;
    top: 19vw;
    left: 5vw;
    z-index: 999;
    padding: 10px;
    border-radius: 3px;
    box-shadow: 0 0 10px #cccbcb;
    background-color: #fff;
  }

  .formal_classes_title {
    display: flex;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: #e2eaf6;
    color: #909399;
    font-size: 14px;
    font-weight: bold;
  }

  .formal_classes_content {
    display: flex;
    height: 60px;
    font-size: 14px;
    color: #606266;
    background-color: #fff;
    border-bottom: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
  }

  .plr-20 {
    padding: 0 20px;
  }

  .class_wi90 {
    width: 95px !important;
  }
  .class_wi130 {
    width: 150px !important;
  }
  .class_wi1 {
    width: 100px !important;
  }
  .class_wi2 {
    width: 220px !important;
  }

  .class_wi3 {
    width: 300px !important;
  }

  .class_Center {
    text-align: center;
  }

  .border_r {
    border-right: 1px solid #dfe6ec;
  }

  .flex_column {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .lh-60 {
    line-height: 60px;
  }

  .text-ellipsis {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 3; //行数
    line-clamp: 3;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }

  .plr-10 {
    padding: 0 10px;
  }
</style>
