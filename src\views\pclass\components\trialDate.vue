<!--交付中心-试课学员管理-试课列表-->
<template>
  <div>
    <el-select ref="selectLable" v-model="timeValue" placeholder="请选择日期" v-loadmore="loadMore" @change="changeTime"
      :disabled="disabled">
      <el-option v-for="item in dates" :key="item.value" :label="item.label" :value="item.value"></el-option>
    </el-select>
  </div>
</template>
  
<script>

import moment from 'moment'//导入文件
moment.locale('zh-cn')

export default {
  directives: {  // 在组件中接受一个 directives 的选项
    loadmore: {
      inserted(el, binding) {
        const dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');  // 获取下拉框元素
        dom.addEventListener('scroll', function () {       // 监听元素触底
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  name: "trialDate",
  props: {
    dateTime: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      dates: [],
      options: [],
      chooseNum: 60,
      timeValue: '',
      show: false
    };
  },
  computed: {
    //自动计算当前时间
    currentdateindex: function (nv) {
      console.log(nv)
      if (nv == 0) {
        let h = parseInt(moment().format("HH"))
        this.hours = []
        for (let i = h; i < 24; i++) {
          let str = i;
          if (i < 10) {
            str = '0' + str;
          } else {
            str = '' + str;
          }
          this.hours.push(str);
        }
        this.minutes = []
        let m = parseInt(moment().format("mm"))
        for (let i = m; i < 60; i++) {
          let str = i;
          if (i < 10) {
            str = '0' + str;
          } else {
            str = '' + str;
          }
          this.minutes.push(str);
        }
      }
    }
  },
  mounted() {
    console.log('1111111111111111111111')
    this.initDate();
  },
  methods: {
    //初始化时间
    initDate() {
      //设置日期数组
      this.dates = [];
      let date = moment().format("YYYY-MM-DD");
      console.log(this.dateTime);
      if (this.dateTime < date) {
        this.show = true;
      }
      for (let i = 0; i <= this.chooseNum; i++) {
        this.dates.push({
          value: moment(this.show ? this.dateTime : date).add(i, 'days').format("YYYY-MM-DD"),
          time: moment(this.show ? this.dateTime : date).add(i, 'days').format("YYYY-MM-DD"),
          label: moment(this.show ? this.dateTime : date).add(i, 'days').format("MMMDo dddd")
        })
      }
      console.log(this.dates)
      this.timeValue = this.dateTime;
      this.$emit('onChild', this.timeValue);  //子组件向父组件传值
      this.$forceUpdate();
    },


    loadMore() {
      console.log('触底了')
      let that = this;
      for (let i = 0; i <= that.chooseNum; i++) {
        that.dates.push({
          value: moment(that.dates[that.dates.length - 1].time).add(1, 'days').format("YYYY-MM-DD"),
          time: moment(that.dates[that.dates.length - 1].time).add(1, 'days').format("YYYY-MM-DD"),
          label: moment(that.dates[that.dates.length - 1].time).add(1, 'days').format('MMMDo dddd')
        })
      }
      console.log(that.dates)

    },

    changeTime(val) {
      console.log(val)
      let data = val ? this.dates.find(ele => ele.value === val).time : '';
      this.$emit('onChild', data)  //子组件向父组件传值
    }

    //滚动切换时间
    // bindDateChange(e) { //有效日期的滚动日期时间方法
    //     let valueArr = e.detail.value
    //     this.hours = []
    //     this.minutes = []
    //     if (valueArr[0] != 0) {
    //         for (let i = 0; i < 24; i++) {
    //             let str = i;
    //             if (i < 10) {
    //                 str = '0' + str;
    //             } else {
    //                 str = '' + str;
    //             }
    //             this.hours.push(str);
    //         }
    //         for (let i = 0; i < 60; i++) {
    //             let str = i;
    //             if (i < 10) {
    //                 str = '0' + str;
    //             } else {
    //                 str = '' + str;
    //             }
    //             this.minutes.push(str);
    //         }
    //     } else {
    //         let h = parseInt(moment().format("HH"))
    //         this.hours = []
    //         for (let i = h; i < 24; i++) {
    //             let str = i;
    //             if (i < 10) {
    //                 str = '0' + str;
    //             } else {
    //                 str = '' + str;
    //             }
    //             this.hours.push(str);
    //         }
    //         this.minutes = []
    //         let m = parseInt(moment().format("mm"))
    //         for (let i = m; i < 60; i++) {
    //             let str = i;
    //             if (i < 10) {
    //                 str = '0' + str;
    //             } else {
    //                 str = '' + str;
    //             }
    //             this.minutes.push(str);
    //         }
    //     }

    //     let dateStr = this.formatdates[valueArr[0]];
    //     let hourStr = this.hours[valueArr[1]];
    //     let minuteStr = this.minutes[valueArr[2]];
    //     console.log(dateStr + ' ' + hourStr + ':' + minuteStr)
    //     this.$emit("select", {
    //         time: moment(dateStr + ' ' + hourStr + ':' + minuteStr).format("YYYY-MM-DD HH:mm")
    //     });

  },
};
</script>
  
<style scoped>
body {
  background-color: #f5f7fa;
}
</style>
  
<style lang="scss" scoped></style>
  