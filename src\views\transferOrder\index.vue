<template>
  <div class="app-container">
    <el-form :inline="true" class="container-card" label-width="96px" label-position="left" ref="dataQuery" :model="dataQuery" :rules="rules">
      <el-row>
        <el-col :span="6" :xs="24">
          <el-form-item label="姓名：" prop="studentName">
            <el-input id="studentName" v-model="dataQuery.studentName" name="id" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="学员编号：" prop="studentCode">
            <el-input id="studentCode" v-model="dataQuery.studentCode" name="id" placeholder="请输入学员编号" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="联系方式：" prop="phone">
            <el-input id="phone" v-model="dataQuery.phone" name="id" placeholder="请输入联系方式" type="text" oninput="value=value.replace(/[^\d]/g, '')" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="是否成交:" prop="hasDeal">
            <el-select v-model="dataQuery.hasDeal" filterable value-key="value" placeholder="请选择" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '已成交' },
                  { value: 0, label: '未成交' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="6" :xs="24">
          <el-form-item label="课程类型:" prop="isEnable">
            <el-select v-model="dataQuery.curriculumName" filterable value-key="value" placeholder="请选择课程类型" clearable>
              <el-option
                v-for="(item, index) in [
                  { value: 1, label: '鼎英语' },
                  { value: 0, label: '鼎学能' },
                  { value: -1, label: '珠心算' },
                  { value: -2, label: '拼音法' }
                ]"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
      <!-- ----------------------------- -->
      <el-row>
        <el-col :span="8" :xs="20">
          <el-form-item label="创建时间：" prop="regTime">
            <el-date-picker
              style="width: 100%"
              v-model="regTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="推荐人：" prop="referrerName">
            <el-input id="referrerName" v-model="dataQuery.referrerName" name="id" placeholder="请输入推荐人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="24">
          <el-form-item label="推荐人手机号：" label-width="120px" prop="referrerPhone">
            <el-input
              id="referrerPhone"
              v-model="dataQuery.referrerPhone"
              name="id"
              placeholder="请输入推荐人手机号"
              type="text"
              oninput="value=value.replace(/[^\d]/g, '')"
              maxlength="11"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" :xs="20" style="text-align: right">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="
                () => {
                  this.tablePage.currentPage = 1;
                  fetchData();
                }
              "
            >
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="rest()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table
      class="common-table"
      v-loading="tableLoading"
      :data="tableData"
      id="out-table"
      height="400px"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      default-expand-all
      :tree-props="{ list: 'children', hasChildren: 'true' }"
      :header-cell-style="{ color: '#666', height: '50px' }"
      :row-style="{ height: '40px' }"
    >
      <el-table-column prop="studentName" label="姓名" width="150px"></el-table-column>
      <el-table-column prop="studentCode" label="学员编号" width="150px"></el-table-column>
      <el-table-column prop="expTime" label="试课时间" width="200px">
        <template v-slot="{ row }">
          <el-tag>{{ row.expTime }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="curriculumName" label="课程类型" width="100px"></el-table-column>
      <el-table-column prop="phone" label="联系方式" width="150px"></el-table-column>
      <el-table-column prop="referrerName" label="推荐人" width="100px"></el-table-column>
      <el-table-column prop="referrerPhone" label="推荐人手机号" width="150px"></el-table-column>
      <el-table-column prop="hasDeal" label="是否成交" width="150px">
        <template v-slot="{ row }">
          <el-tag type="success" v-if="row.hasDeal == 1">已成交</el-tag>
          <el-tag type="danger" v-else>未成交</el-tag>
          <!-- <div v-else-if="row.hasDeal == 2" class="green">成交中</div> -->
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="成交金额" width="150px">
        <template v-slot="{ row }">
          <span v-if="row.amount == 0">-</span>
          <span v-else>{{ (row.amount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="incomeAmount" label="提成">
        <template v-slot="{ row }">
          <span v-if="row.incomeAmount == 0">-</span>
          <span v-else>{{ (row.incomeAmount * 0.01).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="dealCourseNum" label="成交课时" width="150px"></el-table-column>
      <el-table-column prop="dealDeliverCourseNum" label="成交交付课时" width="150px"></el-table-column>
      <el-table-column prop="waitCourseNum" label="待生效课时" width="150px"></el-table-column>
      <el-table-column prop="waitDeliverCourseNum" label="待生效交付课时" width="150px"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200px">
        <template v-slot="{ row }">
          <el-tag>{{ row.createTime }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150px" fixed="right">
        <template v-slot="{ row }">
          <span type="text" v-if="row.hasOvertime"></span>
          <el-button type="text" v-else-if="row.hasDeal == 0 && row.waitCourseNum != 0 && row.waitDeliverCourseNum != 0" @click="openPay(row)">修改</el-button>
          <el-button type="text" v-else-if="row.hasDeal == 0" @click="openPay(row)">充值</el-button>
          <span type="text" v-else-if="row.hasDeal == 2">-</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-col :span="24" style="overflow-x: auto" :xs="24">
      <el-pagination
        :current-page="tablePage.currentPage"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tablePage.totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-col>
    <el-dialog title="充值/修改课时" :visible.sync="dialogVisible" width="30%" @close="closePay()">
      <el-form :inline="true" label-width="120px" label-position="left" ref="rechargeData" :model="rechargeData" v-loading="dialogLoading" :rules="dialogRules">
        <el-form-item label="学段:" prop="level" class="w100">
          <el-select v-model="rechargeData.level" filterable value-key="value" placeholder="请选择" clearable>
            <el-option
              v-for="(item, index) in [
                { value: 0, label: '小学' },
                { value: 1, label: '初中' },
                { value: 2, label: '高中' }
              ]"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="充值时长：" prop="courseNum" class="w100">
          <el-input
            id="merchantName"
            type="text"
            v-model="rechargeData.courseNum"
            name="id"
            placeholder="请输入充值时长："
            oninput="value=value.replace(/[^\d]/g,'')"
            maxlength="5"
            @blur="checkCourseNum"
          />
        </el-form-item>
        <el-form-item label="充值交付时长：" prop="deliverCourseNum" class="w100">
          <el-input
            id="deliverCourseNum"
            type="text"
            v-model="rechargeData.deliverCourseNum"
            name="id"
            placeholder="请输入充值交付时长："
            oninput="value=value.replace(/[^\d]/g,'')"
            maxlength="5"
            @blur="checkDeliverCourseNum"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closePay" :disabled="dialogLoading">取 消</el-button>
        <el-button type="primary" @click="confirmPay" :disabled="dialogLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { ossPrClient } from '@/api/alibaba';
  import orderApi from '@/api/transferOrder';
  import { dxSource, pageParamNames } from '@/utils/constants';
  import store from '@/store';
  export default {
    data() {
      return {
        token: store.getters.token,
        //充值课时
        dialogVisible: false,
        dialogLoading: false,
        // 分页
        tablePage: {
          currentPage: 1,
          size: 10,
          totalPage: null,
          totalItems: null
        },
        tableData: [],
        // 查询条件
        dataQuery: {
          studentName: '',
          studentCode: '',
          phone: '',
          referrerName: '',
          referrerPhone: '',
          hasDeal: ''
        },
        dataQuery2: {
          merchantCode: '',
          merchantName: '',
          isEnable: ''
        },
        regTime: '',
        tableLoading: false,
        //规则
        rules: {
          studentName: [{ min: 0, max: 10, message: '姓名最多输入10个字符', trigger: 'blur' }],
          referrerName: [{ min: 0, max: 10, message: '推荐人姓名最多输入10个字符', trigger: 'blur' }],
          studentCode: [
            { min: 0, max: 10, message: '编号最多输入10个数字', trigger: 'blur' },
            { pattern: /^\d*$/, message: '请输入数字编号', trigger: 'blur' }
          ],
          phone: [
            { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ],
          referrerPhone: [
            { min: 11, max: 11, message: '请输入11位手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
          ]
        },
        dialogRules: {
          //必填 不能等于0
          level: [{ required: true, message: '请选择学段', trigger: 'change' }],
          courseNum: [
            { required: true, message: '请输入充值时长', trigger: 'blur' },
            { pattern: /^[1-9]\d*$/, message: '请输入大于0的课时数', trigger: 'input' }
          ],
          deliverCourseNum: [
            { required: true, message: '请输入交付时长', trigger: 'blur' },
            { pattern: /^[1-9]\d*$/, message: '请输入大于0的数字', trigger: 'input' }
          ]
        },
        //充值数据
        rechargeData: {
          id: null,
          studentCode: null,
          merchantCode: null,
          deliverCourseNum: null,
          courseNum: null,
          level: null
        }
      };
    },
    computed: {
      ...mapGetters(['setpayUrl'])
    },
    mounted() {
      this.fetchData();
      ossPrClient();
    },
    methods: {
      // 分页
      handleSizeChange(val) {
        this.tablePage.size = val;
        this.fetchData();
      },
      handleCurrentChange(val) {
        this.tablePage.currentPage = val;
        this.fetchData();
      },
      //重置
      rest() {
        this.$refs.dataQuery.resetFields();
        this.regTime = '';
        this.tablePage.currentPage = 1;
        this.fetchData();
      },
      // 查询提现列表
      fetchData() {
        this.$refs['dataQuery'].validate((valid) => {
          if (valid) {
            const that = this;
            that.tableLoading = true;
            var a = that.regTime;
            console.log(a);

            if (a != null) {
              that.dataQuery.startTime = a[0];
              that.dataQuery.endTime = a[1];
            } else {
              delete that.dataQuery.startTime;
              delete that.dataQuery.endTime;
            }
            orderApi.queryList(this.dataQuery, this.tablePage).then((res) => {
              this.tableData = res.data.data ? res.data.data : [];
              this.tableLoading = false;
              // 设置分页
              pageParamNames.forEach((name) => this.$set(this.tablePage, name, Number(res.data[name])));
            });
          }
        });
      },
      //打开充值弹窗
      openPay(row) {
        console.log(row);
        this.dialogVisible = true;
        this.rechargeData = {
          id: row.id,
          studentCode: row.studentCode,
          // merchantCode: row.merchantCode,
          deliverCourseNum: null,
          courseNum: null,
          level: row.level
        };
      },
      closePay() {
        this.dialogVisible = false;
        this.rechargeData = {
          id: null,
          studentCode: null,
          merchantCode: null,
          deliverCourseNum: null,
          courseNum: null,
          level: null
        };
        this.$nextTick(() => {
          this.$refs['rechargeData'].resetFields();
        });
      },
      //确认充值
      confirmPay() {
        const that = this;
        this.$refs['rechargeData'].validate((valid) => {
          if (!valid) return;
          this.dialogLoading = true;
          orderApi
            .goPay(this.rechargeData)
            .then((res) => {
              that.dialogLoading = false;
              that.dialogVisible = false;
              that.fetchData();
              this.$message.success('充值成功');
              // console.log(res);
              // const split = dxSource.split('##'); //["ZNYY", "BROWSER", "WEB"]
              // res.data.dxSource = res.data.registerCallIInfo.appSource + '##' + split[1] + '##' + split[2];
              // let params = JSON.stringify(res.data);
              // let req = 'token=' + this.token + '&params=' + params + '&back=' + window.location.href;
              // //需要编码两遍，避免出现+号等
              // var encode = Base64.encode(Base64.encode(req));
              // window.open(this.setpayUrl + 'product?' + encode, '_blank');
            })
            .catch((err) => {
              that.dialogLoading = false;
              that.dialogVisible = false;
            });
        });
      },
      checkCourseNum(event) {
        // 获取当前输入框的值
        let currentValue = event.target.value;
        if (currentValue * 1) {
          this.rechargeData.courseNum = currentValue * 1;
        } else {
          this.rechargeData.courseNum = '';
        }
      },
      checkDeliverCourseNum(event) {
        // 获取当前输入框的值
        let currentValue = event.target.value;
        if (currentValue * 1) {
          this.rechargeData.deliverCourseNum = currentValue * 1;
        } else {
          this.rechargeData.deliverCourseNum = '';
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container-card {
    padding: 15px 15px 15px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .info {
    color: #dcdfe6 !important;
  }
  .image {
    width: 80% !important;
    margin: 0 auto;
    display: block;
  }
  .w100 {
    width: 100% !important;
    display: block !important;
  }
  .el-form-item.w100 > :last-child {
    width: calc(100% - 120px) !important;
    // :last-child {
    //   width: 100% !important;
    // }
    .el-select {
      width: 100% !important;
    }
  }
</style>
